"use strict";var mD=Object.create;var $=Object.defineProperty;var dD=Object.getOwnPropertyDescriptor;var _D=Object.getOwnPropertyNames;var pD=Object.getPrototypeOf,AD=Object.prototype.hasOwnProperty;var Y=(u,D)=>()=>(D||u((D={exports:{}}).exports,D),D.exports),gD=(u,D)=>{for(var e in D)$(u,e,{get:D[e],enumerable:!0})},vu=(u,D,e,t)=>{if(D&&typeof D=="object"||typeof D=="function")for(let r of _D(D))!AD.call(u,r)&&r!==e&&$(u,r,{get:()=>D[r],enumerable:!(t=dD(D,r))||t.enumerable});return u};var c=(u,D,e)=>(e=u!=null?mD(pD(u)):{},vu(D||!u||!u.__esModule?$(e,"default",{value:u,enumerable:!0}):e,u)),bD=u=>vu($({},"__esModule",{value:!0}),u);var wu=Y((ve,Ou)=>{var xD=require("node:tty"),TD=xD?.WriteStream?.prototype?.hasColors?.()??!1,s=(u,D)=>{if(!TD)return r=>r;let e=`\x1B[${u}m`,t=`\x1B[${D}m`;return r=>{let n=r+"",i=n.indexOf(t);if(i===-1)return e+n+t;let F=e,x=0;for(;i!==-1;)F+=n.slice(x,i)+e,x=i+t.length,i=n.indexOf(t,x);return F+=n.slice(x)+t,F}},o={};o.reset=s(0,0);o.bold=s(1,22);o.dim=s(2,22);o.italic=s(3,23);o.underline=s(4,24);o.overline=s(53,55);o.inverse=s(7,27);o.hidden=s(8,28);o.strikethrough=s(9,29);o.black=s(30,39);o.red=s(31,39);o.green=s(32,39);o.yellow=s(33,39);o.blue=s(34,39);o.magenta=s(35,39);o.cyan=s(36,39);o.white=s(37,39);o.gray=s(90,39);o.bgBlack=s(40,49);o.bgRed=s(41,49);o.bgGreen=s(42,49);o.bgYellow=s(43,49);o.bgBlue=s(44,49);o.bgMagenta=s(45,49);o.bgCyan=s(46,49);o.bgWhite=s(47,49);o.bgGray=s(100,49);o.redBright=s(91,39);o.greenBright=s(92,39);o.yellowBright=s(93,39);o.blueBright=s(94,39);o.magentaBright=s(95,39);o.cyanBright=s(96,39);o.whiteBright=s(97,39);o.bgRedBright=s(101,49);o.bgGreenBright=s(102,49);o.bgYellowBright=s(103,49);o.bgBlueBright=s(104,49);o.bgMagentaBright=s(105,49);o.bgCyanBright=s(106,49);o.bgWhiteBright=s(107,49);Ou.exports=o});var qu=Y((Xe,JD)=>{JD.exports={dots:{interval:80,frames:["\u280B","\u2819","\u2839","\u2838","\u283C","\u2834","\u2826","\u2827","\u2807","\u280F"]},dots2:{interval:80,frames:["\u28FE","\u28FD","\u28FB","\u28BF","\u287F","\u28DF","\u28EF","\u28F7"]},dots3:{interval:80,frames:["\u280B","\u2819","\u281A","\u281E","\u2816","\u2826","\u2834","\u2832","\u2833","\u2813"]},dots4:{interval:80,frames:["\u2804","\u2806","\u2807","\u280B","\u2819","\u2838","\u2830","\u2820","\u2830","\u2838","\u2819","\u280B","\u2807","\u2806"]},dots5:{interval:80,frames:["\u280B","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B"]},dots6:{interval:80,frames:["\u2801","\u2809","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2824","\u2804","\u2804","\u2824","\u2834","\u2832","\u2812","\u2802","\u2802","\u2812","\u281A","\u2819","\u2809","\u2801"]},dots7:{interval:80,frames:["\u2808","\u2809","\u280B","\u2813","\u2812","\u2810","\u2810","\u2812","\u2816","\u2826","\u2824","\u2820","\u2820","\u2824","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B","\u2809","\u2808"]},dots8:{interval:80,frames:["\u2801","\u2801","\u2809","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2824","\u2804","\u2804","\u2824","\u2820","\u2820","\u2824","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B","\u2809","\u2808","\u2808"]},dots9:{interval:80,frames:["\u28B9","\u28BA","\u28BC","\u28F8","\u28C7","\u2867","\u2857","\u284F"]},dots10:{interval:80,frames:["\u2884","\u2882","\u2881","\u2841","\u2848","\u2850","\u2860"]},dots11:{interval:100,frames:["\u2801","\u2802","\u2804","\u2840","\u2880","\u2820","\u2810","\u2808"]},dots12:{interval:80,frames:["\u2880\u2800","\u2840\u2800","\u2804\u2800","\u2882\u2800","\u2842\u2800","\u2805\u2800","\u2883\u2800","\u2843\u2800","\u280D\u2800","\u288B\u2800","\u284B\u2800","\u280D\u2801","\u288B\u2801","\u284B\u2801","\u280D\u2809","\u280B\u2809","\u280B\u2809","\u2809\u2819","\u2809\u2819","\u2809\u2829","\u2808\u2899","\u2808\u2859","\u2888\u2829","\u2840\u2899","\u2804\u2859","\u2882\u2829","\u2842\u2898","\u2805\u2858","\u2883\u2828","\u2843\u2890","\u280D\u2850","\u288B\u2820","\u284B\u2880","\u280D\u2841","\u288B\u2801","\u284B\u2801","\u280D\u2809","\u280B\u2809","\u280B\u2809","\u2809\u2819","\u2809\u2819","\u2809\u2829","\u2808\u2899","\u2808\u2859","\u2808\u2829","\u2800\u2899","\u2800\u2859","\u2800\u2829","\u2800\u2898","\u2800\u2858","\u2800\u2828","\u2800\u2890","\u2800\u2850","\u2800\u2820","\u2800\u2880","\u2800\u2840"]},dots13:{interval:80,frames:["\u28FC","\u28F9","\u28BB","\u283F","\u285F","\u28CF","\u28E7","\u28F6"]},dots8Bit:{interval:80,frames:["\u2800","\u2801","\u2802","\u2803","\u2804","\u2805","\u2806","\u2807","\u2840","\u2841","\u2842","\u2843","\u2844","\u2845","\u2846","\u2847","\u2808","\u2809","\u280A","\u280B","\u280C","\u280D","\u280E","\u280F","\u2848","\u2849","\u284A","\u284B","\u284C","\u284D","\u284E","\u284F","\u2810","\u2811","\u2812","\u2813","\u2814","\u2815","\u2816","\u2817","\u2850","\u2851","\u2852","\u2853","\u2854","\u2855","\u2856","\u2857","\u2818","\u2819","\u281A","\u281B","\u281C","\u281D","\u281E","\u281F","\u2858","\u2859","\u285A","\u285B","\u285C","\u285D","\u285E","\u285F","\u2820","\u2821","\u2822","\u2823","\u2824","\u2825","\u2826","\u2827","\u2860","\u2861","\u2862","\u2863","\u2864","\u2865","\u2866","\u2867","\u2828","\u2829","\u282A","\u282B","\u282C","\u282D","\u282E","\u282F","\u2868","\u2869","\u286A","\u286B","\u286C","\u286D","\u286E","\u286F","\u2830","\u2831","\u2832","\u2833","\u2834","\u2835","\u2836","\u2837","\u2870","\u2871","\u2872","\u2873","\u2874","\u2875","\u2876","\u2877","\u2838","\u2839","\u283A","\u283B","\u283C","\u283D","\u283E","\u283F","\u2878","\u2879","\u287A","\u287B","\u287C","\u287D","\u287E","\u287F","\u2880","\u2881","\u2882","\u2883","\u2884","\u2885","\u2886","\u2887","\u28C0","\u28C1","\u28C2","\u28C3","\u28C4","\u28C5","\u28C6","\u28C7","\u2888","\u2889","\u288A","\u288B","\u288C","\u288D","\u288E","\u288F","\u28C8","\u28C9","\u28CA","\u28CB","\u28CC","\u28CD","\u28CE","\u28CF","\u2890","\u2891","\u2892","\u2893","\u2894","\u2895","\u2896","\u2897","\u28D0","\u28D1","\u28D2","\u28D3","\u28D4","\u28D5","\u28D6","\u28D7","\u2898","\u2899","\u289A","\u289B","\u289C","\u289D","\u289E","\u289F","\u28D8","\u28D9","\u28DA","\u28DB","\u28DC","\u28DD","\u28DE","\u28DF","\u28A0","\u28A1","\u28A2","\u28A3","\u28A4","\u28A5","\u28A6","\u28A7","\u28E0","\u28E1","\u28E2","\u28E3","\u28E4","\u28E5","\u28E6","\u28E7","\u28A8","\u28A9","\u28AA","\u28AB","\u28AC","\u28AD","\u28AE","\u28AF","\u28E8","\u28E9","\u28EA","\u28EB","\u28EC","\u28ED","\u28EE","\u28EF","\u28B0","\u28B1","\u28B2","\u28B3","\u28B4","\u28B5","\u28B6","\u28B7","\u28F0","\u28F1","\u28F2","\u28F3","\u28F4","\u28F5","\u28F6","\u28F7","\u28B8","\u28B9","\u28BA","\u28BB","\u28BC","\u28BD","\u28BE","\u28BF","\u28F8","\u28F9","\u28FA","\u28FB","\u28FC","\u28FD","\u28FE","\u28FF"]},sand:{interval:80,frames:["\u2801","\u2802","\u2804","\u2840","\u2848","\u2850","\u2860","\u28C0","\u28C1","\u28C2","\u28C4","\u28CC","\u28D4","\u28E4","\u28E5","\u28E6","\u28EE","\u28F6","\u28F7","\u28FF","\u287F","\u283F","\u289F","\u281F","\u285B","\u281B","\u282B","\u288B","\u280B","\u280D","\u2849","\u2809","\u2811","\u2821","\u2881"]},line:{interval:130,frames:["-","\\","|","/"]},line2:{interval:100,frames:["\u2802","-","\u2013","\u2014","\u2013","-"]},pipe:{interval:100,frames:["\u2524","\u2518","\u2534","\u2514","\u251C","\u250C","\u252C","\u2510"]},simpleDots:{interval:400,frames:[".  ",".. ","...","   "]},simpleDotsScrolling:{interval:200,frames:[".  ",".. ","..."," ..","  .","   "]},star:{interval:70,frames:["\u2736","\u2738","\u2739","\u273A","\u2739","\u2737"]},star2:{interval:80,frames:["+","x","*"]},flip:{interval:70,frames:["_","_","_","-","`","`","'","\xB4","-","_","_","_"]},hamburger:{interval:100,frames:["\u2631","\u2632","\u2634"]},growVertical:{interval:120,frames:["\u2581","\u2583","\u2584","\u2585","\u2586","\u2587","\u2586","\u2585","\u2584","\u2583"]},growHorizontal:{interval:120,frames:["\u258F","\u258E","\u258D","\u258C","\u258B","\u258A","\u2589","\u258A","\u258B","\u258C","\u258D","\u258E"]},balloon:{interval:140,frames:[" ",".","o","O","@","*"," "]},balloon2:{interval:120,frames:[".","o","O","\xB0","O","o","."]},noise:{interval:100,frames:["\u2593","\u2592","\u2591"]},bounce:{interval:120,frames:["\u2801","\u2802","\u2804","\u2802"]},boxBounce:{interval:120,frames:["\u2596","\u2598","\u259D","\u2597"]},boxBounce2:{interval:100,frames:["\u258C","\u2580","\u2590","\u2584"]},triangle:{interval:50,frames:["\u25E2","\u25E3","\u25E4","\u25E5"]},binary:{interval:80,frames:["010010","001100","100101","111010","111101","010111","101011","111000","110011","110101"]},arc:{interval:100,frames:["\u25DC","\u25E0","\u25DD","\u25DE","\u25E1","\u25DF"]},circle:{interval:120,frames:["\u25E1","\u2299","\u25E0"]},squareCorners:{interval:180,frames:["\u25F0","\u25F3","\u25F2","\u25F1"]},circleQuarters:{interval:120,frames:["\u25F4","\u25F7","\u25F6","\u25F5"]},circleHalves:{interval:50,frames:["\u25D0","\u25D3","\u25D1","\u25D2"]},squish:{interval:100,frames:["\u256B","\u256A"]},toggle:{interval:250,frames:["\u22B6","\u22B7"]},toggle2:{interval:80,frames:["\u25AB","\u25AA"]},toggle3:{interval:120,frames:["\u25A1","\u25A0"]},toggle4:{interval:100,frames:["\u25A0","\u25A1","\u25AA","\u25AB"]},toggle5:{interval:100,frames:["\u25AE","\u25AF"]},toggle6:{interval:300,frames:["\u101D","\u1040"]},toggle7:{interval:80,frames:["\u29BE","\u29BF"]},toggle8:{interval:100,frames:["\u25CD","\u25CC"]},toggle9:{interval:100,frames:["\u25C9","\u25CE"]},toggle10:{interval:100,frames:["\u3282","\u3280","\u3281"]},toggle11:{interval:50,frames:["\u29C7","\u29C6"]},toggle12:{interval:120,frames:["\u2617","\u2616"]},toggle13:{interval:80,frames:["=","*","-"]},arrow:{interval:100,frames:["\u2190","\u2196","\u2191","\u2197","\u2192","\u2198","\u2193","\u2199"]},arrow2:{interval:80,frames:["\u2B06\uFE0F ","\u2197\uFE0F ","\u27A1\uFE0F ","\u2198\uFE0F ","\u2B07\uFE0F ","\u2199\uFE0F ","\u2B05\uFE0F ","\u2196\uFE0F "]},arrow3:{interval:120,frames:["\u25B9\u25B9\u25B9\u25B9\u25B9","\u25B8\u25B9\u25B9\u25B9\u25B9","\u25B9\u25B8\u25B9\u25B9\u25B9","\u25B9\u25B9\u25B8\u25B9\u25B9","\u25B9\u25B9\u25B9\u25B8\u25B9","\u25B9\u25B9\u25B9\u25B9\u25B8"]},bouncingBar:{interval:80,frames:["[    ]","[=   ]","[==  ]","[=== ]","[====]","[ ===]","[  ==]","[   =]","[    ]","[   =]","[  ==]","[ ===]","[====]","[=== ]","[==  ]","[=   ]"]},bouncingBall:{interval:80,frames:["( \u25CF    )","(  \u25CF   )","(   \u25CF  )","(    \u25CF )","(     \u25CF)","(    \u25CF )","(   \u25CF  )","(  \u25CF   )","( \u25CF    )","(\u25CF     )"]},smiley:{interval:200,frames:["\u{1F604} ","\u{1F61D} "]},monkey:{interval:300,frames:["\u{1F648} ","\u{1F648} ","\u{1F649} ","\u{1F64A} "]},hearts:{interval:100,frames:["\u{1F49B} ","\u{1F499} ","\u{1F49C} ","\u{1F49A} ","\u2764\uFE0F "]},clock:{interval:100,frames:["\u{1F55B} ","\u{1F550} ","\u{1F551} ","\u{1F552} ","\u{1F553} ","\u{1F554} ","\u{1F555} ","\u{1F556} ","\u{1F557} ","\u{1F558} ","\u{1F559} ","\u{1F55A} "]},earth:{interval:180,frames:["\u{1F30D} ","\u{1F30E} ","\u{1F30F} "]},material:{interval:17,frames:["\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581"]},moon:{interval:80,frames:["\u{1F311} ","\u{1F312} ","\u{1F313} ","\u{1F314} ","\u{1F315} ","\u{1F316} ","\u{1F317} ","\u{1F318} "]},runner:{interval:140,frames:["\u{1F6B6} ","\u{1F3C3} "]},pong:{interval:80,frames:["\u2590\u2802       \u258C","\u2590\u2808       \u258C","\u2590 \u2802      \u258C","\u2590 \u2820      \u258C","\u2590  \u2840     \u258C","\u2590  \u2820     \u258C","\u2590   \u2802    \u258C","\u2590   \u2808    \u258C","\u2590    \u2802   \u258C","\u2590    \u2820   \u258C","\u2590     \u2840  \u258C","\u2590     \u2820  \u258C","\u2590      \u2802 \u258C","\u2590      \u2808 \u258C","\u2590       \u2802\u258C","\u2590       \u2820\u258C","\u2590       \u2840\u258C","\u2590      \u2820 \u258C","\u2590      \u2802 \u258C","\u2590     \u2808  \u258C","\u2590     \u2802  \u258C","\u2590    \u2820   \u258C","\u2590    \u2840   \u258C","\u2590   \u2820    \u258C","\u2590   \u2802    \u258C","\u2590  \u2808     \u258C","\u2590  \u2802     \u258C","\u2590 \u2820      \u258C","\u2590 \u2840      \u258C","\u2590\u2820       \u258C"]},shark:{interval:120,frames:["\u2590|\\____________\u258C","\u2590_|\\___________\u258C","\u2590__|\\__________\u258C","\u2590___|\\_________\u258C","\u2590____|\\________\u258C","\u2590_____|\\_______\u258C","\u2590______|\\______\u258C","\u2590_______|\\_____\u258C","\u2590________|\\____\u258C","\u2590_________|\\___\u258C","\u2590__________|\\__\u258C","\u2590___________|\\_\u258C","\u2590____________|\\\u258C","\u2590____________/|\u258C","\u2590___________/|_\u258C","\u2590__________/|__\u258C","\u2590_________/|___\u258C","\u2590________/|____\u258C","\u2590_______/|_____\u258C","\u2590______/|______\u258C","\u2590_____/|_______\u258C","\u2590____/|________\u258C","\u2590___/|_________\u258C","\u2590__/|__________\u258C","\u2590_/|___________\u258C","\u2590/|____________\u258C"]},dqpb:{interval:100,frames:["d","q","p","b"]},weather:{interval:100,frames:["\u2600\uFE0F ","\u2600\uFE0F ","\u2600\uFE0F ","\u{1F324} ","\u26C5\uFE0F ","\u{1F325} ","\u2601\uFE0F ","\u{1F327} ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u26C8 ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u2601\uFE0F ","\u{1F325} ","\u26C5\uFE0F ","\u{1F324} ","\u2600\uFE0F ","\u2600\uFE0F "]},christmas:{interval:400,frames:["\u{1F332}","\u{1F384}"]},grenade:{interval:80,frames:["\u060C  ","\u2032  "," \xB4 "," \u203E ","  \u2E0C","  \u2E0A","  |","  \u204E","  \u2055"," \u0DF4 ","  \u2053","   ","   ","   "]},point:{interval:125,frames:["\u2219\u2219\u2219","\u25CF\u2219\u2219","\u2219\u25CF\u2219","\u2219\u2219\u25CF","\u2219\u2219\u2219"]},layer:{interval:150,frames:["-","=","\u2261"]},betaWave:{interval:80,frames:["\u03C1\u03B2\u03B2\u03B2\u03B2\u03B2\u03B2","\u03B2\u03C1\u03B2\u03B2\u03B2\u03B2\u03B2","\u03B2\u03B2\u03C1\u03B2\u03B2\u03B2\u03B2","\u03B2\u03B2\u03B2\u03C1\u03B2\u03B2\u03B2","\u03B2\u03B2\u03B2\u03B2\u03C1\u03B2\u03B2","\u03B2\u03B2\u03B2\u03B2\u03B2\u03C1\u03B2","\u03B2\u03B2\u03B2\u03B2\u03B2\u03B2\u03C1"]},fingerDance:{interval:160,frames:["\u{1F918} ","\u{1F91F} ","\u{1F596} ","\u270B ","\u{1F91A} ","\u{1F446} "]},fistBump:{interval:80,frames:["\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u3000\u{1F91C}\u3000\u3000\u{1F91B}\u3000 ","\u3000\u3000\u{1F91C}\u{1F91B}\u3000\u3000 ","\u3000\u{1F91C}\u2728\u{1F91B}\u3000\u3000 ","\u{1F91C}\u3000\u2728\u3000\u{1F91B}\u3000 "]},soccerHeader:{interval:80,frames:[" \u{1F9D1}\u26BD\uFE0F       \u{1F9D1} ","\u{1F9D1}  \u26BD\uFE0F      \u{1F9D1} ","\u{1F9D1}   \u26BD\uFE0F     \u{1F9D1} ","\u{1F9D1}    \u26BD\uFE0F    \u{1F9D1} ","\u{1F9D1}     \u26BD\uFE0F   \u{1F9D1} ","\u{1F9D1}      \u26BD\uFE0F  \u{1F9D1} ","\u{1F9D1}       \u26BD\uFE0F\u{1F9D1}  ","\u{1F9D1}      \u26BD\uFE0F  \u{1F9D1} ","\u{1F9D1}     \u26BD\uFE0F   \u{1F9D1} ","\u{1F9D1}    \u26BD\uFE0F    \u{1F9D1} ","\u{1F9D1}   \u26BD\uFE0F     \u{1F9D1} ","\u{1F9D1}  \u26BD\uFE0F      \u{1F9D1} "]},mindblown:{interval:160,frames:["\u{1F610} ","\u{1F610} ","\u{1F62E} ","\u{1F62E} ","\u{1F626} ","\u{1F626} ","\u{1F627} ","\u{1F627} ","\u{1F92F} ","\u{1F4A5} ","\u2728 ","\u3000 ","\u3000 ","\u3000 "]},speaker:{interval:160,frames:["\u{1F508} ","\u{1F509} ","\u{1F50A} ","\u{1F509} "]},orangePulse:{interval:100,frames:["\u{1F538} ","\u{1F536} ","\u{1F7E0} ","\u{1F7E0} ","\u{1F536} "]},bluePulse:{interval:100,frames:["\u{1F539} ","\u{1F537} ","\u{1F535} ","\u{1F535} ","\u{1F537} "]},orangeBluePulse:{interval:100,frames:["\u{1F538} ","\u{1F536} ","\u{1F7E0} ","\u{1F7E0} ","\u{1F536} ","\u{1F539} ","\u{1F537} ","\u{1F535} ","\u{1F535} ","\u{1F537} "]},timeTravel:{interval:100,frames:["\u{1F55B} ","\u{1F55A} ","\u{1F559} ","\u{1F558} ","\u{1F557} ","\u{1F556} ","\u{1F555} ","\u{1F554} ","\u{1F553} ","\u{1F552} ","\u{1F551} ","\u{1F550} "]},aesthetic:{interval:80,frames:["\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0","\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1\u25B1"]},dwarfFortress:{interval:80,frames:[" \u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2593\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2593\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2592\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2592\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2591\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2591\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A \u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2593\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2593\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2592\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2592\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2591\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2591\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A \u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2593\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2593\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2592\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2592\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2591\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2591\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A \u2588\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2593\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2593\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2592\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2592\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2591\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2591\u2588\u2588\xA3\xA3\xA3  ","   \u263A \u2588\u2588\xA3\xA3\xA3  ","    \u263A\u2588\u2588\xA3\xA3\xA3  ","    \u263A\u2588\u2588\xA3\xA3\xA3  ","    \u263A\u2593\u2588\xA3\xA3\xA3  ","    \u263A\u2593\u2588\xA3\xA3\xA3  ","    \u263A\u2592\u2588\xA3\xA3\xA3  ","    \u263A\u2592\u2588\xA3\xA3\xA3  ","    \u263A\u2591\u2588\xA3\xA3\xA3  ","    \u263A\u2591\u2588\xA3\xA3\xA3  ","    \u263A \u2588\xA3\xA3\xA3  ","     \u263A\u2588\xA3\xA3\xA3  ","     \u263A\u2588\xA3\xA3\xA3  ","     \u263A\u2593\xA3\xA3\xA3  ","     \u263A\u2593\xA3\xA3\xA3  ","     \u263A\u2592\xA3\xA3\xA3  ","     \u263A\u2592\xA3\xA3\xA3  ","     \u263A\u2591\xA3\xA3\xA3  ","     \u263A\u2591\xA3\xA3\xA3  ","     \u263A \xA3\xA3\xA3  ","      \u263A\xA3\xA3\xA3  ","      \u263A\xA3\xA3\xA3  ","      \u263A\u2593\xA3\xA3  ","      \u263A\u2593\xA3\xA3  ","      \u263A\u2592\xA3\xA3  ","      \u263A\u2592\xA3\xA3  ","      \u263A\u2591\xA3\xA3  ","      \u263A\u2591\xA3\xA3  ","      \u263A \xA3\xA3  ","       \u263A\xA3\xA3  ","       \u263A\xA3\xA3  ","       \u263A\u2593\xA3  ","       \u263A\u2593\xA3  ","       \u263A\u2592\xA3  ","       \u263A\u2592\xA3  ","       \u263A\u2591\xA3  ","       \u263A\u2591\xA3  ","       \u263A \xA3  ","        \u263A\xA3  ","        \u263A\xA3  ","        \u263A\u2593  ","        \u263A\u2593  ","        \u263A\u2592  ","        \u263A\u2592  ","        \u263A\u2591  ","        \u263A\u2591  ","        \u263A   ","        \u263A  &","        \u263A \u263C&","       \u263A \u263C &","       \u263A\u263C  &","      \u263A\u263C  & ","      \u203C   & ","     \u263A   &  ","    \u203C    &  ","   \u263A    &   ","  \u203C     &   "," \u263A     &    ","\u203C      &    ","      &     ","      &     ","     &   \u2591  ","     &   \u2592  ","    &    \u2593  ","    &    \xA3  ","   &    \u2591\xA3  ","   &    \u2592\xA3  ","  &     \u2593\xA3  ","  &     \xA3\xA3  "," &     \u2591\xA3\xA3  "," &     \u2592\xA3\xA3  ","&      \u2593\xA3\xA3  ","&      \xA3\xA3\xA3  ","      \u2591\xA3\xA3\xA3  ","      \u2592\xA3\xA3\xA3  ","      \u2593\xA3\xA3\xA3  ","      \u2588\xA3\xA3\xA3  ","     \u2591\u2588\xA3\xA3\xA3  ","     \u2592\u2588\xA3\xA3\xA3  ","     \u2593\u2588\xA3\xA3\xA3  ","     \u2588\u2588\xA3\xA3\xA3  ","    \u2591\u2588\u2588\xA3\xA3\xA3  ","    \u2592\u2588\u2588\xA3\xA3\xA3  ","    \u2593\u2588\u2588\xA3\xA3\xA3  ","    \u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2591\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2592\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2593\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2591\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2592\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2593\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2591\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2592\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2593\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "]}}});var Eu=Y((Qe,zu)=>{"use strict";var z=Object.assign({},qu()),Zu=Object.keys(z);Object.defineProperty(z,"random",{get(){let u=Math.floor(Math.random()*Zu.length),D=Zu[u];return z[D]}});zu.exports=z});var ED=Y((pt,CD)=>{CD.exports=()=>/[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26D3\uFE0F?(?:\u200D\uD83D\uDCA5)?|\u26F9(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF43\uDF45-\uDF4A\uDF4C-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDF44(?:\u200D\uD83D\uDFEB)?|\uDF4B(?:\u200D\uD83D\uDFE9)?|\uDFC3(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4\uDEB5](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE41\uDE43\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC08(?:\u200D\u2B1B)?|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC26(?:\u200D(?:\u2B1B|\uD83D\uDD25))?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?|\uDE42(?:\u200D[\u2194\u2195]\uFE0F?)?|\uDEB6(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE89\uDE8F-\uDEC2\uDEC6\uDECE-\uDEDC\uDEDF-\uDEE9]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDCE(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1|\uDDD1\u200D\uD83E\uDDD2(?:\u200D\uD83E\uDDD2)?|\uDDD2(?:\u200D\uD83E\uDDD2)?))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g});var Te={};gD(Te,{initLogger:()=>xe,internalOnlyHint:()=>Ae,log:()=>R,logObject:()=>ge,wait:()=>be});module.exports=bD(Te);var l=c(wu());var L=c(require("node:process"),1);var Ru=(u=0)=>D=>`\x1B[${D+u}m`,Su=(u=0)=>D=>`\x1B[${38+u};5;${D}m`,Iu=(u=0)=>(D,e,t)=>`\x1B[${38+u};2;${D};${e};${t}m`,a={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],gray:[90,39],grey:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgGray:[100,49],bgGrey:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}},Oe=Object.keys(a.modifier),yD=Object.keys(a.color),vD=Object.keys(a.bgColor),we=[...yD,...vD];function OD(){let u=new Map;for(let[D,e]of Object.entries(a)){for(let[t,r]of Object.entries(e))a[t]={open:`\x1B[${r[0]}m`,close:`\x1B[${r[1]}m`},e[t]=a[t],u.set(r[0],r[1]);Object.defineProperty(a,D,{value:e,enumerable:!1})}return Object.defineProperty(a,"codes",{value:u,enumerable:!1}),a.color.close="\x1B[39m",a.bgColor.close="\x1B[49m",a.color.ansi=Ru(),a.color.ansi256=Su(),a.color.ansi16m=Iu(),a.bgColor.ansi=Ru(10),a.bgColor.ansi256=Su(10),a.bgColor.ansi16m=Iu(10),Object.defineProperties(a,{rgbToAnsi256:{value(D,e,t){return D===e&&e===t?D<8?16:D>248?231:Math.round((D-8)/247*24)+232:16+36*Math.round(D/255*5)+6*Math.round(e/255*5)+Math.round(t/255*5)},enumerable:!1},hexToRgb:{value(D){let e=/[a-f\d]{6}|[a-f\d]{3}/i.exec(D.toString(16));if(!e)return[0,0,0];let[t]=e;t.length===3&&(t=[...t].map(n=>n+n).join(""));let r=Number.parseInt(t,16);return[r>>16&255,r>>8&255,r&255]},enumerable:!1},hexToAnsi256:{value:D=>a.rgbToAnsi256(...a.hexToRgb(D)),enumerable:!1},ansi256ToAnsi:{value(D){if(D<8)return 30+D;if(D<16)return 90+(D-8);let e,t,r;if(D>=232)e=((D-232)*10+8)/255,t=e,r=e;else{D-=16;let F=D%36;e=Math.floor(D/36)/5,t=Math.floor(F/6)/5,r=F%6/5}let n=Math.max(e,t,r)*2;if(n===0)return 30;let i=30+(Math.round(r)<<2|Math.round(t)<<1|Math.round(e));return n===2&&(i+=60),i},enumerable:!1},rgbToAnsi:{value:(D,e,t)=>a.ansi256ToAnsi(a.rgbToAnsi256(D,e,t)),enumerable:!1},hexToAnsi:{value:D=>a.ansi256ToAnsi(a.hexToAnsi256(D)),enumerable:!1}}),a}var wD=OD(),d=wD;var W=c(require("node:process"),1),Nu=c(require("node:os"),1),uu=c(require("node:tty"),1);function h(u,D=globalThis.Deno?globalThis.Deno.args:W.default.argv){let e=u.startsWith("-")?"":u.length===1?"-":"--",t=D.indexOf(e+u),r=D.indexOf("--");return t!==-1&&(r===-1||t<r)}var{env:E}=W.default,U;h("no-color")||h("no-colors")||h("color=false")||h("color=never")?U=0:(h("color")||h("colors")||h("color=true")||h("color=always"))&&(U=1);function RD(){if("FORCE_COLOR"in E)return E.FORCE_COLOR==="true"?1:E.FORCE_COLOR==="false"?0:E.FORCE_COLOR.length===0?1:Math.min(Number.parseInt(E.FORCE_COLOR,10),3)}function SD(u){return u===0?!1:{level:u,hasBasic:!0,has256:u>=2,has16m:u>=3}}function ID(u,{streamIsTTY:D,sniffFlags:e=!0}={}){let t=RD();t!==void 0&&(U=t);let r=e?U:t;if(r===0)return 0;if(e){if(h("color=16m")||h("color=full")||h("color=truecolor"))return 3;if(h("color=256"))return 2}if("TF_BUILD"in E&&"AGENT_NAME"in E)return 1;if(u&&!D&&r===void 0)return 0;let n=r||0;if(E.TERM==="dumb")return n;if(W.default.platform==="win32"){let i=Nu.default.release().split(".");return Number(i[0])>=10&&Number(i[2])>=10586?Number(i[2])>=14931?3:2:1}if("CI"in E)return"GITHUB_ACTIONS"in E||"GITEA_ACTIONS"in E?3:["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","BUILDKITE","DRONE"].some(i=>i in E)||E.CI_NAME==="codeship"?1:n;if("TEAMCITY_VERSION"in E)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(E.TEAMCITY_VERSION)?1:0;if(E.COLORTERM==="truecolor"||E.TERM==="xterm-kitty")return 3;if("TERM_PROGRAM"in E){let i=Number.parseInt((E.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(E.TERM_PROGRAM){case"iTerm.app":return i>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(E.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(E.TERM)||"COLORTERM"in E?1:n}function Mu(u,D={}){let e=ID(u,{streamIsTTY:u&&u.isTTY,...D});return SD(e)}var MD={stdout:Mu({isTTY:uu.default.isatty(1)}),stderr:Mu({isTTY:uu.default.isatty(2)})},Pu=MD;function ju(u,D,e){let t=u.indexOf(D);if(t===-1)return u;let r=D.length,n=0,i="";do i+=u.slice(n,t)+D+e,n=t+r,t=u.indexOf(D,n);while(t!==-1);return i+=u.slice(n),i}function Gu(u,D,e,t){let r=0,n="";do{let i=u[t-1]==="\r";n+=u.slice(r,i?t-1:t)+D+(i?`\r
`:`
`)+e,r=t+1,t=u.indexOf(`
`,r)}while(t!==-1);return n+=u.slice(r),n}var{stdout:Lu,stderr:ku}=Pu,Du=Symbol("GENERATOR"),T=Symbol("STYLER"),S=Symbol("IS_EMPTY"),$u=["ansi","ansi","ansi256","ansi16m"],y=Object.create(null),ND=(u,D={})=>{if(D.level&&!(Number.isInteger(D.level)&&D.level>=0&&D.level<=3))throw new Error("The `level` option should be an integer from 0 to 3");let e=Lu?Lu.level:0;u.level=D.level===void 0?e:D.level};var PD=u=>{let D=(...e)=>e.join(" ");return ND(D,u),Object.setPrototypeOf(D,I.prototype),D};function I(u){return PD(u)}Object.setPrototypeOf(I.prototype,Function.prototype);for(let[u,D]of Object.entries(d))y[u]={get(){let e=V(this,tu(D.open,D.close,this[T]),this[S]);return Object.defineProperty(this,u,{value:e}),e}};y.visible={get(){let u=V(this,this[T],!0);return Object.defineProperty(this,"visible",{value:u}),u}};var eu=(u,D,e,...t)=>u==="rgb"?D==="ansi16m"?d[e].ansi16m(...t):D==="ansi256"?d[e].ansi256(d.rgbToAnsi256(...t)):d[e].ansi(d.rgbToAnsi(...t)):u==="hex"?eu("rgb",D,e,...d.hexToRgb(...t)):d[e][u](...t),jD=["rgb","hex","ansi256"];for(let u of jD){y[u]={get(){let{level:e}=this;return function(...t){let r=tu(eu(u,$u[e],"color",...t),d.color.close,this[T]);return V(this,r,this[S])}}};let D="bg"+u[0].toUpperCase()+u.slice(1);y[D]={get(){let{level:e}=this;return function(...t){let r=tu(eu(u,$u[e],"bgColor",...t),d.bgColor.close,this[T]);return V(this,r,this[S])}}}}var GD=Object.defineProperties(()=>{},{...y,level:{enumerable:!0,get(){return this[Du].level},set(u){this[Du].level=u}}}),tu=(u,D,e)=>{let t,r;return e===void 0?(t=u,r=D):(t=e.openAll+u,r=D+e.closeAll),{open:u,close:D,openAll:t,closeAll:r,parent:e}},V=(u,D,e)=>{let t=(...r)=>LD(t,r.length===1?""+r[0]:r.join(" "));return Object.setPrototypeOf(t,GD),t[Du]=u,t[T]=D,t[S]=e,t},LD=(u,D)=>{if(u.level<=0||!D)return u[S]?"":D;let e=u[T];if(e===void 0)return D;let{openAll:t,closeAll:r}=e;if(D.includes("\x1B"))for(;e!==void 0;)D=ju(D,e.close,e.open),e=e.parent;let n=D.indexOf(`
`);return n!==-1&&(D=Gu(D,r,t,n)),t+D+r};Object.defineProperties(I.prototype,y);var kD=I(),je=I({level:ku?ku.level:0});var Yu=kD;var au=c(require("node:process"),1);var M=c(require("node:process"),1);var $D=(u,D,e,t)=>{if(e==="length"||e==="prototype"||e==="arguments"||e==="caller")return;let r=Object.getOwnPropertyDescriptor(u,e),n=Object.getOwnPropertyDescriptor(D,e);!YD(r,n)&&t||Object.defineProperty(u,e,n)},YD=function(u,D){return u===void 0||u.configurable||u.writable===D.writable&&u.enumerable===D.enumerable&&u.configurable===D.configurable&&(u.writable||u.value===D.value)},UD=(u,D)=>{let e=Object.getPrototypeOf(D);e!==Object.getPrototypeOf(u)&&Object.setPrototypeOf(u,e)},WD=(u,D)=>`/* Wrapped ${u}*/
${D}`,VD=Object.getOwnPropertyDescriptor(Function.prototype,"toString"),HD=Object.getOwnPropertyDescriptor(Function.prototype.toString,"name"),KD=(u,D,e)=>{let t=e===""?"":`with ${e.trim()}() `,r=WD.bind(null,t,D.toString());Object.defineProperty(r,"name",HD);let{writable:n,enumerable:i,configurable:F}=VD;Object.defineProperty(u,"toString",{value:r,writable:n,enumerable:i,configurable:F})};function ru(u,D,{ignoreNonConfigurable:e=!1}={}){let{name:t}=u;for(let r of Reflect.ownKeys(D))$D(u,D,r,e);return UD(u,D),KD(u,D,t),u}var H=new WeakMap,Uu=(u,D={})=>{if(typeof u!="function")throw new TypeError("Expected a function");let e,t=0,r=u.displayName||u.name||"<anonymous>",n=function(...i){if(H.set(n,++t),t===1)e=u.apply(this,i),u=void 0;else if(D.throw===!0)throw new Error(`Function \`${r}\` can only be called once`);return e};return ru(n,u),H.set(n,t),n};Uu.callCount=u=>{if(!H.has(u))throw new Error(`The given function \`${u.name}\` is not wrapped by the \`onetime\` package`);return H.get(u)};var Wu=Uu;var b=[];b.push("SIGHUP","SIGINT","SIGTERM");process.platform!=="win32"&&b.push("SIGALRM","SIGABRT","SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");process.platform==="linux"&&b.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT");var K=u=>!!u&&typeof u=="object"&&typeof u.removeListener=="function"&&typeof u.emit=="function"&&typeof u.reallyExit=="function"&&typeof u.listeners=="function"&&typeof u.kill=="function"&&typeof u.pid=="number"&&typeof u.on=="function",nu=Symbol.for("signal-exit emitter"),iu=globalThis,qD=Object.defineProperty.bind(Object),ou=class{emitted={afterExit:!1,exit:!1};listeners={afterExit:[],exit:[]};count=0;id=Math.random();constructor(){if(iu[nu])return iu[nu];qD(iu,nu,{value:this,writable:!1,enumerable:!1,configurable:!1})}on(D,e){this.listeners[D].push(e)}removeListener(D,e){let t=this.listeners[D],r=t.indexOf(e);r!==-1&&(r===0&&t.length===1?t.length=0:t.splice(r,1))}emit(D,e,t){if(this.emitted[D])return!1;this.emitted[D]=!0;let r=!1;for(let n of this.listeners[D])r=n(e,t)===!0||r;return D==="exit"&&(r=this.emit("afterExit",e,t)||r),r}},q=class{},ZD=u=>({onExit(D,e){return u.onExit(D,e)},load(){return u.load()},unload(){return u.unload()}}),su=class extends q{onExit(){return()=>{}}load(){}unload(){}},Fu=class extends q{#i=lu.platform==="win32"?"SIGINT":"SIGHUP";#e=new ou;#u;#r;#C;#D={};#n=!1;constructor(D){super(),this.#u=D,this.#D={};for(let e of b)this.#D[e]=()=>{let t=this.#u.listeners(e),{count:r}=this.#e,n=D;if(typeof n.__signal_exit_emitter__=="object"&&typeof n.__signal_exit_emitter__.count=="number"&&(r+=n.__signal_exit_emitter__.count),t.length===r){this.unload();let i=this.#e.emit("exit",null,e),F=e==="SIGHUP"?this.#i:e;i||D.kill(D.pid,F)}};this.#C=D.reallyExit,this.#r=D.emit}onExit(D,e){if(!K(this.#u))return()=>{};this.#n===!1&&this.load();let t=e?.alwaysLast?"afterExit":"exit";return this.#e.on(t,D),()=>{this.#e.removeListener(t,D),this.#e.listeners.exit.length===0&&this.#e.listeners.afterExit.length===0&&this.unload()}}load(){if(!this.#n){this.#n=!0,this.#e.count+=1;for(let D of b)try{let e=this.#D[D];e&&this.#u.on(D,e)}catch{}this.#u.emit=(D,...e)=>this.#E(D,...e),this.#u.reallyExit=D=>this.#t(D)}}unload(){this.#n&&(this.#n=!1,b.forEach(D=>{let e=this.#D[D];if(!e)throw new Error("Listener not defined for signal: "+D);try{this.#u.removeListener(D,e)}catch{}}),this.#u.emit=this.#r,this.#u.reallyExit=this.#C,this.#e.count-=1)}#t(D){return K(this.#u)?(this.#u.exitCode=D||0,this.#e.emit("exit",this.#u.exitCode,null),this.#C.call(this.#u,this.#u.exitCode)):0}#E(D,...e){let t=this.#r;if(D==="exit"&&K(this.#u)){typeof e[0]=="number"&&(this.#u.exitCode=e[0]);let r=t.call(this.#u,D,...e);return this.#e.emit("exit",this.#u.exitCode,null),r}else return t.call(this.#u,D,...e)}},lu=globalThis.process,{onExit:Vu,load:We,unload:Ve}=ZD(K(lu)?new Fu(lu):new su);var Hu=M.default.stderr.isTTY?M.default.stderr:M.default.stdout.isTTY?M.default.stdout:void 0,zD=Hu?Wu(()=>{Vu(()=>{Hu.write("\x1B[?25h")},{alwaysLast:!0})}):()=>{},Ku=zD;var Z=!1,v={};v.show=(u=au.default.stderr)=>{u.isTTY&&(Z=!1,u.write("\x1B[?25h"))};v.hide=(u=au.default.stderr)=>{u.isTTY&&(Ku(),Z=!0,u.write("\x1B[?25l"))};v.toggle=(u,D)=>{u!==void 0&&(Z=u),Z?v.show(D):v.hide(D)};var Cu=v;var k=c(Eu(),1);var Ju=(u=0)=>D=>`\x1B[${D+u}m`,Xu=(u=0)=>D=>`\x1B[${38+u};5;${D}m`,Qu=(u=0)=>(D,e,t)=>`\x1B[${38+u};2;${D};${e};${t}m`,C={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],gray:[90,39],grey:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgGray:[100,49],bgGrey:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}},ut=Object.keys(C.modifier),XD=Object.keys(C.color),QD=Object.keys(C.bgColor),Dt=[...XD,...QD];function ue(){let u=new Map;for(let[D,e]of Object.entries(C)){for(let[t,r]of Object.entries(e))C[t]={open:`\x1B[${r[0]}m`,close:`\x1B[${r[1]}m`},e[t]=C[t],u.set(r[0],r[1]);Object.defineProperty(C,D,{value:e,enumerable:!1})}return Object.defineProperty(C,"codes",{value:u,enumerable:!1}),C.color.close="\x1B[39m",C.bgColor.close="\x1B[49m",C.color.ansi=Ju(),C.color.ansi256=Xu(),C.color.ansi16m=Qu(),C.bgColor.ansi=Ju(10),C.bgColor.ansi256=Xu(10),C.bgColor.ansi16m=Qu(10),Object.defineProperties(C,{rgbToAnsi256:{value(D,e,t){return D===e&&e===t?D<8?16:D>248?231:Math.round((D-8)/247*24)+232:16+36*Math.round(D/255*5)+6*Math.round(e/255*5)+Math.round(t/255*5)},enumerable:!1},hexToRgb:{value(D){let e=/[a-f\d]{6}|[a-f\d]{3}/i.exec(D.toString(16));if(!e)return[0,0,0];let[t]=e;t.length===3&&(t=[...t].map(n=>n+n).join(""));let r=Number.parseInt(t,16);return[r>>16&255,r>>8&255,r&255]},enumerable:!1},hexToAnsi256:{value:D=>C.rgbToAnsi256(...C.hexToRgb(D)),enumerable:!1},ansi256ToAnsi:{value(D){if(D<8)return 30+D;if(D<16)return 90+(D-8);let e,t,r;if(D>=232)e=((D-232)*10+8)/255,t=e,r=e;else{D-=16;let F=D%36;e=Math.floor(D/36)/5,t=Math.floor(F/6)/5,r=F%6/5}let n=Math.max(e,t,r)*2;if(n===0)return 30;let i=30+(Math.round(r)<<2|Math.round(t)<<1|Math.round(e));return n===2&&(i+=60),i},enumerable:!1},rgbToAnsi:{value:(D,e,t)=>C.ansi256ToAnsi(C.rgbToAnsi256(D,e,t)),enumerable:!1},hexToAnsi:{value:D=>C.ansi256ToAnsi(C.hexToAnsi256(D)),enumerable:!1}}),C}var De=ue(),_=De;var X=c(require("node:process"),1),DD=c(require("node:os"),1),fu=c(require("node:tty"),1);function B(u,D=globalThis.Deno?globalThis.Deno.args:X.default.argv){let e=u.startsWith("-")?"":u.length===1?"-":"--",t=D.indexOf(e+u),r=D.indexOf("--");return t!==-1&&(r===-1||t<r)}var{env:f}=X.default,J;B("no-color")||B("no-colors")||B("color=false")||B("color=never")?J=0:(B("color")||B("colors")||B("color=true")||B("color=always"))&&(J=1);function ee(){if("FORCE_COLOR"in f)return f.FORCE_COLOR==="true"?1:f.FORCE_COLOR==="false"?0:f.FORCE_COLOR.length===0?1:Math.min(Number.parseInt(f.FORCE_COLOR,10),3)}function te(u){return u===0?!1:{level:u,hasBasic:!0,has256:u>=2,has16m:u>=3}}function re(u,{streamIsTTY:D,sniffFlags:e=!0}={}){let t=ee();t!==void 0&&(J=t);let r=e?J:t;if(r===0)return 0;if(e){if(B("color=16m")||B("color=full")||B("color=truecolor"))return 3;if(B("color=256"))return 2}if("TF_BUILD"in f&&"AGENT_NAME"in f)return 1;if(u&&!D&&r===void 0)return 0;let n=r||0;if(f.TERM==="dumb")return n;if(X.default.platform==="win32"){let i=DD.default.release().split(".");return Number(i[0])>=10&&Number(i[2])>=10586?Number(i[2])>=14931?3:2:1}if("CI"in f)return"GITHUB_ACTIONS"in f||"GITEA_ACTIONS"in f?3:["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","BUILDKITE","DRONE"].some(i=>i in f)||f.CI_NAME==="codeship"?1:n;if("TEAMCITY_VERSION"in f)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(f.TEAMCITY_VERSION)?1:0;if(f.COLORTERM==="truecolor"||f.TERM==="xterm-kitty")return 3;if("TERM_PROGRAM"in f){let i=Number.parseInt((f.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(f.TERM_PROGRAM){case"iTerm.app":return i>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(f.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(f.TERM)||"COLORTERM"in f?1:n}function uD(u,D={}){let e=re(u,{streamIsTTY:u&&u.isTTY,...D});return te(e)}var ne={stdout:uD({isTTY:fu.default.isatty(1)}),stderr:uD({isTTY:fu.default.isatty(2)})},eD=ne;function tD(u,D,e){let t=u.indexOf(D);if(t===-1)return u;let r=D.length,n=0,i="";do i+=u.slice(n,t)+D+e,n=t+r,t=u.indexOf(D,n);while(t!==-1);return i+=u.slice(n),i}function rD(u,D,e,t){let r=0,n="";do{let i=u[t-1]==="\r";n+=u.slice(r,i?t-1:t)+D+(i?`\r
`:`
`)+e,r=t+1,t=u.indexOf(`
`,r)}while(t!==-1);return n+=u.slice(r),n}var{stdout:nD,stderr:iD}=eD,cu=Symbol("GENERATOR"),O=Symbol("STYLER"),N=Symbol("IS_EMPTY"),oD=["ansi","ansi","ansi256","ansi16m"],w=Object.create(null),ie=(u,D={})=>{if(D.level&&!(Number.isInteger(D.level)&&D.level>=0&&D.level<=3))throw new Error("The `level` option should be an integer from 0 to 3");let e=nD?nD.level:0;u.level=D.level===void 0?e:D.level};var oe=u=>{let D=(...e)=>e.join(" ");return ie(D,u),Object.setPrototypeOf(D,P.prototype),D};function P(u){return oe(u)}Object.setPrototypeOf(P.prototype,Function.prototype);for(let[u,D]of Object.entries(_))w[u]={get(){let e=Q(this,Bu(D.open,D.close,this[O]),this[N]);return Object.defineProperty(this,u,{value:e}),e}};w.visible={get(){let u=Q(this,this[O],!0);return Object.defineProperty(this,"visible",{value:u}),u}};var hu=(u,D,e,...t)=>u==="rgb"?D==="ansi16m"?_[e].ansi16m(...t):D==="ansi256"?_[e].ansi256(_.rgbToAnsi256(...t)):_[e].ansi(_.rgbToAnsi(...t)):u==="hex"?hu("rgb",D,e,..._.hexToRgb(...t)):_[e][u](...t),se=["rgb","hex","ansi256"];for(let u of se){w[u]={get(){let{level:e}=this;return function(...t){let r=Bu(hu(u,oD[e],"color",...t),_.color.close,this[O]);return Q(this,r,this[N])}}};let D="bg"+u[0].toUpperCase()+u.slice(1);w[D]={get(){let{level:e}=this;return function(...t){let r=Bu(hu(u,oD[e],"bgColor",...t),_.bgColor.close,this[O]);return Q(this,r,this[N])}}}}var Fe=Object.defineProperties(()=>{},{...w,level:{enumerable:!0,get(){return this[cu].level},set(u){this[cu].level=u}}}),Bu=(u,D,e)=>{let t,r;return e===void 0?(t=u,r=D):(t=e.openAll+u,r=D+e.closeAll),{open:u,close:D,openAll:t,closeAll:r,parent:e}},Q=(u,D,e)=>{let t=(...r)=>le(t,r.length===1?""+r[0]:r.join(" "));return Object.setPrototypeOf(t,Fe),t[cu]=u,t[O]=D,t[N]=e,t},le=(u,D)=>{if(u.level<=0||!D)return u[N]?"":D;let e=u[O];if(e===void 0)return D;let{openAll:t,closeAll:r}=e;if(D.includes("\x1B"))for(;e!==void 0;)D=tD(D,e.close,e.open),e=e.parent;let n=D.indexOf(`
`);return n!==-1&&(D=rD(D,r,t,n)),t+D+r};Object.defineProperties(P.prototype,w);var ae=P(),st=P({level:iD?iD.level:0});var A=ae;var m=c(require("node:process"),1);function mu(){return m.default.platform!=="win32"?m.default.env.TERM!=="linux":!!m.default.env.CI||!!m.default.env.WT_SESSION||!!m.default.env.TERMINUS_SUBLIME||m.default.env.ConEmuTask==="{cmd::Cmder}"||m.default.env.TERM_PROGRAM==="Terminus-Sublime"||m.default.env.TERM_PROGRAM==="vscode"||m.default.env.TERM==="xterm-256color"||m.default.env.TERM==="alacritty"||m.default.env.TERMINAL_EMULATOR==="JetBrains-JediTerm"}var Ce={info:A.blue("\u2139"),success:A.green("\u2714"),warning:A.yellow("\u26A0"),error:A.red("\u2716")},Ee={info:A.blue("i"),success:A.green("\u221A"),warning:A.yellow("\u203C"),error:A.red("\xD7")},fe=mu()?Ce:Ee,j=fe;function du({onlyFirst:u=!1}={}){let e=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?(?:\\u0007|\\u001B\\u005C|\\u009C))","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-nq-uy=><~]))"].join("|");return new RegExp(e,u?void 0:"g")}var ce=du();function G(u){if(typeof u!="string")throw new TypeError(`Expected a \`string\`, got \`${typeof u}\``);return u.replace(ce,"")}function sD(u){return u===161||u===164||u===167||u===168||u===170||u===173||u===174||u>=176&&u<=180||u>=182&&u<=186||u>=188&&u<=191||u===198||u===208||u===215||u===216||u>=222&&u<=225||u===230||u>=232&&u<=234||u===236||u===237||u===240||u===242||u===243||u>=247&&u<=250||u===252||u===254||u===257||u===273||u===275||u===283||u===294||u===295||u===299||u>=305&&u<=307||u===312||u>=319&&u<=322||u===324||u>=328&&u<=331||u===333||u===338||u===339||u===358||u===359||u===363||u===462||u===464||u===466||u===468||u===470||u===472||u===474||u===476||u===593||u===609||u===708||u===711||u>=713&&u<=715||u===717||u===720||u>=728&&u<=731||u===733||u===735||u>=768&&u<=879||u>=913&&u<=929||u>=931&&u<=937||u>=945&&u<=961||u>=963&&u<=969||u===1025||u>=1040&&u<=1103||u===1105||u===8208||u>=8211&&u<=8214||u===8216||u===8217||u===8220||u===8221||u>=8224&&u<=8226||u>=8228&&u<=8231||u===8240||u===8242||u===8243||u===8245||u===8251||u===8254||u===8308||u===8319||u>=8321&&u<=8324||u===8364||u===8451||u===8453||u===8457||u===8467||u===8470||u===8481||u===8482||u===8486||u===8491||u===8531||u===8532||u>=8539&&u<=8542||u>=8544&&u<=8555||u>=8560&&u<=8569||u===8585||u>=8592&&u<=8601||u===8632||u===8633||u===8658||u===8660||u===8679||u===8704||u===8706||u===8707||u===8711||u===8712||u===8715||u===8719||u===8721||u===8725||u===8730||u>=8733&&u<=8736||u===8739||u===8741||u>=8743&&u<=8748||u===8750||u>=8756&&u<=8759||u===8764||u===8765||u===8776||u===8780||u===8786||u===8800||u===8801||u>=8804&&u<=8807||u===8810||u===8811||u===8814||u===8815||u===8834||u===8835||u===8838||u===8839||u===8853||u===8857||u===8869||u===8895||u===8978||u>=9312&&u<=9449||u>=9451&&u<=9547||u>=9552&&u<=9587||u>=9600&&u<=9615||u>=9618&&u<=9621||u===9632||u===9633||u>=9635&&u<=9641||u===9650||u===9651||u===9654||u===9655||u===9660||u===9661||u===9664||u===9665||u>=9670&&u<=9672||u===9675||u>=9678&&u<=9681||u>=9698&&u<=9701||u===9711||u===9733||u===9734||u===9737||u===9742||u===9743||u===9756||u===9758||u===9792||u===9794||u===9824||u===9825||u>=9827&&u<=9829||u>=9831&&u<=9834||u===9836||u===9837||u===9839||u===9886||u===9887||u===9919||u>=9926&&u<=9933||u>=9935&&u<=9939||u>=9941&&u<=9953||u===9955||u===9960||u===9961||u>=9963&&u<=9969||u===9972||u>=9974&&u<=9977||u===9979||u===9980||u===9982||u===9983||u===10045||u>=10102&&u<=10111||u>=11094&&u<=11097||u>=12872&&u<=12879||u>=57344&&u<=63743||u>=65024&&u<=65039||u===65533||u>=127232&&u<=127242||u>=127248&&u<=127277||u>=127280&&u<=127337||u>=127344&&u<=127373||u===127375||u===127376||u>=127387&&u<=127404||u>=917760&&u<=917999||u>=983040&&u<=1048573||u>=1048576&&u<=1114109}function FD(u){return u===12288||u>=65281&&u<=65376||u>=65504&&u<=65510}function lD(u){return u>=4352&&u<=4447||u===8986||u===8987||u===9001||u===9002||u>=9193&&u<=9196||u===9200||u===9203||u===9725||u===9726||u===9748||u===9749||u>=9776&&u<=9783||u>=9800&&u<=9811||u===9855||u>=9866&&u<=9871||u===9875||u===9889||u===9898||u===9899||u===9917||u===9918||u===9924||u===9925||u===9934||u===9940||u===9962||u===9970||u===9971||u===9973||u===9978||u===9981||u===9989||u===9994||u===9995||u===10024||u===10060||u===10062||u>=10067&&u<=10069||u===10071||u>=10133&&u<=10135||u===10160||u===10175||u===11035||u===11036||u===11088||u===11093||u>=11904&&u<=11929||u>=11931&&u<=12019||u>=12032&&u<=12245||u>=12272&&u<=12287||u>=12289&&u<=12350||u>=12353&&u<=12438||u>=12441&&u<=12543||u>=12549&&u<=12591||u>=12593&&u<=12686||u>=12688&&u<=12773||u>=12783&&u<=12830||u>=12832&&u<=12871||u>=12880&&u<=42124||u>=42128&&u<=42182||u>=43360&&u<=43388||u>=44032&&u<=55203||u>=63744&&u<=64255||u>=65040&&u<=65049||u>=65072&&u<=65106||u>=65108&&u<=65126||u>=65128&&u<=65131||u>=94176&&u<=94180||u===94192||u===94193||u>=94208&&u<=100343||u>=100352&&u<=101589||u>=101631&&u<=101640||u>=110576&&u<=110579||u>=110581&&u<=110587||u===110589||u===110590||u>=110592&&u<=110882||u===110898||u>=110928&&u<=110930||u===110933||u>=110948&&u<=110951||u>=110960&&u<=111355||u>=119552&&u<=119638||u>=119648&&u<=119670||u===126980||u===127183||u===127374||u>=127377&&u<=127386||u>=127488&&u<=127490||u>=127504&&u<=127547||u>=127552&&u<=127560||u===127568||u===127569||u>=127584&&u<=127589||u>=127744&&u<=127776||u>=127789&&u<=127797||u>=127799&&u<=127868||u>=127870&&u<=127891||u>=127904&&u<=127946||u>=127951&&u<=127955||u>=127968&&u<=127984||u===127988||u>=127992&&u<=128062||u===128064||u>=128066&&u<=128252||u>=128255&&u<=128317||u>=128331&&u<=128334||u>=128336&&u<=128359||u===128378||u===128405||u===128406||u===128420||u>=128507&&u<=128591||u>=128640&&u<=128709||u===128716||u>=128720&&u<=128722||u>=128725&&u<=128727||u>=128732&&u<=128735||u===128747||u===128748||u>=128756&&u<=128764||u>=128992&&u<=129003||u===129008||u>=129292&&u<=129338||u>=129340&&u<=129349||u>=129351&&u<=129535||u>=129648&&u<=129660||u>=129664&&u<=129673||u>=129679&&u<=129734||u>=129742&&u<=129756||u>=129759&&u<=129769||u>=129776&&u<=129784||u>=131072&&u<=196605||u>=196608&&u<=262141}function he(u){if(!Number.isSafeInteger(u))throw new TypeError(`Expected a code point, got \`${typeof u}\`.`)}function aD(u,{ambiguousAsWide:D=!1}={}){return he(u),FD(u)||lD(u)||D&&sD(u)?2:1}var fD=c(ED(),1),Be=new Intl.Segmenter,me=/^\p{Default_Ignorable_Code_Point}$/u;function _u(u,D={}){if(typeof u!="string"||u.length===0)return 0;let{ambiguousIsNarrow:e=!0,countAnsiEscapeCodes:t=!1}=D;if(t||(u=G(u)),u.length===0)return 0;let r=0,n={ambiguousAsWide:!e};for(let{segment:i}of Be.segment(u)){let F=i.codePointAt(0);if(!(F<=31||F>=127&&F<=159)&&!(F>=8203&&F<=8207||F===65279)&&!(F>=768&&F<=879||F>=6832&&F<=6911||F>=7616&&F<=7679||F>=8400&&F<=8447||F>=65056&&F<=65071)&&!(F>=55296&&F<=57343)&&!(F>=65024&&F<=65039)&&!me.test(i)){if((0,fD.default)().test(i)){r+=2;continue}r+=aD(F,n)}}return r}function pu({stream:u=process.stdout}={}){return!!(u&&u.isTTY&&process.env.TERM!=="dumb"&&!("CI"in process.env))}var Au=c(require("node:process"),1);function gu(){let{env:u}=Au.default,{TERM:D,TERM_PROGRAM:e}=u;return Au.default.platform!=="win32"?D!=="linux":!!u.WT_SESSION||!!u.TERMINUS_SUBLIME||u.ConEmuTask==="{cmd::Cmder}"||e==="Terminus-Sublime"||e==="vscode"||D==="xterm-256color"||D==="alacritty"||D==="rxvt-unicode"||D==="rxvt-unicode-256color"||u.TERMINAL_EMULATOR==="JetBrains-JediTerm"}var p=c(require("node:process"),1),de=3,bu=class{#i=0;start(){this.#i++,this.#i===1&&this.#e()}stop(){if(this.#i<=0)throw new Error("`stop` called more times than `start`");this.#i--,this.#i===0&&this.#u()}#e(){p.default.platform==="win32"||!p.default.stdin.isTTY||(p.default.stdin.setRawMode(!0),p.default.stdin.on("data",this.#r),p.default.stdin.resume())}#u(){p.default.stdin.isTTY&&(p.default.stdin.off("data",this.#r),p.default.stdin.pause(),p.default.stdin.setRawMode(!1))}#r(D){D[0]===de&&p.default.emit("SIGINT")}},_e=new bu,xu=_e;var pe=c(Eu(),1),Tu=class{#i=0;#e=!1;#u=0;#r=-1;#C=0;#D;#n;#t;#E;#c;#F;#l;#a;#h;#o;#s;color;constructor(D){typeof D=="string"&&(D={text:D}),this.#D={color:"cyan",stream:L.default.stderr,discardStdin:!0,hideCursor:!0,...D},this.color=this.#D.color,this.spinner=this.#D.spinner,this.#c=this.#D.interval,this.#t=this.#D.stream,this.#F=typeof this.#D.isEnabled=="boolean"?this.#D.isEnabled:pu({stream:this.#t}),this.#l=typeof this.#D.isSilent=="boolean"?this.#D.isSilent:!1,this.text=this.#D.text,this.prefixText=this.#D.prefixText,this.suffixText=this.#D.suffixText,this.indent=this.#D.indent,L.default.env.NODE_ENV==="test"&&(this._stream=this.#t,this._isEnabled=this.#F,Object.defineProperty(this,"_linesToClear",{get(){return this.#i},set(e){this.#i=e}}),Object.defineProperty(this,"_frameIndex",{get(){return this.#r}}),Object.defineProperty(this,"_lineCount",{get(){return this.#u}}))}get indent(){return this.#a}set indent(D=0){if(!(D>=0&&Number.isInteger(D)))throw new Error("The `indent` option must be an integer from 0 and up");this.#a=D,this.#f()}get interval(){return this.#c??this.#n.interval??100}get spinner(){return this.#n}set spinner(D){if(this.#r=-1,this.#c=void 0,typeof D=="object"){if(D.frames===void 0)throw new Error("The given spinner must have a `frames` property");this.#n=D}else if(!gu())this.#n=k.default.line;else if(D===void 0)this.#n=k.default.dots;else if(D!=="default"&&k.default[D])this.#n=k.default[D];else throw new Error(`There is no built-in spinner named '${D}'. See https://github.com/sindresorhus/cli-spinners/blob/main/spinners.json for a full list.`)}get text(){return this.#h}set text(D=""){this.#h=D,this.#f()}get prefixText(){return this.#o}set prefixText(D=""){this.#o=D,this.#f()}get suffixText(){return this.#s}set suffixText(D=""){this.#s=D,this.#f()}get isSpinning(){return this.#E!==void 0}#B(D=this.#o,e=" "){return typeof D=="string"&&D!==""?D+e:typeof D=="function"?D()+e:""}#m(D=this.#s,e=" "){return typeof D=="string"&&D!==""?e+D:typeof D=="function"?e+D():""}#f(){let D=this.#t.columns??80,e=this.#B(this.#o,"-"),t=this.#m(this.#s,"-"),r=" ".repeat(this.#a)+e+"--"+this.#h+"--"+t;this.#u=0;for(let n of G(r).split(`
`))this.#u+=Math.max(1,Math.ceil(_u(n,{countAnsiEscapeCodes:!0})/D))}get isEnabled(){return this.#F&&!this.#l}set isEnabled(D){if(typeof D!="boolean")throw new TypeError("The `isEnabled` option must be a boolean");this.#F=D}get isSilent(){return this.#l}set isSilent(D){if(typeof D!="boolean")throw new TypeError("The `isSilent` option must be a boolean");this.#l=D}frame(){let D=Date.now();(this.#r===-1||D-this.#C>=this.interval)&&(this.#r=++this.#r%this.#n.frames.length,this.#C=D);let{frames:e}=this.#n,t=e[this.#r];this.color&&(t=Yu[this.color](t));let r=typeof this.#o=="string"&&this.#o!==""?this.#o+" ":"",n=typeof this.text=="string"?" "+this.text:"",i=typeof this.#s=="string"&&this.#s!==""?" "+this.#s:"";return r+t+n+i}clear(){if(!this.#F||!this.#t.isTTY)return this;this.#t.cursorTo(0);for(let D=0;D<this.#i;D++)D>0&&this.#t.moveCursor(0,-1),this.#t.clearLine(1);return(this.#a||this.lastIndent!==this.#a)&&this.#t.cursorTo(this.#a),this.lastIndent=this.#a,this.#i=0,this}render(){return this.#l?this:(this.clear(),this.#t.write(this.frame()),this.#i=this.#u,this)}start(D){return D&&(this.text=D),this.#l?this:this.#F?this.isSpinning?this:(this.#D.hideCursor&&Cu.hide(this.#t),this.#D.discardStdin&&L.default.stdin.isTTY&&(this.#e=!0,xu.start()),this.render(),this.#E=setInterval(this.render.bind(this),this.interval),this):(this.text&&this.#t.write(`- ${this.text}
`),this)}stop(){return this.#F?(clearInterval(this.#E),this.#E=void 0,this.#r=0,this.clear(),this.#D.hideCursor&&Cu.show(this.#t),this.#D.discardStdin&&L.default.stdin.isTTY&&this.#e&&(xu.stop(),this.#e=!1),this):this}succeed(D){return this.stopAndPersist({symbol:j.success,text:D})}fail(D){return this.stopAndPersist({symbol:j.error,text:D})}warn(D){return this.stopAndPersist({symbol:j.warning,text:D})}info(D){return this.stopAndPersist({symbol:j.info,text:D})}stopAndPersist(D={}){if(this.#l)return this;let e=D.prefixText??this.#o,t=this.#B(e," "),r=D.symbol??" ",n=D.text??this.text,F=typeof n=="string"?(r?" ":"")+n:"",x=D.suffixText??this.#s,hD=this.#m(x," "),BD=t+r+F+hD+`
`;return this.stop(),this.#t.write(BD),this}};function yu(u){return new Tu(u)}var Ae=(0,l.blue)((0,l.dim)("internal only"));function R(u,D,e){console.log(g[u]+D),typeof e?.exit<"u"&&process.exit(e.exit)}function ge(u){Object.entries(u).forEach(([D,e])=>{console.log(`${(0,l.blue)(`- ${D}: `)}${e}`)})}async function be(u,D,e){if(!cD){R("wait",u);try{let r=await D();r&&console.log(r),R("success",u);return}catch(r){return R("error",u),e?.printError!==!1&&console.log((0,l.red)(r.message)),r}}let t=yu({spinner:"simpleDots",prefixText:g.wait+u}).start();try{let r=await D();t.stop(),R("success",u),r&&console.log(r)}catch(r){return t.stop(),R("error",u),e?.printError!==!1&&console.error(r.message),r}}var g={wait:`\u{1F550}${(0,l.blue)("wait")}  - `,info:`${process.env.GITHUB_ACTIONS==="true"?"\u2139\uFE0F":"\u2139\uFE0F "}${(0,l.cyan)("info")}  - `,success:`\u2705${(0,l.green)("ready")}  - `,warn:`${process.env.GITHUB_ACTIONS==="true"?"\u26A0\uFE0F":"\u26A0\uFE0F "}${(0,l.yellow)("warn")}  - `,error:`\u{1F4A5}${(0,l.red)("error")}  - `,event:`\u26A1\uFE0F${(0,l.magenta)("event")}  - `,paymentPrompt:`\u{1F4B0}${(0,l.yellowBright)("plan")}  - `},cD=!0;function xe(u,D){u||(g.wait=`${(0,l.blue)("wait")}  - `,g.info=`${(0,l.cyan)("info")}  - `,g.success=`${(0,l.green)("ready")}  - `,g.warn=`${(0,l.yellow)("warn")}  - `,g.error=`${(0,l.red)("error")}  - `,g.event=`${(0,l.magenta)("event")}  - `,g.paymentPrompt=`${(0,l.yellowBright)("plan")}  - `),D&&(cD=!1)}0&&(module.exports={initLogger,internalOnlyHint,log,logObject,wait});
