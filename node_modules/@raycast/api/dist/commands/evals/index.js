"use strict";var Rd=Object.create;var Ut=Object.defineProperty;var Id=Object.getOwnPropertyDescriptor;var Md=Object.getOwnPropertyNames;var Pd=Object.getPrototypeOf,kd=Object.prototype.hasOwnProperty;var h=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Ld=(e,t)=>{for(var r in t)Ut(e,r,{get:t[r],enumerable:!0})},ms=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of Md(t))!kd.call(e,o)&&o!==r&&Ut(e,o,{get:()=>t[o],enumerable:!(n=Id(t,o))||n.enumerable});return e};var F=(e,t,r)=>(r=e!=null?Rd(Pd(e)):{},ms(t||!e||!e.__esModule?Ut(r,"default",{value:e,enumerable:!0}):r,e)),Nd=e=>ms(Ut({},"__esModule",{value:!0}),e);var Fs=h((l1,hs)=>{"use strict";hs.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}});var vn=h((D1,Es)=>{var ut=Fs(),gs={};for(let e of Object.keys(ut))gs[ut[e]]=e;var d={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};Es.exports=d;for(let e of Object.keys(d)){if(!("channels"in d[e]))throw new Error("missing channels property: "+e);if(!("labels"in d[e]))throw new Error("missing channel labels property: "+e);if(d[e].labels.length!==d[e].channels)throw new Error("channel and label counts mismatch: "+e);let{channels:t,labels:r}=d[e];delete d[e].channels,delete d[e].labels,Object.defineProperty(d[e],"channels",{value:t}),Object.defineProperty(d[e],"labels",{value:r})}d.rgb.hsl=function(e){let t=e[0]/255,r=e[1]/255,n=e[2]/255,o=Math.min(t,r,n),i=Math.max(t,r,n),s=i-o,u,a;i===o?u=0:t===i?u=(r-n)/s:r===i?u=2+(n-t)/s:n===i&&(u=4+(t-r)/s),u=Math.min(u*60,360),u<0&&(u+=360);let c=(o+i)/2;return i===o?a=0:c<=.5?a=s/(i+o):a=s/(2-i-o),[u,a*100,c*100]};d.rgb.hsv=function(e){let t,r,n,o,i,s=e[0]/255,u=e[1]/255,a=e[2]/255,c=Math.max(s,u,a),l=c-Math.min(s,u,a),D=function(f){return(c-f)/6/l+1/2};return l===0?(o=0,i=0):(i=l/c,t=D(s),r=D(u),n=D(a),s===c?o=n-r:u===c?o=1/3+t-n:a===c&&(o=2/3+r-t),o<0?o+=1:o>1&&(o-=1)),[o*360,i*100,c*100]};d.rgb.hwb=function(e){let t=e[0],r=e[1],n=e[2],o=d.rgb.hsl(e)[0],i=1/255*Math.min(t,Math.min(r,n));return n=1-1/255*Math.max(t,Math.max(r,n)),[o,i*100,n*100]};d.rgb.cmyk=function(e){let t=e[0]/255,r=e[1]/255,n=e[2]/255,o=Math.min(1-t,1-r,1-n),i=(1-t-o)/(1-o)||0,s=(1-r-o)/(1-o)||0,u=(1-n-o)/(1-o)||0;return[i*100,s*100,u*100,o*100]};function $d(e,t){return(e[0]-t[0])**2+(e[1]-t[1])**2+(e[2]-t[2])**2}d.rgb.keyword=function(e){let t=gs[e];if(t)return t;let r=1/0,n;for(let o of Object.keys(ut)){let i=ut[o],s=$d(e,i);s<r&&(r=s,n=o)}return n};d.keyword.rgb=function(e){return ut[e]};d.rgb.xyz=function(e){let t=e[0]/255,r=e[1]/255,n=e[2]/255;t=t>.04045?((t+.055)/1.055)**2.4:t/12.92,r=r>.04045?((r+.055)/1.055)**2.4:r/12.92,n=n>.04045?((n+.055)/1.055)**2.4:n/12.92;let o=t*.4124+r*.3576+n*.1805,i=t*.2126+r*.7152+n*.0722,s=t*.0193+r*.1192+n*.9505;return[o*100,i*100,s*100]};d.rgb.lab=function(e){let t=d.rgb.xyz(e),r=t[0],n=t[1],o=t[2];r/=95.047,n/=100,o/=108.883,r=r>.008856?r**(1/3):7.787*r+16/116,n=n>.008856?n**(1/3):7.787*n+16/116,o=o>.008856?o**(1/3):7.787*o+16/116;let i=116*n-16,s=500*(r-n),u=200*(n-o);return[i,s,u]};d.hsl.rgb=function(e){let t=e[0]/360,r=e[1]/100,n=e[2]/100,o,i,s;if(r===0)return s=n*255,[s,s,s];n<.5?o=n*(1+r):o=n+r-n*r;let u=2*n-o,a=[0,0,0];for(let c=0;c<3;c++)i=t+1/3*-(c-1),i<0&&i++,i>1&&i--,6*i<1?s=u+(o-u)*6*i:2*i<1?s=o:3*i<2?s=u+(o-u)*(2/3-i)*6:s=u,a[c]=s*255;return a};d.hsl.hsv=function(e){let t=e[0],r=e[1]/100,n=e[2]/100,o=r,i=Math.max(n,.01);n*=2,r*=n<=1?n:2-n,o*=i<=1?i:2-i;let s=(n+r)/2,u=n===0?2*o/(i+o):2*r/(n+r);return[t,u*100,s*100]};d.hsv.rgb=function(e){let t=e[0]/60,r=e[1]/100,n=e[2]/100,o=Math.floor(t)%6,i=t-Math.floor(t),s=255*n*(1-r),u=255*n*(1-r*i),a=255*n*(1-r*(1-i));switch(n*=255,o){case 0:return[n,a,s];case 1:return[u,n,s];case 2:return[s,n,a];case 3:return[s,u,n];case 4:return[a,s,n];case 5:return[n,s,u]}};d.hsv.hsl=function(e){let t=e[0],r=e[1]/100,n=e[2]/100,o=Math.max(n,.01),i,s;s=(2-r)*n;let u=(2-r)*o;return i=r*o,i/=u<=1?u:2-u,i=i||0,s/=2,[t,i*100,s*100]};d.hwb.rgb=function(e){let t=e[0]/360,r=e[1]/100,n=e[2]/100,o=r+n,i;o>1&&(r/=o,n/=o);let s=Math.floor(6*t),u=1-n;i=6*t-s,(s&1)!==0&&(i=1-i);let a=r+i*(u-r),c,l,D;switch(s){default:case 6:case 0:c=u,l=a,D=r;break;case 1:c=a,l=u,D=r;break;case 2:c=r,l=u,D=a;break;case 3:c=r,l=a,D=u;break;case 4:c=a,l=r,D=u;break;case 5:c=u,l=r,D=a;break}return[c*255,l*255,D*255]};d.cmyk.rgb=function(e){let t=e[0]/100,r=e[1]/100,n=e[2]/100,o=e[3]/100,i=1-Math.min(1,t*(1-o)+o),s=1-Math.min(1,r*(1-o)+o),u=1-Math.min(1,n*(1-o)+o);return[i*255,s*255,u*255]};d.xyz.rgb=function(e){let t=e[0]/100,r=e[1]/100,n=e[2]/100,o,i,s;return o=t*3.2406+r*-1.5372+n*-.4986,i=t*-.9689+r*1.8758+n*.0415,s=t*.0557+r*-.204+n*1.057,o=o>.0031308?1.055*o**(1/2.4)-.055:o*12.92,i=i>.0031308?1.055*i**(1/2.4)-.055:i*12.92,s=s>.0031308?1.055*s**(1/2.4)-.055:s*12.92,o=Math.min(Math.max(0,o),1),i=Math.min(Math.max(0,i),1),s=Math.min(Math.max(0,s),1),[o*255,i*255,s*255]};d.xyz.lab=function(e){let t=e[0],r=e[1],n=e[2];t/=95.047,r/=100,n/=108.883,t=t>.008856?t**(1/3):7.787*t+16/116,r=r>.008856?r**(1/3):7.787*r+16/116,n=n>.008856?n**(1/3):7.787*n+16/116;let o=116*r-16,i=500*(t-r),s=200*(r-n);return[o,i,s]};d.lab.xyz=function(e){let t=e[0],r=e[1],n=e[2],o,i,s;i=(t+16)/116,o=r/500+i,s=i-n/200;let u=i**3,a=o**3,c=s**3;return i=u>.008856?u:(i-16/116)/7.787,o=a>.008856?a:(o-16/116)/7.787,s=c>.008856?c:(s-16/116)/7.787,o*=95.047,i*=100,s*=108.883,[o,i,s]};d.lab.lch=function(e){let t=e[0],r=e[1],n=e[2],o;o=Math.atan2(n,r)*360/2/Math.PI,o<0&&(o+=360);let s=Math.sqrt(r*r+n*n);return[t,s,o]};d.lch.lab=function(e){let t=e[0],r=e[1],o=e[2]/360*2*Math.PI,i=r*Math.cos(o),s=r*Math.sin(o);return[t,i,s]};d.rgb.ansi16=function(e,t=null){let[r,n,o]=e,i=t===null?d.rgb.hsv(e)[2]:t;if(i=Math.round(i/50),i===0)return 30;let s=30+(Math.round(o/255)<<2|Math.round(n/255)<<1|Math.round(r/255));return i===2&&(s+=60),s};d.hsv.ansi16=function(e){return d.rgb.ansi16(d.hsv.rgb(e),e[2])};d.rgb.ansi256=function(e){let t=e[0],r=e[1],n=e[2];return t===r&&r===n?t<8?16:t>248?231:Math.round((t-8)/247*24)+232:16+36*Math.round(t/255*5)+6*Math.round(r/255*5)+Math.round(n/255*5)};d.ansi16.rgb=function(e){let t=e%10;if(t===0||t===7)return e>50&&(t+=3.5),t=t/10.5*255,[t,t,t];let r=(~~(e>50)+1)*.5,n=(t&1)*r*255,o=(t>>1&1)*r*255,i=(t>>2&1)*r*255;return[n,o,i]};d.ansi256.rgb=function(e){if(e>=232){let i=(e-232)*10+8;return[i,i,i]}e-=16;let t,r=Math.floor(e/36)/5*255,n=Math.floor((t=e%36)/6)/5*255,o=t%6/5*255;return[r,n,o]};d.rgb.hex=function(e){let r=(((Math.round(e[0])&255)<<16)+((Math.round(e[1])&255)<<8)+(Math.round(e[2])&255)).toString(16).toUpperCase();return"000000".substring(r.length)+r};d.hex.rgb=function(e){let t=e.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!t)return[0,0,0];let r=t[0];t[0].length===3&&(r=r.split("").map(u=>u+u).join(""));let n=parseInt(r,16),o=n>>16&255,i=n>>8&255,s=n&255;return[o,i,s]};d.rgb.hcg=function(e){let t=e[0]/255,r=e[1]/255,n=e[2]/255,o=Math.max(Math.max(t,r),n),i=Math.min(Math.min(t,r),n),s=o-i,u,a;return s<1?u=i/(1-s):u=0,s<=0?a=0:o===t?a=(r-n)/s%6:o===r?a=2+(n-t)/s:a=4+(t-r)/s,a/=6,a%=1,[a*360,s*100,u*100]};d.hsl.hcg=function(e){let t=e[1]/100,r=e[2]/100,n=r<.5?2*t*r:2*t*(1-r),o=0;return n<1&&(o=(r-.5*n)/(1-n)),[e[0],n*100,o*100]};d.hsv.hcg=function(e){let t=e[1]/100,r=e[2]/100,n=t*r,o=0;return n<1&&(o=(r-n)/(1-n)),[e[0],n*100,o*100]};d.hcg.rgb=function(e){let t=e[0]/360,r=e[1]/100,n=e[2]/100;if(r===0)return[n*255,n*255,n*255];let o=[0,0,0],i=t%1*6,s=i%1,u=1-s,a=0;switch(Math.floor(i)){case 0:o[0]=1,o[1]=s,o[2]=0;break;case 1:o[0]=u,o[1]=1,o[2]=0;break;case 2:o[0]=0,o[1]=1,o[2]=s;break;case 3:o[0]=0,o[1]=u,o[2]=1;break;case 4:o[0]=s,o[1]=0,o[2]=1;break;default:o[0]=1,o[1]=0,o[2]=u}return a=(1-r)*n,[(r*o[0]+a)*255,(r*o[1]+a)*255,(r*o[2]+a)*255]};d.hcg.hsv=function(e){let t=e[1]/100,r=e[2]/100,n=t+r*(1-t),o=0;return n>0&&(o=t/n),[e[0],o*100,n*100]};d.hcg.hsl=function(e){let t=e[1]/100,n=e[2]/100*(1-t)+.5*t,o=0;return n>0&&n<.5?o=t/(2*n):n>=.5&&n<1&&(o=t/(2*(1-n))),[e[0],o*100,n*100]};d.hcg.hwb=function(e){let t=e[1]/100,r=e[2]/100,n=t+r*(1-t);return[e[0],(n-t)*100,(1-n)*100]};d.hwb.hcg=function(e){let t=e[1]/100,n=1-e[2]/100,o=n-t,i=0;return o<1&&(i=(n-o)/(1-o)),[e[0],o*100,i*100]};d.apple.rgb=function(e){return[e[0]/65535*255,e[1]/65535*255,e[2]/65535*255]};d.rgb.apple=function(e){return[e[0]/255*65535,e[1]/255*65535,e[2]/255*65535]};d.gray.rgb=function(e){return[e[0]/100*255,e[0]/100*255,e[0]/100*255]};d.gray.hsl=function(e){return[0,0,e[0]]};d.gray.hsv=d.gray.hsl;d.gray.hwb=function(e){return[0,100,e[0]]};d.gray.cmyk=function(e){return[0,0,0,e[0]]};d.gray.lab=function(e){return[e[0],0,0]};d.gray.hex=function(e){let t=Math.round(e[0]/100*255)&255,n=((t<<16)+(t<<8)+t).toString(16).toUpperCase();return"000000".substring(n.length)+n};d.rgb.gray=function(e){return[(e[0]+e[1]+e[2])/3/255*100]}});var ys=h((f1,Cs)=>{var Gt=vn();function jd(){let e={},t=Object.keys(Gt);for(let r=t.length,n=0;n<r;n++)e[t[n]]={distance:-1,parent:null};return e}function Ud(e){let t=jd(),r=[e];for(t[e].distance=0;r.length;){let n=r.pop(),o=Object.keys(Gt[n]);for(let i=o.length,s=0;s<i;s++){let u=o[s],a=t[u];a.distance===-1&&(a.distance=t[n].distance+1,a.parent=n,r.unshift(u))}}return t}function Gd(e,t){return function(r){return t(e(r))}}function zd(e,t){let r=[t[e].parent,e],n=Gt[t[e].parent][e],o=t[e].parent;for(;t[o].parent;)r.unshift(t[o].parent),n=Gd(Gt[t[o].parent][o],n),o=t[o].parent;return n.conversion=r,n}Cs.exports=function(e){let t=Ud(e),r={},n=Object.keys(t);for(let o=n.length,i=0;i<o;i++){let s=n[i];t[s].parent!==null&&(r[s]=zd(s,t))}return r}});var ws=h((p1,bs)=>{var Rn=vn(),Wd=ys(),Re={},Yd=Object.keys(Rn);function qd(e){let t=function(...r){let n=r[0];return n==null?n:(n.length>1&&(r=n),e(r))};return"conversion"in e&&(t.conversion=e.conversion),t}function Vd(e){let t=function(...r){let n=r[0];if(n==null)return n;n.length>1&&(r=n);let o=e(r);if(typeof o=="object")for(let i=o.length,s=0;s<i;s++)o[s]=Math.round(o[s]);return o};return"conversion"in e&&(t.conversion=e.conversion),t}Yd.forEach(e=>{Re[e]={},Object.defineProperty(Re[e],"channels",{value:Rn[e].channels}),Object.defineProperty(Re[e],"labels",{value:Rn[e].labels});let t=Wd(e);Object.keys(t).forEach(n=>{let o=t[n];Re[e][n]=Vd(o),Re[e][n].raw=qd(o)})});bs.exports=Re});var _s=h((d1,Ts)=>{"use strict";var xs=(e,t)=>(...r)=>`\x1B[${e(...r)+t}m`,Ss=(e,t)=>(...r)=>{let n=e(...r);return`\x1B[${38+t};5;${n}m`},As=(e,t)=>(...r)=>{let n=e(...r);return`\x1B[${38+t};2;${n[0]};${n[1]};${n[2]}m`},zt=e=>e,Bs=(e,t,r)=>[e,t,r],Ie=(e,t,r)=>{Object.defineProperty(e,t,{get:()=>{let n=r();return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0}),n},enumerable:!0,configurable:!0})},In,Me=(e,t,r,n)=>{In===void 0&&(In=ws());let o=n?10:0,i={};for(let[s,u]of Object.entries(In)){let a=s==="ansi16"?"ansi":s;s===t?i[a]=e(r,o):typeof u=="object"&&(i[a]=e(u[t],o))}return i};function Hd(){let e=new Map,t={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};t.color.gray=t.color.blackBright,t.bgColor.bgGray=t.bgColor.bgBlackBright,t.color.grey=t.color.blackBright,t.bgColor.bgGrey=t.bgColor.bgBlackBright;for(let[r,n]of Object.entries(t)){for(let[o,i]of Object.entries(n))t[o]={open:`\x1B[${i[0]}m`,close:`\x1B[${i[1]}m`},n[o]=t[o],e.set(i[0],i[1]);Object.defineProperty(t,r,{value:n,enumerable:!1})}return Object.defineProperty(t,"codes",{value:e,enumerable:!1}),t.color.close="\x1B[39m",t.bgColor.close="\x1B[49m",Ie(t.color,"ansi",()=>Me(xs,"ansi16",zt,!1)),Ie(t.color,"ansi256",()=>Me(Ss,"ansi256",zt,!1)),Ie(t.color,"ansi16m",()=>Me(As,"rgb",Bs,!1)),Ie(t.bgColor,"ansi",()=>Me(xs,"ansi16",zt,!0)),Ie(t.bgColor,"ansi256",()=>Me(Ss,"ansi256",zt,!0)),Ie(t.bgColor,"ansi16m",()=>Me(As,"rgb",Bs,!0)),t}Object.defineProperty(Ts,"exports",{enumerable:!0,get:Hd})});var vs=h((m1,Os)=>{"use strict";Os.exports=(e,t=process.argv)=>{let r=e.startsWith("-")?"":e.length===1?"-":"--",n=t.indexOf(r+e),o=t.indexOf("--");return n!==-1&&(o===-1||n<o)}});var Ms=h((h1,Is)=>{"use strict";var Kd=require("os"),Rs=require("tty"),N=vs(),{env:M}=process,fe;N("no-color")||N("no-colors")||N("color=false")||N("color=never")?fe=0:(N("color")||N("colors")||N("color=true")||N("color=always"))&&(fe=1);"FORCE_COLOR"in M&&(M.FORCE_COLOR==="true"?fe=1:M.FORCE_COLOR==="false"?fe=0:fe=M.FORCE_COLOR.length===0?1:Math.min(parseInt(M.FORCE_COLOR,10),3));function Mn(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function Pn(e,t){if(fe===0)return 0;if(N("color=16m")||N("color=full")||N("color=truecolor"))return 3;if(N("color=256"))return 2;if(e&&!t&&fe===void 0)return 0;let r=fe||0;if(M.TERM==="dumb")return r;if(process.platform==="win32"){let n=Kd.release().split(".");return Number(n[0])>=10&&Number(n[2])>=10586?Number(n[2])>=14931?3:2:1}if("CI"in M)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(n=>n in M)||M.CI_NAME==="codeship"?1:r;if("TEAMCITY_VERSION"in M)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(M.TEAMCITY_VERSION)?1:0;if(M.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in M){let n=parseInt((M.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(M.TERM_PROGRAM){case"iTerm.app":return n>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(M.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(M.TERM)||"COLORTERM"in M?1:r}function Jd(e){let t=Pn(e,e&&e.isTTY);return Mn(t)}Is.exports={supportsColor:Jd,stdout:Mn(Pn(!0,Rs.isatty(1))),stderr:Mn(Pn(!0,Rs.isatty(2)))}});var ks=h((F1,Ps)=>{"use strict";var Xd=(e,t,r)=>{let n=e.indexOf(t);if(n===-1)return e;let o=t.length,i=0,s="";do s+=e.substr(i,n-i)+t+r,i=n+o,n=e.indexOf(t,i);while(n!==-1);return s+=e.substr(i),s},Zd=(e,t,r,n)=>{let o=0,i="";do{let s=e[n-1]==="\r";i+=e.substr(o,(s?n-1:n)-o)+t+(s?`\r
`:`
`)+r,o=n+1,n=e.indexOf(`
`,o)}while(n!==-1);return i+=e.substr(o),i};Ps.exports={stringReplaceAll:Xd,stringEncaseCRLFWithFirstIndex:Zd}});var Us=h((g1,js)=>{"use strict";var Qd=/(?:\\(u(?:[a-f\d]{4}|\{[a-f\d]{1,6}\})|x[a-f\d]{2}|.))|(?:\{(~)?(\w+(?:\([^)]*\))?(?:\.\w+(?:\([^)]*\))?)*)(?:[ \t]|(?=\r?\n)))|(\})|((?:.|[\r\n\f])+?)/gi,Ls=/(?:^|\.)(\w+)(?:\(([^)]*)\))?/g,e0=/^(['"])((?:\\.|(?!\1)[^\\])*)\1$/,t0=/\\(u(?:[a-f\d]{4}|{[a-f\d]{1,6}})|x[a-f\d]{2}|.)|([^\\])/gi,r0=new Map([["n",`
`],["r","\r"],["t","	"],["b","\b"],["f","\f"],["v","\v"],["0","\0"],["\\","\\"],["e","\x1B"],["a","\x07"]]);function $s(e){let t=e[0]==="u",r=e[1]==="{";return t&&!r&&e.length===5||e[0]==="x"&&e.length===3?String.fromCharCode(parseInt(e.slice(1),16)):t&&r?String.fromCodePoint(parseInt(e.slice(2,-1),16)):r0.get(e)||e}function n0(e,t){let r=[],n=t.trim().split(/\s*,\s*/g),o;for(let i of n){let s=Number(i);if(!Number.isNaN(s))r.push(s);else if(o=i.match(e0))r.push(o[2].replace(t0,(u,a,c)=>a?$s(a):c));else throw new Error(`Invalid Chalk template style argument: ${i} (in style '${e}')`)}return r}function o0(e){Ls.lastIndex=0;let t=[],r;for(;(r=Ls.exec(e))!==null;){let n=r[1];if(r[2]){let o=n0(n,r[2]);t.push([n].concat(o))}else t.push([n])}return t}function Ns(e,t){let r={};for(let o of t)for(let i of o.styles)r[i[0]]=o.inverse?null:i.slice(1);let n=e;for(let[o,i]of Object.entries(r))if(Array.isArray(i)){if(!(o in n))throw new Error(`Unknown Chalk style: ${o}`);n=i.length>0?n[o](...i):n[o]}return n}js.exports=(e,t)=>{let r=[],n=[],o=[];if(t.replace(Qd,(i,s,u,a,c,l)=>{if(s)o.push($s(s));else if(a){let D=o.join("");o=[],n.push(r.length===0?D:Ns(e,r)(D)),r.push({inverse:u,styles:o0(a)})}else if(c){if(r.length===0)throw new Error("Found extraneous } in Chalk template literal");n.push(Ns(e,r)(o.join(""))),o=[],r.pop()}else o.push(l)}),n.push(o.join("")),r.length>0){let i=`Chalk template literal is missing ${r.length} closing bracket${r.length===1?"":"s"} (\`}\`)`;throw new Error(i)}return n.join("")}});var Un=h((E1,Vs)=>{"use strict";var at=_s(),{stdout:Ln,stderr:Nn}=Ms(),{stringReplaceAll:i0,stringEncaseCRLFWithFirstIndex:s0}=ks(),{isArray:Wt}=Array,zs=["ansi","ansi","ansi256","ansi16m"],Pe=Object.create(null),u0=(e,t={})=>{if(t.level&&!(Number.isInteger(t.level)&&t.level>=0&&t.level<=3))throw new Error("The `level` option should be an integer from 0 to 3");let r=Ln?Ln.level:0;e.level=t.level===void 0?r:t.level},$n=class{constructor(t){return Ws(t)}},Ws=e=>{let t={};return u0(t,e),t.template=(...r)=>qs(t.template,...r),Object.setPrototypeOf(t,Yt.prototype),Object.setPrototypeOf(t.template,t),t.template.constructor=()=>{throw new Error("`chalk.constructor()` is deprecated. Use `new chalk.Instance()` instead.")},t.template.Instance=$n,t.template};function Yt(e){return Ws(e)}for(let[e,t]of Object.entries(at))Pe[e]={get(){let r=qt(this,jn(t.open,t.close,this._styler),this._isEmpty);return Object.defineProperty(this,e,{value:r}),r}};Pe.visible={get(){let e=qt(this,this._styler,!0);return Object.defineProperty(this,"visible",{value:e}),e}};var Ys=["rgb","hex","keyword","hsl","hsv","hwb","ansi","ansi256"];for(let e of Ys)Pe[e]={get(){let{level:t}=this;return function(...r){let n=jn(at.color[zs[t]][e](...r),at.color.close,this._styler);return qt(this,n,this._isEmpty)}}};for(let e of Ys){let t="bg"+e[0].toUpperCase()+e.slice(1);Pe[t]={get(){let{level:r}=this;return function(...n){let o=jn(at.bgColor[zs[r]][e](...n),at.bgColor.close,this._styler);return qt(this,o,this._isEmpty)}}}}var a0=Object.defineProperties(()=>{},{...Pe,level:{enumerable:!0,get(){return this._generator.level},set(e){this._generator.level=e}}}),jn=(e,t,r)=>{let n,o;return r===void 0?(n=e,o=t):(n=r.openAll+e,o=t+r.closeAll),{open:e,close:t,openAll:n,closeAll:o,parent:r}},qt=(e,t,r)=>{let n=(...o)=>Wt(o[0])&&Wt(o[0].raw)?Gs(n,qs(n,...o)):Gs(n,o.length===1?""+o[0]:o.join(" "));return Object.setPrototypeOf(n,a0),n._generator=e,n._styler=t,n._isEmpty=r,n},Gs=(e,t)=>{if(e.level<=0||!t)return e._isEmpty?"":t;let r=e._styler;if(r===void 0)return t;let{openAll:n,closeAll:o}=r;if(t.indexOf("\x1B")!==-1)for(;r!==void 0;)t=i0(t,r.close,r.open),r=r.parent;let i=t.indexOf(`
`);return i!==-1&&(t=s0(t,o,n,i)),n+t+o},kn,qs=(e,...t)=>{let[r]=t;if(!Wt(r)||!Wt(r.raw))return t.join(" ");let n=t.slice(1),o=[r.raw[0]];for(let i=1;i<r.length;i++)o.push(String(n[i-1]).replace(/[{}\\]/g,"\\$&"),String(r.raw[i]));return kn===void 0&&(kn=Us()),kn(e,o.join(""))};Object.defineProperties(Yt.prototype,Pe);var Vt=Yt();Vt.supportsColor=Ln;Vt.stderr=Yt({level:Nn?Nn.level:0});Vt.stderr.supportsColor=Nn;Vs.exports=Vt});var Zs=h((x1,Xs)=>{var f0=require("node:tty"),p0=f0?.WriteStream?.prototype?.hasColors?.()??!1,E=(e,t)=>{if(!p0)return o=>o;let r=`\x1B[${e}m`,n=`\x1B[${t}m`;return o=>{let i=o+"",s=i.indexOf(n);if(s===-1)return r+i+n;let u=r,a=0;for(;s!==-1;)u+=i.slice(a,s)+r,a=s+n.length,s=i.indexOf(n,a);return u+=i.slice(a)+n,u}},g={};g.reset=E(0,0);g.bold=E(1,22);g.dim=E(2,22);g.italic=E(3,23);g.underline=E(4,24);g.overline=E(53,55);g.inverse=E(7,27);g.hidden=E(8,28);g.strikethrough=E(9,29);g.black=E(30,39);g.red=E(31,39);g.green=E(32,39);g.yellow=E(33,39);g.blue=E(34,39);g.magenta=E(35,39);g.cyan=E(36,39);g.white=E(37,39);g.gray=E(90,39);g.bgBlack=E(40,49);g.bgRed=E(41,49);g.bgGreen=E(42,49);g.bgYellow=E(43,49);g.bgBlue=E(44,49);g.bgMagenta=E(45,49);g.bgCyan=E(46,49);g.bgWhite=E(47,49);g.bgGray=E(100,49);g.redBright=E(91,39);g.greenBright=E(92,39);g.yellowBright=E(93,39);g.blueBright=E(94,39);g.magentaBright=E(95,39);g.cyanBright=E(96,39);g.whiteBright=E(97,39);g.bgRedBright=E(101,49);g.bgGreenBright=E(102,49);g.bgYellowBright=E(103,49);g.bgBlueBright=E(104,49);g.bgMagentaBright=E(105,49);g.bgCyanBright=E(106,49);g.bgWhiteBright=E(107,49);Xs.exports=g});var mu=h((H1,N0)=>{N0.exports={dots:{interval:80,frames:["\u280B","\u2819","\u2839","\u2838","\u283C","\u2834","\u2826","\u2827","\u2807","\u280F"]},dots2:{interval:80,frames:["\u28FE","\u28FD","\u28FB","\u28BF","\u287F","\u28DF","\u28EF","\u28F7"]},dots3:{interval:80,frames:["\u280B","\u2819","\u281A","\u281E","\u2816","\u2826","\u2834","\u2832","\u2833","\u2813"]},dots4:{interval:80,frames:["\u2804","\u2806","\u2807","\u280B","\u2819","\u2838","\u2830","\u2820","\u2830","\u2838","\u2819","\u280B","\u2807","\u2806"]},dots5:{interval:80,frames:["\u280B","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B"]},dots6:{interval:80,frames:["\u2801","\u2809","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2824","\u2804","\u2804","\u2824","\u2834","\u2832","\u2812","\u2802","\u2802","\u2812","\u281A","\u2819","\u2809","\u2801"]},dots7:{interval:80,frames:["\u2808","\u2809","\u280B","\u2813","\u2812","\u2810","\u2810","\u2812","\u2816","\u2826","\u2824","\u2820","\u2820","\u2824","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B","\u2809","\u2808"]},dots8:{interval:80,frames:["\u2801","\u2801","\u2809","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2824","\u2804","\u2804","\u2824","\u2820","\u2820","\u2824","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B","\u2809","\u2808","\u2808"]},dots9:{interval:80,frames:["\u28B9","\u28BA","\u28BC","\u28F8","\u28C7","\u2867","\u2857","\u284F"]},dots10:{interval:80,frames:["\u2884","\u2882","\u2881","\u2841","\u2848","\u2850","\u2860"]},dots11:{interval:100,frames:["\u2801","\u2802","\u2804","\u2840","\u2880","\u2820","\u2810","\u2808"]},dots12:{interval:80,frames:["\u2880\u2800","\u2840\u2800","\u2804\u2800","\u2882\u2800","\u2842\u2800","\u2805\u2800","\u2883\u2800","\u2843\u2800","\u280D\u2800","\u288B\u2800","\u284B\u2800","\u280D\u2801","\u288B\u2801","\u284B\u2801","\u280D\u2809","\u280B\u2809","\u280B\u2809","\u2809\u2819","\u2809\u2819","\u2809\u2829","\u2808\u2899","\u2808\u2859","\u2888\u2829","\u2840\u2899","\u2804\u2859","\u2882\u2829","\u2842\u2898","\u2805\u2858","\u2883\u2828","\u2843\u2890","\u280D\u2850","\u288B\u2820","\u284B\u2880","\u280D\u2841","\u288B\u2801","\u284B\u2801","\u280D\u2809","\u280B\u2809","\u280B\u2809","\u2809\u2819","\u2809\u2819","\u2809\u2829","\u2808\u2899","\u2808\u2859","\u2808\u2829","\u2800\u2899","\u2800\u2859","\u2800\u2829","\u2800\u2898","\u2800\u2858","\u2800\u2828","\u2800\u2890","\u2800\u2850","\u2800\u2820","\u2800\u2880","\u2800\u2840"]},dots13:{interval:80,frames:["\u28FC","\u28F9","\u28BB","\u283F","\u285F","\u28CF","\u28E7","\u28F6"]},dots8Bit:{interval:80,frames:["\u2800","\u2801","\u2802","\u2803","\u2804","\u2805","\u2806","\u2807","\u2840","\u2841","\u2842","\u2843","\u2844","\u2845","\u2846","\u2847","\u2808","\u2809","\u280A","\u280B","\u280C","\u280D","\u280E","\u280F","\u2848","\u2849","\u284A","\u284B","\u284C","\u284D","\u284E","\u284F","\u2810","\u2811","\u2812","\u2813","\u2814","\u2815","\u2816","\u2817","\u2850","\u2851","\u2852","\u2853","\u2854","\u2855","\u2856","\u2857","\u2818","\u2819","\u281A","\u281B","\u281C","\u281D","\u281E","\u281F","\u2858","\u2859","\u285A","\u285B","\u285C","\u285D","\u285E","\u285F","\u2820","\u2821","\u2822","\u2823","\u2824","\u2825","\u2826","\u2827","\u2860","\u2861","\u2862","\u2863","\u2864","\u2865","\u2866","\u2867","\u2828","\u2829","\u282A","\u282B","\u282C","\u282D","\u282E","\u282F","\u2868","\u2869","\u286A","\u286B","\u286C","\u286D","\u286E","\u286F","\u2830","\u2831","\u2832","\u2833","\u2834","\u2835","\u2836","\u2837","\u2870","\u2871","\u2872","\u2873","\u2874","\u2875","\u2876","\u2877","\u2838","\u2839","\u283A","\u283B","\u283C","\u283D","\u283E","\u283F","\u2878","\u2879","\u287A","\u287B","\u287C","\u287D","\u287E","\u287F","\u2880","\u2881","\u2882","\u2883","\u2884","\u2885","\u2886","\u2887","\u28C0","\u28C1","\u28C2","\u28C3","\u28C4","\u28C5","\u28C6","\u28C7","\u2888","\u2889","\u288A","\u288B","\u288C","\u288D","\u288E","\u288F","\u28C8","\u28C9","\u28CA","\u28CB","\u28CC","\u28CD","\u28CE","\u28CF","\u2890","\u2891","\u2892","\u2893","\u2894","\u2895","\u2896","\u2897","\u28D0","\u28D1","\u28D2","\u28D3","\u28D4","\u28D5","\u28D6","\u28D7","\u2898","\u2899","\u289A","\u289B","\u289C","\u289D","\u289E","\u289F","\u28D8","\u28D9","\u28DA","\u28DB","\u28DC","\u28DD","\u28DE","\u28DF","\u28A0","\u28A1","\u28A2","\u28A3","\u28A4","\u28A5","\u28A6","\u28A7","\u28E0","\u28E1","\u28E2","\u28E3","\u28E4","\u28E5","\u28E6","\u28E7","\u28A8","\u28A9","\u28AA","\u28AB","\u28AC","\u28AD","\u28AE","\u28AF","\u28E8","\u28E9","\u28EA","\u28EB","\u28EC","\u28ED","\u28EE","\u28EF","\u28B0","\u28B1","\u28B2","\u28B3","\u28B4","\u28B5","\u28B6","\u28B7","\u28F0","\u28F1","\u28F2","\u28F3","\u28F4","\u28F5","\u28F6","\u28F7","\u28B8","\u28B9","\u28BA","\u28BB","\u28BC","\u28BD","\u28BE","\u28BF","\u28F8","\u28F9","\u28FA","\u28FB","\u28FC","\u28FD","\u28FE","\u28FF"]},sand:{interval:80,frames:["\u2801","\u2802","\u2804","\u2840","\u2848","\u2850","\u2860","\u28C0","\u28C1","\u28C2","\u28C4","\u28CC","\u28D4","\u28E4","\u28E5","\u28E6","\u28EE","\u28F6","\u28F7","\u28FF","\u287F","\u283F","\u289F","\u281F","\u285B","\u281B","\u282B","\u288B","\u280B","\u280D","\u2849","\u2809","\u2811","\u2821","\u2881"]},line:{interval:130,frames:["-","\\","|","/"]},line2:{interval:100,frames:["\u2802","-","\u2013","\u2014","\u2013","-"]},pipe:{interval:100,frames:["\u2524","\u2518","\u2534","\u2514","\u251C","\u250C","\u252C","\u2510"]},simpleDots:{interval:400,frames:[".  ",".. ","...","   "]},simpleDotsScrolling:{interval:200,frames:[".  ",".. ","..."," ..","  .","   "]},star:{interval:70,frames:["\u2736","\u2738","\u2739","\u273A","\u2739","\u2737"]},star2:{interval:80,frames:["+","x","*"]},flip:{interval:70,frames:["_","_","_","-","`","`","'","\xB4","-","_","_","_"]},hamburger:{interval:100,frames:["\u2631","\u2632","\u2634"]},growVertical:{interval:120,frames:["\u2581","\u2583","\u2584","\u2585","\u2586","\u2587","\u2586","\u2585","\u2584","\u2583"]},growHorizontal:{interval:120,frames:["\u258F","\u258E","\u258D","\u258C","\u258B","\u258A","\u2589","\u258A","\u258B","\u258C","\u258D","\u258E"]},balloon:{interval:140,frames:[" ",".","o","O","@","*"," "]},balloon2:{interval:120,frames:[".","o","O","\xB0","O","o","."]},noise:{interval:100,frames:["\u2593","\u2592","\u2591"]},bounce:{interval:120,frames:["\u2801","\u2802","\u2804","\u2802"]},boxBounce:{interval:120,frames:["\u2596","\u2598","\u259D","\u2597"]},boxBounce2:{interval:100,frames:["\u258C","\u2580","\u2590","\u2584"]},triangle:{interval:50,frames:["\u25E2","\u25E3","\u25E4","\u25E5"]},binary:{interval:80,frames:["010010","001100","100101","111010","111101","010111","101011","111000","110011","110101"]},arc:{interval:100,frames:["\u25DC","\u25E0","\u25DD","\u25DE","\u25E1","\u25DF"]},circle:{interval:120,frames:["\u25E1","\u2299","\u25E0"]},squareCorners:{interval:180,frames:["\u25F0","\u25F3","\u25F2","\u25F1"]},circleQuarters:{interval:120,frames:["\u25F4","\u25F7","\u25F6","\u25F5"]},circleHalves:{interval:50,frames:["\u25D0","\u25D3","\u25D1","\u25D2"]},squish:{interval:100,frames:["\u256B","\u256A"]},toggle:{interval:250,frames:["\u22B6","\u22B7"]},toggle2:{interval:80,frames:["\u25AB","\u25AA"]},toggle3:{interval:120,frames:["\u25A1","\u25A0"]},toggle4:{interval:100,frames:["\u25A0","\u25A1","\u25AA","\u25AB"]},toggle5:{interval:100,frames:["\u25AE","\u25AF"]},toggle6:{interval:300,frames:["\u101D","\u1040"]},toggle7:{interval:80,frames:["\u29BE","\u29BF"]},toggle8:{interval:100,frames:["\u25CD","\u25CC"]},toggle9:{interval:100,frames:["\u25C9","\u25CE"]},toggle10:{interval:100,frames:["\u3282","\u3280","\u3281"]},toggle11:{interval:50,frames:["\u29C7","\u29C6"]},toggle12:{interval:120,frames:["\u2617","\u2616"]},toggle13:{interval:80,frames:["=","*","-"]},arrow:{interval:100,frames:["\u2190","\u2196","\u2191","\u2197","\u2192","\u2198","\u2193","\u2199"]},arrow2:{interval:80,frames:["\u2B06\uFE0F ","\u2197\uFE0F ","\u27A1\uFE0F ","\u2198\uFE0F ","\u2B07\uFE0F ","\u2199\uFE0F ","\u2B05\uFE0F ","\u2196\uFE0F "]},arrow3:{interval:120,frames:["\u25B9\u25B9\u25B9\u25B9\u25B9","\u25B8\u25B9\u25B9\u25B9\u25B9","\u25B9\u25B8\u25B9\u25B9\u25B9","\u25B9\u25B9\u25B8\u25B9\u25B9","\u25B9\u25B9\u25B9\u25B8\u25B9","\u25B9\u25B9\u25B9\u25B9\u25B8"]},bouncingBar:{interval:80,frames:["[    ]","[=   ]","[==  ]","[=== ]","[====]","[ ===]","[  ==]","[   =]","[    ]","[   =]","[  ==]","[ ===]","[====]","[=== ]","[==  ]","[=   ]"]},bouncingBall:{interval:80,frames:["( \u25CF    )","(  \u25CF   )","(   \u25CF  )","(    \u25CF )","(     \u25CF)","(    \u25CF )","(   \u25CF  )","(  \u25CF   )","( \u25CF    )","(\u25CF     )"]},smiley:{interval:200,frames:["\u{1F604} ","\u{1F61D} "]},monkey:{interval:300,frames:["\u{1F648} ","\u{1F648} ","\u{1F649} ","\u{1F64A} "]},hearts:{interval:100,frames:["\u{1F49B} ","\u{1F499} ","\u{1F49C} ","\u{1F49A} ","\u2764\uFE0F "]},clock:{interval:100,frames:["\u{1F55B} ","\u{1F550} ","\u{1F551} ","\u{1F552} ","\u{1F553} ","\u{1F554} ","\u{1F555} ","\u{1F556} ","\u{1F557} ","\u{1F558} ","\u{1F559} ","\u{1F55A} "]},earth:{interval:180,frames:["\u{1F30D} ","\u{1F30E} ","\u{1F30F} "]},material:{interval:17,frames:["\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581"]},moon:{interval:80,frames:["\u{1F311} ","\u{1F312} ","\u{1F313} ","\u{1F314} ","\u{1F315} ","\u{1F316} ","\u{1F317} ","\u{1F318} "]},runner:{interval:140,frames:["\u{1F6B6} ","\u{1F3C3} "]},pong:{interval:80,frames:["\u2590\u2802       \u258C","\u2590\u2808       \u258C","\u2590 \u2802      \u258C","\u2590 \u2820      \u258C","\u2590  \u2840     \u258C","\u2590  \u2820     \u258C","\u2590   \u2802    \u258C","\u2590   \u2808    \u258C","\u2590    \u2802   \u258C","\u2590    \u2820   \u258C","\u2590     \u2840  \u258C","\u2590     \u2820  \u258C","\u2590      \u2802 \u258C","\u2590      \u2808 \u258C","\u2590       \u2802\u258C","\u2590       \u2820\u258C","\u2590       \u2840\u258C","\u2590      \u2820 \u258C","\u2590      \u2802 \u258C","\u2590     \u2808  \u258C","\u2590     \u2802  \u258C","\u2590    \u2820   \u258C","\u2590    \u2840   \u258C","\u2590   \u2820    \u258C","\u2590   \u2802    \u258C","\u2590  \u2808     \u258C","\u2590  \u2802     \u258C","\u2590 \u2820      \u258C","\u2590 \u2840      \u258C","\u2590\u2820       \u258C"]},shark:{interval:120,frames:["\u2590|\\____________\u258C","\u2590_|\\___________\u258C","\u2590__|\\__________\u258C","\u2590___|\\_________\u258C","\u2590____|\\________\u258C","\u2590_____|\\_______\u258C","\u2590______|\\______\u258C","\u2590_______|\\_____\u258C","\u2590________|\\____\u258C","\u2590_________|\\___\u258C","\u2590__________|\\__\u258C","\u2590___________|\\_\u258C","\u2590____________|\\\u258C","\u2590____________/|\u258C","\u2590___________/|_\u258C","\u2590__________/|__\u258C","\u2590_________/|___\u258C","\u2590________/|____\u258C","\u2590_______/|_____\u258C","\u2590______/|______\u258C","\u2590_____/|_______\u258C","\u2590____/|________\u258C","\u2590___/|_________\u258C","\u2590__/|__________\u258C","\u2590_/|___________\u258C","\u2590/|____________\u258C"]},dqpb:{interval:100,frames:["d","q","p","b"]},weather:{interval:100,frames:["\u2600\uFE0F ","\u2600\uFE0F ","\u2600\uFE0F ","\u{1F324} ","\u26C5\uFE0F ","\u{1F325} ","\u2601\uFE0F ","\u{1F327} ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u26C8 ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u2601\uFE0F ","\u{1F325} ","\u26C5\uFE0F ","\u{1F324} ","\u2600\uFE0F ","\u2600\uFE0F "]},christmas:{interval:400,frames:["\u{1F332}","\u{1F384}"]},grenade:{interval:80,frames:["\u060C  ","\u2032  "," \xB4 "," \u203E ","  \u2E0C","  \u2E0A","  |","  \u204E","  \u2055"," \u0DF4 ","  \u2053","   ","   ","   "]},point:{interval:125,frames:["\u2219\u2219\u2219","\u25CF\u2219\u2219","\u2219\u25CF\u2219","\u2219\u2219\u25CF","\u2219\u2219\u2219"]},layer:{interval:150,frames:["-","=","\u2261"]},betaWave:{interval:80,frames:["\u03C1\u03B2\u03B2\u03B2\u03B2\u03B2\u03B2","\u03B2\u03C1\u03B2\u03B2\u03B2\u03B2\u03B2","\u03B2\u03B2\u03C1\u03B2\u03B2\u03B2\u03B2","\u03B2\u03B2\u03B2\u03C1\u03B2\u03B2\u03B2","\u03B2\u03B2\u03B2\u03B2\u03C1\u03B2\u03B2","\u03B2\u03B2\u03B2\u03B2\u03B2\u03C1\u03B2","\u03B2\u03B2\u03B2\u03B2\u03B2\u03B2\u03C1"]},fingerDance:{interval:160,frames:["\u{1F918} ","\u{1F91F} ","\u{1F596} ","\u270B ","\u{1F91A} ","\u{1F446} "]},fistBump:{interval:80,frames:["\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u3000\u{1F91C}\u3000\u3000\u{1F91B}\u3000 ","\u3000\u3000\u{1F91C}\u{1F91B}\u3000\u3000 ","\u3000\u{1F91C}\u2728\u{1F91B}\u3000\u3000 ","\u{1F91C}\u3000\u2728\u3000\u{1F91B}\u3000 "]},soccerHeader:{interval:80,frames:[" \u{1F9D1}\u26BD\uFE0F       \u{1F9D1} ","\u{1F9D1}  \u26BD\uFE0F      \u{1F9D1} ","\u{1F9D1}   \u26BD\uFE0F     \u{1F9D1} ","\u{1F9D1}    \u26BD\uFE0F    \u{1F9D1} ","\u{1F9D1}     \u26BD\uFE0F   \u{1F9D1} ","\u{1F9D1}      \u26BD\uFE0F  \u{1F9D1} ","\u{1F9D1}       \u26BD\uFE0F\u{1F9D1}  ","\u{1F9D1}      \u26BD\uFE0F  \u{1F9D1} ","\u{1F9D1}     \u26BD\uFE0F   \u{1F9D1} ","\u{1F9D1}    \u26BD\uFE0F    \u{1F9D1} ","\u{1F9D1}   \u26BD\uFE0F     \u{1F9D1} ","\u{1F9D1}  \u26BD\uFE0F      \u{1F9D1} "]},mindblown:{interval:160,frames:["\u{1F610} ","\u{1F610} ","\u{1F62E} ","\u{1F62E} ","\u{1F626} ","\u{1F626} ","\u{1F627} ","\u{1F627} ","\u{1F92F} ","\u{1F4A5} ","\u2728 ","\u3000 ","\u3000 ","\u3000 "]},speaker:{interval:160,frames:["\u{1F508} ","\u{1F509} ","\u{1F50A} ","\u{1F509} "]},orangePulse:{interval:100,frames:["\u{1F538} ","\u{1F536} ","\u{1F7E0} ","\u{1F7E0} ","\u{1F536} "]},bluePulse:{interval:100,frames:["\u{1F539} ","\u{1F537} ","\u{1F535} ","\u{1F535} ","\u{1F537} "]},orangeBluePulse:{interval:100,frames:["\u{1F538} ","\u{1F536} ","\u{1F7E0} ","\u{1F7E0} ","\u{1F536} ","\u{1F539} ","\u{1F537} ","\u{1F535} ","\u{1F535} ","\u{1F537} "]},timeTravel:{interval:100,frames:["\u{1F55B} ","\u{1F55A} ","\u{1F559} ","\u{1F558} ","\u{1F557} ","\u{1F556} ","\u{1F555} ","\u{1F554} ","\u{1F553} ","\u{1F552} ","\u{1F551} ","\u{1F550} "]},aesthetic:{interval:80,frames:["\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0","\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1\u25B1"]},dwarfFortress:{interval:80,frames:[" \u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2593\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2593\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2592\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2592\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2591\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2591\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A \u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2593\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2593\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2592\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2592\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2591\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2591\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A \u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2593\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2593\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2592\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2592\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2591\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2591\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A \u2588\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2593\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2593\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2592\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2592\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2591\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2591\u2588\u2588\xA3\xA3\xA3  ","   \u263A \u2588\u2588\xA3\xA3\xA3  ","    \u263A\u2588\u2588\xA3\xA3\xA3  ","    \u263A\u2588\u2588\xA3\xA3\xA3  ","    \u263A\u2593\u2588\xA3\xA3\xA3  ","    \u263A\u2593\u2588\xA3\xA3\xA3  ","    \u263A\u2592\u2588\xA3\xA3\xA3  ","    \u263A\u2592\u2588\xA3\xA3\xA3  ","    \u263A\u2591\u2588\xA3\xA3\xA3  ","    \u263A\u2591\u2588\xA3\xA3\xA3  ","    \u263A \u2588\xA3\xA3\xA3  ","     \u263A\u2588\xA3\xA3\xA3  ","     \u263A\u2588\xA3\xA3\xA3  ","     \u263A\u2593\xA3\xA3\xA3  ","     \u263A\u2593\xA3\xA3\xA3  ","     \u263A\u2592\xA3\xA3\xA3  ","     \u263A\u2592\xA3\xA3\xA3  ","     \u263A\u2591\xA3\xA3\xA3  ","     \u263A\u2591\xA3\xA3\xA3  ","     \u263A \xA3\xA3\xA3  ","      \u263A\xA3\xA3\xA3  ","      \u263A\xA3\xA3\xA3  ","      \u263A\u2593\xA3\xA3  ","      \u263A\u2593\xA3\xA3  ","      \u263A\u2592\xA3\xA3  ","      \u263A\u2592\xA3\xA3  ","      \u263A\u2591\xA3\xA3  ","      \u263A\u2591\xA3\xA3  ","      \u263A \xA3\xA3  ","       \u263A\xA3\xA3  ","       \u263A\xA3\xA3  ","       \u263A\u2593\xA3  ","       \u263A\u2593\xA3  ","       \u263A\u2592\xA3  ","       \u263A\u2592\xA3  ","       \u263A\u2591\xA3  ","       \u263A\u2591\xA3  ","       \u263A \xA3  ","        \u263A\xA3  ","        \u263A\xA3  ","        \u263A\u2593  ","        \u263A\u2593  ","        \u263A\u2592  ","        \u263A\u2592  ","        \u263A\u2591  ","        \u263A\u2591  ","        \u263A   ","        \u263A  &","        \u263A \u263C&","       \u263A \u263C &","       \u263A\u263C  &","      \u263A\u263C  & ","      \u203C   & ","     \u263A   &  ","    \u203C    &  ","   \u263A    &   ","  \u203C     &   "," \u263A     &    ","\u203C      &    ","      &     ","      &     ","     &   \u2591  ","     &   \u2592  ","    &    \u2593  ","    &    \xA3  ","   &    \u2591\xA3  ","   &    \u2592\xA3  ","  &     \u2593\xA3  ","  &     \xA3\xA3  "," &     \u2591\xA3\xA3  "," &     \u2592\xA3\xA3  ","&      \u2593\xA3\xA3  ","&      \xA3\xA3\xA3  ","      \u2591\xA3\xA3\xA3  ","      \u2592\xA3\xA3\xA3  ","      \u2593\xA3\xA3\xA3  ","      \u2588\xA3\xA3\xA3  ","     \u2591\u2588\xA3\xA3\xA3  ","     \u2592\u2588\xA3\xA3\xA3  ","     \u2593\u2588\xA3\xA3\xA3  ","     \u2588\u2588\xA3\xA3\xA3  ","    \u2591\u2588\u2588\xA3\xA3\xA3  ","    \u2592\u2588\u2588\xA3\xA3\xA3  ","    \u2593\u2588\u2588\xA3\xA3\xA3  ","    \u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2591\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2592\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2593\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2591\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2592\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2593\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2591\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2592\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2593\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "]}}});var no=h((K1,Fu)=>{"use strict";var rr=Object.assign({},mu()),hu=Object.keys(rr);Object.defineProperty(rr,"random",{get(){let e=Math.floor(Math.random()*hu.length),t=hu[e];return rr[t]}});Fu.exports=rr});var Mu=h((Fy,Iu)=>{Iu.exports=()=>/[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26D3\uFE0F?(?:\u200D\uD83D\uDCA5)?|\u26F9(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF43\uDF45-\uDF4A\uDF4C-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDF44(?:\u200D\uD83D\uDFEB)?|\uDF4B(?:\u200D\uD83D\uDFE9)?|\uDFC3(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4\uDEB5](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE41\uDE43\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC08(?:\u200D\u2B1B)?|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC26(?:\u200D(?:\u2B1B|\uD83D\uDD25))?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?|\uDE42(?:\u200D[\u2194\u2195]\uFE0F?)?|\uDEB6(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE89\uDE8F-\uDEC2\uDEC6\uDECE-\uDEDC\uDEDF-\uDEE9]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDCE(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1|\uDDD1\u200D\uD83E\uDDD2(?:\u200D\uD83E\uDDD2)?|\uDDD2(?:\u200D\uD83E\uDDD2)?))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g});var Eo=h((Py,Nu)=>{"use strict";var Lu=require("fs"),go;function lm(){try{return Lu.statSync("/.dockerenv"),!0}catch{return!1}}function Dm(){try{return Lu.readFileSync("/proc/self/cgroup","utf8").includes("docker")}catch{return!1}}Nu.exports=()=>(go===void 0&&(go=lm()||Dm()),go)});var Uu=h((ky,Co)=>{"use strict";var fm=require("os"),pm=require("fs"),$u=Eo(),ju=()=>{if(process.platform!=="linux")return!1;if(fm.release().toLowerCase().includes("microsoft"))return!$u();try{return pm.readFileSync("/proc/version","utf8").toLowerCase().includes("microsoft")?!$u():!1}catch{return!1}};process.env.__IS_WSL_TEST__?Co.exports=ju:Co.exports=ju()});var zu=h((Ly,Gu)=>{"use strict";Gu.exports=(e,t,r)=>{let n=o=>Object.defineProperty(e,t,{value:o,enumerable:!0,writable:!0});return Object.defineProperty(e,t,{configurable:!0,enumerable:!0,get(){let o=r();return n(o),o},set(o){n(o)}}),e}});var Ju=h((Ny,Ku)=>{var dm=require("path"),mm=require("child_process"),{promises:ur,constants:Hu}=require("fs"),sr=Uu(),hm=Eo(),bo=zu(),Wu=dm.join(__dirname,"xdg-open"),{platform:Ue,arch:Yu}=process,Fm=()=>{try{return ur.statSync("/run/.containerenv"),!0}catch{return!1}},yo;function gm(){return yo===void 0&&(yo=Fm()||hm()),yo}var Em=(()=>{let e="/mnt/",t;return async function(){if(t)return t;let r="/etc/wsl.conf",n=!1;try{await ur.access(r,Hu.F_OK),n=!0}catch{}if(!n)return e;let o=await ur.readFile(r,{encoding:"utf8"}),i=/(?<!#.*)root\s*=\s*(?<mountPoint>.*)/g.exec(o);return i?(t=i.groups.mountPoint.trim(),t=t.endsWith("/")?t:`${t}/`,t):e}})(),qu=async(e,t)=>{let r;for(let n of e)try{return await t(n)}catch(o){r=o}throw r},ar=async e=>{if(e={wait:!1,background:!1,newInstance:!1,allowNonzeroExitCode:!1,...e},Array.isArray(e.app))return qu(e.app,u=>ar({...e,app:u}));let{name:t,arguments:r=[]}=e.app||{};if(r=[...r],Array.isArray(t))return qu(t,u=>ar({...e,app:{name:u,arguments:r}}));let n,o=[],i={};if(Ue==="darwin")n="open",e.wait&&o.push("--wait-apps"),e.background&&o.push("--background"),e.newInstance&&o.push("--new"),t&&o.push("-a",t);else if(Ue==="win32"||sr&&!gm()&&!t){let u=await Em();n=sr?`${u}c/Windows/System32/WindowsPowerShell/v1.0/powershell.exe`:`${process.env.SYSTEMROOT}\\System32\\WindowsPowerShell\\v1.0\\powershell`,o.push("-NoProfile","-NonInteractive","\u2013ExecutionPolicy","Bypass","-EncodedCommand"),sr||(i.windowsVerbatimArguments=!0);let a=["Start"];e.wait&&a.push("-Wait"),t?(a.push(`"\`"${t}\`""`,"-ArgumentList"),e.target&&r.unshift(e.target)):e.target&&a.push(`"${e.target}"`),r.length>0&&(r=r.map(c=>`"\`"${c}\`""`),a.push(r.join(","))),e.target=Buffer.from(a.join(" "),"utf16le").toString("base64")}else{if(t)n=t;else{let u=!__dirname||__dirname==="/",a=!1;try{await ur.access(Wu,Hu.X_OK),a=!0}catch{}n=process.versions.electron||Ue==="android"||u||!a?"xdg-open":Wu}r.length>0&&o.push(...r),e.wait||(i.stdio="ignore",i.detached=!0)}e.target&&o.push(e.target),Ue==="darwin"&&r.length>0&&o.push("--args",...r);let s=mm.spawn(n,o,i);return e.wait?new Promise((u,a)=>{s.once("error",a),s.once("close",c=>{if(!e.allowNonzeroExitCode&&c>0){a(new Error(`Exited with code ${c}`));return}u(s)})}):(s.unref(),s)},wo=(e,t)=>{if(typeof e!="string")throw new TypeError("Expected a `target`");return ar({...t,target:e})},Cm=(e,t)=>{if(typeof e!="string")throw new TypeError("Expected a `name`");let{arguments:r=[]}=t||{};if(r!=null&&!Array.isArray(r))throw new TypeError("Expected `appArguments` as Array type");return ar({...t,app:{name:e,arguments:r}})};function Vu(e){if(typeof e=="string"||Array.isArray(e))return e;let{[Yu]:t}=e;if(!t)throw new Error(`${Yu} is not supported`);return t}function xo({[Ue]:e},{wsl:t}){if(t&&sr)return Vu(t);if(!e)throw new Error(`${Ue} is not supported`);return Vu(e)}var cr={};bo(cr,"chrome",()=>xo({darwin:"google chrome",win32:"chrome",linux:["google-chrome","google-chrome-stable","chromium"]},{wsl:{ia32:"/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe",x64:["/mnt/c/Program Files/Google/Chrome/Application/chrome.exe","/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe"]}}));bo(cr,"firefox",()=>xo({darwin:"firefox",win32:"C:\\Program Files\\Mozilla Firefox\\firefox.exe",linux:"firefox"},{wsl:"/mnt/c/Program Files/Mozilla Firefox/firefox.exe"}));bo(cr,"edge",()=>xo({darwin:"microsoft edge",win32:"msedge",linux:["microsoft-edge","microsoft-edge-dev"]},{wsl:"/mnt/c/Program Files (x86)/Microsoft/Edge/Application/msedge.exe"}));wo.apps=cr;wo.openApp=Cm;Ku.exports=wo});var ka=h((S3,Pa)=>{Pa.exports=Ma;Ma.sync=mh;var Ra=require("fs");function dh(e,t){var r=t.pathExt!==void 0?t.pathExt:process.env.PATHEXT;if(!r||(r=r.split(";"),r.indexOf("")!==-1))return!0;for(var n=0;n<r.length;n++){var o=r[n].toLowerCase();if(o&&e.substr(-o.length).toLowerCase()===o)return!0}return!1}function Ia(e,t,r){return!e.isSymbolicLink()&&!e.isFile()?!1:dh(t,r)}function Ma(e,t,r){Ra.stat(e,function(n,o){r(n,n?!1:Ia(o,e,t))})}function mh(e,t){return Ia(Ra.statSync(e),e,t)}});var Ua=h((A3,ja)=>{ja.exports=Na;Na.sync=hh;var La=require("fs");function Na(e,t,r){La.stat(e,function(n,o){r(n,n?!1:$a(o,t))})}function hh(e,t){return $a(La.statSync(e),t)}function $a(e,t){return e.isFile()&&Fh(e,t)}function Fh(e,t){var r=e.mode,n=e.uid,o=e.gid,i=t.uid!==void 0?t.uid:process.getuid&&process.getuid(),s=t.gid!==void 0?t.gid:process.getgid&&process.getgid(),u=parseInt("100",8),a=parseInt("010",8),c=parseInt("001",8),l=u|a,D=r&c||r&a&&o===s||r&u&&n===i||r&l&&i===0;return D}});var za=h((T3,Ga)=>{var B3=require("fs"),xr;process.platform==="win32"||global.TESTING_WINDOWS?xr=ka():xr=Ua();Ga.exports=Mo;Mo.sync=gh;function Mo(e,t,r){if(typeof t=="function"&&(r=t,t={}),!r){if(typeof Promise!="function")throw new TypeError("callback not provided");return new Promise(function(n,o){Mo(e,t||{},function(i,s){i?o(i):n(s)})})}xr(e,t||{},function(n,o){n&&(n.code==="EACCES"||t&&t.ignoreErrors)&&(n=null,o=!1),r(n,o)})}function gh(e,t){try{return xr.sync(e,t||{})}catch(r){if(t&&t.ignoreErrors||r.code==="EACCES")return!1;throw r}}});var Ja=h((_3,Ka)=>{var Ye=process.platform==="win32"||process.env.OSTYPE==="cygwin"||process.env.OSTYPE==="msys",Wa=require("path"),Eh=Ye?";":":",Ya=za(),qa=e=>Object.assign(new Error(`not found: ${e}`),{code:"ENOENT"}),Va=(e,t)=>{let r=t.colon||Eh,n=e.match(/\//)||Ye&&e.match(/\\/)?[""]:[...Ye?[process.cwd()]:[],...(t.path||process.env.PATH||"").split(r)],o=Ye?t.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM":"",i=Ye?o.split(r):[""];return Ye&&e.indexOf(".")!==-1&&i[0]!==""&&i.unshift(""),{pathEnv:n,pathExt:i,pathExtExe:o}},Ha=(e,t,r)=>{typeof t=="function"&&(r=t,t={}),t||(t={});let{pathEnv:n,pathExt:o,pathExtExe:i}=Va(e,t),s=[],u=c=>new Promise((l,D)=>{if(c===n.length)return t.all&&s.length?l(s):D(qa(e));let f=n[c],p=/^".*"$/.test(f)?f.slice(1,-1):f,m=Wa.join(p,e),y=!p&&/^\.[\\\/]/.test(e)?e.slice(0,2)+m:m;l(a(y,c,0))}),a=(c,l,D)=>new Promise((f,p)=>{if(D===o.length)return f(u(l+1));let m=o[D];Ya(c+m,{pathExt:i},(y,v)=>{if(!y&&v)if(t.all)s.push(c+m);else return f(c+m);return f(a(c,l,D+1))})});return r?u(0).then(c=>r(null,c),r):u(0)},Ch=(e,t)=>{t=t||{};let{pathEnv:r,pathExt:n,pathExtExe:o}=Va(e,t),i=[];for(let s=0;s<r.length;s++){let u=r[s],a=/^".*"$/.test(u)?u.slice(1,-1):u,c=Wa.join(a,e),l=!a&&/^\.[\\\/]/.test(e)?e.slice(0,2)+c:c;for(let D=0;D<n.length;D++){let f=l+n[D];try{if(Ya.sync(f,{pathExt:o}))if(t.all)i.push(f);else return f}catch{}}}if(t.all&&i.length)return i;if(t.nothrow)return null;throw qa(e)};Ka.exports=Ha;Ha.sync=Ch});var Za=h((O3,Po)=>{"use strict";var Xa=(e={})=>{let t=e.env||process.env;return(e.platform||process.platform)!=="win32"?"PATH":Object.keys(t).reverse().find(n=>n.toUpperCase()==="PATH")||"Path"};Po.exports=Xa;Po.exports.default=Xa});var rc=h((v3,tc)=>{"use strict";var Qa=require("path"),yh=Ja(),bh=Za();function ec(e,t){let r=e.options.env||process.env,n=process.cwd(),o=e.options.cwd!=null,i=o&&process.chdir!==void 0&&!process.chdir.disabled;if(i)try{process.chdir(e.options.cwd)}catch{}let s;try{s=yh.sync(e.command,{path:r[bh({env:r})],pathExt:t?Qa.delimiter:void 0})}catch{}finally{i&&process.chdir(n)}return s&&(s=Qa.resolve(o?e.options.cwd:"",s)),s}function wh(e){return ec(e)||ec(e,!0)}tc.exports=wh});var nc=h((R3,Lo)=>{"use strict";var ko=/([()\][%!^"`<>&|;, *?])/g;function xh(e){return e=e.replace(ko,"^$1"),e}function Sh(e,t){return e=`${e}`,e=e.replace(/(?=(\\+?)?)\1"/g,'$1$1\\"'),e=e.replace(/(?=(\\+?)?)\1$/,"$1$1"),e=`"${e}"`,e=e.replace(ko,"^$1"),t&&(e=e.replace(ko,"^$1")),e}Lo.exports.command=xh;Lo.exports.argument=Sh});var ic=h((I3,oc)=>{"use strict";oc.exports=/^#!(.*)/});var uc=h((M3,sc)=>{"use strict";var Ah=ic();sc.exports=(e="")=>{let t=e.match(Ah);if(!t)return null;let[r,n]=t[0].replace(/#! ?/,"").split(" "),o=r.split("/").pop();return o==="env"?n:n?`${o} ${n}`:o}});var cc=h((P3,ac)=>{"use strict";var No=require("fs"),Bh=uc();function Th(e){let r=Buffer.alloc(150),n;try{n=No.openSync(e,"r"),No.readSync(n,r,0,150,0),No.closeSync(n)}catch{}return Bh(r.toString())}ac.exports=Th});var pc=h((k3,fc)=>{"use strict";var _h=require("path"),lc=rc(),Dc=nc(),Oh=cc(),vh=process.platform==="win32",Rh=/\.(?:com|exe)$/i,Ih=/node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;function Mh(e){e.file=lc(e);let t=e.file&&Oh(e.file);return t?(e.args.unshift(e.file),e.command=t,lc(e)):e.file}function Ph(e){if(!vh)return e;let t=Mh(e),r=!Rh.test(t);if(e.options.forceShell||r){let n=Ih.test(t);e.command=_h.normalize(e.command),e.command=Dc.command(e.command),e.args=e.args.map(i=>Dc.argument(i,n));let o=[e.command].concat(e.args).join(" ");e.args=["/d","/s","/c",`"${o}"`],e.command=process.env.comspec||"cmd.exe",e.options.windowsVerbatimArguments=!0}return e}function kh(e,t,r){t&&!Array.isArray(t)&&(r=t,t=null),t=t?t.slice(0):[],r=Object.assign({},r);let n={command:e,args:t,options:r,file:void 0,original:{command:e,args:t}};return r.shell?n:Ph(n)}fc.exports=kh});var hc=h((L3,mc)=>{"use strict";var $o=process.platform==="win32";function jo(e,t){return Object.assign(new Error(`${t} ${e.command} ENOENT`),{code:"ENOENT",errno:"ENOENT",syscall:`${t} ${e.command}`,path:e.command,spawnargs:e.args})}function Lh(e,t){if(!$o)return;let r=e.emit;e.emit=function(n,o){if(n==="exit"){let i=dc(o,t);if(i)return r.call(e,"error",i)}return r.apply(e,arguments)}}function dc(e,t){return $o&&e===1&&!t.file?jo(t.original,"spawn"):null}function Nh(e,t){return $o&&e===1&&!t.file?jo(t.original,"spawnSync"):null}mc.exports={hookChildProcess:Lh,verifyENOENT:dc,verifyENOENTSync:Nh,notFoundError:jo}});var Ec=h((N3,qe)=>{"use strict";var Fc=require("child_process"),Uo=pc(),Go=hc();function gc(e,t,r){let n=Uo(e,t,r),o=Fc.spawn(n.command,n.args,n.options);return Go.hookChildProcess(o,n),o}function $h(e,t,r){let n=Uo(e,t,r),o=Fc.spawnSync(n.command,n.args,n.options);return o.error=o.error||Go.verifyENOENTSync(o.status,n),o}qe.exports=gc;qe.exports.spawn=gc;qe.exports.sync=$h;qe.exports._parse=Uo;qe.exports._enoent=Go});var Kp=h((IT,Hp)=>{var os=class{constructor(t,r,n){this.etaBufferLength=t||100,this.valueBuffer=[n],this.timeBuffer=[r],this.eta="0"}update(t,r,n){this.valueBuffer.push(r),this.timeBuffer.push(t),this.calculate(n-r)}getTime(){return this.eta}calculate(t){let r=this.valueBuffer.length,n=Math.min(this.etaBufferLength,r),o=this.valueBuffer[r-1]-this.valueBuffer[r-n],i=this.timeBuffer[r-1]-this.timeBuffer[r-n],s=o/i;this.valueBuffer=this.valueBuffer.slice(-this.etaBufferLength),this.timeBuffer=this.timeBuffer.slice(-this.etaBufferLength);let u=Math.ceil(t/s/1e3);isNaN(u)?this.eta="NULL":isFinite(u)?u>1e7?this.eta="INF":u<0?this.eta=0:this.eta=u:this.eta="INF"}};Hp.exports=os});var ss=h((MT,Jp)=>{var Oe=require("readline"),is=class{constructor(t){this.stream=t,this.linewrap=!0,this.dy=0}cursorSave(){this.stream.isTTY&&this.stream.write("\x1B7")}cursorRestore(){this.stream.isTTY&&this.stream.write("\x1B8")}cursor(t){this.stream.isTTY&&(t?this.stream.write("\x1B[?25h"):this.stream.write("\x1B[?25l"))}cursorTo(t=null,r=null){this.stream.isTTY&&Oe.cursorTo(this.stream,t,r)}cursorRelative(t=null,r=null){this.stream.isTTY&&(this.dy=this.dy+r,Oe.moveCursor(this.stream,t,r))}cursorRelativeReset(){this.stream.isTTY&&(Oe.moveCursor(this.stream,0,-this.dy),Oe.cursorTo(this.stream,0,null),this.dy=0)}clearRight(){this.stream.isTTY&&Oe.clearLine(this.stream,1)}clearLine(){this.stream.isTTY&&Oe.clearLine(this.stream,0)}clearBottom(){this.stream.isTTY&&Oe.clearScreenDown(this.stream)}newline(){this.stream.write(`
`),this.dy++}write(t,r=!1){this.linewrap===!0&&r===!1?this.stream.write(t.substr(0,this.getWidth())):this.stream.write(t)}lineWrapping(t){this.stream.isTTY&&(this.linewrap=t,t?this.stream.write("\x1B[?7h"):this.stream.write("\x1B[?7l"))}isTTY(){return this.stream.isTTY===!0}getWidth(){return this.stream.columns||(this.stream.isTTY?80:200)}};Jp.exports=is});var Zp=h((PT,Xp)=>{"use strict";Xp.exports=({onlyFirst:e=!1}={})=>{let t=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(t,e?void 0:"g")}});var ed=h((kT,Qp)=>{"use strict";var MC=Zp();Qp.exports=e=>typeof e=="string"?e.replace(MC(),""):e});var rd=h((LT,us)=>{"use strict";var td=e=>Number.isNaN(e)?!1:e>=4352&&(e<=4447||e===9001||e===9002||11904<=e&&e<=12871&&e!==12351||12880<=e&&e<=19903||19968<=e&&e<=42182||43360<=e&&e<=43388||44032<=e&&e<=55203||63744<=e&&e<=64255||65040<=e&&e<=65049||65072<=e&&e<=65131||65281<=e&&e<=65376||65504<=e&&e<=65510||110592<=e&&e<=110593||127488<=e&&e<=127569||131072<=e&&e<=262141);us.exports=td;us.exports.default=td});var od=h((NT,nd)=>{"use strict";nd.exports=function(){return/\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62(?:\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74|\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F|\uD83D\uDC68(?:\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68\uD83C\uDFFB|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFE])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83D\uDC68|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D[\uDC66\uDC67])|[\u2695\u2696\u2708]\uFE0F|\uD83D[\uDC66\uDC67]|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|(?:\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708])\uFE0F|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C[\uDFFB-\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFB\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)\uD83C\uDFFB|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])|\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1)|(?:\uD83E\uDDD1\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFE])|(?:\uD83E\uDDD1\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)(?:\uD83C[\uDFFB\uDFFC])|\uD83D\uDC69(?:\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFC-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|(?:\uD83E\uDDD1\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)(?:\uD83C[\uDFFB-\uDFFD])|\uD83D\uDC69\u200D\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D\uDC41\uFE0F\u200D\uD83D\uDDE8|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|(?:(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)\uFE0F|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD6-\uDDDD])(?:(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\u200D[\u2640\u2642])|\uD83C\uDFF4\u200D\u2620)\uFE0F|\uD83D\uDC69\u200D\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C\uDFF3\uFE0F\u200D\uD83C\uDF08|\uD83D\uDC15\u200D\uD83E\uDDBA|\uD83D\uDC69\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC67|\uD83C\uDDFD\uD83C\uDDF0|\uD83C\uDDF4\uD83C\uDDF2|\uD83C\uDDF6\uD83C\uDDE6|[#\*0-9]\uFE0F\u20E3|\uD83C\uDDE7(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF])|\uD83C\uDDF9(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF])|\uD83C\uDDEA(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA])|\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])|\uD83C\uDDF7(?:\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC])|\uD83D\uDC69(?:\uD83C[\uDFFB-\uDFFF])|\uD83C\uDDF2(?:\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF])|\uD83C\uDDE6(?:\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF])|\uD83C\uDDF0(?:\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF])|\uD83C\uDDED(?:\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA])|\uD83C\uDDE9(?:\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF])|\uD83C\uDDFE(?:\uD83C[\uDDEA\uDDF9])|\uD83C\uDDEC(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE])|\uD83C\uDDF8(?:\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF])|\uD83C\uDDEB(?:\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7])|\uD83C\uDDF5(?:\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE])|\uD83C\uDDFB(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA])|\uD83C\uDDF3(?:\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF])|\uD83C\uDDE8(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF])|\uD83C\uDDF1(?:\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE])|\uD83C\uDDFF(?:\uD83C[\uDDE6\uDDF2\uDDFC])|\uD83C\uDDFC(?:\uD83C[\uDDEB\uDDF8])|\uD83C\uDDFA(?:\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF])|\uD83C\uDDEE(?:\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9])|\uD83C\uDDEF(?:\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5])|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u270A-\u270D]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC70\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDCAA\uDD74\uDD7A\uDD90\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD36\uDDB5\uDDB6\uDDBB\uDDD2-\uDDD5])(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u270A\u270B\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF93\uDFA0-\uDFCA\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF4\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC3E\uDC40\uDC42-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDD7A\uDD95\uDD96\uDDA4\uDDFB-\uDE4F\uDE80-\uDEC5\uDECC\uDED0-\uDED2\uDED5\uDEEB\uDEEC\uDEF4-\uDEFA\uDFE0-\uDFEB]|\uD83E[\uDD0D-\uDD3A\uDD3C-\uDD45\uDD47-\uDD71\uDD73-\uDD76\uDD7A-\uDDA2\uDDA5-\uDDAA\uDDAE-\uDDCA\uDDCD-\uDDFF\uDE70-\uDE73\uDE78-\uDE7A\uDE80-\uDE82\uDE90-\uDE95])|(?:[#\*0-9\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u261D\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692-\u2697\u2699\u269B\u269C\u26A0\u26A1\u26AA\u26AB\u26B0\u26B1\u26BD\u26BE\u26C4\u26C5\u26C8\u26CE\u26CF\u26D1\u26D3\u26D4\u26E9\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763\u2764\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDCCF\uDD70\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE02\uDE1A\uDE2F\uDE32-\uDE3A\uDE50\uDE51\uDF00-\uDF21\uDF24-\uDF93\uDF96\uDF97\uDF99-\uDF9B\uDF9E-\uDFF0\uDFF3-\uDFF5\uDFF7-\uDFFF]|\uD83D[\uDC00-\uDCFD\uDCFF-\uDD3D\uDD49-\uDD4E\uDD50-\uDD67\uDD6F\uDD70\uDD73-\uDD7A\uDD87\uDD8A-\uDD8D\uDD90\uDD95\uDD96\uDDA4\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA-\uDE4F\uDE80-\uDEC5\uDECB-\uDED2\uDED5\uDEE0-\uDEE5\uDEE9\uDEEB\uDEEC\uDEF0\uDEF3-\uDEFA\uDFE0-\uDFEB]|\uD83E[\uDD0D-\uDD3A\uDD3C-\uDD45\uDD47-\uDD71\uDD73-\uDD76\uDD7A-\uDDA2\uDDA5-\uDDAA\uDDAE-\uDDCA\uDDCD-\uDDFF\uDE70-\uDE73\uDE78-\uDE7A\uDE80-\uDE82\uDE90-\uDE95])\uFE0F|(?:[\u261D\u26F9\u270A-\u270D]|\uD83C[\uDF85\uDFC2-\uDFC4\uDFC7\uDFCA-\uDFCC]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66-\uDC78\uDC7C\uDC81-\uDC83\uDC85-\uDC87\uDC8F\uDC91\uDCAA\uDD74\uDD75\uDD7A\uDD90\uDD95\uDD96\uDE45-\uDE47\uDE4B-\uDE4F\uDEA3\uDEB4-\uDEB6\uDEC0\uDECC]|\uD83E[\uDD0F\uDD18-\uDD1F\uDD26\uDD30-\uDD39\uDD3C-\uDD3E\uDDB5\uDDB6\uDDB8\uDDB9\uDDBB\uDDCD-\uDDCF\uDDD1-\uDDDD])/g}});var sd=h(($T,as)=>{"use strict";var PC=ed(),kC=rd(),LC=od(),id=e=>{if(typeof e!="string"||e.length===0||(e=PC(e),e.length===0))return 0;e=e.replace(LC(),"  ");let t=0;for(let r=0;r<e.length;r++){let n=e.codePointAt(r);n<=31||n>=127&&n<=159||n>=768&&n<=879||(n>65535&&r++,t+=kC(n)?2:1)}return t};as.exports=id;as.exports.default=id});var cs=h((jT,ud)=>{ud.exports=function(t,r,n){if(r.autopadding!==!0)return t;function o(i,s){return(r.autopaddingChar+i).slice(-s)}switch(n){case"percentage":return o(t,3);default:return t}}});var ls=h((UT,ad)=>{ad.exports=function(t,r){let n=Math.round(t*r.barsize),o=r.barsize-n;return r.barCompleteString.substr(0,n)+r.barGlue+r.barIncompleteString.substr(0,o)}});var Ds=h((GT,cd)=>{cd.exports=function(t,r,n){function o(s){return n?n*Math.round(s/n):s}function i(s){return(r.autopaddingChar+s).slice(-2)}return t>3600?i(Math.floor(t/3600))+"h"+i(o(t%3600/60))+"m":t>60?i(Math.floor(t/60))+"m"+i(o(t%60))+"s":t>10?i(o(t))+"s":i(t)+"s"}});var fs=h((zT,ld)=>{var NC=sd(),$C=cs(),jC=ls(),UC=Ds();ld.exports=function(t,r,n){let o=t.format,i=t.formatTime||UC,s=t.formatValue||$C,u=t.formatBar||jC,a=Math.floor(r.progress*100)+"",c=r.stopTime||Date.now(),l=Math.round((c-r.startTime)/1e3),D=Object.assign({},n,{bar:u(r.progress,t),percentage:s(a,t,"percentage"),total:s(r.total,t,"total"),value:s(r.value,t,"value"),eta:s(r.eta,t,"eta"),eta_formatted:i(r.eta,t,5),duration:s(l,t,"duration"),duration_formatted:i(l,t,1)});o=o.replace(/\{(\w+)\}/g,function(m,y){return typeof D[y]<"u"?D[y]:m});let f=Math.max(0,r.maxWidth-NC(o)-2),p=Math.floor(f/2);switch(t.align){case"right":o=f>0?" ".repeat(f)+o:o;break;case"center":o=p>0?" ".repeat(p)+o:o;break;case"left":default:break}return o}});var Tn=h((WT,Dd)=>{function b(e,t){return typeof e>"u"||e===null?t:e}Dd.exports={parse:function(t,r){let n={},o=Object.assign({},r,t);return n.throttleTime=1e3/b(o.fps,10),n.stream=b(o.stream,process.stderr),n.terminal=b(o.terminal,null),n.clearOnComplete=b(o.clearOnComplete,!1),n.stopOnComplete=b(o.stopOnComplete,!1),n.barsize=b(o.barsize,40),n.align=b(o.align,"left"),n.hideCursor=b(o.hideCursor,!1),n.linewrap=b(o.linewrap,!1),n.barGlue=b(o.barGlue,""),n.barCompleteChar=b(o.barCompleteChar,"="),n.barIncompleteChar=b(o.barIncompleteChar,"-"),n.format=b(o.format,"progress [{bar}] {percentage}% | ETA: {eta}s | {value}/{total}"),n.formatTime=b(o.formatTime,null),n.formatValue=b(o.formatValue,null),n.formatBar=b(o.formatBar,null),n.etaBufferLength=b(o.etaBuffer,10),n.etaAsynchronousUpdate=b(o.etaAsynchronousUpdate,!1),n.progressCalculationRelative=b(o.progressCalculationRelative,!1),n.synchronousUpdate=b(o.synchronousUpdate,!0),n.noTTYOutput=b(o.noTTYOutput,!1),n.notTTYSchedule=b(o.notTTYSchedule,2e3),n.emptyOnZero=b(o.emptyOnZero,!1),n.forceRedraw=b(o.forceRedraw,!1),n.autopadding=b(o.autopadding,!1),n.gracefulExit=b(o.gracefulExit,!1),n},assignDerivedOptions:function(t){return t.barCompleteString=t.barCompleteChar.repeat(t.barsize+1),t.barIncompleteString=t.barIncompleteChar.repeat(t.barsize+1),t.autopaddingChar=t.autopadding?b(t.autopaddingChar,"   "):"",t}}});var ps=h((qT,pd)=>{var fd=Kp(),GC=ss(),zC=fs(),WC=Tn(),YC=require("events");pd.exports=class extends YC{constructor(t){super(),this.options=WC.assignDerivedOptions(t),this.terminal=this.options.terminal?this.options.terminal:new GC(this.options.stream),this.value=0,this.startValue=0,this.total=100,this.lastDrawnString=null,this.startTime=null,this.stopTime=null,this.lastRedraw=Date.now(),this.eta=new fd(this.options.etaBufferLength,0,0),this.payload={},this.isActive=!1,this.formatter=typeof this.options.format=="function"?this.options.format:zC}render(t=!1){let r={progress:this.getProgress(),eta:this.eta.getTime(),startTime:this.startTime,stopTime:this.stopTime,total:this.total,value:this.value,maxWidth:this.terminal.getWidth()};this.options.etaAsynchronousUpdate&&this.updateETA();let n=this.formatter(this.options,r,this.payload);(t||this.options.forceRedraw||this.options.noTTYOutput&&!this.terminal.isTTY()||this.lastDrawnString!=n)&&(this.emit("redraw-pre"),this.terminal.cursorTo(0,null),this.terminal.write(n),this.terminal.clearRight(),this.lastDrawnString=n,this.lastRedraw=Date.now(),this.emit("redraw-post"))}start(t,r,n){this.value=r||0,this.total=typeof t<"u"&&t>=0?t:100,this.startValue=r||0,this.payload=n||{},this.startTime=Date.now(),this.stopTime=null,this.lastDrawnString="",this.eta=new fd(this.options.etaBufferLength,this.startTime,this.value),this.isActive=!0,this.emit("start",t,r)}stop(){this.isActive=!1,this.stopTime=Date.now(),this.emit("stop",this.total,this.value)}update(t,r={}){typeof t=="number"&&(this.value=t,this.eta.update(Date.now(),t,this.total));let n=(typeof t=="object"?t:r)||{};this.emit("update",this.total,this.value);for(let o in n)this.payload[o]=n[o];this.value>=this.getTotal()&&this.options.stopOnComplete&&this.stop()}getProgress(){let t=this.value/this.total;return this.options.progressCalculationRelative&&(t=(this.value-this.startValue)/(this.total-this.startValue)),isNaN(t)&&(t=this.options&&this.options.emptyOnZero?0:1),t=Math.min(Math.max(t,0),1),t}increment(t=1,r={}){typeof t=="object"?this.update(this.value+1,t):this.update(this.value+t,r)}getTotal(){return this.total}setTotal(t){typeof t<"u"&&t>=0&&(this.total=t)}updateETA(){this.eta.update(Date.now(),this.value,this.total)}}});var md=h((HT,dd)=>{var qC=ps(),VC=Tn();dd.exports=class extends qC{constructor(t,r){super(VC.parse(t,r)),this.timer=null,this.options.noTTYOutput&&this.terminal.isTTY()===!1&&(this.options.synchronousUpdate=!1),this.schedulingRate=this.terminal.isTTY()?this.options.throttleTime:this.options.notTTYSchedule,this.sigintCallback=null}render(){this.timer&&(clearTimeout(this.timer),this.timer=null),super.render(),this.options.noTTYOutput&&this.terminal.isTTY()===!1&&this.terminal.newline(),this.timer=setTimeout(this.render.bind(this),this.schedulingRate)}update(t,r){this.timer&&(super.update(t,r),this.options.synchronousUpdate&&this.lastRedraw+this.options.throttleTime*2<Date.now()&&this.render())}start(t,r,n){this.options.noTTYOutput===!1&&this.terminal.isTTY()===!1||(this.sigintCallback===null&&this.options.gracefulExit&&(this.sigintCallback=this.stop.bind(this),process.once("SIGINT",this.sigintCallback),process.once("SIGTERM",this.sigintCallback)),this.terminal.cursorSave(),this.options.hideCursor===!0&&this.terminal.cursor(!1),this.options.linewrap===!1&&this.terminal.lineWrapping(!1),super.start(t,r,n),this.render())}stop(){this.timer&&(this.sigintCallback&&(process.removeListener("SIGINT",this.sigintCallback),process.removeListener("SIGTERM",this.sigintCallback),this.sigintCallback=null),this.render(),super.stop(),clearTimeout(this.timer),this.timer=null,this.options.hideCursor===!0&&this.terminal.cursor(!0),this.options.linewrap===!1&&this.terminal.lineWrapping(!0),this.terminal.cursorRestore(),this.options.clearOnComplete?(this.terminal.cursorTo(0,null),this.terminal.clearLine()):this.terminal.newline())}}});var Fd=h((JT,hd)=>{var HC=ss(),KC=ps(),JC=Tn(),XC=require("events");hd.exports=class extends XC{constructor(t,r){super(),this.bars=[],this.options=JC.parse(t,r),this.options.synchronousUpdate=!1,this.terminal=this.options.terminal?this.options.terminal:new HC(this.options.stream),this.timer=null,this.isActive=!1,this.schedulingRate=this.terminal.isTTY()?this.options.throttleTime:this.options.notTTYSchedule,this.loggingBuffer=[],this.sigintCallback=null}create(t,r,n,o={}){let i=new KC(Object.assign({},this.options,{terminal:this.terminal},o));return this.bars.push(i),this.options.noTTYOutput===!1&&this.terminal.isTTY()===!1||(this.sigintCallback===null&&this.options.gracefulExit&&(this.sigintCallback=this.stop.bind(this),process.once("SIGINT",this.sigintCallback),process.once("SIGTERM",this.sigintCallback)),this.isActive||(this.options.hideCursor===!0&&this.terminal.cursor(!1),this.options.linewrap===!1&&this.terminal.lineWrapping(!1),this.timer=setTimeout(this.update.bind(this),this.schedulingRate)),this.isActive=!0,i.start(t,r,n),this.emit("start")),i}remove(t){let r=this.bars.indexOf(t);return r<0?!1:(this.bars.splice(r,1),this.update(),this.terminal.newline(),this.terminal.clearBottom(),!0)}update(){if(this.timer&&(clearTimeout(this.timer),this.timer=null),this.emit("update-pre"),this.terminal.cursorRelativeReset(),this.emit("redraw-pre"),this.loggingBuffer.length>0)for(this.terminal.clearLine();this.loggingBuffer.length>0;)this.terminal.write(this.loggingBuffer.shift(),!0);for(let t=0;t<this.bars.length;t++)t>0&&this.terminal.newline(),this.bars[t].render();this.emit("redraw-post"),this.options.noTTYOutput&&this.terminal.isTTY()===!1&&(this.terminal.newline(),this.terminal.newline()),this.timer=setTimeout(this.update.bind(this),this.schedulingRate),this.emit("update-post"),this.options.stopOnComplete&&!this.bars.find(t=>t.isActive)&&this.stop()}stop(){if(clearTimeout(this.timer),this.timer=null,this.sigintCallback&&(process.removeListener("SIGINT",this.sigintCallback),process.removeListener("SIGTERM",this.sigintCallback),this.sigintCallback=null),this.isActive=!1,this.options.hideCursor===!0&&this.terminal.cursor(!0),this.options.linewrap===!1&&this.terminal.lineWrapping(!0),this.terminal.cursorRelativeReset(),this.emit("stop-pre-clear"),this.options.clearOnComplete)this.terminal.clearBottom();else{for(let t=0;t<this.bars.length;t++)t>0&&this.terminal.newline(),this.bars[t].render(),this.bars[t].stop();this.terminal.newline()}this.emit("stop")}log(t){this.loggingBuffer.push(t)}}});var Ed=h((XT,gd)=>{gd.exports={format:"progress [{bar}] {percentage}% | ETA: {eta}s | {value}/{total}",barCompleteChar:"=",barIncompleteChar:"-"}});var yd=h((ZT,Cd)=>{Cd.exports={format:" {bar} {percentage}% | ETA: {eta}s | {value}/{total}",barCompleteChar:"\u2588",barIncompleteChar:"\u2591"}});var wd=h((QT,bd)=>{bd.exports={format:" \x1B[90m{bar}\x1B[0m {percentage}% | ETA: {eta}s | {value}/{total}",barCompleteChar:"\u2588",barIncompleteChar:"\u2591"}});var Sd=h((e5,xd)=>{xd.exports={format:" {bar}\u25A0 {percentage}% | ETA: {eta}s | {value}/{total}",barCompleteChar:"\u25A0",barIncompleteChar:" "}});var Bd=h((t5,Ad)=>{var ZC=Ed(),QC=yd(),e1=wd(),t1=Sd();Ad.exports={legacy:ZC,shades_classic:QC,shades_grey:e1,rect:t1}});var Od=h((r5,_d)=>{var Td=md(),r1=Fd(),n1=Bd(),o1=fs(),i1=cs(),s1=ls(),u1=Ds();_d.exports={Bar:Td,SingleBar:Td,MultiBar:r1,Presets:n1,Format:{Formatter:o1,BarFormat:s1,ValueFormat:i1,TimeFormat:u1}}});var a1={};Ld(a1,{default:()=>_n});module.exports=Nd(a1);var vd=F(Un()),jt=require("@oclif/core");var Xu=require("@oclif/core");var lt=F(require("node:fs")),zn=F(require("node:path")),Hs=F(require("node:os"));var ct={production:{url:"https://www.raycast.com",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},staging:{url:"https://www.rayca.st",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},development:{url:"http://localhost:3000",clientID:"rOCD_LhnXFObSLu9v7788qOGu25FZDqEzGjet4Q45ZY"}}[process.env.API_ENV||"production"],c0="e69bae0ec90f5e838555",oe={},Ks;function Js(e){Ks=e;try{oe=JSON.parse(lt.readFileSync(zn.join(D0(),"config.json"),"utf8"))}catch(t){if(t instanceof Error&&t.code==="ENOENT")return;throw new Error(`Failed to read config file: ${t}`)}}function Dt(e){switch(e){case"raycastApiURL":return process.env.RAY_APIURL||oe.APIURL||ct.url;case"raycastAccessToken":return process.env.RAY_TOKEN||oe.Token||oe.AccessToken||"";case"raycastClientId":return process.env.RAY_CLIENT_ID||oe.ClientID||ct.clientID;case"githubClientId":return process.env.RAY_GithubClientID||oe.GithubClientID||c0;case"githubAccessToken":return process.env.GITHUB_ACCESS_TOKEN||oe.GithubAccessToken||"";case"flavorName":return process.env.RAY_Target?process.env.RAY_Target:typeof oe.Target<"u"?oe.Target:Gn(process.platform==="win32"?"x":"release")}}function Gn(e){switch(e){case"debug":return"debug";case"internal":return"internal";case"release":return"";case"x":return"x";case"x-internal":return"x-internal";case"x-development":return"x-development";case void 0:return Dt("flavorName")}}function l0(){let e=Gn(Ks);return e==""?"raycast":`raycast-${e}`}function D0(){let e=zn.join(Hs.default.homedir(),".config",l0());return lt.mkdirSync(e,{recursive:!0}),e}var B=F(Zs());var Ct=F(require("node:process"),1);var Qs=(e=0)=>t=>`\x1B[${t+e}m`,eu=(e=0)=>t=>`\x1B[${38+e};5;${t}m`,tu=(e=0)=>(t,r,n)=>`\x1B[${38+e};2;${t};${r};${n}m`,S={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],gray:[90,39],grey:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgGray:[100,49],bgGrey:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}},S1=Object.keys(S.modifier),d0=Object.keys(S.color),m0=Object.keys(S.bgColor),A1=[...d0,...m0];function h0(){let e=new Map;for(let[t,r]of Object.entries(S)){for(let[n,o]of Object.entries(r))S[n]={open:`\x1B[${o[0]}m`,close:`\x1B[${o[1]}m`},r[n]=S[n],e.set(o[0],o[1]);Object.defineProperty(S,t,{value:r,enumerable:!1})}return Object.defineProperty(S,"codes",{value:e,enumerable:!1}),S.color.close="\x1B[39m",S.bgColor.close="\x1B[49m",S.color.ansi=Qs(),S.color.ansi256=eu(),S.color.ansi16m=tu(),S.bgColor.ansi=Qs(10),S.bgColor.ansi256=eu(10),S.bgColor.ansi16m=tu(10),Object.defineProperties(S,{rgbToAnsi256:{value(t,r,n){return t===r&&r===n?t<8?16:t>248?231:Math.round((t-8)/247*24)+232:16+36*Math.round(t/255*5)+6*Math.round(r/255*5)+Math.round(n/255*5)},enumerable:!1},hexToRgb:{value(t){let r=/[a-f\d]{6}|[a-f\d]{3}/i.exec(t.toString(16));if(!r)return[0,0,0];let[n]=r;n.length===3&&(n=[...n].map(i=>i+i).join(""));let o=Number.parseInt(n,16);return[o>>16&255,o>>8&255,o&255]},enumerable:!1},hexToAnsi256:{value:t=>S.rgbToAnsi256(...S.hexToRgb(t)),enumerable:!1},ansi256ToAnsi:{value(t){if(t<8)return 30+t;if(t<16)return 90+(t-8);let r,n,o;if(t>=232)r=((t-232)*10+8)/255,n=r,o=r;else{t-=16;let u=t%36;r=Math.floor(t/36)/5,n=Math.floor(u/6)/5,o=u%6/5}let i=Math.max(r,n,o)*2;if(i===0)return 30;let s=30+(Math.round(o)<<2|Math.round(n)<<1|Math.round(r));return i===2&&(s+=60),s},enumerable:!1},rgbToAnsi:{value:(t,r,n)=>S.ansi256ToAnsi(S.rgbToAnsi256(t,r,n)),enumerable:!1},hexToAnsi:{value:t=>S.ansi256ToAnsi(S.hexToAnsi256(t)),enumerable:!1}}),S}var F0=h0(),W=F0;var Kt=F(require("node:process"),1),nu=F(require("node:os"),1),Wn=F(require("node:tty"),1);function $(e,t=globalThis.Deno?globalThis.Deno.args:Kt.default.argv){let r=e.startsWith("-")?"":e.length===1?"-":"--",n=t.indexOf(r+e),o=t.indexOf("--");return n!==-1&&(o===-1||n<o)}var{env:T}=Kt.default,Ht;$("no-color")||$("no-colors")||$("color=false")||$("color=never")?Ht=0:($("color")||$("colors")||$("color=true")||$("color=always"))&&(Ht=1);function g0(){if("FORCE_COLOR"in T)return T.FORCE_COLOR==="true"?1:T.FORCE_COLOR==="false"?0:T.FORCE_COLOR.length===0?1:Math.min(Number.parseInt(T.FORCE_COLOR,10),3)}function E0(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function C0(e,{streamIsTTY:t,sniffFlags:r=!0}={}){let n=g0();n!==void 0&&(Ht=n);let o=r?Ht:n;if(o===0)return 0;if(r){if($("color=16m")||$("color=full")||$("color=truecolor"))return 3;if($("color=256"))return 2}if("TF_BUILD"in T&&"AGENT_NAME"in T)return 1;if(e&&!t&&o===void 0)return 0;let i=o||0;if(T.TERM==="dumb")return i;if(Kt.default.platform==="win32"){let s=nu.default.release().split(".");return Number(s[0])>=10&&Number(s[2])>=10586?Number(s[2])>=14931?3:2:1}if("CI"in T)return"GITHUB_ACTIONS"in T||"GITEA_ACTIONS"in T?3:["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","BUILDKITE","DRONE"].some(s=>s in T)||T.CI_NAME==="codeship"?1:i;if("TEAMCITY_VERSION"in T)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(T.TEAMCITY_VERSION)?1:0;if(T.COLORTERM==="truecolor"||T.TERM==="xterm-kitty")return 3;if("TERM_PROGRAM"in T){let s=Number.parseInt((T.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(T.TERM_PROGRAM){case"iTerm.app":return s>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(T.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(T.TERM)||"COLORTERM"in T?1:i}function ru(e,t={}){let r=C0(e,{streamIsTTY:e&&e.isTTY,...t});return E0(r)}var y0={stdout:ru({isTTY:Wn.default.isatty(1)}),stderr:ru({isTTY:Wn.default.isatty(2)})},ou=y0;function iu(e,t,r){let n=e.indexOf(t);if(n===-1)return e;let o=t.length,i=0,s="";do s+=e.slice(i,n)+t+r,i=n+o,n=e.indexOf(t,i);while(n!==-1);return s+=e.slice(i),s}function su(e,t,r,n){let o=0,i="";do{let s=e[n-1]==="\r";i+=e.slice(o,s?n-1:n)+t+(s?`\r
`:`
`)+r,o=n+1,n=e.indexOf(`
`,o)}while(n!==-1);return i+=e.slice(o),i}var{stdout:uu,stderr:au}=ou,Yn=Symbol("GENERATOR"),ke=Symbol("STYLER"),ft=Symbol("IS_EMPTY"),cu=["ansi","ansi","ansi256","ansi16m"],Le=Object.create(null),b0=(e,t={})=>{if(t.level&&!(Number.isInteger(t.level)&&t.level>=0&&t.level<=3))throw new Error("The `level` option should be an integer from 0 to 3");let r=uu?uu.level:0;e.level=t.level===void 0?r:t.level};var w0=e=>{let t=(...r)=>r.join(" ");return b0(t,e),Object.setPrototypeOf(t,pt.prototype),t};function pt(e){return w0(e)}Object.setPrototypeOf(pt.prototype,Function.prototype);for(let[e,t]of Object.entries(W))Le[e]={get(){let r=Jt(this,Vn(t.open,t.close,this[ke]),this[ft]);return Object.defineProperty(this,e,{value:r}),r}};Le.visible={get(){let e=Jt(this,this[ke],!0);return Object.defineProperty(this,"visible",{value:e}),e}};var qn=(e,t,r,...n)=>e==="rgb"?t==="ansi16m"?W[r].ansi16m(...n):t==="ansi256"?W[r].ansi256(W.rgbToAnsi256(...n)):W[r].ansi(W.rgbToAnsi(...n)):e==="hex"?qn("rgb",t,r,...W.hexToRgb(...n)):W[r][e](...n),x0=["rgb","hex","ansi256"];for(let e of x0){Le[e]={get(){let{level:r}=this;return function(...n){let o=Vn(qn(e,cu[r],"color",...n),W.color.close,this[ke]);return Jt(this,o,this[ft])}}};let t="bg"+e[0].toUpperCase()+e.slice(1);Le[t]={get(){let{level:r}=this;return function(...n){let o=Vn(qn(e,cu[r],"bgColor",...n),W.bgColor.close,this[ke]);return Jt(this,o,this[ft])}}}}var S0=Object.defineProperties(()=>{},{...Le,level:{enumerable:!0,get(){return this[Yn].level},set(e){this[Yn].level=e}}}),Vn=(e,t,r)=>{let n,o;return r===void 0?(n=e,o=t):(n=r.openAll+e,o=t+r.closeAll),{open:e,close:t,openAll:n,closeAll:o,parent:r}},Jt=(e,t,r)=>{let n=(...o)=>A0(n,o.length===1?""+o[0]:o.join(" "));return Object.setPrototypeOf(n,S0),n[Yn]=e,n[ke]=t,n[ft]=r,n},A0=(e,t)=>{if(e.level<=0||!t)return e[ft]?"":t;let r=e[ke];if(r===void 0)return t;let{openAll:n,closeAll:o}=r;if(t.includes("\x1B"))for(;r!==void 0;)t=iu(t,r.close,r.open),r=r.parent;let i=t.indexOf(`
`);return i!==-1&&(t=su(t,o,n,i)),n+t+o};Object.defineProperties(pt.prototype,Le);var B0=pt(),I1=pt({level:au?au.level:0});var lu=B0;var to=F(require("node:process"),1);var dt=F(require("node:process"),1);var T0=(e,t,r,n)=>{if(r==="length"||r==="prototype"||r==="arguments"||r==="caller")return;let o=Object.getOwnPropertyDescriptor(e,r),i=Object.getOwnPropertyDescriptor(t,r);!_0(o,i)&&n||Object.defineProperty(e,r,i)},_0=function(e,t){return e===void 0||e.configurable||e.writable===t.writable&&e.enumerable===t.enumerable&&e.configurable===t.configurable&&(e.writable||e.value===t.value)},O0=(e,t)=>{let r=Object.getPrototypeOf(t);r!==Object.getPrototypeOf(e)&&Object.setPrototypeOf(e,r)},v0=(e,t)=>`/* Wrapped ${e}*/
${t}`,R0=Object.getOwnPropertyDescriptor(Function.prototype,"toString"),I0=Object.getOwnPropertyDescriptor(Function.prototype.toString,"name"),M0=(e,t,r)=>{let n=r===""?"":`with ${r.trim()}() `,o=v0.bind(null,n,t.toString());Object.defineProperty(o,"name",I0);let{writable:i,enumerable:s,configurable:u}=R0;Object.defineProperty(e,"toString",{value:o,writable:i,enumerable:s,configurable:u})};function Hn(e,t,{ignoreNonConfigurable:r=!1}={}){let{name:n}=e;for(let o of Reflect.ownKeys(t))T0(e,t,o,r);return O0(e,t),M0(e,t,n),e}var Xt=new WeakMap,Du=(e,t={})=>{if(typeof e!="function")throw new TypeError("Expected a function");let r,n=0,o=e.displayName||e.name||"<anonymous>",i=function(...s){if(Xt.set(i,++n),n===1)r=e.apply(this,s),e=void 0;else if(t.throw===!0)throw new Error(`Function \`${o}\` can only be called once`);return r};return Hn(i,e),Xt.set(i,n),i};Du.callCount=e=>{if(!Xt.has(e))throw new Error(`The given function \`${e.name}\` is not wrapped by the \`onetime\` package`);return Xt.get(e)};var fu=Du;var ye=[];ye.push("SIGHUP","SIGINT","SIGTERM");process.platform!=="win32"&&ye.push("SIGALRM","SIGABRT","SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");process.platform==="linux"&&ye.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT");var Zt=e=>!!e&&typeof e=="object"&&typeof e.removeListener=="function"&&typeof e.emit=="function"&&typeof e.reallyExit=="function"&&typeof e.listeners=="function"&&typeof e.kill=="function"&&typeof e.pid=="number"&&typeof e.on=="function",Kn=Symbol.for("signal-exit emitter"),Jn=globalThis,P0=Object.defineProperty.bind(Object),Xn=class{emitted={afterExit:!1,exit:!1};listeners={afterExit:[],exit:[]};count=0;id=Math.random();constructor(){if(Jn[Kn])return Jn[Kn];P0(Jn,Kn,{value:this,writable:!1,enumerable:!1,configurable:!1})}on(t,r){this.listeners[t].push(r)}removeListener(t,r){let n=this.listeners[t],o=n.indexOf(r);o!==-1&&(o===0&&n.length===1?n.length=0:n.splice(o,1))}emit(t,r,n){if(this.emitted[t])return!1;this.emitted[t]=!0;let o=!1;for(let i of this.listeners[t])o=i(r,n)===!0||o;return t==="exit"&&(o=this.emit("afterExit",r,n)||o),o}},Qt=class{},k0=e=>({onExit(t,r){return e.onExit(t,r)},load(){return e.load()},unload(){return e.unload()}}),Zn=class extends Qt{onExit(){return()=>{}}load(){}unload(){}},Qn=class extends Qt{#r=eo.platform==="win32"?"SIGINT":"SIGHUP";#o=new Xn;#e;#n;#u;#t={};#s=!1;constructor(t){super(),this.#e=t,this.#t={};for(let r of ye)this.#t[r]=()=>{let n=this.#e.listeners(r),{count:o}=this.#o,i=t;if(typeof i.__signal_exit_emitter__=="object"&&typeof i.__signal_exit_emitter__.count=="number"&&(o+=i.__signal_exit_emitter__.count),n.length===o){this.unload();let s=this.#o.emit("exit",null,r),u=r==="SIGHUP"?this.#r:r;s||t.kill(t.pid,u)}};this.#u=t.reallyExit,this.#n=t.emit}onExit(t,r){if(!Zt(this.#e))return()=>{};this.#s===!1&&this.load();let n=r?.alwaysLast?"afterExit":"exit";return this.#o.on(n,t),()=>{this.#o.removeListener(n,t),this.#o.listeners.exit.length===0&&this.#o.listeners.afterExit.length===0&&this.unload()}}load(){if(!this.#s){this.#s=!0,this.#o.count+=1;for(let t of ye)try{let r=this.#t[t];r&&this.#e.on(t,r)}catch{}this.#e.emit=(t,...r)=>this.#p(t,...r),this.#e.reallyExit=t=>this.#i(t)}}unload(){this.#s&&(this.#s=!1,ye.forEach(t=>{let r=this.#t[t];if(!r)throw new Error("Listener not defined for signal: "+t);try{this.#e.removeListener(t,r)}catch{}}),this.#e.emit=this.#n,this.#e.reallyExit=this.#u,this.#o.count-=1)}#i(t){return Zt(this.#e)?(this.#e.exitCode=t||0,this.#o.emit("exit",this.#e.exitCode,null),this.#u.call(this.#e,this.#e.exitCode)):0}#p(t,...r){let n=this.#n;if(t==="exit"&&Zt(this.#e)){typeof r[0]=="number"&&(this.#e.exitCode=r[0]);let o=n.call(this.#e,t,...r);return this.#o.emit("exit",this.#e.exitCode,null),o}else return n.call(this.#e,t,...r)}},eo=globalThis.process,{onExit:er,load:j1,unload:U1}=k0(Zt(eo)?new Qn(eo):new Zn);var pu=dt.default.stderr.isTTY?dt.default.stderr:dt.default.stdout.isTTY?dt.default.stdout:void 0,L0=pu?fu(()=>{er(()=>{pu.write("\x1B[?25h")},{alwaysLast:!0})}):()=>{},du=L0;var tr=!1,Ne={};Ne.show=(e=to.default.stderr)=>{e.isTTY&&(tr=!1,e.write("\x1B[?25h"))};Ne.hide=(e=to.default.stderr)=>{e.isTTY&&(du(),tr=!0,e.write("\x1B[?25l"))};Ne.toggle=(e,t)=>{e!==void 0&&(tr=e),tr?Ne.show(t):Ne.hide(t)};var ro=Ne;var yt=F(no(),1);var gu=(e=0)=>t=>`\x1B[${t+e}m`,Eu=(e=0)=>t=>`\x1B[${38+e};5;${t}m`,Cu=(e=0)=>(t,r,n)=>`\x1B[${38+e};2;${t};${r};${n}m`,A={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],gray:[90,39],grey:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgGray:[100,49],bgGrey:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}},J1=Object.keys(A.modifier),$0=Object.keys(A.color),j0=Object.keys(A.bgColor),X1=[...$0,...j0];function U0(){let e=new Map;for(let[t,r]of Object.entries(A)){for(let[n,o]of Object.entries(r))A[n]={open:`\x1B[${o[0]}m`,close:`\x1B[${o[1]}m`},r[n]=A[n],e.set(o[0],o[1]);Object.defineProperty(A,t,{value:r,enumerable:!1})}return Object.defineProperty(A,"codes",{value:e,enumerable:!1}),A.color.close="\x1B[39m",A.bgColor.close="\x1B[49m",A.color.ansi=gu(),A.color.ansi256=Eu(),A.color.ansi16m=Cu(),A.bgColor.ansi=gu(10),A.bgColor.ansi256=Eu(10),A.bgColor.ansi16m=Cu(10),Object.defineProperties(A,{rgbToAnsi256:{value(t,r,n){return t===r&&r===n?t<8?16:t>248?231:Math.round((t-8)/247*24)+232:16+36*Math.round(t/255*5)+6*Math.round(r/255*5)+Math.round(n/255*5)},enumerable:!1},hexToRgb:{value(t){let r=/[a-f\d]{6}|[a-f\d]{3}/i.exec(t.toString(16));if(!r)return[0,0,0];let[n]=r;n.length===3&&(n=[...n].map(i=>i+i).join(""));let o=Number.parseInt(n,16);return[o>>16&255,o>>8&255,o&255]},enumerable:!1},hexToAnsi256:{value:t=>A.rgbToAnsi256(...A.hexToRgb(t)),enumerable:!1},ansi256ToAnsi:{value(t){if(t<8)return 30+t;if(t<16)return 90+(t-8);let r,n,o;if(t>=232)r=((t-232)*10+8)/255,n=r,o=r;else{t-=16;let u=t%36;r=Math.floor(t/36)/5,n=Math.floor(u/6)/5,o=u%6/5}let i=Math.max(r,n,o)*2;if(i===0)return 30;let s=30+(Math.round(o)<<2|Math.round(n)<<1|Math.round(r));return i===2&&(s+=60),s},enumerable:!1},rgbToAnsi:{value:(t,r,n)=>A.ansi256ToAnsi(A.rgbToAnsi256(t,r,n)),enumerable:!1},hexToAnsi:{value:t=>A.ansi256ToAnsi(A.hexToAnsi256(t)),enumerable:!1}}),A}var G0=U0(),Y=G0;var or=F(require("node:process"),1),bu=F(require("node:os"),1),oo=F(require("node:tty"),1);function j(e,t=globalThis.Deno?globalThis.Deno.args:or.default.argv){let r=e.startsWith("-")?"":e.length===1?"-":"--",n=t.indexOf(r+e),o=t.indexOf("--");return n!==-1&&(o===-1||n<o)}var{env:_}=or.default,nr;j("no-color")||j("no-colors")||j("color=false")||j("color=never")?nr=0:(j("color")||j("colors")||j("color=true")||j("color=always"))&&(nr=1);function z0(){if("FORCE_COLOR"in _)return _.FORCE_COLOR==="true"?1:_.FORCE_COLOR==="false"?0:_.FORCE_COLOR.length===0?1:Math.min(Number.parseInt(_.FORCE_COLOR,10),3)}function W0(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function Y0(e,{streamIsTTY:t,sniffFlags:r=!0}={}){let n=z0();n!==void 0&&(nr=n);let o=r?nr:n;if(o===0)return 0;if(r){if(j("color=16m")||j("color=full")||j("color=truecolor"))return 3;if(j("color=256"))return 2}if("TF_BUILD"in _&&"AGENT_NAME"in _)return 1;if(e&&!t&&o===void 0)return 0;let i=o||0;if(_.TERM==="dumb")return i;if(or.default.platform==="win32"){let s=bu.default.release().split(".");return Number(s[0])>=10&&Number(s[2])>=10586?Number(s[2])>=14931?3:2:1}if("CI"in _)return"GITHUB_ACTIONS"in _||"GITEA_ACTIONS"in _?3:["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","BUILDKITE","DRONE"].some(s=>s in _)||_.CI_NAME==="codeship"?1:i;if("TEAMCITY_VERSION"in _)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(_.TEAMCITY_VERSION)?1:0;if(_.COLORTERM==="truecolor"||_.TERM==="xterm-kitty")return 3;if("TERM_PROGRAM"in _){let s=Number.parseInt((_.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(_.TERM_PROGRAM){case"iTerm.app":return s>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(_.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(_.TERM)||"COLORTERM"in _?1:i}function yu(e,t={}){let r=Y0(e,{streamIsTTY:e&&e.isTTY,...t});return W0(r)}var q0={stdout:yu({isTTY:oo.default.isatty(1)}),stderr:yu({isTTY:oo.default.isatty(2)})},wu=q0;function xu(e,t,r){let n=e.indexOf(t);if(n===-1)return e;let o=t.length,i=0,s="";do s+=e.slice(i,n)+t+r,i=n+o,n=e.indexOf(t,i);while(n!==-1);return s+=e.slice(i),s}function Su(e,t,r,n){let o=0,i="";do{let s=e[n-1]==="\r";i+=e.slice(o,s?n-1:n)+t+(s?`\r
`:`
`)+r,o=n+1,n=e.indexOf(`
`,o)}while(n!==-1);return i+=e.slice(o),i}var{stdout:Au,stderr:Bu}=wu,io=Symbol("GENERATOR"),$e=Symbol("STYLER"),mt=Symbol("IS_EMPTY"),Tu=["ansi","ansi","ansi256","ansi16m"],je=Object.create(null),V0=(e,t={})=>{if(t.level&&!(Number.isInteger(t.level)&&t.level>=0&&t.level<=3))throw new Error("The `level` option should be an integer from 0 to 3");let r=Au?Au.level:0;e.level=t.level===void 0?r:t.level};var H0=e=>{let t=(...r)=>r.join(" ");return V0(t,e),Object.setPrototypeOf(t,ht.prototype),t};function ht(e){return H0(e)}Object.setPrototypeOf(ht.prototype,Function.prototype);for(let[e,t]of Object.entries(Y))je[e]={get(){let r=ir(this,uo(t.open,t.close,this[$e]),this[mt]);return Object.defineProperty(this,e,{value:r}),r}};je.visible={get(){let e=ir(this,this[$e],!0);return Object.defineProperty(this,"visible",{value:e}),e}};var so=(e,t,r,...n)=>e==="rgb"?t==="ansi16m"?Y[r].ansi16m(...n):t==="ansi256"?Y[r].ansi256(Y.rgbToAnsi256(...n)):Y[r].ansi(Y.rgbToAnsi(...n)):e==="hex"?so("rgb",t,r,...Y.hexToRgb(...n)):Y[r][e](...n),K0=["rgb","hex","ansi256"];for(let e of K0){je[e]={get(){let{level:r}=this;return function(...n){let o=uo(so(e,Tu[r],"color",...n),Y.color.close,this[$e]);return ir(this,o,this[mt])}}};let t="bg"+e[0].toUpperCase()+e.slice(1);je[t]={get(){let{level:r}=this;return function(...n){let o=uo(so(e,Tu[r],"bgColor",...n),Y.bgColor.close,this[$e]);return ir(this,o,this[mt])}}}}var J0=Object.defineProperties(()=>{},{...je,level:{enumerable:!0,get(){return this[io].level},set(e){this[io].level=e}}}),uo=(e,t,r)=>{let n,o;return r===void 0?(n=e,o=t):(n=r.openAll+e,o=t+r.closeAll),{open:e,close:t,openAll:n,closeAll:o,parent:r}},ir=(e,t,r)=>{let n=(...o)=>X0(n,o.length===1?""+o[0]:o.join(" "));return Object.setPrototypeOf(n,J0),n[io]=e,n[$e]=t,n[mt]=r,n},X0=(e,t)=>{if(e.level<=0||!t)return e[mt]?"":t;let r=e[$e];if(r===void 0)return t;let{openAll:n,closeAll:o}=r;if(t.includes("\x1B"))for(;r!==void 0;)t=xu(t,r.close,r.open),r=r.parent;let i=t.indexOf(`
`);return i!==-1&&(t=Su(t,o,n,i)),n+t+o};Object.defineProperties(ht.prototype,je);var Z0=ht(),oy=ht({level:Bu?Bu.level:0});var ie=Z0;var U=F(require("node:process"),1);function ao(){return U.default.platform!=="win32"?U.default.env.TERM!=="linux":!!U.default.env.CI||!!U.default.env.WT_SESSION||!!U.default.env.TERMINUS_SUBLIME||U.default.env.ConEmuTask==="{cmd::Cmder}"||U.default.env.TERM_PROGRAM==="Terminus-Sublime"||U.default.env.TERM_PROGRAM==="vscode"||U.default.env.TERM==="xterm-256color"||U.default.env.TERM==="alacritty"||U.default.env.TERMINAL_EMULATOR==="JetBrains-JediTerm"}var Q0={info:ie.blue("\u2139"),success:ie.green("\u2714"),warning:ie.yellow("\u26A0"),error:ie.red("\u2716")},em={info:ie.blue("i"),success:ie.green("\u221A"),warning:ie.yellow("\u203C"),error:ie.red("\xD7")},tm=ao()?Q0:em,Ft=tm;function co({onlyFirst:e=!1}={}){let r=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?(?:\\u0007|\\u001B\\u005C|\\u009C))","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-nq-uy=><~]))"].join("|");return new RegExp(r,e?void 0:"g")}var rm=co();function gt(e){if(typeof e!="string")throw new TypeError(`Expected a \`string\`, got \`${typeof e}\``);return e.replace(rm,"")}function _u(e){return e===161||e===164||e===167||e===168||e===170||e===173||e===174||e>=176&&e<=180||e>=182&&e<=186||e>=188&&e<=191||e===198||e===208||e===215||e===216||e>=222&&e<=225||e===230||e>=232&&e<=234||e===236||e===237||e===240||e===242||e===243||e>=247&&e<=250||e===252||e===254||e===257||e===273||e===275||e===283||e===294||e===295||e===299||e>=305&&e<=307||e===312||e>=319&&e<=322||e===324||e>=328&&e<=331||e===333||e===338||e===339||e===358||e===359||e===363||e===462||e===464||e===466||e===468||e===470||e===472||e===474||e===476||e===593||e===609||e===708||e===711||e>=713&&e<=715||e===717||e===720||e>=728&&e<=731||e===733||e===735||e>=768&&e<=879||e>=913&&e<=929||e>=931&&e<=937||e>=945&&e<=961||e>=963&&e<=969||e===1025||e>=1040&&e<=1103||e===1105||e===8208||e>=8211&&e<=8214||e===8216||e===8217||e===8220||e===8221||e>=8224&&e<=8226||e>=8228&&e<=8231||e===8240||e===8242||e===8243||e===8245||e===8251||e===8254||e===8308||e===8319||e>=8321&&e<=8324||e===8364||e===8451||e===8453||e===8457||e===8467||e===8470||e===8481||e===8482||e===8486||e===8491||e===8531||e===8532||e>=8539&&e<=8542||e>=8544&&e<=8555||e>=8560&&e<=8569||e===8585||e>=8592&&e<=8601||e===8632||e===8633||e===8658||e===8660||e===8679||e===8704||e===8706||e===8707||e===8711||e===8712||e===8715||e===8719||e===8721||e===8725||e===8730||e>=8733&&e<=8736||e===8739||e===8741||e>=8743&&e<=8748||e===8750||e>=8756&&e<=8759||e===8764||e===8765||e===8776||e===8780||e===8786||e===8800||e===8801||e>=8804&&e<=8807||e===8810||e===8811||e===8814||e===8815||e===8834||e===8835||e===8838||e===8839||e===8853||e===8857||e===8869||e===8895||e===8978||e>=9312&&e<=9449||e>=9451&&e<=9547||e>=9552&&e<=9587||e>=9600&&e<=9615||e>=9618&&e<=9621||e===9632||e===9633||e>=9635&&e<=9641||e===9650||e===9651||e===9654||e===9655||e===9660||e===9661||e===9664||e===9665||e>=9670&&e<=9672||e===9675||e>=9678&&e<=9681||e>=9698&&e<=9701||e===9711||e===9733||e===9734||e===9737||e===9742||e===9743||e===9756||e===9758||e===9792||e===9794||e===9824||e===9825||e>=9827&&e<=9829||e>=9831&&e<=9834||e===9836||e===9837||e===9839||e===9886||e===9887||e===9919||e>=9926&&e<=9933||e>=9935&&e<=9939||e>=9941&&e<=9953||e===9955||e===9960||e===9961||e>=9963&&e<=9969||e===9972||e>=9974&&e<=9977||e===9979||e===9980||e===9982||e===9983||e===10045||e>=10102&&e<=10111||e>=11094&&e<=11097||e>=12872&&e<=12879||e>=57344&&e<=63743||e>=65024&&e<=65039||e===65533||e>=127232&&e<=127242||e>=127248&&e<=127277||e>=127280&&e<=127337||e>=127344&&e<=127373||e===127375||e===127376||e>=127387&&e<=127404||e>=917760&&e<=917999||e>=983040&&e<=1048573||e>=1048576&&e<=1114109}function Ou(e){return e===12288||e>=65281&&e<=65376||e>=65504&&e<=65510}function vu(e){return e>=4352&&e<=4447||e===8986||e===8987||e===9001||e===9002||e>=9193&&e<=9196||e===9200||e===9203||e===9725||e===9726||e===9748||e===9749||e>=9776&&e<=9783||e>=9800&&e<=9811||e===9855||e>=9866&&e<=9871||e===9875||e===9889||e===9898||e===9899||e===9917||e===9918||e===9924||e===9925||e===9934||e===9940||e===9962||e===9970||e===9971||e===9973||e===9978||e===9981||e===9989||e===9994||e===9995||e===10024||e===10060||e===10062||e>=10067&&e<=10069||e===10071||e>=10133&&e<=10135||e===10160||e===10175||e===11035||e===11036||e===11088||e===11093||e>=11904&&e<=11929||e>=11931&&e<=12019||e>=12032&&e<=12245||e>=12272&&e<=12287||e>=12289&&e<=12350||e>=12353&&e<=12438||e>=12441&&e<=12543||e>=12549&&e<=12591||e>=12593&&e<=12686||e>=12688&&e<=12773||e>=12783&&e<=12830||e>=12832&&e<=12871||e>=12880&&e<=42124||e>=42128&&e<=42182||e>=43360&&e<=43388||e>=44032&&e<=55203||e>=63744&&e<=64255||e>=65040&&e<=65049||e>=65072&&e<=65106||e>=65108&&e<=65126||e>=65128&&e<=65131||e>=94176&&e<=94180||e===94192||e===94193||e>=94208&&e<=100343||e>=100352&&e<=101589||e>=101631&&e<=101640||e>=110576&&e<=110579||e>=110581&&e<=110587||e===110589||e===110590||e>=110592&&e<=110882||e===110898||e>=110928&&e<=110930||e===110933||e>=110948&&e<=110951||e>=110960&&e<=111355||e>=119552&&e<=119638||e>=119648&&e<=119670||e===126980||e===127183||e===127374||e>=127377&&e<=127386||e>=127488&&e<=127490||e>=127504&&e<=127547||e>=127552&&e<=127560||e===127568||e===127569||e>=127584&&e<=127589||e>=127744&&e<=127776||e>=127789&&e<=127797||e>=127799&&e<=127868||e>=127870&&e<=127891||e>=127904&&e<=127946||e>=127951&&e<=127955||e>=127968&&e<=127984||e===127988||e>=127992&&e<=128062||e===128064||e>=128066&&e<=128252||e>=128255&&e<=128317||e>=128331&&e<=128334||e>=128336&&e<=128359||e===128378||e===128405||e===128406||e===128420||e>=128507&&e<=128591||e>=128640&&e<=128709||e===128716||e>=128720&&e<=128722||e>=128725&&e<=128727||e>=128732&&e<=128735||e===128747||e===128748||e>=128756&&e<=128764||e>=128992&&e<=129003||e===129008||e>=129292&&e<=129338||e>=129340&&e<=129349||e>=129351&&e<=129535||e>=129648&&e<=129660||e>=129664&&e<=129673||e>=129679&&e<=129734||e>=129742&&e<=129756||e>=129759&&e<=129769||e>=129776&&e<=129784||e>=131072&&e<=196605||e>=196608&&e<=262141}function nm(e){if(!Number.isSafeInteger(e))throw new TypeError(`Expected a code point, got \`${typeof e}\`.`)}function Ru(e,{ambiguousAsWide:t=!1}={}){return nm(e),Ou(e)||vu(e)||t&&_u(e)?2:1}var Pu=F(Mu(),1),om=new Intl.Segmenter,im=/^\p{Default_Ignorable_Code_Point}$/u;function lo(e,t={}){if(typeof e!="string"||e.length===0)return 0;let{ambiguousIsNarrow:r=!0,countAnsiEscapeCodes:n=!1}=t;if(n||(e=gt(e)),e.length===0)return 0;let o=0,i={ambiguousAsWide:!r};for(let{segment:s}of om.segment(e)){let u=s.codePointAt(0);if(!(u<=31||u>=127&&u<=159)&&!(u>=8203&&u<=8207||u===65279)&&!(u>=768&&u<=879||u>=6832&&u<=6911||u>=7616&&u<=7679||u>=8400&&u<=8447||u>=65056&&u<=65071)&&!(u>=55296&&u<=57343)&&!(u>=65024&&u<=65039)&&!im.test(s)){if((0,Pu.default)().test(s)){o+=2;continue}o+=Ru(u,i)}}return o}function Do({stream:e=process.stdout}={}){return!!(e&&e.isTTY&&process.env.TERM!=="dumb"&&!("CI"in process.env))}var fo=F(require("node:process"),1);function Et(){let{env:e}=fo.default,{TERM:t,TERM_PROGRAM:r}=e;return fo.default.platform!=="win32"?t!=="linux":!!e.WT_SESSION||!!e.TERMINUS_SUBLIME||e.ConEmuTask==="{cmd::Cmder}"||r==="Terminus-Sublime"||r==="vscode"||t==="xterm-256color"||t==="alacritty"||t==="rxvt-unicode"||t==="rxvt-unicode-256color"||e.TERMINAL_EMULATOR==="JetBrains-JediTerm"}var q=F(require("node:process"),1),sm=3,po=class{#r=0;start(){this.#r++,this.#r===1&&this.#o()}stop(){if(this.#r<=0)throw new Error("`stop` called more times than `start`");this.#r--,this.#r===0&&this.#e()}#o(){q.default.platform==="win32"||!q.default.stdin.isTTY||(q.default.stdin.setRawMode(!0),q.default.stdin.on("data",this.#n),q.default.stdin.resume())}#e(){q.default.stdin.isTTY&&(q.default.stdin.off("data",this.#n),q.default.stdin.pause(),q.default.stdin.setRawMode(!1))}#n(t){t[0]===sm&&q.default.emit("SIGINT")}},um=new po,mo=um;var am=F(no(),1),ho=class{#r=0;#o=!1;#e=0;#n=-1;#u=0;#t;#s;#i;#p;#m;#l;#D;#f;#h;#a;#c;color;constructor(t){typeof t=="string"&&(t={text:t}),this.#t={color:"cyan",stream:Ct.default.stderr,discardStdin:!0,hideCursor:!0,...t},this.color=this.#t.color,this.spinner=this.#t.spinner,this.#m=this.#t.interval,this.#i=this.#t.stream,this.#l=typeof this.#t.isEnabled=="boolean"?this.#t.isEnabled:Do({stream:this.#i}),this.#D=typeof this.#t.isSilent=="boolean"?this.#t.isSilent:!1,this.text=this.#t.text,this.prefixText=this.#t.prefixText,this.suffixText=this.#t.suffixText,this.indent=this.#t.indent,Ct.default.env.NODE_ENV==="test"&&(this._stream=this.#i,this._isEnabled=this.#l,Object.defineProperty(this,"_linesToClear",{get(){return this.#r},set(r){this.#r=r}}),Object.defineProperty(this,"_frameIndex",{get(){return this.#n}}),Object.defineProperty(this,"_lineCount",{get(){return this.#e}}))}get indent(){return this.#f}set indent(t=0){if(!(t>=0&&Number.isInteger(t)))throw new Error("The `indent` option must be an integer from 0 and up");this.#f=t,this.#d()}get interval(){return this.#m??this.#s.interval??100}get spinner(){return this.#s}set spinner(t){if(this.#n=-1,this.#m=void 0,typeof t=="object"){if(t.frames===void 0)throw new Error("The given spinner must have a `frames` property");this.#s=t}else if(!Et())this.#s=yt.default.line;else if(t===void 0)this.#s=yt.default.dots;else if(t!=="default"&&yt.default[t])this.#s=yt.default[t];else throw new Error(`There is no built-in spinner named '${t}'. See https://github.com/sindresorhus/cli-spinners/blob/main/spinners.json for a full list.`)}get text(){return this.#h}set text(t=""){this.#h=t,this.#d()}get prefixText(){return this.#a}set prefixText(t=""){this.#a=t,this.#d()}get suffixText(){return this.#c}set suffixText(t=""){this.#c=t,this.#d()}get isSpinning(){return this.#p!==void 0}#F(t=this.#a,r=" "){return typeof t=="string"&&t!==""?t+r:typeof t=="function"?t()+r:""}#g(t=this.#c,r=" "){return typeof t=="string"&&t!==""?r+t:typeof t=="function"?r+t():""}#d(){let t=this.#i.columns??80,r=this.#F(this.#a,"-"),n=this.#g(this.#c,"-"),o=" ".repeat(this.#f)+r+"--"+this.#h+"--"+n;this.#e=0;for(let i of gt(o).split(`
`))this.#e+=Math.max(1,Math.ceil(lo(i,{countAnsiEscapeCodes:!0})/t))}get isEnabled(){return this.#l&&!this.#D}set isEnabled(t){if(typeof t!="boolean")throw new TypeError("The `isEnabled` option must be a boolean");this.#l=t}get isSilent(){return this.#D}set isSilent(t){if(typeof t!="boolean")throw new TypeError("The `isSilent` option must be a boolean");this.#D=t}frame(){let t=Date.now();(this.#n===-1||t-this.#u>=this.interval)&&(this.#n=++this.#n%this.#s.frames.length,this.#u=t);let{frames:r}=this.#s,n=r[this.#n];this.color&&(n=lu[this.color](n));let o=typeof this.#a=="string"&&this.#a!==""?this.#a+" ":"",i=typeof this.text=="string"?" "+this.text:"",s=typeof this.#c=="string"&&this.#c!==""?" "+this.#c:"";return o+n+i+s}clear(){if(!this.#l||!this.#i.isTTY)return this;this.#i.cursorTo(0);for(let t=0;t<this.#r;t++)t>0&&this.#i.moveCursor(0,-1),this.#i.clearLine(1);return(this.#f||this.lastIndent!==this.#f)&&this.#i.cursorTo(this.#f),this.lastIndent=this.#f,this.#r=0,this}render(){return this.#D?this:(this.clear(),this.#i.write(this.frame()),this.#r=this.#e,this)}start(t){return t&&(this.text=t),this.#D?this:this.#l?this.isSpinning?this:(this.#t.hideCursor&&ro.hide(this.#i),this.#t.discardStdin&&Ct.default.stdin.isTTY&&(this.#o=!0,mo.start()),this.render(),this.#p=setInterval(this.render.bind(this),this.interval),this):(this.text&&this.#i.write(`- ${this.text}
`),this)}stop(){return this.#l?(clearInterval(this.#p),this.#p=void 0,this.#n=0,this.clear(),this.#t.hideCursor&&ro.show(this.#i),this.#t.discardStdin&&Ct.default.stdin.isTTY&&this.#o&&(mo.stop(),this.#o=!1),this):this}succeed(t){return this.stopAndPersist({symbol:Ft.success,text:t})}fail(t){return this.stopAndPersist({symbol:Ft.error,text:t})}warn(t){return this.stopAndPersist({symbol:Ft.warning,text:t})}info(t){return this.stopAndPersist({symbol:Ft.info,text:t})}stopAndPersist(t={}){if(this.#D)return this;let r=t.prefixText??this.#a,n=this.#F(r," "),o=t.symbol??" ",i=t.text??this.text,u=typeof i=="string"?(o?" ":"")+i:"",a=t.suffixText??this.#c,c=this.#g(a," "),l=n+o+u+c+`
`;return this.stop(),this.#i.write(l),this}};function Fo(e){return new ho(e)}var Iy=(0,B.blue)((0,B.dim)("internal only"));var be={wait:`\u{1F550}${(0,B.blue)("wait")}  - `,info:`${process.env.GITHUB_ACTIONS==="true"?"\u2139\uFE0F":"\u2139\uFE0F "}${(0,B.cyan)("info")}  - `,success:`\u2705${(0,B.green)("ready")}  - `,warn:`${process.env.GITHUB_ACTIONS==="true"?"\u26A0\uFE0F":"\u26A0\uFE0F "}${(0,B.yellow)("warn")}  - `,error:`\u{1F4A5}${(0,B.red)("error")}  - `,event:`\u26A1\uFE0F${(0,B.magenta)("event")}  - `,paymentPrompt:`\u{1F4B0}${(0,B.yellowBright)("plan")}  - `},cm=!0;function ku(e,t){e||(be.wait=`${(0,B.blue)("wait")}  - `,be.info=`${(0,B.cyan)("info")}  - `,be.success=`${(0,B.green)("ready")}  - `,be.warn=`${(0,B.yellow)("warn")}  - `,be.error=`${(0,B.red)("error")}  - `,be.event=`${(0,B.magenta)("event")}  - `,be.paymentPrompt=`${(0,B.yellowBright)("plan")}  - `),t&&(cm=!1)}var ym=require("@oclif/core");var bm=F(Ju());var Hy=`${ct.url}/sessions/success`,Ky=`${ct.url}/sessions/failure`;function Zu(){Dt("raycastAccessToken")===""&&Xu.ux.error("please first log in first using `npx ray login`",{exit:1})}var pe=require("@oclif/core");var lr=class extends pe.Command{static baseFlags={"exit-on-error":pe.Flags.boolean({default:!0,helpGroup:"GLOBAL",aliases:["exitOnError"],deprecateAliases:!0,summary:"Always exit with non-zero code on error",allowNo:!0}),emoji:pe.Flags.boolean({default:!1,helpGroup:"GLOBAL",summary:"Prefix output with emojis \u{1F308}"}),help:pe.Flags.boolean({default:!1,helpGroup:"GLOBAL",summary:"Show the help message for the command"}),"non-interactive":pe.Flags.boolean({char:"I",default:!1,helpGroup:"GLOBAL",summary:"Disable interactive outputs, useful for CI"}),target:pe.Flags.option({char:"t",description:"Raycast app target",helpGroup:"GLOBAL",multiple:!1,options:["debug","internal","release","x","x-development","x-internal"],hidden:!0})()};flags;args;async init(){await super.init(),process.on("SIGINT",()=>process.exit(1));let{args:t,flags:r}=await this.parse({flags:this.ctor.flags,baseFlags:super.ctor.baseFlags,enableJsonFlag:this.ctor.enableJsonFlag,args:this.ctor.args,strict:this.ctor.strict});this.flags=r,this.args=t,Js(this.flags.target),ku(this.flags.emoji,this.flags["non-interactive"])}error(t,r){return r?.message&&t instanceof Error&&(t.message=`${r.message} (${t.message})`,delete r.message),super.error(t,r)}async catch(t){return super.catch(t)}async finally(t){return super.finally(t)}};var Sn=F(require("path"));var xn=F(require("path")),jp=F(require("fs")),Up=F(require("os"));function O(e){if(typeof e!="object"||e===null)return!1;let t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}var Qu=require("node:url"),Ge=(e,t)=>{let r=Ao(wm(e));if(typeof r!="string")throw new TypeError(`${t} must be a string or a file URL: ${r}.`);return r},wm=e=>So(e)?e.toString():e,So=e=>typeof e!="string"&&e&&Object.getPrototypeOf(e)===String.prototype,Ao=e=>e instanceof URL?(0,Qu.fileURLToPath)(e):e;var Dr=(e,t=[],r={})=>{let n=Ge(e,"First argument"),[o,i]=O(t)?[[],t]:[t,r];if(!Array.isArray(o))throw new TypeError(`Second argument must be either an array of arguments or an options object: ${o}`);if(o.some(a=>typeof a=="object"&&a!==null))throw new TypeError(`Second argument must be an array of strings: ${o}`);let s=o.map(String),u=s.find(a=>a.includes("\0"));if(u!==void 0)throw new TypeError(`Arguments cannot contain null bytes ("\\0"): ${u}`);if(!O(i))throw new TypeError(`Last argument must be an options object: ${i}`);return[n,s,i]};var aa=require("node:child_process");var ea=require("node:string_decoder"),{toString:ta}=Object.prototype,ra=e=>ta.call(e)==="[object ArrayBuffer]",R=e=>ta.call(e)==="[object Uint8Array]",se=e=>new Uint8Array(e.buffer,e.byteOffset,e.byteLength),xm=new TextEncoder,na=e=>xm.encode(e),Sm=new TextDecoder,fr=e=>Sm.decode(e),oa=(e,t)=>Am(e,t).join(""),Am=(e,t)=>{if(t==="utf8"&&e.every(i=>typeof i=="string"))return e;let r=new ea.StringDecoder(t),n=e.map(i=>typeof i=="string"?na(i):i).map(i=>r.write(i)),o=r.end();return o===""?n:[...n,o]},bt=e=>e.length===1&&R(e[0])?e[0]:Bo(Bm(e)),Bm=e=>e.map(t=>typeof t=="string"?na(t):t),Bo=e=>{let t=new Uint8Array(Tm(e)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t},Tm=e=>{let t=0;for(let r of e)t+=r.length;return t};var ca=e=>Array.isArray(e)&&Array.isArray(e.raw),la=(e,t)=>{let r=[];for(let[i,s]of e.entries())r=_m({templates:e,expressions:t,tokens:r,index:i,template:s});if(r.length===0)throw new TypeError("Template script must not be empty");let[n,...o]=r;return[n,o,{}]},_m=({templates:e,expressions:t,tokens:r,index:n,template:o})=>{if(o===void 0)throw new TypeError(`Invalid backslash sequence: ${e.raw[n]}`);let{nextTokens:i,leadingWhitespaces:s,trailingWhitespaces:u}=Om(o,e.raw[n]),a=sa(r,i,s);if(n===t.length)return a;let c=t[n],l=Array.isArray(c)?c.map(D=>ua(D)):[ua(c)];return sa(a,l,u)},Om=(e,t)=>{if(t.length===0)return{nextTokens:[],leadingWhitespaces:!1,trailingWhitespaces:!1};let r=[],n=0,o=ia.has(t[0]);for(let s=0,u=0;s<e.length;s+=1,u+=1){let a=t[u];if(ia.has(a))n!==s&&r.push(e.slice(n,s)),n=s+1;else if(a==="\\"){let c=t[u+1];c===`
`?(s-=1,u+=1):c==="u"&&t[u+2]==="{"?u=t.indexOf("}",u+3):u+=vm[c]??1}}let i=n===e.length;return i||r.push(e.slice(n)),{nextTokens:r,leadingWhitespaces:o,trailingWhitespaces:i}},ia=new Set([" ","	","\r",`
`]),vm={x:3,u:5},sa=(e,t,r)=>r||e.length===0||t.length===0?[...e,...t]:[...e.slice(0,-1),`${e.at(-1)}${t[0]}`,...t.slice(1)],ua=e=>{let t=typeof e;if(t==="string")return e;if(t==="number")return String(e);if(O(e)&&("stdout"in e||"isMaxBuffer"in e))return Rm(e);throw e instanceof aa.ChildProcess||Object.prototype.toString.call(e)==="[object Promise]"?new TypeError("Unexpected subprocess in template expression. Please use ${await subprocess} instead of ${subprocess}."):new TypeError(`Unexpected "${t}" in template expression`)},Rm=({stdout:e})=>{if(typeof e=="string")return e;if(R(e))return fr(e);throw e===void 0?new TypeError(`Missing result.stdout in template expression. This is probably due to the previous subprocess' "stdout" option.`):new TypeError(`Unexpected "${typeof e}" stdout in template expression`)};var yf=require("node:child_process");var fa=require("node:util");var pr=F(require("node:process"),1),V=e=>dr.includes(e),dr=[pr.default.stdin,pr.default.stdout,pr.default.stderr],G=["stdin","stdout","stderr"],mr=e=>G[e]??`stdio[${e}]`;var pa=e=>{let t={...e};for(let r of Oo)t[r]=To(e,r);return t},To=(e,t)=>{let r=Array.from({length:Im(e)+1}),n=Mm(e[t],r,t);return $m(n,t)},Im=({stdio:e})=>Array.isArray(e)?Math.max(e.length,G.length):G.length,Mm=(e,t,r)=>O(e)?Pm(e,t,r):t.fill(e),Pm=(e,t,r)=>{for(let n of Object.keys(e).sort(km))for(let o of Lm(n,r,t))t[o]=e[n];return t},km=(e,t)=>Da(e)<Da(t)?1:-1,Da=e=>e==="stdout"||e==="stderr"?0:e==="all"?2:1,Lm=(e,t,r)=>{if(e==="ipc")return[r.length-1];let n=_o(e);if(n===void 0||n===0)throw new TypeError(`"${t}.${e}" is invalid.
It must be "${t}.stdout", "${t}.stderr", "${t}.all", "${t}.ipc", or "${t}.fd3", "${t}.fd4" (and so on).`);if(n>=r.length)throw new TypeError(`"${t}.${e}" is invalid: that file descriptor does not exist.
Please set the "stdio" option to ensure that file descriptor exists.`);return n==="all"?[1,2]:[n]},_o=e=>{if(e==="all")return e;if(G.includes(e))return G.indexOf(e);let t=Nm.exec(e);if(t!==null)return Number(t[1])},Nm=/^fd(\d+)$/,$m=(e,t)=>e.map(r=>r===void 0?Um[t]:r),jm=(0,fa.debuglog)("execa").enabled?"full":"none",Um={lines:!1,buffer:!0,maxBuffer:1e3*1e3*100,verbose:jm,stripFinalNewline:!0},Oo=["lines","buffer","maxBuffer","verbose","stripFinalNewline"],ue=(e,t)=>t==="ipc"?e.at(-1):e[t];var ze=({verbose:e},t)=>vo(e,t)!=="none",We=({verbose:e},t)=>!["none","short"].includes(vo(e,t)),da=({verbose:e},t)=>{let r=vo(e,t);return hr(r)?r:void 0},vo=(e,t)=>t===void 0?Gm(e):ue(e,t),Gm=e=>e.find(t=>hr(t))??Fr.findLast(t=>e.includes(t)),hr=e=>typeof e=="function",Fr=["none","short","full"];var _a=require("node:util");var ma=require("node:process"),ha=require("node:util"),Fa=(e,t)=>{let r=[e,...t],n=r.join(" "),o=r.map(i=>Hm(ga(i))).join(" ");return{command:n,escapedCommand:o}},wt=e=>(0,ha.stripVTControlCharacters)(e).split(`
`).map(t=>ga(t)).join(`
`),ga=e=>e.replaceAll(Ym,t=>zm(t)),zm=e=>{let t=qm[e];if(t!==void 0)return t;let r=e.codePointAt(0),n=r.toString(16);return r<=Vm?`\\u${n.padStart(4,"0")}`:`\\U${n}`},Wm=()=>{try{return new RegExp("\\p{Separator}|\\p{Other}","gu")}catch{return/[\s\u0000-\u001F\u007F-\u009F\u00AD]/g}},Ym=Wm(),qm={" ":" ","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t"},Vm=65535,Hm=e=>Km.test(e)?e:ma.platform==="win32"?`"${e.replaceAll('"','""')}"`:`'${e.replaceAll("'","'\\''")}'`,Km=/^[\w./-]+$/;var Ea={circleQuestionMark:"(?)",questionMarkPrefix:"(?)",square:"\u2588",squareDarkShade:"\u2593",squareMediumShade:"\u2592",squareLightShade:"\u2591",squareTop:"\u2580",squareBottom:"\u2584",squareLeft:"\u258C",squareRight:"\u2590",squareCenter:"\u25A0",bullet:"\u25CF",dot:"\u2024",ellipsis:"\u2026",pointerSmall:"\u203A",triangleUp:"\u25B2",triangleUpSmall:"\u25B4",triangleDown:"\u25BC",triangleDownSmall:"\u25BE",triangleLeftSmall:"\u25C2",triangleRightSmall:"\u25B8",home:"\u2302",heart:"\u2665",musicNote:"\u266A",musicNoteBeamed:"\u266B",arrowUp:"\u2191",arrowDown:"\u2193",arrowLeft:"\u2190",arrowRight:"\u2192",arrowLeftRight:"\u2194",arrowUpDown:"\u2195",almostEqual:"\u2248",notEqual:"\u2260",lessOrEqual:"\u2264",greaterOrEqual:"\u2265",identical:"\u2261",infinity:"\u221E",subscriptZero:"\u2080",subscriptOne:"\u2081",subscriptTwo:"\u2082",subscriptThree:"\u2083",subscriptFour:"\u2084",subscriptFive:"\u2085",subscriptSix:"\u2086",subscriptSeven:"\u2087",subscriptEight:"\u2088",subscriptNine:"\u2089",oneHalf:"\xBD",oneThird:"\u2153",oneQuarter:"\xBC",oneFifth:"\u2155",oneSixth:"\u2159",oneEighth:"\u215B",twoThirds:"\u2154",twoFifths:"\u2156",threeQuarters:"\xBE",threeFifths:"\u2157",threeEighths:"\u215C",fourFifths:"\u2158",fiveSixths:"\u215A",fiveEighths:"\u215D",sevenEighths:"\u215E",line:"\u2500",lineBold:"\u2501",lineDouble:"\u2550",lineDashed0:"\u2504",lineDashed1:"\u2505",lineDashed2:"\u2508",lineDashed3:"\u2509",lineDashed4:"\u254C",lineDashed5:"\u254D",lineDashed6:"\u2574",lineDashed7:"\u2576",lineDashed8:"\u2578",lineDashed9:"\u257A",lineDashed10:"\u257C",lineDashed11:"\u257E",lineDashed12:"\u2212",lineDashed13:"\u2013",lineDashed14:"\u2010",lineDashed15:"\u2043",lineVertical:"\u2502",lineVerticalBold:"\u2503",lineVerticalDouble:"\u2551",lineVerticalDashed0:"\u2506",lineVerticalDashed1:"\u2507",lineVerticalDashed2:"\u250A",lineVerticalDashed3:"\u250B",lineVerticalDashed4:"\u254E",lineVerticalDashed5:"\u254F",lineVerticalDashed6:"\u2575",lineVerticalDashed7:"\u2577",lineVerticalDashed8:"\u2579",lineVerticalDashed9:"\u257B",lineVerticalDashed10:"\u257D",lineVerticalDashed11:"\u257F",lineDownLeft:"\u2510",lineDownLeftArc:"\u256E",lineDownBoldLeftBold:"\u2513",lineDownBoldLeft:"\u2512",lineDownLeftBold:"\u2511",lineDownDoubleLeftDouble:"\u2557",lineDownDoubleLeft:"\u2556",lineDownLeftDouble:"\u2555",lineDownRight:"\u250C",lineDownRightArc:"\u256D",lineDownBoldRightBold:"\u250F",lineDownBoldRight:"\u250E",lineDownRightBold:"\u250D",lineDownDoubleRightDouble:"\u2554",lineDownDoubleRight:"\u2553",lineDownRightDouble:"\u2552",lineUpLeft:"\u2518",lineUpLeftArc:"\u256F",lineUpBoldLeftBold:"\u251B",lineUpBoldLeft:"\u251A",lineUpLeftBold:"\u2519",lineUpDoubleLeftDouble:"\u255D",lineUpDoubleLeft:"\u255C",lineUpLeftDouble:"\u255B",lineUpRight:"\u2514",lineUpRightArc:"\u2570",lineUpBoldRightBold:"\u2517",lineUpBoldRight:"\u2516",lineUpRightBold:"\u2515",lineUpDoubleRightDouble:"\u255A",lineUpDoubleRight:"\u2559",lineUpRightDouble:"\u2558",lineUpDownLeft:"\u2524",lineUpBoldDownBoldLeftBold:"\u252B",lineUpBoldDownBoldLeft:"\u2528",lineUpDownLeftBold:"\u2525",lineUpBoldDownLeftBold:"\u2529",lineUpDownBoldLeftBold:"\u252A",lineUpDownBoldLeft:"\u2527",lineUpBoldDownLeft:"\u2526",lineUpDoubleDownDoubleLeftDouble:"\u2563",lineUpDoubleDownDoubleLeft:"\u2562",lineUpDownLeftDouble:"\u2561",lineUpDownRight:"\u251C",lineUpBoldDownBoldRightBold:"\u2523",lineUpBoldDownBoldRight:"\u2520",lineUpDownRightBold:"\u251D",lineUpBoldDownRightBold:"\u2521",lineUpDownBoldRightBold:"\u2522",lineUpDownBoldRight:"\u251F",lineUpBoldDownRight:"\u251E",lineUpDoubleDownDoubleRightDouble:"\u2560",lineUpDoubleDownDoubleRight:"\u255F",lineUpDownRightDouble:"\u255E",lineDownLeftRight:"\u252C",lineDownBoldLeftBoldRightBold:"\u2533",lineDownLeftBoldRightBold:"\u252F",lineDownBoldLeftRight:"\u2530",lineDownBoldLeftBoldRight:"\u2531",lineDownBoldLeftRightBold:"\u2532",lineDownLeftRightBold:"\u252E",lineDownLeftBoldRight:"\u252D",lineDownDoubleLeftDoubleRightDouble:"\u2566",lineDownDoubleLeftRight:"\u2565",lineDownLeftDoubleRightDouble:"\u2564",lineUpLeftRight:"\u2534",lineUpBoldLeftBoldRightBold:"\u253B",lineUpLeftBoldRightBold:"\u2537",lineUpBoldLeftRight:"\u2538",lineUpBoldLeftBoldRight:"\u2539",lineUpBoldLeftRightBold:"\u253A",lineUpLeftRightBold:"\u2536",lineUpLeftBoldRight:"\u2535",lineUpDoubleLeftDoubleRightDouble:"\u2569",lineUpDoubleLeftRight:"\u2568",lineUpLeftDoubleRightDouble:"\u2567",lineUpDownLeftRight:"\u253C",lineUpBoldDownBoldLeftBoldRightBold:"\u254B",lineUpDownBoldLeftBoldRightBold:"\u2548",lineUpBoldDownLeftBoldRightBold:"\u2547",lineUpBoldDownBoldLeftRightBold:"\u254A",lineUpBoldDownBoldLeftBoldRight:"\u2549",lineUpBoldDownLeftRight:"\u2540",lineUpDownBoldLeftRight:"\u2541",lineUpDownLeftBoldRight:"\u253D",lineUpDownLeftRightBold:"\u253E",lineUpBoldDownBoldLeftRight:"\u2542",lineUpDownLeftBoldRightBold:"\u253F",lineUpBoldDownLeftBoldRight:"\u2543",lineUpBoldDownLeftRightBold:"\u2544",lineUpDownBoldLeftBoldRight:"\u2545",lineUpDownBoldLeftRightBold:"\u2546",lineUpDoubleDownDoubleLeftDoubleRightDouble:"\u256C",lineUpDoubleDownDoubleLeftRight:"\u256B",lineUpDownLeftDoubleRightDouble:"\u256A",lineCross:"\u2573",lineBackslash:"\u2572",lineSlash:"\u2571"},Ca={tick:"\u2714",info:"\u2139",warning:"\u26A0",cross:"\u2718",squareSmall:"\u25FB",squareSmallFilled:"\u25FC",circle:"\u25EF",circleFilled:"\u25C9",circleDotted:"\u25CC",circleDouble:"\u25CE",circleCircle:"\u24DE",circleCross:"\u24E7",circlePipe:"\u24BE",radioOn:"\u25C9",radioOff:"\u25EF",checkboxOn:"\u2612",checkboxOff:"\u2610",checkboxCircleOn:"\u24E7",checkboxCircleOff:"\u24BE",pointer:"\u276F",triangleUpOutline:"\u25B3",triangleLeft:"\u25C0",triangleRight:"\u25B6",lozenge:"\u25C6",lozengeOutline:"\u25C7",hamburger:"\u2630",smiley:"\u32E1",mustache:"\u0DF4",star:"\u2605",play:"\u25B6",nodejs:"\u2B22",oneSeventh:"\u2150",oneNinth:"\u2151",oneTenth:"\u2152"},Jm={tick:"\u221A",info:"i",warning:"\u203C",cross:"\xD7",squareSmall:"\u25A1",squareSmallFilled:"\u25A0",circle:"( )",circleFilled:"(*)",circleDotted:"( )",circleDouble:"( )",circleCircle:"(\u25CB)",circleCross:"(\xD7)",circlePipe:"(\u2502)",radioOn:"(*)",radioOff:"( )",checkboxOn:"[\xD7]",checkboxOff:"[ ]",checkboxCircleOn:"(\xD7)",checkboxCircleOff:"( )",pointer:">",triangleUpOutline:"\u2206",triangleLeft:"\u25C4",triangleRight:"\u25BA",lozenge:"\u2666",lozengeOutline:"\u25CA",hamburger:"\u2261",smiley:"\u263A",mustache:"\u250C\u2500\u2510",star:"\u2736",play:"\u25BA",nodejs:"\u2666",oneSeventh:"1/7",oneNinth:"1/9",oneTenth:"1/10"},Xm={...Ea,...Ca},Zm={...Ea,...Jm},Qm=Et(),eh=Qm?Xm:Zm,gr=eh,gb=Object.entries(Ca);var ya=F(require("node:tty"),1),th=ya.default?.WriteStream?.prototype?.hasColors?.()??!1,C=(e,t)=>{if(!th)return o=>o;let r=`\x1B[${e}m`,n=`\x1B[${t}m`;return o=>{let i=o+"",s=i.indexOf(n);if(s===-1)return r+i+n;let u=r,a=0;for(;s!==-1;)u+=i.slice(a,s)+r,a=s+n.length,s=i.indexOf(n,a);return u+=i.slice(a)+n,u}},Cb=C(0,0),ba=C(1,22),yb=C(2,22),bb=C(3,23),wb=C(4,24),xb=C(53,55),Sb=C(7,27),Ab=C(8,28),Bb=C(9,29),Tb=C(30,39),_b=C(31,39),Ob=C(32,39),vb=C(33,39),Rb=C(34,39),Ib=C(35,39),Mb=C(36,39),Pb=C(37,39),Er=C(90,39),kb=C(40,49),Lb=C(41,49),Nb=C(42,49),$b=C(43,49),jb=C(44,49),Ub=C(45,49),Gb=C(46,49),zb=C(47,49),Wb=C(100,49),wa=C(91,39),Yb=C(92,39),xa=C(93,39),qb=C(94,39),Vb=C(95,39),Hb=C(96,39),Kb=C(97,39),Jb=C(101,49),Xb=C(102,49),Zb=C(103,49),Qb=C(104,49),e3=C(105,49),t3=C(106,49),r3=C(107,49);var Ba=({type:e,message:t,timestamp:r,piped:n,commandId:o,result:{failed:i=!1}={},options:{reject:s=!0}})=>{let u=rh(r),a=nh[e]({failed:i,reject:s,piped:n}),c=oh[e]({reject:s});return`${Er(`[${u}]`)} ${Er(`[${o}]`)} ${c(a)} ${c(t)}`},rh=e=>`${Cr(e.getHours(),2)}:${Cr(e.getMinutes(),2)}:${Cr(e.getSeconds(),2)}.${Cr(e.getMilliseconds(),3)}`,Cr=(e,t)=>String(e).padStart(t,"0"),Sa=({failed:e,reject:t})=>e?t?gr.cross:gr.warning:gr.tick,nh={command:({piped:e})=>e?"|":"$",output:()=>" ",ipc:()=>"*",error:Sa,duration:Sa},Aa=e=>e,oh={command:()=>ba,output:()=>Aa,ipc:()=>Aa,error:({reject:e})=>e?wa:xa,duration:()=>Er};var Ta=(e,t,r)=>{let n=da(t,r);return e.map(({verboseLine:o,verboseObject:i})=>ih(o,i,n)).filter(o=>o!==void 0).map(o=>sh(o)).join("")},ih=(e,t,r)=>{if(r===void 0)return e;let n=r(e,t);if(typeof n=="string")return n},sh=e=>e.endsWith(`
`)?e:`${e}
`;var X=({type:e,verboseMessage:t,fdNumber:r,verboseInfo:n,result:o})=>{let i=uh({type:e,result:o,verboseInfo:n}),s=ah(t,i),u=Ta(s,n,r);u!==""&&console.warn(u.slice(0,-1))},uh=({type:e,result:t,verboseInfo:{escapedCommand:r,commandId:n,rawOptions:{piped:o=!1,...i}}})=>({type:e,escapedCommand:r,commandId:`${n}`,timestamp:new Date,piped:o,result:t,options:i}),ah=(e,t)=>e.split(`
`).map(r=>ch({...t,message:r})),ch=e=>({verboseLine:Ba(e),verboseObject:e}),yr=e=>{let t=typeof e=="string"?e:(0,_a.inspect)(e);return wt(t).replaceAll("	"," ".repeat(lh))},lh=2;var Oa=(e,t)=>{ze(t)&&X({type:"command",verboseMessage:e,verboseInfo:t})};var va=(e,t,r)=>{ph(e);let n=Dh(e);return{verbose:e,escapedCommand:t,commandId:n,rawOptions:r}},Dh=e=>ze({verbose:e})?fh++:void 0,fh=0n,ph=e=>{for(let t of e){if(t===!1)throw new TypeError(`The "verbose: false" option was renamed to "verbose: 'none'".`);if(t===!0)throw new TypeError(`The "verbose: true" option was renamed to "verbose: 'short'".`);if(!Fr.includes(t)&&!hr(t)){let r=Fr.map(n=>`'${n}'`).join(", ");throw new TypeError(`The "verbose" option must not be ${t}. Allowed values are: ${r} or a function.`)}}};var Ro=require("node:process"),br=()=>Ro.hrtime.bigint(),Io=e=>Number(Ro.hrtime.bigint()-e)/1e6;var wr=(e,t,r)=>{let n=br(),{command:o,escapedCommand:i}=Fa(e,t),s=To(r,"verbose"),u=va(s,i,{...r});return Oa(i,u),{command:o,escapedCommand:i,startTime:n,verboseInfo:u}};var Yl=F(require("node:path"),1),ni=F(require("node:process"),1),ql=F(Ec(),1);var xt=F(require("node:process"),1),de=F(require("node:path"),1);function Sr(e={}){let{env:t=process.env,platform:r=process.platform}=e;return r!=="win32"?"PATH":Object.keys(t).reverse().find(n=>n.toUpperCase()==="PATH")||"Path"}var Cc=require("node:util"),Wo=require("node:child_process"),zo=F(require("node:path"),1),yc=require("node:url"),j3=(0,Cc.promisify)(Wo.execFile);function Ar(e){return e instanceof URL?(0,yc.fileURLToPath)(e):e}function bc(e){return{*[Symbol.iterator](){let t=zo.default.resolve(Ar(e)),r;for(;r!==t;)yield t,r=t,t=zo.default.resolve(t,"..")}}}var U3=10*1024*1024;var jh=({cwd:e=xt.default.cwd(),path:t=xt.default.env[Sr()],preferLocal:r=!0,execPath:n=xt.default.execPath,addExecPath:o=!0}={})=>{let i=de.default.resolve(Ar(e)),s=[],u=t.split(de.default.delimiter);return r&&Uh(s,u,i),o&&Gh(s,u,n,i),t===""||t===de.default.delimiter?`${s.join(de.default.delimiter)}${t}`:[...s,t].join(de.default.delimiter)},Uh=(e,t,r)=>{for(let n of bc(r)){let o=de.default.join(n,"node_modules/.bin");t.includes(o)||e.push(o)}},Gh=(e,t,r,n)=>{let o=de.default.resolve(n,Ar(r),"..");t.includes(o)||e.push(o)},wc=({env:e=xt.default.env,...t}={})=>{e={...e};let r=Sr({env:e});return t.path=e[r],e[r]=jh(t),e};var $c=require("node:timers/promises");var xc=(e,t,r)=>{let n=r?At:St,o=e instanceof H?{}:{cause:e};return new n(t,o)},H=class extends Error{},Sc=(e,t)=>{Object.defineProperty(e.prototype,"name",{value:t,writable:!0,enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,Bc,{value:!0,writable:!1,enumerable:!1,configurable:!1})},Ac=e=>Br(e)&&Bc in e,Bc=Symbol("isExecaError"),Br=e=>Object.prototype.toString.call(e)==="[object Error]",St=class extends Error{};Sc(St,St.name);var At=class extends Error{};Sc(At,At.name);var Ve=require("node:os");var Ic=require("node:os");var Tc=()=>{let e=Oc-_c+1;return Array.from({length:e},zh)},zh=(e,t)=>({name:`SIGRT${t+1}`,number:_c+t,action:"terminate",description:"Application-specific signal (realtime)",standard:"posix"}),_c=34,Oc=64;var Rc=require("node:os");var vc=[{name:"SIGHUP",number:1,action:"terminate",description:"Terminal closed",standard:"posix"},{name:"SIGINT",number:2,action:"terminate",description:"User interruption with CTRL-C",standard:"ansi"},{name:"SIGQUIT",number:3,action:"core",description:"User interruption with CTRL-\\",standard:"posix"},{name:"SIGILL",number:4,action:"core",description:"Invalid machine instruction",standard:"ansi"},{name:"SIGTRAP",number:5,action:"core",description:"Debugger breakpoint",standard:"posix"},{name:"SIGABRT",number:6,action:"core",description:"Aborted",standard:"ansi"},{name:"SIGIOT",number:6,action:"core",description:"Aborted",standard:"bsd"},{name:"SIGBUS",number:7,action:"core",description:"Bus error due to misaligned, non-existing address or paging error",standard:"bsd"},{name:"SIGEMT",number:7,action:"terminate",description:"Command should be emulated but is not implemented",standard:"other"},{name:"SIGFPE",number:8,action:"core",description:"Floating point arithmetic error",standard:"ansi"},{name:"SIGKILL",number:9,action:"terminate",description:"Forced termination",standard:"posix",forced:!0},{name:"SIGUSR1",number:10,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGSEGV",number:11,action:"core",description:"Segmentation fault",standard:"ansi"},{name:"SIGUSR2",number:12,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGPIPE",number:13,action:"terminate",description:"Broken pipe or socket",standard:"posix"},{name:"SIGALRM",number:14,action:"terminate",description:"Timeout or timer",standard:"posix"},{name:"SIGTERM",number:15,action:"terminate",description:"Termination",standard:"ansi"},{name:"SIGSTKFLT",number:16,action:"terminate",description:"Stack is empty or overflowed",standard:"other"},{name:"SIGCHLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"posix"},{name:"SIGCLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"other"},{name:"SIGCONT",number:18,action:"unpause",description:"Unpaused",standard:"posix",forced:!0},{name:"SIGSTOP",number:19,action:"pause",description:"Paused",standard:"posix",forced:!0},{name:"SIGTSTP",number:20,action:"pause",description:'Paused using CTRL-Z or "suspend"',standard:"posix"},{name:"SIGTTIN",number:21,action:"pause",description:"Background process cannot read terminal input",standard:"posix"},{name:"SIGBREAK",number:21,action:"terminate",description:"User interruption with CTRL-BREAK",standard:"other"},{name:"SIGTTOU",number:22,action:"pause",description:"Background process cannot write to terminal output",standard:"posix"},{name:"SIGURG",number:23,action:"ignore",description:"Socket received out-of-band data",standard:"bsd"},{name:"SIGXCPU",number:24,action:"core",description:"Process timed out",standard:"bsd"},{name:"SIGXFSZ",number:25,action:"core",description:"File too big",standard:"bsd"},{name:"SIGVTALRM",number:26,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGPROF",number:27,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGWINCH",number:28,action:"ignore",description:"Terminal window size changed",standard:"bsd"},{name:"SIGIO",number:29,action:"terminate",description:"I/O is available",standard:"other"},{name:"SIGPOLL",number:29,action:"terminate",description:"Watched event",standard:"other"},{name:"SIGINFO",number:29,action:"ignore",description:"Request for process information",standard:"other"},{name:"SIGPWR",number:30,action:"terminate",description:"Device running out of power",standard:"systemv"},{name:"SIGSYS",number:31,action:"core",description:"Invalid system call",standard:"other"},{name:"SIGUNUSED",number:31,action:"terminate",description:"Invalid system call",standard:"other"}];var Yo=()=>{let e=Tc();return[...vc,...e].map(Wh)},Wh=({name:e,number:t,description:r,action:n,forced:o=!1,standard:i})=>{let{signals:{[e]:s}}=Rc.constants,u=s!==void 0;return{name:e,number:u?s:t,description:r,supported:u,action:n,forced:o,standard:i}};var Yh=()=>{let e=Yo();return Object.fromEntries(e.map(qh))},qh=({name:e,number:t,description:r,supported:n,action:o,forced:i,standard:s})=>[e,{name:e,number:t,description:r,supported:n,action:o,forced:i,standard:s}],Mc=Yh(),Vh=()=>{let e=Yo(),t=65,r=Array.from({length:t},(n,o)=>Hh(o,e));return Object.assign({},...r)},Hh=(e,t)=>{let r=Kh(e,t);if(r===void 0)return{};let{name:n,description:o,supported:i,action:s,forced:u,standard:a}=r;return{[e]:{name:n,number:e,description:o,supported:i,action:s,forced:u,standard:a}}},Kh=(e,t)=>{let r=t.find(({name:n})=>Ic.constants.signals[n]===e);return r!==void 0?r:t.find(n=>n.number===e)},ew=Vh();var kc=e=>{let t="option `killSignal`";if(e===0)throw new TypeError(`Invalid ${t}: 0 cannot be used.`);return Nc(e,t)},Lc=e=>e===0?e:Nc(e,"`subprocess.kill()`'s argument"),Nc=(e,t)=>{if(Number.isInteger(e))return Jh(e,t);if(typeof e=="string")return Zh(e,t);throw new TypeError(`Invalid ${t} ${String(e)}: it must be a string or an integer.
${qo()}`)},Jh=(e,t)=>{if(Pc.has(e))return Pc.get(e);throw new TypeError(`Invalid ${t} ${e}: this signal integer does not exist.
${qo()}`)},Xh=()=>new Map(Object.entries(Ve.constants.signals).reverse().map(([e,t])=>[t,e])),Pc=Xh(),Zh=(e,t)=>{if(e in Ve.constants.signals)return e;throw e.toUpperCase()in Ve.constants.signals?new TypeError(`Invalid ${t} '${e}': please rename it to '${e.toUpperCase()}'.`):new TypeError(`Invalid ${t} '${e}': this signal name does not exist.
${qo()}`)},qo=()=>`Available signal names: ${Qh()}.
Available signal numbers: ${eF()}.`,Qh=()=>Object.keys(Ve.constants.signals).sort().map(e=>`'${e}'`).join(", "),eF=()=>[...new Set(Object.values(Ve.constants.signals).sort((e,t)=>e-t))].join(", "),Tr=e=>Mc[e].description;var jc=e=>{if(e===!1)return e;if(e===!0)return tF;if(!Number.isFinite(e)||e<0)throw new TypeError(`Expected the \`forceKillAfterDelay\` option to be a non-negative integer, got \`${e}\` (${typeof e})`);return e},tF=1e3*5,Uc=({kill:e,options:{forceKillAfterDelay:t,killSignal:r},onInternalError:n,context:o,controller:i},s,u)=>{let{signal:a,error:c}=rF(s,u,r);nF(c,n);let l=e(a);return oF({kill:e,signal:a,forceKillAfterDelay:t,killSignal:r,killResult:l,context:o,controller:i}),l},rF=(e,t,r)=>{let[n=r,o]=Br(e)?[void 0,e]:[e,t];if(typeof n!="string"&&!Number.isInteger(n))throw new TypeError(`The first argument must be an error instance or a signal name string/integer: ${String(n)}`);if(o!==void 0&&!Br(o))throw new TypeError(`The second argument is optional. If specified, it must be an error instance: ${o}`);return{signal:Lc(n),error:o}},nF=(e,t)=>{e!==void 0&&t.reject(e)},oF=async({kill:e,signal:t,forceKillAfterDelay:r,killSignal:n,killResult:o,context:i,controller:s})=>{t===n&&o&&Vo({kill:e,forceKillAfterDelay:r,context:i,controllerSignal:s.signal})},Vo=async({kill:e,forceKillAfterDelay:t,context:r,controllerSignal:n})=>{if(t!==!1)try{await(0,$c.setTimeout)(t,void 0,{signal:n}),e("SIGKILL")&&(r.isForcefullyTerminated??=!0)}catch{}};var Gc=require("node:events"),_r=async(e,t)=>{e.aborted||await(0,Gc.once)(e,"abort",{signal:t})};var zc=({cancelSignal:e})=>{if(e!==void 0&&Object.prototype.toString.call(e)!=="[object AbortSignal]")throw new Error(`The \`cancelSignal\` option must be an AbortSignal: ${String(e)}`)},Wc=({subprocess:e,cancelSignal:t,gracefulCancel:r,context:n,controller:o})=>t===void 0||r?[]:[iF(e,t,n,o)],iF=async(e,t,r,{signal:n})=>{throw await _r(t,n),r.terminationReason??="cancel",e.kill(),t.reason};var wl=require("node:timers/promises");var yl=require("node:util");var He=({methodName:e,isSubprocess:t,ipc:r,isConnected:n})=>{sF(e,t,r),Ho(e,t,n)},sF=(e,t,r)=>{if(!r)throw new Error(`${K(e,t)} can only be used if the \`ipc\` option is \`true\`.`)},Ho=(e,t,r)=>{if(!r)throw new Error(`${K(e,t)} cannot be used: the ${me(t)} has already exited or disconnected.`)},Yc=e=>{throw new Error(`${K("getOneMessage",e)} could not complete: the ${me(e)} exited or disconnected.`)},qc=e=>{throw new Error(`${K("sendMessage",e)} failed: the ${me(e)} is sending a message too, instead of listening to incoming messages.
This can be fixed by both sending a message and listening to incoming messages at the same time:

const [receivedMessage] = await Promise.all([
	${K("getOneMessage",e)},
	${K("sendMessage",e,"message, {strict: true}")},
]);`)},Or=(e,t)=>new Error(`${K("sendMessage",t)} failed when sending an acknowledgment response to the ${me(t)}.`,{cause:e}),Vc=e=>{throw new Error(`${K("sendMessage",e)} failed: the ${me(e)} is not listening to incoming messages.`)},Hc=e=>{throw new Error(`${K("sendMessage",e)} failed: the ${me(e)} exited without listening to incoming messages.`)},Kc=()=>new Error(`\`cancelSignal\` aborted: the ${me(!0)} disconnected.`),Jc=()=>{throw new Error("`getCancelSignal()` cannot be used without setting the `cancelSignal` subprocess option.")},Xc=({error:e,methodName:t,isSubprocess:r})=>{if(e.code==="EPIPE")throw new Error(`${K(t,r)} cannot be used: the ${me(r)} is disconnecting.`,{cause:e})},Zc=({error:e,methodName:t,isSubprocess:r,message:n})=>{if(uF(e))throw new Error(`${K(t,r)}'s argument type is invalid: the message cannot be serialized: ${String(n)}.`,{cause:e})},uF=({code:e,message:t})=>aF.has(e)||cF.some(r=>t.includes(r)),aF=new Set(["ERR_MISSING_ARGS","ERR_INVALID_ARG_TYPE"]),cF=["could not be cloned","circular structure","call stack size exceeded"],K=(e,t,r="")=>e==="cancelSignal"?"`cancelSignal`'s `controller.abort()`":`${lF(t)}${e}(${r})`,lF=e=>e?"":"subprocess.",me=e=>e?"parent process":"subprocess",Ke=e=>{e.connected&&e.disconnect()};var Z=()=>{let e={},t=new Promise((r,n)=>{Object.assign(e,{resolve:r,reject:n})});return Object.assign(t,e)};var Rr=(e,t="stdin")=>{let{options:n,fileDescriptors:o}=Q.get(e),i=Qc(o,t,!0),s=e.stdio[i];if(s===null)throw new TypeError(el(i,t,n,!0));return s},Je=(e,t="stdout")=>{let{options:n,fileDescriptors:o}=Q.get(e),i=Qc(o,t,!1),s=i==="all"?e.all:e.stdio[i];if(s==null)throw new TypeError(el(i,t,n,!1));return s},Q=new WeakMap,Qc=(e,t,r)=>{let n=DF(t,r);return fF(n,t,r,e),n},DF=(e,t)=>{let r=_o(e);if(r!==void 0)return r;let{validOptions:n,defaultValue:o}=t?{validOptions:'"stdin"',defaultValue:"stdin"}:{validOptions:'"stdout", "stderr", "all"',defaultValue:"stdout"};throw new TypeError(`"${Bt(t)}" must not be "${e}".
It must be ${n} or "fd3", "fd4" (and so on).
It is optional and defaults to "${o}".`)},fF=(e,t,r,n)=>{let o=n[tl(e)];if(o===void 0)throw new TypeError(`"${Bt(r)}" must not be ${t}. That file descriptor does not exist.
Please set the "stdio" option to ensure that file descriptor exists.`);if(o.direction==="input"&&!r)throw new TypeError(`"${Bt(r)}" must not be ${t}. It must be a readable stream, not writable.`);if(o.direction!=="input"&&r)throw new TypeError(`"${Bt(r)}" must not be ${t}. It must be a writable stream, not readable.`)},el=(e,t,r,n)=>{if(e==="all"&&!r.all)return`The "all" option must be true to use "from: 'all'".`;let{optionName:o,optionValue:i}=pF(e,r);return`The "${o}: ${vr(i)}" option is incompatible with using "${Bt(n)}: ${vr(t)}".
Please set this option with "pipe" instead.`},pF=(e,{stdin:t,stdout:r,stderr:n,stdio:o})=>{let i=tl(e);return i===0&&t!==void 0?{optionName:"stdin",optionValue:t}:i===1&&r!==void 0?{optionName:"stdout",optionValue:r}:i===2&&n!==void 0?{optionName:"stderr",optionValue:n}:{optionName:`stdio[${i}]`,optionValue:o[i]}},tl=e=>e==="all"?1:e,Bt=e=>e?"to":"from",vr=e=>typeof e=="string"?`'${e}'`:typeof e=="number"?`${e}`:"Stream";var dl=require("node:events");var rl=require("node:events"),we=(e,t,r)=>{let n=e.getMaxListeners();n===0||n===Number.POSITIVE_INFINITY||(e.setMaxListeners(n+t),(0,rl.addAbortListener)(r,()=>{e.setMaxListeners(e.getMaxListeners()-t)}))};var pl=require("node:events");var il=require("node:events"),sl=require("node:timers/promises");var Ir=(e,t)=>{t&&Ko(e)},Ko=e=>{e.refCounted()},Mr=(e,t)=>{t&&Jo(e)},Jo=e=>{e.unrefCounted()},nl=(e,t)=>{t&&(Jo(e),Jo(e))},ol=(e,t)=>{t&&(Ko(e),Ko(e))};var ul=async({anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n},o)=>{if(ll(o)||fl(o))return;Pr.has(e)||Pr.set(e,[]);let i=Pr.get(e);if(i.push(o),!(i.length>1))for(;i.length>0;){await Dl(e,n,o),await sl.scheduler.yield();let s=await cl({wrappedMessage:i[0],anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n});i.shift(),n.emit("message",s),n.emit("message:done")}},al=async({anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n,boundOnMessage:o})=>{Xo();let i=Pr.get(e);for(;i?.length>0;)await(0,il.once)(n,"message:done");e.removeListener("message",o),ol(t,r),n.connected=!1,n.emit("disconnect")},Pr=new WeakMap;var he=(e,t,r)=>{if(kr.has(e))return kr.get(e);let n=new pl.EventEmitter;return n.connected=!0,kr.set(e,n),dF({ipcEmitter:n,anyProcess:e,channel:t,isSubprocess:r}),n},kr=new WeakMap,dF=({ipcEmitter:e,anyProcess:t,channel:r,isSubprocess:n})=>{let o=ul.bind(void 0,{anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:e});t.on("message",o),t.once("disconnect",al.bind(void 0,{anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:e,boundOnMessage:o})),nl(r,n)},Lr=e=>{let t=kr.get(e);return t===void 0?e.channel!==null:t.connected};var ml=({anyProcess:e,channel:t,isSubprocess:r,message:n,strict:o})=>{if(!o)return n;let i=he(e,t,r),s=jr(e,i);return{id:mF++,type:$r,message:n,hasListeners:s}},mF=0n,hl=(e,t)=>{if(!(t?.type!==$r||t.hasListeners))for(let{id:r}of e)r!==void 0&&Nr[r].resolve({isDeadlock:!0,hasListeners:!1})},cl=async({wrappedMessage:e,anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:o})=>{if(e?.type!==$r||!t.connected)return e;let{id:i,message:s}=e,u={id:i,type:gl,message:jr(t,o)};try{await Ur({anyProcess:t,channel:r,isSubprocess:n,ipc:!0},u)}catch(a){o.emit("strict:error",a)}return s},ll=e=>{if(e?.type!==gl)return!1;let{id:t,message:r}=e;return Nr[t]?.resolve({isDeadlock:!1,hasListeners:r}),!0},Fl=async(e,t,r)=>{if(e?.type!==$r)return;let n=Z();Nr[e.id]=n;let o=new AbortController;try{let{isDeadlock:i,hasListeners:s}=await Promise.race([n,hF(t,r,o)]);i&&qc(r),s||Vc(r)}finally{o.abort(),delete Nr[e.id]}},Nr={},hF=async(e,t,{signal:r})=>{we(e,1,r),await(0,dl.once)(e,"disconnect",{signal:r}),Hc(t)},$r="execa:ipc:request",gl="execa:ipc:response";var El=(e,t,r)=>{Tt.has(e)||Tt.set(e,new Set);let n=Tt.get(e),o=Z(),i=r?t.id:void 0,s={onMessageSent:o,id:i};return n.add(s),{outgoingMessages:n,outgoingMessage:s}},Cl=({outgoingMessages:e,outgoingMessage:t})=>{e.delete(t),t.onMessageSent.resolve()},Dl=async(e,t,r)=>{for(;!jr(e,t)&&Tt.get(e)?.size>0;){let n=[...Tt.get(e)];hl(n,r),await Promise.all(n.map(({onMessageSent:o})=>o))}},Tt=new WeakMap,jr=(e,t)=>t.listenerCount("message")>FF(e),FF=e=>Q.has(e)&&!ue(Q.get(e).options.buffer,"ipc")?1:0;var Ur=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},o,{strict:i=!1}={})=>{let s="sendMessage";return He({methodName:s,isSubprocess:r,ipc:n,isConnected:e.connected}),gF({anyProcess:e,channel:t,methodName:s,isSubprocess:r,message:o,strict:i})},gF=async({anyProcess:e,channel:t,methodName:r,isSubprocess:n,message:o,strict:i})=>{let s=ml({anyProcess:e,channel:t,isSubprocess:n,message:o,strict:i}),u=El(e,s,i);try{await Qo({anyProcess:e,methodName:r,isSubprocess:n,wrappedMessage:s,message:o})}catch(a){throw Ke(e),a}finally{Cl(u)}},Qo=async({anyProcess:e,methodName:t,isSubprocess:r,wrappedMessage:n,message:o})=>{let i=EF(e);try{await Promise.all([Fl(n,e,r),i(n)])}catch(s){throw Xc({error:s,methodName:t,isSubprocess:r}),Zc({error:s,methodName:t,isSubprocess:r,message:o}),s}},EF=e=>{if(Zo.has(e))return Zo.get(e);let t=(0,yl.promisify)(e.send.bind(e));return Zo.set(e,t),t},Zo=new WeakMap;var xl=(e,t)=>{let r="cancelSignal";return Ho(r,!1,e.connected),Qo({anyProcess:e,methodName:r,isSubprocess:!1,wrappedMessage:{type:Al,message:t},message:t})},Sl=async({anyProcess:e,channel:t,isSubprocess:r,ipc:n})=>(await CF({anyProcess:e,channel:t,isSubprocess:r,ipc:n}),ei.signal),CF=async({anyProcess:e,channel:t,isSubprocess:r,ipc:n})=>{if(!bl){if(bl=!0,!n){Jc();return}if(t===null){Xo();return}he(e,t,r),await wl.scheduler.yield()}},bl=!1,fl=e=>e?.type!==Al?!1:(ei.abort(e.message),!0),Al="execa:ipc:cancel",Xo=()=>{ei.abort(Kc())},ei=new AbortController;var Bl=({gracefulCancel:e,cancelSignal:t,ipc:r,serialization:n})=>{if(e){if(t===void 0)throw new Error("The `cancelSignal` option must be defined when setting the `gracefulCancel` option.");if(!r)throw new Error("The `ipc` option cannot be false when setting the `gracefulCancel` option.");if(n==="json")throw new Error("The `serialization` option cannot be 'json' when setting the `gracefulCancel` option.")}},Tl=({subprocess:e,cancelSignal:t,gracefulCancel:r,forceKillAfterDelay:n,context:o,controller:i})=>r?[yF({subprocess:e,cancelSignal:t,forceKillAfterDelay:n,context:o,controller:i})]:[],yF=async({subprocess:e,cancelSignal:t,forceKillAfterDelay:r,context:n,controller:{signal:o}})=>{await _r(t,o);let i=bF(t);throw await xl(e,i),Vo({kill:e.kill,forceKillAfterDelay:r,context:n,controllerSignal:o}),n.terminationReason??="gracefulCancel",t.reason},bF=({reason:e})=>{if(!(e instanceof DOMException))return e;let t=new Error(e.message);return Object.defineProperty(t,"stack",{value:e.stack,enumerable:!1,configurable:!0,writable:!0}),t};var _l=require("node:timers/promises");var Ol=({timeout:e})=>{if(e!==void 0&&(!Number.isFinite(e)||e<0))throw new TypeError(`Expected the \`timeout\` option to be a non-negative integer, got \`${e}\` (${typeof e})`)},vl=(e,t,r,n)=>t===0||t===void 0?[]:[wF(e,t,r,n)],wF=async(e,t,r,{signal:n})=>{throw await(0,_l.setTimeout)(t,void 0,{signal:n}),r.terminationReason??="timeout",e.kill(),new H};var Gr=require("node:process"),ti=F(require("node:path"),1);var Rl=({options:e})=>{if(e.node===!1)throw new TypeError('The "node" option cannot be false with `execaNode()`.');return{options:{...e,node:!0}}},Il=(e,t,{node:r=!1,nodePath:n=Gr.execPath,nodeOptions:o=Gr.execArgv.filter(a=>!a.startsWith("--inspect")),cwd:i,execPath:s,...u})=>{if(s!==void 0)throw new TypeError('The "execPath" option has been removed. Please use the "nodePath" option instead.');let a=Ge(n,'The "nodePath" option'),c=ti.default.resolve(i,a),l={...u,nodePath:c,node:r,cwd:i};if(!r)return[e,t,l];if(ti.default.basename(e,".exe")==="node")throw new TypeError('When the "node" option is true, the first argument does not need to be "node".');return[c,[...o,e,...t],{ipc:!0,...l,shell:!1}]};var Ml=require("node:v8"),Pl=({ipcInput:e,ipc:t,serialization:r})=>{if(e!==void 0){if(!t)throw new Error("The `ipcInput` option cannot be set unless the `ipc` option is `true`.");AF[r](e)}},xF=e=>{try{(0,Ml.serialize)(e)}catch(t){throw new Error("The `ipcInput` option is not serializable with a structured clone.",{cause:t})}},SF=e=>{try{JSON.stringify(e)}catch(t){throw new Error("The `ipcInput` option is not serializable with JSON.",{cause:t})}},AF={advanced:xF,json:SF},kl=async(e,t)=>{t!==void 0&&await e.sendMessage(t)};var Nl=({encoding:e})=>{if(ri.has(e))return;let t=TF(e);if(t!==void 0)throw new TypeError(`Invalid option \`encoding: ${zr(e)}\`.
Please rename it to ${zr(t)}.`);let r=[...ri].map(n=>zr(n)).join(", ");throw new TypeError(`Invalid option \`encoding: ${zr(e)}\`.
Please rename it to one of: ${r}.`)},BF=new Set(["utf8","utf16le"]),k=new Set(["buffer","hex","base64","base64url","latin1","ascii"]),ri=new Set([...BF,...k]),TF=e=>{if(e===null)return"buffer";if(typeof e!="string")return;let t=e.toLowerCase();if(t in Ll)return Ll[t];if(ri.has(t))return t},Ll={"utf-8":"utf8","utf-16le":"utf16le","ucs-2":"utf16le",ucs2:"utf16le",binary:"latin1"},zr=e=>typeof e=="string"?`"${e}"`:String(e);var $l=require("node:fs"),jl=F(require("node:path"),1),Ul=F(require("node:process"),1);var Gl=(e=zl())=>{let t=Ge(e,'The "cwd" option');return jl.default.resolve(t)},zl=()=>{try{return Ul.default.cwd()}catch(e){throw e.message=`The current directory does not exist.
${e.message}`,e}},Wl=(e,t)=>{if(t===zl())return e;let r;try{r=(0,$l.statSync)(t)}catch(n){return`The "cwd" option is invalid: ${t}.
${n.message}
${e}`}return r.isDirectory()?e:`The "cwd" option is not a directory: ${t}.
${e}`};var Wr=(e,t,r)=>{r.cwd=Gl(r.cwd);let[n,o,i]=Il(e,t,r),{command:s,args:u,options:a}=ql.default._parse(n,o,i),c=pa(a),l=_F(c);return Ol(l),Nl(l),Pl(l),zc(l),Bl(l),l.shell=Ao(l.shell),l.env=OF(l),l.killSignal=kc(l.killSignal),l.forceKillAfterDelay=jc(l.forceKillAfterDelay),l.lines=l.lines.map((D,f)=>D&&!k.has(l.encoding)&&l.buffer[f]),ni.default.platform==="win32"&&Yl.default.basename(s,".exe")==="cmd"&&u.unshift("/q"),{file:s,commandArguments:u,options:l}},_F=({extendEnv:e=!0,preferLocal:t=!1,cwd:r,localDir:n=r,encoding:o="utf8",reject:i=!0,cleanup:s=!0,all:u=!1,windowsHide:a=!0,killSignal:c="SIGTERM",forceKillAfterDelay:l=!0,gracefulCancel:D=!1,ipcInput:f,ipc:p=f!==void 0||D,serialization:m="advanced",...y})=>({...y,extendEnv:e,preferLocal:t,cwd:r,localDirectory:n,encoding:o,reject:i,cleanup:s,all:u,windowsHide:a,killSignal:c,forceKillAfterDelay:l,gracefulCancel:D,ipcInput:f,ipc:p,serialization:m}),OF=({env:e,extendEnv:t,preferLocal:r,node:n,localDirectory:o,nodePath:i})=>{let s=t?{...ni.default.env,...e}:e;return r||n?wc({env:s,cwd:o,execPath:i,preferLocal:r,addExecPath:n}):s};var Yr=(e,t,r)=>r.shell&&t.length>0?[[e,...t].join(" "),[],r]:[e,t,r];var dD=require("node:util");function Xe(e){if(typeof e=="string")return vF(e);if(!(ArrayBuffer.isView(e)&&e.BYTES_PER_ELEMENT===1))throw new Error("Input must be a string or a Uint8Array");return RF(e)}var vF=e=>e.at(-1)===Vl?e.slice(0,e.at(-2)===Hl?-2:-1):e,RF=e=>e.at(-1)===IF?e.subarray(0,e.at(-2)===MF?-2:-1):e,Vl=`
`,IF=Vl.codePointAt(0),Hl="\r",MF=Hl.codePointAt(0);var sD=require("node:events"),uD=require("node:stream/promises");function J(e,{checkOpen:t=!0}={}){return e!==null&&typeof e=="object"&&(e.writable||e.readable||!t||e.writable===void 0&&e.readable===void 0)&&typeof e.pipe=="function"}function oi(e,{checkOpen:t=!0}={}){return J(e,{checkOpen:t})&&(e.writable||!t)&&typeof e.write=="function"&&typeof e.end=="function"&&typeof e.writable=="boolean"&&typeof e.writableObjectMode=="boolean"&&typeof e.destroy=="function"&&typeof e.destroyed=="boolean"}function xe(e,{checkOpen:t=!0}={}){return J(e,{checkOpen:t})&&(e.readable||!t)&&typeof e.read=="function"&&typeof e.readable=="boolean"&&typeof e.readableObjectMode=="boolean"&&typeof e.destroy=="function"&&typeof e.destroyed=="boolean"}function ii(e,t){return oi(e,t)&&xe(e,t)}var PF=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype),si=class{#r;#o;#e=!1;#n=void 0;constructor(t,r){this.#r=t,this.#o=r}next(){let t=()=>this.#u();return this.#n=this.#n?this.#n.then(t,t):t(),this.#n}return(t){let r=()=>this.#t(t);return this.#n?this.#n.then(r,r):r()}async#u(){if(this.#e)return{done:!0,value:void 0};let t;try{t=await this.#r.read()}catch(r){throw this.#n=void 0,this.#e=!0,this.#r.releaseLock(),r}return t.done&&(this.#n=void 0,this.#e=!0,this.#r.releaseLock()),t}async#t(t){if(this.#e)return{done:!0,value:t};if(this.#e=!0,!this.#o){let r=this.#r.cancel(t);return this.#r.releaseLock(),await r,{done:!0,value:t}}return this.#r.releaseLock(),{done:!0,value:t}}},ui=Symbol();function Kl(){return this[ui].next()}Object.defineProperty(Kl,"name",{value:"next"});function Jl(e){return this[ui].return(e)}Object.defineProperty(Jl,"name",{value:"return"});var kF=Object.create(PF,{next:{enumerable:!0,configurable:!0,writable:!0,value:Kl},return:{enumerable:!0,configurable:!0,writable:!0,value:Jl}});function ai({preventCancel:e=!1}={}){let t=this.getReader(),r=new si(t,e),n=Object.create(kF);return n[ui]=r,n}var Xl=e=>{if(xe(e,{checkOpen:!1})&&_t.on!==void 0)return NF(e);if(typeof e?.[Symbol.asyncIterator]=="function")return e;if(LF.call(e)==="[object ReadableStream]")return ai.call(e);throw new TypeError("The first argument must be a Readable, a ReadableStream, or an async iterable.")},{toString:LF}=Object.prototype,NF=async function*(e){let t=new AbortController,r={};$F(e,t,r);try{for await(let[n]of _t.on(e,"data",{signal:t.signal}))yield n}catch(n){if(r.error!==void 0)throw r.error;if(!t.signal.aborted)throw n}finally{e.destroy()}},$F=async(e,t,r)=>{try{await _t.finished(e,{cleanup:!0,readable:!0,writable:!1,error:!1})}catch(n){r.error=n}finally{t.abort()}},_t={};var Ze=async(e,{init:t,convertChunk:r,getSize:n,truncateChunk:o,addChunk:i,getFinalChunk:s,finalize:u},{maxBuffer:a=Number.POSITIVE_INFINITY}={})=>{let c=Xl(e),l=t();l.length=0;try{for await(let D of c){let f=UF(D),p=r[f](D,l);eD({convertedChunk:p,state:l,getSize:n,truncateChunk:o,addChunk:i,maxBuffer:a})}return jF({state:l,convertChunk:r,getSize:n,truncateChunk:o,addChunk:i,getFinalChunk:s,maxBuffer:a}),u(l)}catch(D){let f=typeof D=="object"&&D!==null?D:new Error(D);throw f.bufferedData=u(l),f}},jF=({state:e,getSize:t,truncateChunk:r,addChunk:n,getFinalChunk:o,maxBuffer:i})=>{let s=o(e);s!==void 0&&eD({convertedChunk:s,state:e,getSize:t,truncateChunk:r,addChunk:n,maxBuffer:i})},eD=({convertedChunk:e,state:t,getSize:r,truncateChunk:n,addChunk:o,maxBuffer:i})=>{let s=r(e),u=t.length+s;if(u<=i){Zl(e,t,o,u);return}let a=n(e,i-t.length);throw a!==void 0&&Zl(a,t,o,i),new ee},Zl=(e,t,r,n)=>{t.contents=r(e,t,n),t.length=n},UF=e=>{let t=typeof e;if(t==="string")return"string";if(t!=="object"||e===null)return"others";if(globalThis.Buffer?.isBuffer(e))return"buffer";let r=Ql.call(e);return r==="[object ArrayBuffer]"?"arrayBuffer":r==="[object DataView]"?"dataView":Number.isInteger(e.byteLength)&&Number.isInteger(e.byteOffset)&&Ql.call(e.buffer)==="[object ArrayBuffer]"?"typedArray":"others"},{toString:Ql}=Object.prototype,ee=class extends Error{name="MaxBufferError";constructor(){super("maxBuffer exceeded")}};var ae=e=>e,Ot=()=>{},qr=({contents:e})=>e,Vr=e=>{throw new Error(`Streams in object mode are not supported: ${String(e)}`)},Hr=e=>e.length;async function Kr(e,t){return Ze(e,YF,t)}var GF=()=>({contents:[]}),zF=()=>1,WF=(e,{contents:t})=>(t.push(e),t),YF={init:GF,convertChunk:{string:ae,buffer:ae,arrayBuffer:ae,dataView:ae,typedArray:ae,others:ae},getSize:zF,truncateChunk:Ot,addChunk:WF,getFinalChunk:Ot,finalize:qr};async function Jr(e,t){return Ze(e,eg,t)}var qF=()=>({contents:new ArrayBuffer(0)}),VF=e=>HF.encode(e),HF=new TextEncoder,tD=e=>new Uint8Array(e),rD=e=>new Uint8Array(e.buffer,e.byteOffset,e.byteLength),KF=(e,t)=>e.slice(0,t),JF=(e,{contents:t,length:r},n)=>{let o=iD()?ZF(t,n):XF(t,n);return new Uint8Array(o).set(e,r),o},XF=(e,t)=>{if(t<=e.byteLength)return e;let r=new ArrayBuffer(oD(t));return new Uint8Array(r).set(new Uint8Array(e),0),r},ZF=(e,t)=>{if(t<=e.maxByteLength)return e.resize(t),e;let r=new ArrayBuffer(t,{maxByteLength:oD(t)});return new Uint8Array(r).set(new Uint8Array(e),0),r},oD=e=>nD**Math.ceil(Math.log(e)/Math.log(nD)),nD=2,QF=({contents:e,length:t})=>iD()?e:e.slice(0,t),iD=()=>"resize"in ArrayBuffer.prototype,eg={init:qF,convertChunk:{string:VF,buffer:tD,arrayBuffer:tD,dataView:rD,typedArray:rD,others:Vr},getSize:Hr,truncateChunk:KF,addChunk:JF,getFinalChunk:Ot,finalize:QF};async function Zr(e,t){return Ze(e,ig,t)}var tg=()=>({contents:"",textDecoder:new TextDecoder}),Xr=(e,{textDecoder:t})=>t.decode(e,{stream:!0}),rg=(e,{contents:t})=>t+e,ng=(e,t)=>e.slice(0,t),og=({textDecoder:e})=>{let t=e.decode();return t===""?void 0:t},ig={init:tg,convertChunk:{string:ae,buffer:Xr,arrayBuffer:Xr,dataView:Xr,typedArray:Xr,others:Vr},getSize:Hr,truncateChunk:ng,addChunk:rg,getFinalChunk:og,finalize:qr};Object.assign(_t,{on:sD.on,finished:uD.finished});var aD=({error:e,stream:t,readableObjectMode:r,lines:n,encoding:o,fdNumber:i})=>{if(!(e instanceof ee))throw e;if(i==="all")return e;let s=sg(r,n,o);throw e.maxBufferInfo={fdNumber:i,unit:s},t.destroy(),e},sg=(e,t,r)=>e?"objects":t?"lines":r==="buffer"?"bytes":"characters",cD=(e,t,r)=>{if(t.length!==r)return;let n=new ee;throw n.maxBufferInfo={fdNumber:"ipc"},n},lD=(e,t)=>{let{streamName:r,threshold:n,unit:o}=ug(e,t);return`Command's ${r} was larger than ${n} ${o}`},ug=(e,t)=>{if(e?.maxBufferInfo===void 0)return{streamName:"output",threshold:t[1],unit:"bytes"};let{maxBufferInfo:{fdNumber:r,unit:n}}=e;delete e.maxBufferInfo;let o=ue(t,r);return r==="ipc"?{streamName:"IPC output",threshold:o,unit:"messages"}:{streamName:mr(r),threshold:o,unit:n}},DD=(e,t,r)=>e?.code==="ENOBUFS"&&t!==null&&t.some(n=>n!==null&&n.length>Qr(r)),fD=(e,t,r)=>{if(!t)return e;let n=Qr(r);return e.length>n?e.slice(0,n):e},Qr=([,e])=>e;var mD=({stdio:e,all:t,ipcOutput:r,originalError:n,signal:o,signalDescription:i,exitCode:s,escapedCommand:u,timedOut:a,isCanceled:c,isGracefullyCanceled:l,isMaxBuffer:D,isForcefullyTerminated:f,forceKillAfterDelay:p,killSignal:m,maxBuffer:y,timeout:v,cwd:x})=>{let I=n?.code,P=ag({originalError:n,timedOut:a,timeout:v,isMaxBuffer:D,maxBuffer:y,errorCode:I,signal:o,signalDescription:i,exitCode:s,isCanceled:c,isGracefullyCanceled:l,isForcefullyTerminated:f,forceKillAfterDelay:p,killSignal:m}),L=lg(n,x),ne=L===void 0?"":`
${L}`,le=`${P}: ${u}${ne}`,Ce=t===void 0?[e[2],e[1]]:[t],ve=[le,...Ce,...e.slice(3),r.map(De=>Dg(De)).join(`
`)].map(De=>wt(Xe(fg(De)))).filter(Boolean).join(`

`);return{originalMessage:L,shortMessage:le,message:ve}},ag=({originalError:e,timedOut:t,timeout:r,isMaxBuffer:n,maxBuffer:o,errorCode:i,signal:s,signalDescription:u,exitCode:a,isCanceled:c,isGracefullyCanceled:l,isForcefullyTerminated:D,forceKillAfterDelay:f,killSignal:p})=>{let m=cg(D,f);return t?`Command timed out after ${r} milliseconds${m}`:l?s===void 0?`Command was gracefully canceled with exit code ${a}`:D?`Command was gracefully canceled${m}`:`Command was gracefully canceled with ${s} (${u})`:c?`Command was canceled${m}`:n?`${lD(e,o)}${m}`:i!==void 0?`Command failed with ${i}${m}`:D?`Command was killed with ${p} (${Tr(p)})${m}`:s!==void 0?`Command was killed with ${s} (${u})`:a!==void 0?`Command failed with exit code ${a}`:"Command failed"},cg=(e,t)=>e?` and was forcefully terminated after ${t} milliseconds`:"",lg=(e,t)=>{if(e instanceof H)return;let r=Ac(e)?e.originalMessage:String(e?.message??e),n=wt(Wl(r,t));return n===""?void 0:n},Dg=e=>typeof e=="string"?e:(0,dD.inspect)(e),fg=e=>Array.isArray(e)?e.map(t=>Xe(pD(t))).filter(Boolean).join(`
`):pD(e),pD=e=>typeof e=="string"?e:R(e)?fr(e):"";var en=({command:e,escapedCommand:t,stdio:r,all:n,ipcOutput:o,options:{cwd:i},startTime:s})=>hD({command:e,escapedCommand:t,cwd:i,durationMs:Io(s),failed:!1,timedOut:!1,isCanceled:!1,isGracefullyCanceled:!1,isTerminated:!1,isMaxBuffer:!1,isForcefullyTerminated:!1,exitCode:0,stdout:r[1],stderr:r[2],all:n,stdio:r,ipcOutput:o,pipedFrom:[]}),Qe=({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,isSync:s})=>vt({error:e,command:t,escapedCommand:r,startTime:i,timedOut:!1,isCanceled:!1,isGracefullyCanceled:!1,isMaxBuffer:!1,isForcefullyTerminated:!1,stdio:Array.from({length:n.length}),ipcOutput:[],options:o,isSync:s}),vt=({error:e,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:u,isForcefullyTerminated:a,exitCode:c,signal:l,stdio:D,all:f,ipcOutput:p,options:{timeoutDuration:m,timeout:y=m,forceKillAfterDelay:v,killSignal:x,cwd:I,maxBuffer:P},isSync:L})=>{let{exitCode:ne,signal:le,signalDescription:Ce}=dg(c,l),{originalMessage:ve,shortMessage:De,message:On}=mD({stdio:D,all:f,ipcOutput:p,originalError:e,signal:le,signalDescription:Ce,exitCode:ne,escapedCommand:r,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:u,isForcefullyTerminated:a,forceKillAfterDelay:v,killSignal:x,maxBuffer:P,timeout:y,cwd:I}),st=xc(e,On,L);return Object.assign(st,pg({error:st,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:u,isForcefullyTerminated:a,exitCode:ne,signal:le,signalDescription:Ce,stdio:D,all:f,ipcOutput:p,cwd:I,originalMessage:ve,shortMessage:De})),st},pg=({error:e,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:u,isForcefullyTerminated:a,exitCode:c,signal:l,signalDescription:D,stdio:f,all:p,ipcOutput:m,cwd:y,originalMessage:v,shortMessage:x})=>hD({shortMessage:x,originalMessage:v,command:t,escapedCommand:r,cwd:y,durationMs:Io(n),failed:!0,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isTerminated:l!==void 0,isMaxBuffer:u,isForcefullyTerminated:a,exitCode:c,signal:l,signalDescription:D,code:e.cause?.code,stdout:f[1],stderr:f[2],all:p,stdio:f,ipcOutput:m,pipedFrom:[]}),hD=e=>Object.fromEntries(Object.entries(e).filter(([,t])=>t!==void 0)),dg=(e,t)=>{let r=e===null?void 0:e,n=t===null?void 0:t,o=n===void 0?void 0:Tr(t);return{exitCode:r,signal:n,signalDescription:o}};var FD=e=>Number.isFinite(e)?e:0;function mg(e){return{days:Math.trunc(e/864e5),hours:Math.trunc(e/36e5%24),minutes:Math.trunc(e/6e4%60),seconds:Math.trunc(e/1e3%60),milliseconds:Math.trunc(e%1e3),microseconds:Math.trunc(FD(e*1e3)%1e3),nanoseconds:Math.trunc(FD(e*1e6)%1e3)}}function hg(e){return{days:e/86400000n,hours:e/3600000n%24n,minutes:e/60000n%60n,seconds:e/1000n%60n,milliseconds:e%1000n,microseconds:0n,nanoseconds:0n}}function ci(e){switch(typeof e){case"number":{if(Number.isFinite(e))return mg(e);break}case"bigint":return hg(e)}throw new TypeError("Expected a finite number or bigint")}var Fg=e=>e===0||e===0n,gg=(e,t)=>t===1||t===1n?e:`${e}s`,Eg=1e-7,Cg=24n*60n*60n*1000n;function li(e,t){let r=typeof e=="bigint";if(!r&&!Number.isFinite(e))throw new TypeError("Expected a finite number or bigint");t={...t};let n=e<0?"-":"";e=e<0?-e:e,t.colonNotation&&(t.compact=!1,t.formatSubMilliseconds=!1,t.separateMilliseconds=!1,t.verbose=!1),t.compact&&(t.unitCount=1,t.secondsDecimalDigits=0,t.millisecondsDecimalDigits=0);let o=[],i=(l,D)=>{let f=Math.floor(l*10**D+Eg);return(Math.round(f)/10**D).toFixed(D)},s=(l,D,f,p)=>{if(!((o.length===0||!t.colonNotation)&&Fg(l)&&!(t.colonNotation&&f==="m"))){if(p??=String(l),t.colonNotation){let m=p.includes(".")?p.split(".")[0].length:p.length,y=o.length>0?2:1;p="0".repeat(Math.max(0,y-m))+p}else p+=t.verbose?" "+gg(D,l):f;o.push(p)}},u=ci(e),a=BigInt(u.days);if(t.hideYearAndDays?s(BigInt(a)*24n+BigInt(u.hours),"hour","h"):(t.hideYear?s(a,"day","d"):(s(a/365n,"year","y"),s(a%365n,"day","d")),s(Number(u.hours),"hour","h")),s(Number(u.minutes),"minute","m"),!t.hideSeconds)if(t.separateMilliseconds||t.formatSubMilliseconds||!t.colonNotation&&e<1e3){let l=Number(u.seconds),D=Number(u.milliseconds),f=Number(u.microseconds),p=Number(u.nanoseconds);if(s(l,"second","s"),t.formatSubMilliseconds)s(D,"millisecond","ms"),s(f,"microsecond","\xB5s"),s(p,"nanosecond","ns");else{let m=D+f/1e3+p/1e6,y=typeof t.millisecondsDecimalDigits=="number"?t.millisecondsDecimalDigits:0,v=m>=1?Math.round(m):Math.ceil(m),x=y?m.toFixed(y):v;s(Number.parseFloat(x),"millisecond","ms",x)}}else{let l=(r?Number(e%Cg):e)/1e3%60,D=typeof t.secondsDecimalDigits=="number"?t.secondsDecimalDigits:1,f=i(l,D),p=t.keepDecimalsOnWholeSeconds?f:f.replace(/\.0+$/,"");s(Number.parseFloat(p),"second","s",p)}if(o.length===0)return n+"0"+(t.verbose?" milliseconds":"ms");let c=t.colonNotation?":":" ";return typeof t.unitCount=="number"&&(o=o.slice(0,Math.max(t.unitCount,1))),n+o.join(c)}var gD=(e,t)=>{e.failed&&X({type:"error",verboseMessage:e.shortMessage,verboseInfo:t,result:e})};var ED=(e,t)=>{ze(t)&&(gD(e,t),yg(e,t))},yg=(e,t)=>{let r=`(done in ${li(e.durationMs)})`;X({type:"duration",verboseMessage:r,verboseInfo:t,result:e})};var et=(e,t,{reject:r})=>{if(ED(e,t),e.failed&&r)throw e;return e};var gi=require("node:fs");var bD=(e,t)=>Se(e)?"asyncGenerator":SD(e)?"generator":tn(e)?"fileUrl":Ag(e)?"filePath":_g(e)?"webStream":J(e,{checkOpen:!1})?"native":R(e)?"uint8Array":Og(e)?"asyncIterable":vg(e)?"iterable":pi(e)?wD({transform:e},t):Sg(e)?bg(e,t):"native",bg=(e,t)=>ii(e.transform,{checkOpen:!1})?wg(e,t):pi(e.transform)?wD(e,t):xg(e,t),wg=(e,t)=>(xD(e,t,"Duplex stream"),"duplex"),wD=(e,t)=>(xD(e,t,"web TransformStream"),"webTransform"),xD=({final:e,binary:t,objectMode:r},n,o)=>{CD(e,`${n}.final`,o),CD(t,`${n}.binary`,o),Di(r,`${n}.objectMode`)},CD=(e,t,r)=>{if(e!==void 0)throw new TypeError(`The \`${t}\` option can only be defined when using a generator, not a ${r}.`)},xg=({transform:e,final:t,binary:r,objectMode:n},o)=>{if(e!==void 0&&!yD(e))throw new TypeError(`The \`${o}.transform\` option must be a generator, a Duplex stream or a web TransformStream.`);if(ii(t,{checkOpen:!1}))throw new TypeError(`The \`${o}.final\` option must not be a Duplex stream.`);if(pi(t))throw new TypeError(`The \`${o}.final\` option must not be a web TransformStream.`);if(t!==void 0&&!yD(t))throw new TypeError(`The \`${o}.final\` option must be a generator.`);return Di(r,`${o}.binary`),Di(n,`${o}.objectMode`),Se(e)||Se(t)?"asyncGenerator":"generator"},Di=(e,t)=>{if(e!==void 0&&typeof e!="boolean")throw new TypeError(`The \`${t}\` option must use a boolean.`)},yD=e=>Se(e)||SD(e),Se=e=>Object.prototype.toString.call(e)==="[object AsyncGeneratorFunction]",SD=e=>Object.prototype.toString.call(e)==="[object GeneratorFunction]",Sg=e=>O(e)&&(e.transform!==void 0||e.final!==void 0),tn=e=>Object.prototype.toString.call(e)==="[object URL]",AD=e=>tn(e)&&e.protocol!=="file:",Ag=e=>O(e)&&Object.keys(e).length>0&&Object.keys(e).every(t=>Bg.has(t))&&fi(e.file),Bg=new Set(["file","append"]),fi=e=>typeof e=="string",BD=(e,t)=>e==="native"&&typeof t=="string"&&!Tg.has(t),Tg=new Set(["ipc","ignore","inherit","overlapped","pipe"]),TD=e=>Object.prototype.toString.call(e)==="[object ReadableStream]",rn=e=>Object.prototype.toString.call(e)==="[object WritableStream]",_g=e=>TD(e)||rn(e),pi=e=>TD(e?.readable)&&rn(e?.writable),Og=e=>_D(e)&&typeof e[Symbol.asyncIterator]=="function",vg=e=>_D(e)&&typeof e[Symbol.iterator]=="function",_D=e=>typeof e=="object"&&e!==null,z=new Set(["generator","asyncGenerator","duplex","webTransform"]),nn=new Set(["fileUrl","filePath","fileNumber"]),di=new Set(["fileUrl","filePath"]),OD=new Set([...di,"webStream","nodeStream"]),vD=new Set(["webTransform","duplex"]),Fe={generator:"a generator",asyncGenerator:"an async generator",fileUrl:"a file URL",filePath:"a file path string",fileNumber:"a file descriptor number",webStream:"a web stream",nodeStream:"a Node.js stream",webTransform:"a web TransformStream",duplex:"a Duplex stream",native:"any value",iterable:"an iterable",asyncIterable:"an async iterable",string:"a string",uint8Array:"a Uint8Array"};var mi=(e,t,r,n)=>n==="output"?Rg(e,t,r):Ig(e,t,r),Rg=(e,t,r)=>{let n=t!==0&&r[t-1].value.readableObjectMode;return{writableObjectMode:n,readableObjectMode:e??n}},Ig=(e,t,r)=>{let n=t===0?e===!0:r[t-1].value.readableObjectMode,o=t!==r.length-1&&(e??n);return{writableObjectMode:n,readableObjectMode:o}},RD=(e,t)=>{let r=e.findLast(({type:n})=>z.has(n));return r===void 0?!1:t==="input"?r.value.writableObjectMode:r.value.readableObjectMode};var ID=(e,t,r,n)=>[...e.filter(({type:o})=>!z.has(o)),...Mg(e,t,r,n)],Mg=(e,t,r,{encoding:n})=>{let o=e.filter(({type:s})=>z.has(s)),i=Array.from({length:o.length});for(let[s,u]of Object.entries(o))i[s]=Pg({stdioItem:u,index:Number(s),newTransforms:i,optionName:t,direction:r,encoding:n});return $g(i,r)},Pg=({stdioItem:e,stdioItem:{type:t},index:r,newTransforms:n,optionName:o,direction:i,encoding:s})=>t==="duplex"?kg({stdioItem:e,optionName:o}):t==="webTransform"?Lg({stdioItem:e,index:r,newTransforms:n,direction:i}):Ng({stdioItem:e,index:r,newTransforms:n,direction:i,encoding:s}),kg=({stdioItem:e,stdioItem:{value:{transform:t,transform:{writableObjectMode:r,readableObjectMode:n},objectMode:o=n}},optionName:i})=>{if(o&&!n)throw new TypeError(`The \`${i}.objectMode\` option can only be \`true\` if \`new Duplex({objectMode: true})\` is used.`);if(!o&&n)throw new TypeError(`The \`${i}.objectMode\` option cannot be \`false\` if \`new Duplex({objectMode: true})\` is used.`);return{...e,value:{transform:t,writableObjectMode:r,readableObjectMode:n}}},Lg=({stdioItem:e,stdioItem:{value:t},index:r,newTransforms:n,direction:o})=>{let{transform:i,objectMode:s}=O(t)?t:{transform:t},{writableObjectMode:u,readableObjectMode:a}=mi(s,r,n,o);return{...e,value:{transform:i,writableObjectMode:u,readableObjectMode:a}}},Ng=({stdioItem:e,stdioItem:{value:t},index:r,newTransforms:n,direction:o,encoding:i})=>{let{transform:s,final:u,binary:a=!1,preserveNewlines:c=!1,objectMode:l}=O(t)?t:{transform:t},D=a||k.has(i),{writableObjectMode:f,readableObjectMode:p}=mi(l,r,n,o);return{...e,value:{transform:s,final:u,binary:D,preserveNewlines:c,writableObjectMode:f,readableObjectMode:p}}},$g=(e,t)=>t==="input"?e.reverse():e;var on=F(require("node:process"),1);var MD=(e,t,r)=>{let n=e.map(o=>jg(o,t));if(n.includes("input")&&n.includes("output"))throw new TypeError(`The \`${r}\` option must not be an array of both readable and writable values.`);return n.find(Boolean)??zg},jg=({type:e,value:t},r)=>Ug[r]??PD[e](t),Ug=["input","output","output"],tt=()=>{},hi=()=>"input",PD={generator:tt,asyncGenerator:tt,fileUrl:tt,filePath:tt,iterable:hi,asyncIterable:hi,uint8Array:hi,webStream:e=>rn(e)?"output":"input",nodeStream(e){return xe(e,{checkOpen:!1})?oi(e,{checkOpen:!1})?void 0:"input":"output"},webTransform:tt,duplex:tt,native(e){let t=Gg(e);if(t!==void 0)return t;if(J(e,{checkOpen:!1}))return PD.nodeStream(e)}},Gg=e=>{if([0,on.default.stdin].includes(e))return"input";if([1,2,on.default.stdout,on.default.stderr].includes(e))return"output"},zg="output";var kD=(e,t)=>t&&!e.includes("ipc")?[...e,"ipc"]:e;var LD=({stdio:e,ipc:t,buffer:r,...n},o,i)=>{let s=Wg(e,n).map((u,a)=>ND(u,a));return i?qg(s,r,o):kD(s,t)},Wg=(e,t)=>{if(e===void 0)return G.map(n=>t[n]);if(Yg(t))throw new Error(`It's not possible to provide \`stdio\` in combination with one of ${G.map(n=>`\`${n}\``).join(", ")}`);if(typeof e=="string")return[e,e,e];if(!Array.isArray(e))throw new TypeError(`Expected \`stdio\` to be of type \`string\` or \`Array\`, got \`${typeof e}\``);let r=Math.max(e.length,G.length);return Array.from({length:r},(n,o)=>e[o])},Yg=e=>G.some(t=>e[t]!==void 0),ND=(e,t)=>Array.isArray(e)?e.map(r=>ND(r,t)):e??(t>=G.length?"ignore":"pipe"),qg=(e,t,r)=>e.map((n,o)=>!t[o]&&o!==0&&!We(r,o)&&Vg(n)?"ignore":n),Vg=e=>e==="pipe"||Array.isArray(e)&&e.every(t=>t==="pipe");var jD=require("node:fs"),UD=F(require("node:tty"),1);var GD=({stdioItem:e,stdioItem:{type:t},isStdioArray:r,fdNumber:n,direction:o,isSync:i})=>!r||t!=="native"?e:i?Hg({stdioItem:e,fdNumber:n,direction:o}):Xg({stdioItem:e,fdNumber:n}),Hg=({stdioItem:e,stdioItem:{value:t,optionName:r},fdNumber:n,direction:o})=>{let i=Kg({value:t,optionName:r,fdNumber:n,direction:o});if(i!==void 0)return i;if(J(t,{checkOpen:!1}))throw new TypeError(`The \`${r}: Stream\` option cannot both be an array and include a stream with synchronous methods.`);return e},Kg=({value:e,optionName:t,fdNumber:r,direction:n})=>{let o=Jg(e,r);if(o!==void 0){if(n==="output")return{type:"fileNumber",value:o,optionName:t};if(UD.default.isatty(o))throw new TypeError(`The \`${t}: ${vr(e)}\` option is invalid: it cannot be a TTY with synchronous methods.`);return{type:"uint8Array",value:se((0,jD.readFileSync)(o)),optionName:t}}},Jg=(e,t)=>{if(e==="inherit")return t;if(typeof e=="number")return e;let r=dr.indexOf(e);if(r!==-1)return r},Xg=({stdioItem:e,stdioItem:{value:t,optionName:r},fdNumber:n})=>t==="inherit"?{type:"nodeStream",value:$D(n,t,r),optionName:r}:typeof t=="number"?{type:"nodeStream",value:$D(t,t,r),optionName:r}:J(t,{checkOpen:!1})?{type:"nodeStream",value:t,optionName:r}:e,$D=(e,t,r)=>{let n=dr[e];if(n===void 0)throw new TypeError(`The \`${r}: ${t}\` option is invalid: no such standard stream.`);return n};var zD=({input:e,inputFile:t},r)=>r===0?[...Zg(e),...e2(t)]:[],Zg=e=>e===void 0?[]:[{type:Qg(e),value:e,optionName:"input"}],Qg=e=>{if(xe(e,{checkOpen:!1}))return"nodeStream";if(typeof e=="string")return"string";if(R(e))return"uint8Array";throw new Error("The `input` option must be a string, a Uint8Array or a Node.js Readable stream.")},e2=e=>e===void 0?[]:[{...t2(e),optionName:"inputFile"}],t2=e=>{if(tn(e))return{type:"fileUrl",value:e};if(fi(e))return{type:"filePath",value:{file:e}};throw new Error("The `inputFile` option must be a file path string or a file URL.")};var WD=e=>e.filter((t,r)=>e.every((n,o)=>t.value!==n.value||r>=o||t.type==="generator"||t.type==="asyncGenerator")),YD=({stdioItem:{type:e,value:t,optionName:r},direction:n,fileDescriptors:o,isSync:i})=>{let s=r2(o,e);if(s.length!==0){if(i){n2({otherStdioItems:s,type:e,value:t,optionName:r,direction:n});return}if(OD.has(e))return qD({otherStdioItems:s,type:e,value:t,optionName:r,direction:n});vD.has(e)&&i2({otherStdioItems:s,type:e,value:t,optionName:r})}},r2=(e,t)=>e.flatMap(({direction:r,stdioItems:n})=>n.filter(o=>o.type===t).map(o=>({...o,direction:r}))),n2=({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})=>{di.has(t)&&qD({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})},qD=({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})=>{let i=e.filter(u=>o2(u,r));if(i.length===0)return;let s=i.find(u=>u.direction!==o);return VD(s,n,t),o==="output"?i[0].stream:void 0},o2=({type:e,value:t},r)=>e==="filePath"?t.file===r.file:e==="fileUrl"?t.href===r.href:t===r,i2=({otherStdioItems:e,type:t,value:r,optionName:n})=>{let o=e.find(({value:{transform:i}})=>i===r.transform);VD(o,n,t)},VD=(e,t,r)=>{if(e!==void 0)throw new TypeError(`The \`${e.optionName}\` and \`${t}\` options must not target ${Fe[r]} that is the same.`)};var sn=(e,t,r,n)=>{let i=LD(t,r,n).map((u,a)=>s2({stdioOption:u,fdNumber:a,options:t,isSync:n})),s=d2({initialFileDescriptors:i,addProperties:e,options:t,isSync:n});return t.stdio=s.map(({stdioItems:u})=>F2(u)),s},s2=({stdioOption:e,fdNumber:t,options:r,isSync:n})=>{let o=mr(t),{stdioItems:i,isStdioArray:s}=u2({stdioOption:e,fdNumber:t,options:r,optionName:o}),u=MD(i,t,o),a=i.map(D=>GD({stdioItem:D,isStdioArray:s,fdNumber:t,direction:u,isSync:n})),c=ID(a,o,u,r),l=RD(c,u);return p2(c,l),{direction:u,objectMode:l,stdioItems:c}},u2=({stdioOption:e,fdNumber:t,options:r,optionName:n})=>{let i=[...(Array.isArray(e)?e:[e]).map(a=>a2(a,n)),...zD(r,t)],s=WD(i),u=s.length>1;return c2(s,u,n),D2(s),{stdioItems:s,isStdioArray:u}},a2=(e,t)=>({type:bD(e,t),value:e,optionName:t}),c2=(e,t,r)=>{if(e.length===0)throw new TypeError(`The \`${r}\` option must not be an empty array.`);if(t){for(let{value:n,optionName:o}of e)if(l2.has(n))throw new Error(`The \`${o}\` option must not include \`${n}\`.`)}},l2=new Set(["ignore","ipc"]),D2=e=>{for(let t of e)f2(t)},f2=({type:e,value:t,optionName:r})=>{if(AD(t))throw new TypeError(`The \`${r}: URL\` option must use the \`file:\` scheme.
For example, you can use the \`pathToFileURL()\` method of the \`url\` core module.`);if(BD(e,t))throw new TypeError(`The \`${r}: { file: '...' }\` option must be used instead of \`${r}: '...'\`.`)},p2=(e,t)=>{if(!t)return;let r=e.find(({type:n})=>nn.has(n));if(r!==void 0)throw new TypeError(`The \`${r.optionName}\` option cannot use both files and transforms in objectMode.`)},d2=({initialFileDescriptors:e,addProperties:t,options:r,isSync:n})=>{let o=[];try{for(let i of e)o.push(m2({fileDescriptor:i,fileDescriptors:o,addProperties:t,options:r,isSync:n}));return o}catch(i){throw Fi(o),i}},m2=({fileDescriptor:{direction:e,objectMode:t,stdioItems:r},fileDescriptors:n,addProperties:o,options:i,isSync:s})=>{let u=r.map(a=>h2({stdioItem:a,addProperties:o,direction:e,options:i,fileDescriptors:n,isSync:s}));return{direction:e,objectMode:t,stdioItems:u}},h2=({stdioItem:e,addProperties:t,direction:r,options:n,fileDescriptors:o,isSync:i})=>{let s=YD({stdioItem:e,direction:r,fileDescriptors:o,isSync:i});return s!==void 0?{...e,stream:s}:{...e,...t[r][e.type](e,n)}},Fi=e=>{for(let{stdioItems:t}of e)for(let{stream:r}of t)r!==void 0&&!V(r)&&r.destroy()},F2=e=>{if(e.length>1)return e.some(({value:n})=>n==="overlapped")?"overlapped":"pipe";let[{type:t,value:r}]=e;return t==="native"?r:"pipe"};var KD=(e,t)=>sn(E2,e,t,!0),te=({type:e,optionName:t})=>{JD(t,Fe[e])},g2=({optionName:e,value:t})=>((t==="ipc"||t==="overlapped")&&JD(e,`"${t}"`),{}),JD=(e,t)=>{throw new TypeError(`The \`${e}\` option cannot be ${t} with synchronous methods.`)},HD={generator(){},asyncGenerator:te,webStream:te,nodeStream:te,webTransform:te,duplex:te,asyncIterable:te,native:g2},E2={input:{...HD,fileUrl:({value:e})=>({contents:[se((0,gi.readFileSync)(e))]}),filePath:({value:{file:e}})=>({contents:[se((0,gi.readFileSync)(e))]}),fileNumber:te,iterable:({value:e})=>({contents:[...e]}),string:({value:e})=>({contents:[e]}),uint8Array:({value:e})=>({contents:[e]})},output:{...HD,fileUrl:({value:e})=>({path:e}),filePath:({value:{file:e,append:t}})=>({path:e,append:t}),fileNumber:({value:e})=>({path:e}),iterable:te,string:te,uint8Array:te}};var ce=(e,{stripFinalNewline:t},r)=>Ei(t,r)&&e!==void 0&&!Array.isArray(e)?Xe(e):e,Ei=(e,t)=>t==="all"?e[1]||e[2]:e[t];var It=require("node:stream");var un=(e,t,r,n)=>e||r?void 0:ZD(t,n),yi=(e,t,r)=>r?e.flatMap(n=>XD(n,t)):XD(e,t),XD=(e,t)=>{let{transform:r,final:n}=ZD(t,{});return[...r(e),...n()]},ZD=(e,t)=>(t.previousChunks="",{transform:C2.bind(void 0,t,e),final:b2.bind(void 0,t)}),C2=function*(e,t,r){if(typeof r!="string"){yield r;return}let{previousChunks:n}=e,o=-1;for(let i=0;i<r.length;i+=1)if(r[i]===`
`){let s=y2(r,i,t,e),u=r.slice(o+1,i+1-s);n.length>0&&(u=Ci(n,u),n=""),yield u,o=i}o!==r.length-1&&(n=Ci(n,r.slice(o+1))),e.previousChunks=n},y2=(e,t,r,n)=>r?0:(n.isWindowsNewline=t!==0&&e[t-1]==="\r",n.isWindowsNewline?2:1),b2=function*({previousChunks:e}){e.length>0&&(yield e)},QD=({binary:e,preserveNewlines:t,readableObjectMode:r,state:n})=>e||t||r?void 0:{transform:w2.bind(void 0,n)},w2=function*({isWindowsNewline:e=!1},t){let{unixNewline:r,windowsNewline:n,LF:o,concatBytes:i}=typeof t=="string"?x2:A2;if(t.at(-1)===o){yield t;return}yield i(t,e?n:r)},Ci=(e,t)=>`${e}${t}`,x2={windowsNewline:`\r
`,unixNewline:`
`,LF:`
`,concatBytes:Ci},S2=(e,t)=>{let r=new Uint8Array(e.length+t.length);return r.set(e,0),r.set(t,e.length),r},A2={windowsNewline:new Uint8Array([13,10]),unixNewline:new Uint8Array([10]),LF:10,concatBytes:S2};var ef=require("node:buffer");var tf=(e,t)=>e?void 0:B2.bind(void 0,t),B2=function*(e,t){if(typeof t!="string"&&!R(t)&&!ef.Buffer.isBuffer(t))throw new TypeError(`The \`${e}\` option's transform must use "objectMode: true" to receive as input: ${typeof t}.`);yield t},rf=(e,t)=>e?T2.bind(void 0,t):_2.bind(void 0,t),T2=function*(e,t){nf(e,t),yield t},_2=function*(e,t){if(nf(e,t),typeof t!="string"&&!R(t))throw new TypeError(`The \`${e}\` option's function must yield a string or an Uint8Array, not ${typeof t}.`);yield t},nf=(e,t)=>{if(t==null)throw new TypeError(`The \`${e}\` option's function must not call \`yield ${t}\`.
Instead, \`yield\` should either be called with a value, or not be called at all. For example:
  if (condition) { yield value; }`)};var of=require("node:buffer"),sf=require("node:string_decoder");var an=(e,t,r)=>{if(r)return;if(e)return{transform:O2.bind(void 0,new TextEncoder)};let n=new sf.StringDecoder(t);return{transform:v2.bind(void 0,n),final:R2.bind(void 0,n)}},O2=function*(e,t){of.Buffer.isBuffer(t)?yield se(t):typeof t=="string"?yield e.encode(t):yield t},v2=function*(e,t){yield R(t)?e.write(t):t},R2=function*(e){let t=e.end();t!==""&&(yield t)};var bi=require("node:util"),wi=(0,bi.callbackify)(async(e,t,r,n)=>{t.currentIterable=e(...r);try{for await(let o of t.currentIterable)n.push(o)}finally{delete t.currentIterable}}),cn=async function*(e,t,r){if(r===t.length){yield e;return}let{transform:n=M2}=t[r];for await(let o of n(e))yield*cn(o,t,r+1)},uf=async function*(e){for(let[t,{final:r}]of Object.entries(e))yield*I2(r,Number(t),e)},I2=async function*(e,t,r){if(e!==void 0)for await(let n of e())yield*cn(n,r,t+1)},af=(0,bi.callbackify)(async({currentIterable:e},t)=>{if(e!==void 0){await(t?e.throw(t):e.return());return}if(t)throw t}),M2=function*(e){yield e};var xi=(e,t,r,n)=>{try{for(let o of e(...t))r.push(o);n()}catch(o){n(o)}},cf=(e,t)=>[...t.flatMap(r=>[...Ae(r,e,0)]),...Rt(e)],Ae=function*(e,t,r){if(r===t.length){yield e;return}let{transform:n=k2}=t[r];for(let o of n(e))yield*Ae(o,t,r+1)},Rt=function*(e){for(let[t,{final:r}]of Object.entries(e))yield*P2(r,Number(t),e)},P2=function*(e,t,r){if(e!==void 0)for(let n of e())yield*Ae(n,r,t+1)},k2=function*(e){yield e};var Si=({value:e,value:{transform:t,final:r,writableObjectMode:n,readableObjectMode:o},optionName:i},{encoding:s})=>{let u={},a=lf(e,s,i),c=Se(t),l=Se(r),D=c?wi.bind(void 0,cn,u):xi.bind(void 0,Ae),f=c||l?wi.bind(void 0,uf,u):xi.bind(void 0,Rt),p=c||l?af.bind(void 0,u):void 0;return{stream:new It.Transform({writableObjectMode:n,writableHighWaterMark:(0,It.getDefaultHighWaterMark)(n),readableObjectMode:o,readableHighWaterMark:(0,It.getDefaultHighWaterMark)(o),transform(y,v,x){D([y,a,0],this,x)},flush(y){f([a],this,y)},destroy:p})}},ln=(e,t,r,n)=>{let o=t.filter(({type:s})=>s==="generator"),i=n?o.reverse():o;for(let{value:s,optionName:u}of i){let a=lf(s,r,u);e=cf(a,e)}return e},lf=({transform:e,final:t,binary:r,writableObjectMode:n,readableObjectMode:o,preserveNewlines:i},s,u)=>{let a={};return[{transform:tf(n,u)},an(r,s,n),un(r,i,n,a),{transform:e,final:t},{transform:rf(o,u)},QD({binary:r,preserveNewlines:i,readableObjectMode:o,state:a})].filter(Boolean)};var Df=(e,t)=>{for(let r of L2(e))N2(e,r,t)},L2=e=>new Set(Object.entries(e).filter(([,{direction:t}])=>t==="input").map(([t])=>Number(t))),N2=(e,t,r)=>{let{stdioItems:n}=e[t],o=n.filter(({contents:u})=>u!==void 0);if(o.length===0)return;if(t!==0){let[{type:u,optionName:a}]=o;throw new TypeError(`Only the \`stdin\` option, not \`${a}\`, can be ${Fe[u]} with synchronous methods.`)}let s=o.map(({contents:u})=>u).map(u=>$2(u,n));r.input=bt(s)},$2=(e,t)=>{let r=ln(e,t,"utf8",!0);return j2(r),bt(r)},j2=e=>{let t=e.find(r=>typeof r!="string"&&!R(r));if(t!==void 0)throw new TypeError(`The \`stdin\` option is invalid: when passing objects as input, a transform must be used to serialize them to strings or Uint8Arrays: ${t}.`)};var fn=require("node:fs");var Dn=({stdioItems:e,encoding:t,verboseInfo:r,fdNumber:n})=>n!=="all"&&We(r,n)&&!k.has(t)&&U2(n)&&(e.some(({type:o,value:i})=>o==="native"&&G2.has(i))||e.every(({type:o})=>z.has(o))),U2=e=>e===1||e===2,G2=new Set(["pipe","overlapped"]),ff=async(e,t,r,n)=>{for await(let o of e)z2(t)||df(o,r,n)},pf=(e,t,r)=>{for(let n of e)df(n,t,r)},z2=e=>e._readableState.pipes.length>0,df=(e,t,r)=>{let n=yr(e);X({type:"output",verboseMessage:n,fdNumber:t,verboseInfo:r})};var mf=({fileDescriptors:e,syncResult:{output:t},options:r,isMaxBuffer:n,verboseInfo:o})=>{if(t===null)return{output:Array.from({length:3})};let i={},s=new Set([]);return{output:t.map((a,c)=>W2({result:a,fileDescriptors:e,fdNumber:c,state:i,outputFiles:s,isMaxBuffer:n,verboseInfo:o},r)),...i}},W2=({result:e,fileDescriptors:t,fdNumber:r,state:n,outputFiles:o,isMaxBuffer:i,verboseInfo:s},{buffer:u,encoding:a,lines:c,stripFinalNewline:l,maxBuffer:D})=>{if(e===null)return;let f=fD(e,i,D),p=se(f),{stdioItems:m,objectMode:y}=t[r],v=Y2([p],m,a,n),{serializedResult:x,finalResult:I=x}=q2({chunks:v,objectMode:y,encoding:a,lines:c,stripFinalNewline:l,fdNumber:r});V2({serializedResult:x,fdNumber:r,state:n,verboseInfo:s,encoding:a,stdioItems:m,objectMode:y});let P=u[r]?I:void 0;try{return n.error===void 0&&H2(x,m,o),P}catch(L){return n.error=L,P}},Y2=(e,t,r,n)=>{try{return ln(e,t,r,!1)}catch(o){return n.error=o,e}},q2=({chunks:e,objectMode:t,encoding:r,lines:n,stripFinalNewline:o,fdNumber:i})=>{if(t)return{serializedResult:e};if(r==="buffer")return{serializedResult:bt(e)};let s=oa(e,r);return n[i]?{serializedResult:s,finalResult:yi(s,!o[i],t)}:{serializedResult:s}},V2=({serializedResult:e,fdNumber:t,state:r,verboseInfo:n,encoding:o,stdioItems:i,objectMode:s})=>{if(!Dn({stdioItems:i,encoding:o,verboseInfo:n,fdNumber:t}))return;let u=yi(e,!1,s);try{pf(u,t,n)}catch(a){r.error??=a}},H2=(e,t,r)=>{for(let{path:n,append:o}of t.filter(({type:i})=>nn.has(i))){let i=typeof n=="string"?n:n.toString();o||r.has(i)?(0,fn.appendFileSync)(n,e):(r.add(i),(0,fn.writeFileSync)(n,e))}};var hf=([,e,t],r)=>{if(r.all)return e===void 0?t:t===void 0?e:Array.isArray(e)?Array.isArray(t)?[...e,...t]:[...e,ce(t,r,"all")]:Array.isArray(t)?[ce(e,r,"all"),...t]:R(e)&&R(t)?Bo([e,t]):`${e}${t}`};var pn=require("node:events");var Ff=async(e,t)=>{let[r,n]=await K2(e);return t.isForcefullyTerminated??=!1,[r,n]},K2=async e=>{let[t,r]=await Promise.allSettled([(0,pn.once)(e,"spawn"),(0,pn.once)(e,"exit")]);return t.status==="rejected"?[]:r.status==="rejected"?gf(e):r.value},gf=async e=>{try{return await(0,pn.once)(e,"exit")}catch{return gf(e)}},Ef=async e=>{let[t,r]=await e;if(!J2(t,r)&&Ai(t,r))throw new H;return[t,r]},J2=(e,t)=>e===void 0&&t===void 0,Ai=(e,t)=>e!==0||t!==null;var Cf=({error:e,status:t,signal:r,output:n},{maxBuffer:o})=>{let i=X2(e,t,r),s=i?.code==="ETIMEDOUT",u=DD(i,n,o);return{resultError:i,exitCode:t,signal:r,timedOut:s,isMaxBuffer:u}},X2=(e,t,r)=>e!==void 0?e:Ai(t,r)?new H:void 0;var bf=(e,t,r)=>{let{file:n,commandArguments:o,command:i,escapedCommand:s,startTime:u,verboseInfo:a,options:c,fileDescriptors:l}=Z2(e,t,r),D=tE({file:n,commandArguments:o,options:c,command:i,escapedCommand:s,verboseInfo:a,fileDescriptors:l,startTime:u});return et(D,a,c)},Z2=(e,t,r)=>{let{command:n,escapedCommand:o,startTime:i,verboseInfo:s}=wr(e,t,r),u=Q2(r),{file:a,commandArguments:c,options:l}=Wr(e,t,u);eE(l);let D=KD(l,s);return{file:a,commandArguments:c,command:n,escapedCommand:o,startTime:i,verboseInfo:s,options:l,fileDescriptors:D}},Q2=e=>e.node&&!e.ipc?{...e,ipc:!1}:e,eE=({ipc:e,ipcInput:t,detached:r,cancelSignal:n})=>{t&&dn("ipcInput"),e&&dn("ipc: true"),r&&dn("detached: true"),n&&dn("cancelSignal")},dn=e=>{throw new TypeError(`The "${e}" option cannot be used with synchronous methods.`)},tE=({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,verboseInfo:i,fileDescriptors:s,startTime:u})=>{let a=rE({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,fileDescriptors:s,startTime:u});if(a.failed)return a;let{resultError:c,exitCode:l,signal:D,timedOut:f,isMaxBuffer:p}=Cf(a,r),{output:m,error:y=c}=mf({fileDescriptors:s,syncResult:a,options:r,isMaxBuffer:p,verboseInfo:i}),v=m.map((I,P)=>ce(I,r,P)),x=ce(hf(m,r),r,"all");return oE({error:y,exitCode:l,signal:D,timedOut:f,isMaxBuffer:p,stdio:v,all:x,options:r,command:n,escapedCommand:o,startTime:u})},rE=({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,fileDescriptors:i,startTime:s})=>{try{Df(i,r);let u=nE(r);return(0,yf.spawnSync)(...Yr(e,t,u))}catch(u){return Qe({error:u,command:n,escapedCommand:o,fileDescriptors:i,options:r,startTime:s,isSync:!0})}},nE=({encoding:e,maxBuffer:t,...r})=>({...r,encoding:"buffer",maxBuffer:Qr(t)}),oE=({error:e,exitCode:t,signal:r,timedOut:n,isMaxBuffer:o,stdio:i,all:s,options:u,command:a,escapedCommand:c,startTime:l})=>e===void 0?en({command:a,escapedCommand:c,stdio:i,all:s,ipcOutput:[],options:u,startTime:l}):vt({error:e,command:a,escapedCommand:c,timedOut:n,isCanceled:!1,isGracefullyCanceled:!1,isMaxBuffer:o,isForcefullyTerminated:!1,exitCode:t,signal:r,stdio:i,all:s,ipcOutput:[],options:u,startTime:l,isSync:!0});var _p=require("node:events"),Op=require("node:child_process");var Ti=F(require("node:process"),1);var rt=require("node:events");var wf=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},{reference:o=!0,filter:i}={})=>(He({methodName:"getOneMessage",isSubprocess:r,ipc:n,isConnected:Lr(e)}),iE({anyProcess:e,channel:t,isSubprocess:r,filter:i,reference:o})),iE=async({anyProcess:e,channel:t,isSubprocess:r,filter:n,reference:o})=>{Ir(t,o);let i=he(e,t,r),s=new AbortController;try{return await Promise.race([sE(i,n,s),uE(i,r,s),aE(i,r,s)])}catch(u){throw Ke(e),u}finally{s.abort(),Mr(t,o)}},sE=async(e,t,{signal:r})=>{if(t===void 0){let[n]=await(0,rt.once)(e,"message",{signal:r});return n}for await(let[n]of(0,rt.on)(e,"message",{signal:r}))if(t(n))return n},uE=async(e,t,{signal:r})=>{await(0,rt.once)(e,"disconnect",{signal:r}),Yc(t)},aE=async(e,t,{signal:r})=>{let[n]=await(0,rt.once)(e,"strict:error",{signal:r});throw Or(n,t)};var Mt=require("node:events");var Sf=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},{reference:o=!0}={})=>Bi({anyProcess:e,channel:t,isSubprocess:r,ipc:n,shouldAwait:!r,reference:o}),Bi=({anyProcess:e,channel:t,isSubprocess:r,ipc:n,shouldAwait:o,reference:i})=>{He({methodName:"getEachMessage",isSubprocess:r,ipc:n,isConnected:Lr(e)}),Ir(t,i);let s=he(e,t,r),u=new AbortController,a={};return cE(e,s,u),lE({ipcEmitter:s,isSubprocess:r,controller:u,state:a}),DE({anyProcess:e,channel:t,ipcEmitter:s,isSubprocess:r,shouldAwait:o,controller:u,state:a,reference:i})},cE=async(e,t,r)=>{try{await(0,Mt.once)(t,"disconnect",{signal:r.signal}),r.abort()}catch{}},lE=async({ipcEmitter:e,isSubprocess:t,controller:r,state:n})=>{try{let[o]=await(0,Mt.once)(e,"strict:error",{signal:r.signal});n.error=Or(o,t),r.abort()}catch{}},DE=async function*({anyProcess:e,channel:t,ipcEmitter:r,isSubprocess:n,shouldAwait:o,controller:i,state:s,reference:u}){try{for await(let[a]of(0,Mt.on)(r,"message",{signal:i.signal}))xf(s),yield a}catch{xf(s)}finally{i.abort(),Mr(t,u),n||Ke(e),o&&await e}},xf=({error:e})=>{if(e)throw e};var Af=(e,{ipc:t})=>{Object.assign(e,Tf(e,!1,t))},Bf=()=>{let e=Ti.default,t=!0,r=Ti.default.channel!==void 0;return{...Tf(e,t,r),getCancelSignal:Sl.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r})}},Tf=(e,t,r)=>({sendMessage:Ur.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r}),getOneMessage:wf.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r}),getEachMessage:Sf.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r})});var _f=require("node:child_process"),ge=require("node:stream");var Of=({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,verboseInfo:s})=>{Fi(n);let u=new _f.ChildProcess;fE(u,n),Object.assign(u,{readable:pE,writable:dE,duplex:mE});let a=Qe({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,isSync:!1}),c=hE(a,s,o);return{subprocess:u,promise:c}},fE=(e,t)=>{let r=Pt(),n=Pt(),o=Pt(),i=Array.from({length:t.length-3},Pt),s=Pt(),u=[r,n,o,...i];Object.assign(e,{stdin:r,stdout:n,stderr:o,all:s,stdio:u})},Pt=()=>{let e=new ge.PassThrough;return e.end(),e},pE=()=>new ge.Readable({read(){}}),dE=()=>new ge.Writable({write(){}}),mE=()=>new ge.Duplex({read(){},write(){}}),hE=async(e,t,r)=>et(e,t,r);var nt=require("node:fs"),Rf=require("node:buffer"),re=require("node:stream");var If=(e,t)=>sn(FE,e,t,!1),kt=({type:e,optionName:t})=>{throw new TypeError(`The \`${t}\` option cannot be ${Fe[e]}.`)},vf={fileNumber:kt,generator:Si,asyncGenerator:Si,nodeStream:({value:e})=>({stream:e}),webTransform({value:{transform:e,writableObjectMode:t,readableObjectMode:r}}){let n=t||r;return{stream:re.Duplex.fromWeb(e,{objectMode:n})}},duplex:({value:{transform:e}})=>({stream:e}),native(){}},FE={input:{...vf,fileUrl:({value:e})=>({stream:(0,nt.createReadStream)(e)}),filePath:({value:{file:e}})=>({stream:(0,nt.createReadStream)(e)}),webStream:({value:e})=>({stream:re.Readable.fromWeb(e)}),iterable:({value:e})=>({stream:re.Readable.from(e)}),asyncIterable:({value:e})=>({stream:re.Readable.from(e)}),string:({value:e})=>({stream:re.Readable.from(e)}),uint8Array:({value:e})=>({stream:re.Readable.from(Rf.Buffer.from(e))})},output:{...vf,fileUrl:({value:e})=>({stream:(0,nt.createWriteStream)(e)}),filePath:({value:{file:e,append:t}})=>({stream:(0,nt.createWriteStream)(e,t?{flags:"a"}:{})}),webStream:({value:e})=>({stream:re.Writable.fromWeb(e)}),iterable:kt,asyncIterable:kt,string:kt,uint8Array:kt}};var Lt=require("node:events"),hn=require("node:stream"),vi=require("node:stream/promises");function Be(e){if(!Array.isArray(e))throw new TypeError(`Expected an array, got \`${typeof e}\`.`);for(let o of e)Oi(o);let t=e.some(({readableObjectMode:o})=>o),r=gE(e,t),n=new _i({objectMode:t,writableHighWaterMark:r,readableHighWaterMark:r});for(let o of e)n.add(o);return n}var gE=(e,t)=>{if(e.length===0)return(0,hn.getDefaultHighWaterMark)(t);let r=e.filter(({readableObjectMode:n})=>n===t).map(({readableHighWaterMark:n})=>n);return Math.max(...r)},_i=class extends hn.PassThrough{#r=new Set([]);#o=new Set([]);#e=new Set([]);#n;#u=Symbol("unpipe");#t=new WeakMap;add(t){if(Oi(t),this.#r.has(t))return;this.#r.add(t),this.#n??=EE(this,this.#r,this.#u);let r=bE({passThroughStream:this,stream:t,streams:this.#r,ended:this.#o,aborted:this.#e,onFinished:this.#n,unpipeEvent:this.#u});this.#t.set(t,r),t.pipe(this,{end:!1})}async remove(t){if(Oi(t),!this.#r.has(t))return!1;let r=this.#t.get(t);return r===void 0?!1:(this.#t.delete(t),t.unpipe(this),await r,!0)}},EE=async(e,t,r)=>{mn(e,Mf);let n=new AbortController;try{await Promise.race([CE(e,n),yE(e,t,r,n)])}finally{n.abort(),mn(e,-Mf)}},CE=async(e,{signal:t})=>{try{await(0,vi.finished)(e,{signal:t,cleanup:!0})}catch(r){throw kf(e,r),r}},yE=async(e,t,r,{signal:n})=>{for await(let[o]of(0,Lt.on)(e,"unpipe",{signal:n}))t.has(o)&&o.emit(r)},Oi=e=>{if(typeof e?.pipe!="function")throw new TypeError(`Expected a readable stream, got: \`${typeof e}\`.`)},bE=async({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,onFinished:i,unpipeEvent:s})=>{mn(e,Pf);let u=new AbortController;try{await Promise.race([wE(i,t,u),xE({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,controller:u}),SE({stream:t,streams:r,ended:n,aborted:o,unpipeEvent:s,controller:u})])}finally{u.abort(),mn(e,-Pf)}r.size>0&&r.size===n.size+o.size&&(n.size===0&&o.size>0?Ri(e):AE(e))},wE=async(e,t,{signal:r})=>{try{await e,r.aborted||Ri(t)}catch(n){r.aborted||kf(t,n)}},xE=async({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,controller:{signal:i}})=>{try{await(0,vi.finished)(t,{signal:i,cleanup:!0,readable:!0,writable:!1}),r.has(t)&&n.add(t)}catch(s){if(i.aborted||!r.has(t))return;Lf(s)?o.add(t):Nf(e,s)}},SE=async({stream:e,streams:t,ended:r,aborted:n,unpipeEvent:o,controller:{signal:i}})=>{if(await(0,Lt.once)(e,o,{signal:i}),!e.readable)return(0,Lt.once)(i,"abort",{signal:i});t.delete(e),r.delete(e),n.delete(e)},AE=e=>{e.writable&&e.end()},kf=(e,t)=>{Lf(t)?Ri(e):Nf(e,t)},Lf=e=>e?.code==="ERR_STREAM_PREMATURE_CLOSE",Ri=e=>{(e.readable||e.writable)&&e.destroy()},Nf=(e,t)=>{e.destroyed||(e.once("error",BE),e.destroy(t))},BE=()=>{},mn=(e,t)=>{let r=e.getMaxListeners();r!==0&&r!==Number.POSITIVE_INFINITY&&e.setMaxListeners(r+t)},Mf=2,Pf=1;var Ii=require("node:stream/promises");var ot=(e,t)=>{e.pipe(t),TE(e,t),_E(e,t)},TE=async(e,t)=>{if(!(V(e)||V(t))){try{await(0,Ii.finished)(e,{cleanup:!0,readable:!0,writable:!1})}catch{}Mi(t)}},Mi=e=>{e.writable&&e.end()},_E=async(e,t)=>{if(!(V(e)||V(t))){try{await(0,Ii.finished)(t,{cleanup:!0,readable:!1,writable:!0})}catch{}Pi(e)}},Pi=e=>{e.readable&&e.destroy()};var $f=(e,t,r)=>{let n=new Map;for(let[o,{stdioItems:i,direction:s}]of Object.entries(t)){for(let{stream:u}of i.filter(({type:a})=>z.has(a)))OE(e,u,s,o);for(let{stream:u}of i.filter(({type:a})=>!z.has(a)))RE({subprocess:e,stream:u,direction:s,fdNumber:o,pipeGroups:n,controller:r})}for(let[o,i]of n.entries()){let s=i.length===1?i[0]:Be(i);ot(s,o)}},OE=(e,t,r,n)=>{r==="output"?ot(e.stdio[n],t):ot(t,e.stdio[n]);let o=vE[n];o!==void 0&&(e[o]=t),e.stdio[n]=t},vE=["stdin","stdout","stderr"],RE=({subprocess:e,stream:t,direction:r,fdNumber:n,pipeGroups:o,controller:i})=>{if(t===void 0)return;IE(t,i);let[s,u]=r==="output"?[t,e.stdio[n]]:[e.stdio[n],t],a=o.get(s)??[];o.set(s,[...a,u])},IE=(e,{signal:t})=>{V(e)&&we(e,ME,t)},ME=2;var jf=require("node:events");var Uf=(e,{cleanup:t,detached:r},{signal:n})=>{if(!t||r)return;let o=er(()=>{e.kill()});(0,jf.addAbortListener)(n,()=>{o()})};var zf=({source:e,sourcePromise:t,boundOptions:r,createNested:n},...o)=>{let i=br(),{destination:s,destinationStream:u,destinationError:a,from:c,unpipeSignal:l}=PE(r,n,o),{sourceStream:D,sourceError:f}=LE(e,c),{options:p,fileDescriptors:m}=Q.get(e);return{sourcePromise:t,sourceStream:D,sourceOptions:p,sourceError:f,destination:s,destinationStream:u,destinationError:a,unpipeSignal:l,fileDescriptors:m,startTime:i}},PE=(e,t,r)=>{try{let{destination:n,pipeOptions:{from:o,to:i,unpipeSignal:s}={}}=kE(e,t,...r),u=Rr(n,i);return{destination:n,destinationStream:u,from:o,unpipeSignal:s}}catch(n){return{destinationError:n}}},kE=(e,t,r,...n)=>{if(Array.isArray(r))return{destination:t(Gf,e)(r,...n),pipeOptions:e};if(typeof r=="string"||r instanceof URL||So(r)){if(Object.keys(e).length>0)throw new TypeError('Please use .pipe("file", ..., options) or .pipe(execa("file", ..., options)) instead of .pipe(options)("file", ...).');let[o,i,s]=Dr(r,...n);return{destination:t(Gf)(o,i,s),pipeOptions:s}}if(Q.has(r)){if(Object.keys(e).length>0)throw new TypeError("Please use .pipe(options)`command` or .pipe($(options)`command`) instead of .pipe(options)($`command`).");return{destination:r,pipeOptions:n[0]}}throw new TypeError(`The first argument must be a template string, an options object, or an Execa subprocess: ${r}`)},Gf=({options:e})=>({options:{...e,stdin:"pipe",piped:!0}}),LE=(e,t)=>{try{return{sourceStream:Je(e,t)}}catch(r){return{sourceError:r}}};var Yf=({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n,fileDescriptors:o,sourceOptions:i,startTime:s})=>{let u=NE({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n});if(u!==void 0)throw ki({error:u,fileDescriptors:o,sourceOptions:i,startTime:s})},NE=({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n})=>{if(t!==void 0&&n!==void 0)return n;if(n!==void 0)return Pi(e),n;if(t!==void 0)return Mi(r),t},ki=({error:e,fileDescriptors:t,sourceOptions:r,startTime:n})=>Qe({error:e,command:Wf,escapedCommand:Wf,fileDescriptors:t,options:r,startTime:n,isSync:!1}),Wf="source.pipe(destination)";var qf=async e=>{let[{status:t,reason:r,value:n=r},{status:o,reason:i,value:s=i}]=await e;if(s.pipedFrom.includes(n)||s.pipedFrom.push(n),o==="rejected")throw s;if(t==="rejected")throw n;return s};var Vf=require("node:stream/promises");var Hf=(e,t,r)=>{let n=Fn.has(t)?jE(e,t):$E(e,t);return we(e,GE,r.signal),we(t,zE,r.signal),UE(t),n},$E=(e,t)=>{let r=Be([e]);return ot(r,t),Fn.set(t,r),r},jE=(e,t)=>{let r=Fn.get(t);return r.add(e),r},UE=async e=>{try{await(0,Vf.finished)(e,{cleanup:!0,readable:!1,writable:!0})}catch{}Fn.delete(e)},Fn=new WeakMap,GE=2,zE=1;var Kf=require("node:util");var Jf=(e,t)=>e===void 0?[]:[WE(e,t)],WE=async(e,{sourceStream:t,mergedStream:r,fileDescriptors:n,sourceOptions:o,startTime:i})=>{await(0,Kf.aborted)(e,t),await r.remove(t);let s=new Error("Pipe canceled by `unpipeSignal` option.");throw ki({error:s,fileDescriptors:n,sourceOptions:o,startTime:i})};var gn=(e,...t)=>{if(O(t[0]))return gn.bind(void 0,{...e,boundOptions:{...e.boundOptions,...t[0]}});let{destination:r,...n}=zf(e,...t),o=YE({...n,destination:r});return o.pipe=gn.bind(void 0,{...e,source:r,sourcePromise:o,boundOptions:{}}),o},YE=async({sourcePromise:e,sourceStream:t,sourceOptions:r,sourceError:n,destination:o,destinationStream:i,destinationError:s,unpipeSignal:u,fileDescriptors:a,startTime:c})=>{let l=qE(e,o);Yf({sourceStream:t,sourceError:n,destinationStream:i,destinationError:s,fileDescriptors:a,sourceOptions:r,startTime:c});let D=new AbortController;try{let f=Hf(t,i,D);return await Promise.race([qf(l),...Jf(u,{sourceStream:t,mergedStream:f,sourceOptions:r,fileDescriptors:a,startTime:c})])}finally{D.abort()}},qE=(e,t)=>Promise.allSettled([e,t]);var tp=require("node:timers/promises");var Zf=require("node:events"),Qf=require("node:stream");var En=({subprocessStdout:e,subprocess:t,binary:r,shouldEncode:n,encoding:o,preserveNewlines:i})=>{let s=new AbortController;return VE(t,s),ep({stream:e,controller:s,binary:r,shouldEncode:!e.readableObjectMode&&n,encoding:o,shouldSplit:!e.readableObjectMode,preserveNewlines:i})},VE=async(e,t)=>{try{await e}catch{}finally{t.abort()}},Li=({stream:e,onStreamEnd:t,lines:r,encoding:n,stripFinalNewline:o,allMixed:i})=>{let s=new AbortController;HE(t,s,e);let u=e.readableObjectMode&&!i;return ep({stream:e,controller:s,binary:n==="buffer",shouldEncode:!u,encoding:n,shouldSplit:!u&&r,preserveNewlines:!o})},HE=async(e,t,r)=>{try{await e}catch{r.destroy()}finally{t.abort()}},ep=({stream:e,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s})=>{let u=(0,Zf.on)(e,"data",{signal:t.signal,highWaterMark:Xf,highWatermark:Xf});return KE({onStdoutChunk:u,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s})},Ni=(0,Qf.getDefaultHighWaterMark)(!0),Xf=Ni,KE=async function*({onStdoutChunk:e,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s}){let u=JE({binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s});try{for await(let[a]of e)yield*Ae(a,u,0)}catch(a){if(!t.signal.aborted)throw a}finally{yield*Rt(u)}},JE=({binary:e,shouldEncode:t,encoding:r,shouldSplit:n,preserveNewlines:o})=>[an(e,r,!t),un(e,o,!n,{})].filter(Boolean);var rp=async({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,buffer:o,maxBuffer:i,lines:s,allMixed:u,stripFinalNewline:a,verboseInfo:c,streamInfo:l})=>{let D=XE({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,allMixed:u,verboseInfo:c,streamInfo:l});if(!o){await Promise.all([ZE(e),D]);return}let f=Ei(a,r),p=Li({stream:e,onStreamEnd:t,lines:s,encoding:n,stripFinalNewline:f,allMixed:u}),[m]=await Promise.all([QE({stream:e,iterable:p,fdNumber:r,encoding:n,maxBuffer:i,lines:s}),D]);return m},XE=async({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,allMixed:o,verboseInfo:i,streamInfo:{fileDescriptors:s}})=>{if(!Dn({stdioItems:s[r]?.stdioItems,encoding:n,verboseInfo:i,fdNumber:r}))return;let u=Li({stream:e,onStreamEnd:t,lines:!0,encoding:n,stripFinalNewline:!0,allMixed:o});await ff(u,e,r,i)},ZE=async e=>{await(0,tp.setImmediate)(),e.readableFlowing===null&&e.resume()},QE=async({stream:e,stream:{readableObjectMode:t},iterable:r,fdNumber:n,encoding:o,maxBuffer:i,lines:s})=>{try{return t||s?await Kr(r,{maxBuffer:i}):o==="buffer"?new Uint8Array(await Jr(r,{maxBuffer:i})):await Zr(r,{maxBuffer:i})}catch(u){return np(aD({error:u,stream:e,readableObjectMode:t,lines:s,encoding:o,fdNumber:n}))}},$i=async e=>{try{return await e}catch(t){return np(t)}},np=({bufferedData:e})=>ra(e)?new Uint8Array(e):e;var ip=require("node:stream/promises"),Nt=async(e,t,r,{isSameDirection:n,stopOnExit:o=!1}={})=>{let i=eC(e,r),s=new AbortController;try{await Promise.race([...o?[r.exitPromise]:[],(0,ip.finished)(e,{cleanup:!0,signal:s.signal})])}catch(u){i.stdinCleanedUp||nC(u,t,r,n)}finally{s.abort()}},eC=(e,{originalStreams:[t],subprocess:r})=>{let n={stdinCleanedUp:!1};return e===t&&tC(e,r,n),n},tC=(e,t,r)=>{let{_destroy:n}=e;e._destroy=(...o)=>{rC(t,r),n.call(e,...o)}},rC=({exitCode:e,signalCode:t},r)=>{(e!==null||t!==null)&&(r.stdinCleanedUp=!0)},nC=(e,t,r,n)=>{if(!oC(e,t,r,n))throw e},oC=(e,t,r,n=!0)=>r.propagating?op(e)||Cn(e):(r.propagating=!0,ji(r,t)===n?op(e):Cn(e)),ji=({fileDescriptors:e},t)=>t!=="all"&&e[t].direction==="input",Cn=e=>e?.code==="ERR_STREAM_PREMATURE_CLOSE",op=e=>e?.code==="EPIPE";var sp=({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:i,verboseInfo:s,streamInfo:u})=>e.stdio.map((a,c)=>Ui({stream:a,fdNumber:c,encoding:t,buffer:r[c],maxBuffer:n[c],lines:o[c],allMixed:!1,stripFinalNewline:i,verboseInfo:s,streamInfo:u})),Ui=async({stream:e,fdNumber:t,encoding:r,buffer:n,maxBuffer:o,lines:i,allMixed:s,stripFinalNewline:u,verboseInfo:a,streamInfo:c})=>{if(!e)return;let l=Nt(e,t,c);if(ji(c,t)){await l;return}let[D]=await Promise.all([rp({stream:e,onStreamEnd:l,fdNumber:t,encoding:r,buffer:n,maxBuffer:o,lines:i,allMixed:s,stripFinalNewline:u,verboseInfo:a,streamInfo:c}),l]);return D};var up=({stdout:e,stderr:t},{all:r})=>r&&(e||t)?Be([e,t].filter(Boolean)):void 0,ap=({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:i,verboseInfo:s,streamInfo:u})=>Ui({...iC(e,r),fdNumber:"all",encoding:t,maxBuffer:n[1]+n[2],lines:o[1]||o[2],allMixed:sC(e),stripFinalNewline:i,verboseInfo:s,streamInfo:u}),iC=({stdout:e,stderr:t,all:r},[,n,o])=>{let i=n||o;return i?n?o?{stream:r,buffer:i}:{stream:e,buffer:i}:{stream:t,buffer:i}:{stream:r,buffer:i}},sC=({all:e,stdout:t,stderr:r})=>e&&t&&r&&t.readableObjectMode!==r.readableObjectMode;var pp=require("node:events");var cp=e=>We(e,"ipc"),lp=(e,t)=>{let r=yr(e);X({type:"ipc",verboseMessage:r,fdNumber:"ipc",verboseInfo:t})};var Dp=async({subprocess:e,buffer:t,maxBuffer:r,ipc:n,ipcOutput:o,verboseInfo:i})=>{if(!n)return o;let s=cp(i),u=ue(t,"ipc"),a=ue(r,"ipc");for await(let c of Bi({anyProcess:e,channel:e.channel,isSubprocess:!1,ipc:n,shouldAwait:!1,reference:!0}))u&&(cD(e,o,a),o.push(c)),s&&lp(c,i);return o},fp=async(e,t)=>(await Promise.allSettled([e]),t);var dp=async({subprocess:e,options:{encoding:t,buffer:r,maxBuffer:n,lines:o,timeoutDuration:i,cancelSignal:s,gracefulCancel:u,forceKillAfterDelay:a,stripFinalNewline:c,ipc:l,ipcInput:D},context:f,verboseInfo:p,fileDescriptors:m,originalStreams:y,onInternalError:v,controller:x})=>{let I=Ff(e,f),P={originalStreams:y,fileDescriptors:m,subprocess:e,exitPromise:I,propagating:!1},L=sp({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:c,verboseInfo:p,streamInfo:P}),ne=ap({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:c,verboseInfo:p,streamInfo:P}),le=[],Ce=Dp({subprocess:e,buffer:r,maxBuffer:n,ipc:l,ipcOutput:le,verboseInfo:p}),ve=uC(y,e,P),De=aC(m,P);try{return await Promise.race([Promise.all([{},Ef(I),Promise.all(L),ne,Ce,kl(e,D),...ve,...De]),v,cC(e,x),...vl(e,i,f,x),...Wc({subprocess:e,cancelSignal:s,gracefulCancel:u,context:f,controller:x}),...Tl({subprocess:e,cancelSignal:s,gracefulCancel:u,forceKillAfterDelay:a,context:f,controller:x})])}catch(On){return f.terminationReason??="other",Promise.all([{error:On},I,Promise.all(L.map(st=>$i(st))),$i(ne),fp(Ce,le),Promise.allSettled(ve),Promise.allSettled(De)])}},uC=(e,t,r)=>e.map((n,o)=>n===t.stdio[o]?void 0:Nt(n,o,r)),aC=(e,t)=>e.flatMap(({stdioItems:r},n)=>r.filter(({value:o,stream:i=o})=>J(i,{checkOpen:!1})&&!V(i)).map(({type:o,value:i,stream:s=i})=>Nt(s,n,t,{isSameDirection:z.has(o),stopOnExit:o==="native"}))),cC=async(e,{signal:t})=>{let[r]=await(0,pp.once)(e,"error",{signal:t});throw r};var mp=()=>({readableDestroy:new WeakMap,writableFinal:new WeakMap,writableDestroy:new WeakMap}),$t=(e,t,r)=>{let n=e[r];n.has(t)||n.set(t,[]);let o=n.get(t),i=Z();return o.push(i),{resolve:i.resolve.bind(i),promises:o}},it=async({resolve:e,promises:t},r)=>{e();let[n]=await Promise.race([Promise.allSettled([!0,r]),Promise.all([!1,...t])]);return!n};var Fp=require("node:stream"),gp=require("node:util");var Gi=require("node:stream/promises");var zi=async e=>{if(e!==void 0)try{await Wi(e)}catch{}},hp=async e=>{if(e!==void 0)try{await Yi(e)}catch{}},Wi=async e=>{await(0,Gi.finished)(e,{cleanup:!0,readable:!1,writable:!0})},Yi=async e=>{await(0,Gi.finished)(e,{cleanup:!0,readable:!0,writable:!1})},yn=async(e,t)=>{if(await e,t)throw t},bn=(e,t,r)=>{r&&!Cn(r)?e.destroy(r):t&&e.destroy()};var Ep=({subprocess:e,concurrentStreams:t,encoding:r},{from:n,binary:o=!0,preserveNewlines:i=!0}={})=>{let s=o||k.has(r),{subprocessStdout:u,waitReadableDestroy:a}=qi(e,n,t),{readableEncoding:c,readableObjectMode:l,readableHighWaterMark:D}=Vi(u,s),{read:f,onStdoutDataDone:p}=Hi({subprocessStdout:u,subprocess:e,binary:s,encoding:r,preserveNewlines:i}),m=new Fp.Readable({read:f,destroy:(0,gp.callbackify)(Ji.bind(void 0,{subprocessStdout:u,subprocess:e,waitReadableDestroy:a})),highWaterMark:D,objectMode:l,encoding:c});return Ki({subprocessStdout:u,onStdoutDataDone:p,readable:m,subprocess:e}),m},qi=(e,t,r)=>{let n=Je(e,t),o=$t(r,n,"readableDestroy");return{subprocessStdout:n,waitReadableDestroy:o}},Vi=({readableEncoding:e,readableObjectMode:t,readableHighWaterMark:r},n)=>n?{readableEncoding:e,readableObjectMode:t,readableHighWaterMark:r}:{readableEncoding:e,readableObjectMode:!0,readableHighWaterMark:Ni},Hi=({subprocessStdout:e,subprocess:t,binary:r,encoding:n,preserveNewlines:o})=>{let i=Z(),s=En({subprocessStdout:e,subprocess:t,binary:r,shouldEncode:!r,encoding:n,preserveNewlines:o});return{read(){lC(this,s,i)},onStdoutDataDone:i}},lC=async(e,t,r)=>{try{let{value:n,done:o}=await t.next();o?r.resolve():e.push(n)}catch{}},Ki=async({subprocessStdout:e,onStdoutDataDone:t,readable:r,subprocess:n,subprocessStdin:o})=>{try{await Yi(e),await n,await zi(o),await t,r.readable&&r.push(null)}catch(i){await zi(o),Cp(r,i)}},Ji=async({subprocessStdout:e,subprocess:t,waitReadableDestroy:r},n)=>{await it(r,t)&&(Cp(e,n),await yn(t,n))},Cp=(e,t)=>{bn(e,e.readable,t)};var yp=require("node:stream"),Xi=require("node:util");var bp=({subprocess:e,concurrentStreams:t},{to:r}={})=>{let{subprocessStdin:n,waitWritableFinal:o,waitWritableDestroy:i}=Zi(e,r,t),s=new yp.Writable({...Qi(n,e,o),destroy:(0,Xi.callbackify)(ts.bind(void 0,{subprocessStdin:n,subprocess:e,waitWritableFinal:o,waitWritableDestroy:i})),highWaterMark:n.writableHighWaterMark,objectMode:n.writableObjectMode});return es(n,s),s},Zi=(e,t,r)=>{let n=Rr(e,t),o=$t(r,n,"writableFinal"),i=$t(r,n,"writableDestroy");return{subprocessStdin:n,waitWritableFinal:o,waitWritableDestroy:i}},Qi=(e,t,r)=>({write:DC.bind(void 0,e),final:(0,Xi.callbackify)(fC.bind(void 0,e,t,r))}),DC=(e,t,r,n)=>{e.write(t,r)?n():e.once("drain",n)},fC=async(e,t,r)=>{await it(r,t)&&(e.writable&&e.end(),await t)},es=async(e,t,r)=>{try{await Wi(e),t.writable&&t.end()}catch(n){await hp(r),wp(t,n)}},ts=async({subprocessStdin:e,subprocess:t,waitWritableFinal:r,waitWritableDestroy:n},o)=>{await it(r,t),await it(n,t)&&(wp(e,o),await yn(t,o))},wp=(e,t)=>{bn(e,e.writable,t)};var xp=require("node:stream"),Sp=require("node:util");var Ap=({subprocess:e,concurrentStreams:t,encoding:r},{from:n,to:o,binary:i=!0,preserveNewlines:s=!0}={})=>{let u=i||k.has(r),{subprocessStdout:a,waitReadableDestroy:c}=qi(e,n,t),{subprocessStdin:l,waitWritableFinal:D,waitWritableDestroy:f}=Zi(e,o,t),{readableEncoding:p,readableObjectMode:m,readableHighWaterMark:y}=Vi(a,u),{read:v,onStdoutDataDone:x}=Hi({subprocessStdout:a,subprocess:e,binary:u,encoding:r,preserveNewlines:s}),I=new xp.Duplex({read:v,...Qi(l,e,D),destroy:(0,Sp.callbackify)(pC.bind(void 0,{subprocessStdout:a,subprocessStdin:l,subprocess:e,waitReadableDestroy:c,waitWritableFinal:D,waitWritableDestroy:f})),readableHighWaterMark:y,writableHighWaterMark:l.writableHighWaterMark,readableObjectMode:m,writableObjectMode:l.writableObjectMode,encoding:p});return Ki({subprocessStdout:a,onStdoutDataDone:x,readable:I,subprocess:e,subprocessStdin:l}),es(l,I,a),I},pC=async({subprocessStdout:e,subprocessStdin:t,subprocess:r,waitReadableDestroy:n,waitWritableFinal:o,waitWritableDestroy:i},s)=>{await Promise.all([Ji({subprocessStdout:e,subprocess:r,waitReadableDestroy:n},s),ts({subprocessStdin:t,subprocess:r,waitWritableFinal:o,waitWritableDestroy:i},s)])};var rs=(e,t,{from:r,binary:n=!1,preserveNewlines:o=!1}={})=>{let i=n||k.has(t),s=Je(e,r),u=En({subprocessStdout:s,subprocess:e,binary:i,shouldEncode:!0,encoding:t,preserveNewlines:o});return dC(u,s,e)},dC=async function*(e,t,r){try{yield*e}finally{t.readable&&t.destroy(),await r}};var Bp=(e,{encoding:t})=>{let r=mp();e.readable=Ep.bind(void 0,{subprocess:e,concurrentStreams:r,encoding:t}),e.writable=bp.bind(void 0,{subprocess:e,concurrentStreams:r}),e.duplex=Ap.bind(void 0,{subprocess:e,concurrentStreams:r,encoding:t}),e.iterable=rs.bind(void 0,e,t),e[Symbol.asyncIterator]=rs.bind(void 0,e,t,{})};var Tp=(e,t)=>{for(let[r,n]of hC){let o=n.value.bind(t);Reflect.defineProperty(e,r,{...n,value:o})}},mC=(async()=>{})().constructor.prototype,hC=["then","catch","finally"].map(e=>[e,Reflect.getOwnPropertyDescriptor(mC,e)]);var vp=(e,t,r,n)=>{let{file:o,commandArguments:i,command:s,escapedCommand:u,startTime:a,verboseInfo:c,options:l,fileDescriptors:D}=FC(e,t,r),{subprocess:f,promise:p}=EC({file:o,commandArguments:i,options:l,startTime:a,verboseInfo:c,command:s,escapedCommand:u,fileDescriptors:D});return f.pipe=gn.bind(void 0,{source:f,sourcePromise:p,boundOptions:{},createNested:n}),Tp(f,p),Q.set(f,{options:l,fileDescriptors:D}),f},FC=(e,t,r)=>{let{command:n,escapedCommand:o,startTime:i,verboseInfo:s}=wr(e,t,r),{file:u,commandArguments:a,options:c}=Wr(e,t,r),l=gC(c),D=If(l,s);return{file:u,commandArguments:a,command:n,escapedCommand:o,startTime:i,verboseInfo:s,options:l,fileDescriptors:D}},gC=({timeout:e,signal:t,...r})=>{if(t!==void 0)throw new TypeError('The "signal" option has been renamed to "cancelSignal" instead.');return{...r,timeoutDuration:e}},EC=({file:e,commandArguments:t,options:r,startTime:n,verboseInfo:o,command:i,escapedCommand:s,fileDescriptors:u})=>{let a;try{a=(0,Op.spawn)(...Yr(e,t,r))}catch(m){return Of({error:m,command:i,escapedCommand:s,fileDescriptors:u,options:r,startTime:n,verboseInfo:o})}let c=new AbortController;(0,_p.setMaxListeners)(Number.POSITIVE_INFINITY,c.signal);let l=[...a.stdio];$f(a,u,c),Uf(a,r,c);let D={},f=Z();a.kill=Uc.bind(void 0,{kill:a.kill.bind(a),options:r,onInternalError:f,context:D,controller:c}),a.all=up(a,r),Bp(a,r),Af(a,r);let p=CC({subprocess:a,options:r,startTime:n,verboseInfo:o,fileDescriptors:u,originalStreams:l,command:i,escapedCommand:s,context:D,onInternalError:f,controller:c});return{subprocess:a,promise:p}},CC=async({subprocess:e,options:t,startTime:r,verboseInfo:n,fileDescriptors:o,originalStreams:i,command:s,escapedCommand:u,context:a,onInternalError:c,controller:l})=>{let[D,[f,p],m,y,v]=await dp({subprocess:e,options:t,context:a,verboseInfo:n,fileDescriptors:o,originalStreams:i,onInternalError:c,controller:l});l.abort(),c.resolve();let x=m.map((L,ne)=>ce(L,t,ne)),I=ce(y,t,"all"),P=yC({errorInfo:D,exitCode:f,signal:p,stdio:x,all:I,ipcOutput:v,context:a,options:t,command:s,escapedCommand:u,startTime:r});return et(P,n,t)},yC=({errorInfo:e,exitCode:t,signal:r,stdio:n,all:o,ipcOutput:i,context:s,options:u,command:a,escapedCommand:c,startTime:l})=>"error"in e?vt({error:e.error,command:a,escapedCommand:c,timedOut:s.terminationReason==="timeout",isCanceled:s.terminationReason==="cancel"||s.terminationReason==="gracefulCancel",isGracefullyCanceled:s.terminationReason==="gracefulCancel",isMaxBuffer:e.error instanceof ee,isForcefullyTerminated:s.isForcefullyTerminated,exitCode:t,signal:r,stdio:n,all:o,ipcOutput:i,options:u,startTime:l,isSync:!1}):en({command:a,escapedCommand:c,stdio:n,all:o,ipcOutput:i,options:u,startTime:l});var wn=(e,t)=>{let r=Object.fromEntries(Object.entries(t).map(([n,o])=>[n,bC(n,e[n],o)]));return{...e,...r}},bC=(e,t,r)=>wC.has(e)&&O(t)&&O(r)?{...t,...r}:r,wC=new Set(["env",...Oo]);var Ee=(e,t,r,n)=>{let o=(s,u,a)=>Ee(s,u,r,a),i=(...s)=>xC({mapArguments:e,deepOptions:r,boundOptions:t,setBoundExeca:n,createNested:o},...s);return n!==void 0&&n(i,o,t),i},xC=({mapArguments:e,deepOptions:t={},boundOptions:r={},setBoundExeca:n,createNested:o},i,...s)=>{if(O(i))return o(e,wn(r,i),n);let{file:u,commandArguments:a,options:c,isSync:l}=SC({mapArguments:e,firstArgument:i,nextArguments:s,deepOptions:t,boundOptions:r});return l?bf(u,a,c):vp(u,a,c,o)},SC=({mapArguments:e,firstArgument:t,nextArguments:r,deepOptions:n,boundOptions:o})=>{let i=ca(t)?la(t,r):[t,...r],[s,u,a]=Dr(...i),c=wn(wn(n,o),a),{file:l=s,commandArguments:D=u,options:f=c,isSync:p=!1}=e({file:s,commandArguments:u,options:c});return{file:l,commandArguments:D,options:f,isSync:p}};var Rp=({file:e,commandArguments:t})=>Mp(e,t),Ip=({file:e,commandArguments:t})=>({...Mp(e,t),isSync:!0}),Mp=(e,t)=>{if(t.length>0)throw new TypeError(`The command and its arguments must be passed as a single string: ${e} ${t}.`);let[r,...n]=AC(e);return{file:r,commandArguments:n}},AC=e=>{if(typeof e!="string")throw new TypeError(`The command must be a string: ${String(e)}.`);let t=e.trim();if(t==="")return[];let r=[];for(let n of t.split(BC)){let o=r.at(-1);o&&o.endsWith("\\")?r[r.length-1]=`${o.slice(0,-1)} ${n}`:r.push(n)}return r},BC=/ +/g;var Pp=(e,t,r)=>{e.sync=t(TC,r),e.s=e.sync},kp=({options:e})=>Lp(e),TC=({options:e})=>({...Lp(e),isSync:!0}),Lp=e=>({options:{..._C(e),...e}}),_C=({input:e,inputFile:t,stdio:r})=>e===void 0&&t===void 0&&r===void 0?{stdin:"inherit"}:{},Np={preferLocal:!0};var $p=Ee(()=>({})),pT=Ee(()=>({isSync:!0})),dT=Ee(Rp),mT=Ee(Ip),hT=Ee(Rl),FT=Ee(kp,{},Np,Pp),{sendMessage:gT,getOneMessage:ET,getEachMessage:CT,getCancelSignal:yT}=Bf();async function Gp(e){if(e=xn.default.resolve(e),e.endsWith("package.json")&&(e=e.replace(/package\.json$/,"")),!jp.default.existsSync(e))throw`Directory ${e} does not exist`;let t=xn.default.join(Up.tmpdir(),xn.default.basename(e));try{await $p("ray",["build","--output",t],{cwd:e})}catch{throw`Error building extension ${e}`}return t}var An=F(require("fs"));async function ns(e,t=!1){e=Sn.default.resolve(e);let r=An.default.statSync(e).isDirectory(),n=Sn.default.join(e,"package.json");if(e.endsWith("package.json")){if(!t){let o=Fo(`Building extension ${e}`).start();try{let i=await Gp(e);e=Sn.default.join(i,"package.json")}finally{o.stop()}}return JSON.parse(An.default.readFileSync(e).toString())}if(r&&An.default.existsSync(n))return ns(n);throw`${e}: no package.json file found`}async function zp(e,{apiEndpoint:t,...r},n){let o=Dt("raycastAccessToken"),i=await fetch(t,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o}`},body:JSON.stringify({packageJson:e,options:r})});if(!i.ok||i.body===null)throw await i.text();let s=i.body.getReader(),u=new TextDecoder("utf-8"),{done:a,value:c}=await s.read(),l="";for(;!a;){for(l+=u.decode(c,{stream:!0});l.includes(`

`);){let D=l.indexOf(`

`)+2,f=l.substring(0,D);l=l.substring(D),n(JSON.parse(f))}({done:a,value:c}=await s.read())}l&&(n(JSON.parse(l)),l="")}var w=F(Un());var OC=e=>e==="meetsCriteria"?"":e.replace(/([A-Z])/g," $1").trim().split(" ").map(t=>t.charAt(0).toUpperCase()+t.slice(1)).join(" ");function Wp(e){if(typeof e=="string")return Wp({name:e});let t=`Calls Tool ${e.name}`;return e.arguments&&(t+=` with arguments ${Object.keys(e.arguments).join(", ")}`),t}var Bn=e=>{if("meetsCriteria"in e)return e.meetsCriteria;if("callsTool"in e)return Wp(e.callsTool);if("not"in e){let i=Bn(e.not).split(" ");return i[0].endsWith("s")&&(i[0]=i[0].slice(0,-1)),`Doesn't ${i.join(" ")}`}if("or"in e)return`${e.or.map(Bn).join(" or ")}`;let t=Object.keys(e)[0],r=e[t],n=OC(t);return r?`${n} "${r}"`:n};var Te=100,_e=e=>{let t=e.replace(/\u001b\[\d+m/g,"");return Array.from(t).reduce((r,n)=>r+(/\p{Emoji_Presentation}/u.test(n)?2:1),0)},Yp=(e,t=75)=>e?_e(e)<=t?e:e.slice(0,t)+"...":"";function vC(e,t,r=" "){let n=_e(e);return n>=t?e:e+r.repeat(t-n)}function RC(e,t,r=" "){let n=_e(e);return n>=t?e:r.repeat(t-n)+e}function qp(e){let t=e.reduce((s,u)=>Math.max(s,_e(u.name)),0),r=w.default.grey("\u250C"+"\u2500".repeat(t+2)+"\u252C"+"\u2500".repeat(Te)+"\u2510")+`
`,n=w.default.grey("\u251C"+"\u2500".repeat(t+2)+"\u253C"+"\u2500".repeat(Te)+"\u2524")+`
`,o=w.default.grey("\u2514"+"\u2500".repeat(t+2)+"\u2534"+"\u2500".repeat(Te)+"\u2518")+`
`,i=r;return e.forEach((s,u)=>{let a=s.content.split(`
`).flatMap(l=>{let D=[];for(let f=0;f<_e(l);f+=Te)D.push(w.default.grey(l.slice(f,f+Te)));return D.length?D:[""]});i+=w.default.grey("\u2502")+vC(s.name,t+2)+w.default.grey("\u2502");let c=a[0];i+=c+" ".repeat(Te-_e(c))+w.default.grey(`\u2502
`);for(let l=1;l<a.length;l++){i+=w.default.grey("\u2502")+" ".repeat(t+2)+w.default.grey("\u2502");let D=a[l];i+=D+" ".repeat(Te-_e(D))+w.default.grey(`\u2502
`)}u===e.length-1?i+=o:i+=n}),i}function IC(e){let t=e.input,r="messages"in t?t.messages[0]?.content.text:t,n=e.score*100,o=e.assertionResults||[],i;e.status==="skipped"?i=w.default.yellow:e.status==="completed"&&n>=100?i=w.default.green:i=w.default.red;let u=(e.status==="error"||e.status==="completed"&&n<100?w.default.red:w.default.white)(`${e.index+1}. ${Yp(r,200)}
`),a=e.status==="skipped"?"skipped":`Score: ${Math.round(n)}%`;if(u+=i(`   ${RC(a,4)}    `),u+=`

`,e.status==="error")u+=qp([{name:"Error",content:i(e.error)}]);else if(e.status==="completed"){let l=[{name:"Criteria",content:e.expected.map((D,f)=>{let p=o[f]?.result===1,m=Bn(D);return`${p?w.default.green("\u2714"):w.default.red("\u2718")} ${w.default.grey(m)}`}).join(`
`)},{name:"Output",content:w.default.grey(Yp(e.output.text,400))}];if(e.output.calledTools.length>0){let D=e.output.calledTools.map(f=>w.default.grey(`${f.name}(${f.arguments})`)).join(`
`);l.push({name:"Tools",content:D})}u+=qp(l),u+=`
`}return u}function Vp(e){console.log(),e.forEach(u=>{process.stdout.write(IC(u))});let t=e.length?e.reduce((u,a)=>u+a.score,0)/e.filter(u=>u.status!=="skipped").length*100:0;console.log();let r=e.length,n=e.filter(u=>u.status==="error"||u.status==="completed"&&u.score<1).length,o=e.filter(u=>u.status==="completed"&&u.score===1).length,i=e.filter(u=>u.status==="skipped").length,s="Total Score: ";s+=t===100?w.default.green(`${Math.round(t)}%`):w.default.red(`${Math.round(t)}%`),s+=" \u2502 ",s+=w.default.blue(`${o}/${r} Passed`),i>0&&(s+=" \u2502 ",s+=w.default.yellow(`${i} Skipped`)),n>0&&(s+=" \u2502 ",s+=w.default.red(`\u26A0\uFE0F ${n} Failed`)),console.log(s),console.log()}var ds=F(Od()),_n=class e extends lr{static description="Run AI evals defined in extension package.json";static flags={skipBuild:jt.Flags.boolean({char:"s",default:!1,description:"Skip extension build step"}),extension:jt.Flags.string({char:"e",description:"Path to extension. Current directory is used by default",default:"./"}),apiEndpoint:jt.Flags.string({char:"a",description:"AI Evals API endpoint",default:"https://ai-evals.raycast.com/run-evals",hidden:!0}),only:jt.Flags.string({char:"o",description:"Only run specified evals (0-2, 1, 1,2,3)",default:""})};async run(){let{flags:t}=await this.parse(e);Zu();try{let r=await ns(t.extension,t.skipBuild);if(!r){console.error("Error parsing package.json");return}let n=0,o=[],i=new ds.default.SingleBar({clearOnComplete:!1,hideCursor:!0,format:vd.default.blue("\u{1F4BB} Running AI Evals {bar} | {name} | {value}/{total}")},ds.default.Presets.shades_classic);i.start(r?.ai?.evals?.length||0,0,{name:r?.name||"Unknown"});try{let s={apiEndpoint:t.apiEndpoint,only:t.only};await zp(r,s,u=>{i.update(++n),o[u.index]=u})}finally{i.stop()}Vp(o)}catch(r){console.error(r)}}};
