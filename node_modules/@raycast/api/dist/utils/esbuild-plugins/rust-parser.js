"use strict";var d=Object.create;var p=Object.defineProperty;var v=Object.getOwnPropertyDescriptor;var S=Object.getOwnPropertyNames;var $=Object.getPrototypeOf,x=Object.prototype.hasOwnProperty;var F=(i,r)=>{for(var t in r)p(i,t,{get:r[t],enumerable:!0})},f=(i,r,t,e)=>{if(r&&typeof r=="object"||typeof r=="function")for(let s of S(r))!x.call(i,s)&&s!==t&&p(i,s,{get:()=>r[s],enumerable:!(e=v(r,s))||e.enumerable});return i};var y=(i,r,t)=>(t=i!=null?d($(i)):{},f(r||!i||!i.__esModule?p(t,"default",{value:i,enumerable:!0}):t,i)),w=i=>f(p({},"__esModule",{value:!0}),i);var P={};F(P,{RustParser:()=>m,TypeScriptGenerator:()=>l});module.exports=w(P);var o=y(require("node:fs")),u=y(require("node:path")),m=class i{structs=new Map;functions=[];static analyzePackage(r){let t=new i;return t.parseDirectory(u.join(r,"src")),{functions:t.functions,structs:Array.from(t.structs.values())}}parseDirectory(r){if(!o.existsSync(r))return;let t=o.readdirSync(r);for(let e of t){let s=u.join(r,e);if(o.statSync(s).isDirectory())this.parseDirectory(s);else if(e.endsWith(".rs")){let n=o.readFileSync(s,"utf-8");this.parseRustFile(n)}}}parseRustFile(r){let t=this.removeComments(r);this.parseStructs(t),this.parseFunctions(t)}removeComments(r){return r=r.replace(/\/\/.*$/gm,""),r=r.replace(/\/\*[\s\S]*?\*\//g,""),r}parseStructs(r){let t=/#\[derive\([^)]*(?:Serialize|Deserialize)[^)]*\)\]\s*(?:pub\s+)?struct\s+(\w+)\s*\{([^}]+)\}/g,e;for(;(e=t.exec(r))!==null;){let s=e[1],n=e[2],a=this.parseStructFields(n);this.structs.set(s,{name:s,fields:a})}}parseStructFields(r){let t=[],e=/(?:pub\s+)?(\w+)\s*:\s*([^,\n]+)/g,s;for(;(s=e.exec(r))!==null;){let n=s[1],a=s[2].trim();t.push({name:n,rustType:a,tsType:this.rustTypeToTypeScript(a)})}return t}parseFunctions(r){let t=/#\[raycast\]\s*(?:pub\s+)?(async\s+)?fn\s+(\w+)\s*\(([^)]*)\)\s*(?:->\s*([^{]+?))?\s*\{/g,e;for(;(e=t.exec(r))!==null;){let s=!!e[1],n=e[2],a=e[3],c=e[4]?.trim()||"()",T=this.parseParameters(a),{returnType:h,returnTypeTs:g,isResult:R}=this.parseReturnType(c);this.functions.push({name:n,isAsync:s,parameters:T,returnType:h,returnTypeTs:g,isResult:R})}}parseParameters(r){let t=[];if(!r.trim())return t;let e=this.splitParameters(r);for(let s of e){let n=s.trim().match(/(\w+)\s*:\s*(.+)/);if(n){let a=n[1],c=n[2].trim();t.push({name:a,rustType:c,tsType:this.rustTypeToTypeScript(c)})}}return t}splitParameters(r){let t=[],e="",s=0;for(let n of r){if(n==="<")s++;else if(n===">")s--;else if(n===","&&s===0){t.push(e.trim()),e="";continue}e+=n}return e.trim()&&t.push(e.trim()),t}parseReturnType(r){let t=r.trim(),e=t.match(/Result<([^,]+),\s*(.+)>/);if(e){let s=e[1].trim();return{returnType:t,returnTypeTs:this.rustTypeToTypeScript(s),isResult:!0}}return{returnType:t,returnTypeTs:this.rustTypeToTypeScript(t),isResult:!1}}rustTypeToTypeScript(r){let t=r.trim();if(t==="()")return"void";if(t==="String"||t==="&str")return"string";if(t==="bool")return"boolean";if(t.match(/^[ui]\d+$/)||t.match(/^f\d+$/))return"number";let e=t.match(/Option<(.+)>/);if(e)return`${this.rustTypeToTypeScript(e[1])} | null`;let s=t.match(/Vec<(.+)>/);if(s)return`${this.rustTypeToTypeScript(s[1])}[]`;let n=t.match(/Result<([^,]+),\s*(.+)>/);return n?this.rustTypeToTypeScript(n[1]):(this.structs.has(t),t)}},l=class{static generateDefinitions(r,t){let e="";for(let s of t){e+=`export interface ${s.name} {
`;for(let n of s.fields)e+=`  ${n.name}: ${n.tsType};
`;e+=`}

`}for(let s of r){let n=s.parameters.map(c=>`${c.name}: ${c.tsType}`).join(", "),a=s.returnTypeTs==="void"?"Promise<void>":`Promise<${s.returnTypeTs}>`;e+=`export declare function ${s.name}(${n}): ${a};
`}return e}static generateImplementation(r){let t=`const proxy = {
`;for(let e of r){let s=e.parameters.map(a=>a.name).join(", "),n=e.parameters.length>0?`, ${s}`:"";t+=`  ${e.name}: async (${e.parameters.map(a=>`${a.name}`).join(", ")}) => {
`,t+=`    return await runRustFunction("${e.name}"${n});
`,t+=`  },
`}t+=`};

`;for(let e of r)t+=`export const ${e.name} = proxy.${e.name};
`;return t+=`
export default proxy;
`,t}};0&&(module.exports={RustParser,TypeScriptGenerator});
