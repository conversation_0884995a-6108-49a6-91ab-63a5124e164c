"use strict";var Ps=Object.create;var dr=Object.defineProperty;var ks=Object.getOwnPropertyDescriptor;var Bs=Object.getOwnPropertyNames;var Os=Object.getPrototypeOf,qs=Object.prototype.hasOwnProperty;var Ke=(r,o)=>()=>(o||r((o={exports:{}}).exports,o),o.exports),Ds=(r,o)=>{for(var a in o)dr(r,a,{get:o[a],enumerable:!0})},Zo=(r,o,a,i)=>{if(o&&typeof o=="object"||typeof o=="function")for(let l of Bs(o))!qs.call(r,l)&&l!==a&&dr(r,l,{get:()=>o[l],enumerable:!(i=ks(o,l))||i.enumerable});return r};var K=(r,o,a)=>(a=r!=null?Ps(Os(r)):{},Zo(o||!r||!r.__esModule?dr(a,"default",{value:r,enumerable:!0}):a,r)),Ws=r=>Zo(dr({},"__esModule",{value:!0}),r);var Rn=Ke(ka=>{var Fs=Object.create,wr=Object.defineProperty,Is=Object.getOwnPropertyDescriptor,Ls=Object.getOwnPropertyNames,$s=Object.getPrototypeOf,zs=Object.prototype.hasOwnProperty,ma=r=>wr(r,"__esModule",{value:!0}),At=(r,o)=>function(){return r&&(o=(0,r[Object.keys(r)[0]])(r=0)),o},Sn=(r,o)=>function(){return o||(0,r[Object.keys(r)[0]])((o={exports:{}}).exports,o),o.exports},pa=(r,o)=>{ma(r);for(var a in o)wr(r,a,{get:o[a],enumerable:!0})},xs=(r,o,a)=>{if(o&&typeof o=="object"||typeof o=="function")for(let i of Ls(o))!zs.call(r,i)&&i!=="default"&&wr(r,i,{get:()=>o[i],enumerable:!(a=Is(o,i))||a.enumerable});return r},H=r=>xs(ma(wr(r!=null?Fs($s(r)):{},"default",r&&r.__esModule&&"default"in r?{get:()=>r.default,enumerable:!0}:{value:r,enumerable:!0})),r),js=Sn({"node_modules/web-streams-polyfill/dist/ponyfill.es2018.js"(r,o){(function(a,i){typeof r=="object"&&typeof o<"u"?i(r):typeof define=="function"&&define.amd?define(["exports"],i):(a=typeof globalThis<"u"?globalThis:a||self,i(a.WebStreamsPolyfill={}))})(r,function(a){"use strict";let i=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Symbol:e=>`Symbol(${e})`;function l(){}function c(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global}let m=c();function d(e){return typeof e=="object"&&e!==null||typeof e=="function"}let y=l,g=Promise,q=Promise.prototype.then,I=Promise.resolve.bind(g),O=Promise.reject.bind(g);function S(e){return new g(e)}function p(e){return I(e)}function b(e){return O(e)}function C(e,t,n){return q.call(e,t,n)}function v(e,t,n){C(C(e,t,n),void 0,y)}function U(e,t){v(e,t)}function N(e,t){v(e,void 0,t)}function D(e,t,n){return C(e,t,n)}function L(e){C(e,void 0,y)}let z=(()=>{let e=m&&m.queueMicrotask;if(typeof e=="function")return e;let t=p(void 0);return n=>C(t,n)})();function Oe(e,t,n){if(typeof e!="function")throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,n)}function he(e,t,n){try{return p(Oe(e,t,n))}catch(s){return b(s)}}let In=16384;class te{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(t){let n=this._back,s=n;n._elements.length===In-1&&(s={_elements:[],_next:void 0}),n._elements.push(t),s!==n&&(this._back=s,n._next=s),++this._size}shift(){let t=this._front,n=t,s=this._cursor,u=s+1,f=t._elements,h=f[s];return u===In&&(n=t._next,u=0),--this._size,this._cursor=u,t!==n&&(this._front=n),f[s]=void 0,h}forEach(t){let n=this._cursor,s=this._front,u=s._elements;for(;(n!==u.length||s._next!==void 0)&&!(n===u.length&&(s=s._next,u=s._elements,n=0,u.length===0));)t(u[n]),++n}peek(){let t=this._front,n=this._cursor;return t._elements[n]}}function Ln(e,t){e._ownerReadableStream=t,t._reader=e,t._state==="readable"?Br(e):t._state==="closed"?ni(e):$n(e,t._storedError)}function kr(e,t){let n=e._ownerReadableStream;return oe(n,t)}function me(e){e._ownerReadableStream._state==="readable"?Or(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):oi(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),e._ownerReadableStream._reader=void 0,e._ownerReadableStream=void 0}function ze(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function Br(e){e._closedPromise=S((t,n)=>{e._closedPromise_resolve=t,e._closedPromise_reject=n})}function $n(e,t){Br(e),Or(e,t)}function ni(e){Br(e),zn(e)}function Or(e,t){e._closedPromise_reject!==void 0&&(L(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function oi(e,t){$n(e,t)}function zn(e){e._closedPromise_resolve!==void 0&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}let xn=i("[[AbortSteps]]"),jn=i("[[ErrorSteps]]"),qr=i("[[CancelSteps]]"),Dr=i("[[PullSteps]]"),Mn=Number.isFinite||function(e){return typeof e=="number"&&isFinite(e)},ai=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function ii(e){return typeof e=="object"||typeof e=="function"}function pe(e,t){if(e!==void 0&&!ii(e))throw new TypeError(`${t} is not an object.`)}function re(e,t){if(typeof e!="function")throw new TypeError(`${t} is not a function.`)}function si(e){return typeof e=="object"&&e!==null||typeof e=="function"}function Un(e,t){if(!si(e))throw new TypeError(`${t} is not an object.`)}function be(e,t,n){if(e===void 0)throw new TypeError(`Parameter ${t} is required in '${n}'.`)}function Wr(e,t,n){if(e===void 0)throw new TypeError(`${t} is required in '${n}'.`)}function Fr(e){return Number(e)}function Nn(e){return e===0?0:e}function li(e){return Nn(ai(e))}function Hn(e,t){let s=Number.MAX_SAFE_INTEGER,u=Number(e);if(u=Nn(u),!Mn(u))throw new TypeError(`${t} is not a finite number`);if(u=li(u),u<0||u>s)throw new TypeError(`${t} is outside the accepted range of 0 to ${s}, inclusive`);return!Mn(u)||u===0?0:u}function Ir(e,t){if(!Ee(e))throw new TypeError(`${t} is not a ReadableStream.`)}function xe(e){return new it(e)}function Vn(e,t){e._reader._readRequests.push(t)}function Lr(e,t,n){let u=e._reader._readRequests.shift();n?u._closeSteps():u._chunkSteps(t)}function Wt(e){return e._reader._readRequests.length}function Yn(e){let t=e._reader;return!(t===void 0||!we(t))}class it{constructor(t){if(be(t,1,"ReadableStreamDefaultReader"),Ir(t,"First parameter"),Ce(t))throw new TypeError("This stream has already been locked for exclusive reading by another reader");Ln(this,t),this._readRequests=new te}get closed(){return we(this)?this._closedPromise:b(Ft("closed"))}cancel(t=void 0){return we(this)?this._ownerReadableStream===void 0?b(ze("cancel")):kr(this,t):b(Ft("cancel"))}read(){if(!we(this))return b(Ft("read"));if(this._ownerReadableStream===void 0)return b(ze("read from"));let t,n,s=S((f,h)=>{t=f,n=h});return st(this,{_chunkSteps:f=>t({value:f,done:!1}),_closeSteps:()=>t({value:void 0,done:!0}),_errorSteps:f=>n(f)}),s}releaseLock(){if(!we(this))throw Ft("releaseLock");if(this._ownerReadableStream!==void 0){if(this._readRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");me(this)}}}Object.defineProperties(it.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(it.prototype,i.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});function we(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_readRequests")?!1:e instanceof it}function st(e,t){let n=e._ownerReadableStream;n._disturbed=!0,n._state==="closed"?t._closeSteps():n._state==="errored"?t._errorSteps(n._storedError):n._readableStreamController[Dr](t)}function Ft(e){return new TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}let Gn=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype);class Qn{constructor(t,n){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=t,this._preventCancel=n}next(){let t=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?D(this._ongoingPromise,t,t):t(),this._ongoingPromise}return(t){let n=()=>this._returnSteps(t);return this._ongoingPromise?D(this._ongoingPromise,n,n):n()}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});let t=this._reader;if(t._ownerReadableStream===void 0)return b(ze("iterate"));let n,s,u=S((h,_)=>{n=h,s=_});return st(t,{_chunkSteps:h=>{this._ongoingPromise=void 0,z(()=>n({value:h,done:!1}))},_closeSteps:()=>{this._ongoingPromise=void 0,this._isFinished=!0,me(t),n({value:void 0,done:!0})},_errorSteps:h=>{this._ongoingPromise=void 0,this._isFinished=!0,me(t),s(h)}}),u}_returnSteps(t){if(this._isFinished)return Promise.resolve({value:t,done:!0});this._isFinished=!0;let n=this._reader;if(n._ownerReadableStream===void 0)return b(ze("finish iterating"));if(!this._preventCancel){let s=kr(n,t);return me(n),D(s,()=>({value:t,done:!0}))}return me(n),p({value:t,done:!0})}}let Jn={next(){return Kn(this)?this._asyncIteratorImpl.next():b(Xn("next"))},return(e){return Kn(this)?this._asyncIteratorImpl.return(e):b(Xn("return"))}};Gn!==void 0&&Object.setPrototypeOf(Jn,Gn);function ui(e,t){let n=xe(e),s=new Qn(n,t),u=Object.create(Jn);return u._asyncIteratorImpl=s,u}function Kn(e){if(!d(e)||!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof Qn}catch{return!1}}function Xn(e){return new TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}let Zn=Number.isNaN||function(e){return e!==e};function lt(e){return e.slice()}function eo(e,t,n,s,u){new Uint8Array(e).set(new Uint8Array(n,s,u),t)}function Jl(e){return e}function It(e){return!1}function to(e,t,n){if(e.slice)return e.slice(t,n);let s=n-t,u=new ArrayBuffer(s);return eo(u,0,e,t,s),u}function ci(e){return!(typeof e!="number"||Zn(e)||e<0)}function ro(e){let t=to(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(t)}function $r(e){let t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function zr(e,t,n){if(!ci(n)||n===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");e._queue.push({value:t,size:n}),e._queueTotalSize+=n}function fi(e){return e._queue.peek().value}function ve(e){e._queue=new te,e._queueTotalSize=0}class ut{constructor(){throw new TypeError("Illegal constructor")}get view(){if(!xr(this))throw Nr("view");return this._view}respond(t){if(!xr(this))throw Nr("respond");if(be(t,1,"respond"),t=Hn(t,"First parameter"),this._associatedReadableByteStreamController===void 0)throw new TypeError("This BYOB request has been invalidated");It(this._view.buffer),jt(this._associatedReadableByteStreamController,t)}respondWithNewView(t){if(!xr(this))throw Nr("respondWithNewView");if(be(t,1,"respondWithNewView"),!ArrayBuffer.isView(t))throw new TypeError("You can only respond with array buffer views");if(this._associatedReadableByteStreamController===void 0)throw new TypeError("This BYOB request has been invalidated");It(t.buffer),Mt(this._associatedReadableByteStreamController,t)}}Object.defineProperties(ut.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(ut.prototype,i.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class je{constructor(){throw new TypeError("Illegal constructor")}get byobRequest(){if(!qe(this))throw ft("byobRequest");return Ur(this)}get desiredSize(){if(!qe(this))throw ft("desiredSize");return co(this)}close(){if(!qe(this))throw ft("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");let t=this._controlledReadableByteStream._state;if(t!=="readable")throw new TypeError(`The stream (in ${t} state) is not in the readable state and cannot be closed`);ct(this)}enqueue(t){if(!qe(this))throw ft("enqueue");if(be(t,1,"enqueue"),!ArrayBuffer.isView(t))throw new TypeError("chunk must be an array buffer view");if(t.byteLength===0)throw new TypeError("chunk must have non-zero byteLength");if(t.buffer.byteLength===0)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");let n=this._controlledReadableByteStream._state;if(n!=="readable")throw new TypeError(`The stream (in ${n} state) is not in the readable state and cannot be enqueued to`);xt(this,t)}error(t=void 0){if(!qe(this))throw ft("error");ne(this,t)}[qr](t){no(this),ve(this);let n=this._cancelAlgorithm(t);return zt(this),n}[Dr](t){let n=this._controlledReadableByteStream;if(this._queueTotalSize>0){let u=this._queue.shift();this._queueTotalSize-=u.byteLength,so(this);let f=new Uint8Array(u.buffer,u.byteOffset,u.byteLength);t._chunkSteps(f);return}let s=this._autoAllocateChunkSize;if(s!==void 0){let u;try{u=new ArrayBuffer(s)}catch(h){t._errorSteps(h);return}let f={buffer:u,bufferByteLength:s,byteOffset:0,byteLength:s,bytesFilled:0,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(f)}Vn(n,t),De(this)}}Object.defineProperties(je.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(je.prototype,i.toStringTag,{value:"ReadableByteStreamController",configurable:!0});function qe(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")?!1:e instanceof je}function xr(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")?!1:e instanceof ut}function De(e){if(!pi(e))return;if(e._pulling){e._pullAgain=!0;return}e._pulling=!0;let n=e._pullAlgorithm();v(n,()=>{e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,De(e))},s=>{ne(e,s)})}function no(e){Mr(e),e._pendingPullIntos=new te}function jr(e,t){let n=!1;e._state==="closed"&&(n=!0);let s=oo(t);t.readerType==="default"?Lr(e,s,n):gi(e,s,n)}function oo(e){let t=e.bytesFilled,n=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/n)}function Lt(e,t,n,s){e._queue.push({buffer:t,byteOffset:n,byteLength:s}),e._queueTotalSize+=s}function ao(e,t){let n=t.elementSize,s=t.bytesFilled-t.bytesFilled%n,u=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),f=t.bytesFilled+u,h=f-f%n,_=u,E=!1;h>s&&(_=h-t.bytesFilled,E=!0);let P=e._queue;for(;_>0;){let k=P.peek(),B=Math.min(_,k.byteLength),x=t.byteOffset+t.bytesFilled;eo(t.buffer,x,k.buffer,k.byteOffset,B),k.byteLength===B?P.shift():(k.byteOffset+=B,k.byteLength-=B),e._queueTotalSize-=B,io(e,B,t),_-=B}return E}function io(e,t,n){n.bytesFilled+=t}function so(e){e._queueTotalSize===0&&e._closeRequested?(zt(e),_t(e._controlledReadableByteStream)):De(e)}function Mr(e){e._byobRequest!==null&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function lo(e){for(;e._pendingPullIntos.length>0;){if(e._queueTotalSize===0)return;let t=e._pendingPullIntos.peek();ao(e,t)&&($t(e),jr(e._controlledReadableByteStream,t))}}function di(e,t,n){let s=e._controlledReadableByteStream,u=1;t.constructor!==DataView&&(u=t.constructor.BYTES_PER_ELEMENT);let f=t.constructor,h=t.buffer,_={buffer:h,bufferByteLength:h.byteLength,byteOffset:t.byteOffset,byteLength:t.byteLength,bytesFilled:0,elementSize:u,viewConstructor:f,readerType:"byob"};if(e._pendingPullIntos.length>0){e._pendingPullIntos.push(_),mo(s,n);return}if(s._state==="closed"){let E=new f(_.buffer,_.byteOffset,0);n._closeSteps(E);return}if(e._queueTotalSize>0){if(ao(e,_)){let E=oo(_);so(e),n._chunkSteps(E);return}if(e._closeRequested){let E=new TypeError("Insufficient bytes to fill elements in the given buffer");ne(e,E),n._errorSteps(E);return}}e._pendingPullIntos.push(_),mo(s,n),De(e)}function hi(e,t){let n=e._controlledReadableByteStream;if(Hr(n))for(;po(n)>0;){let s=$t(e);jr(n,s)}}function mi(e,t,n){if(io(e,t,n),n.bytesFilled<n.elementSize)return;$t(e);let s=n.bytesFilled%n.elementSize;if(s>0){let u=n.byteOffset+n.bytesFilled,f=to(n.buffer,u-s,u);Lt(e,f,0,f.byteLength)}n.bytesFilled-=s,jr(e._controlledReadableByteStream,n),lo(e)}function uo(e,t){let n=e._pendingPullIntos.peek();Mr(e),e._controlledReadableByteStream._state==="closed"?hi(e):mi(e,t,n),De(e)}function $t(e){return e._pendingPullIntos.shift()}function pi(e){let t=e._controlledReadableByteStream;return t._state!=="readable"||e._closeRequested||!e._started?!1:!!(Yn(t)&&Wt(t)>0||Hr(t)&&po(t)>0||co(e)>0)}function zt(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function ct(e){let t=e._controlledReadableByteStream;if(!(e._closeRequested||t._state!=="readable")){if(e._queueTotalSize>0){e._closeRequested=!0;return}if(e._pendingPullIntos.length>0&&e._pendingPullIntos.peek().bytesFilled>0){let s=new TypeError("Insufficient bytes to fill elements in the given buffer");throw ne(e,s),s}zt(e),_t(t)}}function xt(e,t){let n=e._controlledReadableByteStream;if(e._closeRequested||n._state!=="readable")return;let s=t.buffer,u=t.byteOffset,f=t.byteLength,h=s;if(e._pendingPullIntos.length>0){let _=e._pendingPullIntos.peek();It(_.buffer),_.buffer=_.buffer}if(Mr(e),Yn(n))if(Wt(n)===0)Lt(e,h,u,f);else{e._pendingPullIntos.length>0&&$t(e);let _=new Uint8Array(h,u,f);Lr(n,_,!1)}else Hr(n)?(Lt(e,h,u,f),lo(e)):Lt(e,h,u,f);De(e)}function ne(e,t){let n=e._controlledReadableByteStream;n._state==="readable"&&(no(e),ve(e),zt(e),zo(n,t))}function Ur(e){if(e._byobRequest===null&&e._pendingPullIntos.length>0){let t=e._pendingPullIntos.peek(),n=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),s=Object.create(ut.prototype);yi(s,e,n),e._byobRequest=s}return e._byobRequest}function co(e){let t=e._controlledReadableByteStream._state;return t==="errored"?null:t==="closed"?0:e._strategyHWM-e._queueTotalSize}function jt(e,t){let n=e._pendingPullIntos.peek();if(e._controlledReadableByteStream._state==="closed"){if(t!==0)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(t===0)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(n.bytesFilled+t>n.byteLength)throw new RangeError("bytesWritten out of range")}n.buffer=n.buffer,uo(e,t)}function Mt(e,t){let n=e._pendingPullIntos.peek();if(e._controlledReadableByteStream._state==="closed"){if(t.byteLength!==0)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(t.byteLength===0)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(n.byteOffset+n.bytesFilled!==t.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(n.bufferByteLength!==t.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(n.bytesFilled+t.byteLength>n.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");let u=t.byteLength;n.buffer=t.buffer,uo(e,u)}function fo(e,t,n,s,u,f,h){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=null,t._queue=t._queueTotalSize=void 0,ve(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=f,t._pullAlgorithm=s,t._cancelAlgorithm=u,t._autoAllocateChunkSize=h,t._pendingPullIntos=new te,e._readableStreamController=t;let _=n();v(p(_),()=>{t._started=!0,De(t)},E=>{ne(t,E)})}function bi(e,t,n){let s=Object.create(je.prototype),u=()=>{},f=()=>p(void 0),h=()=>p(void 0);t.start!==void 0&&(u=()=>t.start(s)),t.pull!==void 0&&(f=()=>t.pull(s)),t.cancel!==void 0&&(h=E=>t.cancel(E));let _=t.autoAllocateChunkSize;if(_===0)throw new TypeError("autoAllocateChunkSize must be greater than 0");fo(e,s,u,f,h,n,_)}function yi(e,t,n){e._associatedReadableByteStreamController=t,e._view=n}function Nr(e){return new TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}function ft(e){return new TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}function ho(e){return new dt(e)}function mo(e,t){e._reader._readIntoRequests.push(t)}function gi(e,t,n){let u=e._reader._readIntoRequests.shift();n?u._closeSteps(t):u._chunkSteps(t)}function po(e){return e._reader._readIntoRequests.length}function Hr(e){let t=e._reader;return!(t===void 0||!We(t))}class dt{constructor(t){if(be(t,1,"ReadableStreamBYOBReader"),Ir(t,"First parameter"),Ce(t))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!qe(t._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");Ln(this,t),this._readIntoRequests=new te}get closed(){return We(this)?this._closedPromise:b(Ut("closed"))}cancel(t=void 0){return We(this)?this._ownerReadableStream===void 0?b(ze("cancel")):kr(this,t):b(Ut("cancel"))}read(t){if(!We(this))return b(Ut("read"));if(!ArrayBuffer.isView(t))return b(new TypeError("view must be an array buffer view"));if(t.byteLength===0)return b(new TypeError("view must have non-zero byteLength"));if(t.buffer.byteLength===0)return b(new TypeError("view's buffer must have non-zero byteLength"));if(It(t.buffer),this._ownerReadableStream===void 0)return b(ze("read from"));let n,s,u=S((h,_)=>{n=h,s=_});return bo(this,t,{_chunkSteps:h=>n({value:h,done:!1}),_closeSteps:h=>n({value:h,done:!0}),_errorSteps:h=>s(h)}),u}releaseLock(){if(!We(this))throw Ut("releaseLock");if(this._ownerReadableStream!==void 0){if(this._readIntoRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");me(this)}}}Object.defineProperties(dt.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(dt.prototype,i.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});function We(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")?!1:e instanceof dt}function bo(e,t,n){let s=e._ownerReadableStream;s._disturbed=!0,s._state==="errored"?n._errorSteps(s._storedError):di(s._readableStreamController,t,n)}function Ut(e){return new TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}function ht(e,t){let{highWaterMark:n}=e;if(n===void 0)return t;if(Zn(n)||n<0)throw new RangeError("Invalid highWaterMark");return n}function Nt(e){let{size:t}=e;return t||(()=>1)}function Ht(e,t){pe(e,t);let n=e?.highWaterMark,s=e?.size;return{highWaterMark:n===void 0?void 0:Fr(n),size:s===void 0?void 0:_i(s,`${t} has member 'size' that`)}}function _i(e,t){return re(e,t),n=>Fr(e(n))}function Si(e,t){pe(e,t);let n=e?.abort,s=e?.close,u=e?.start,f=e?.type,h=e?.write;return{abort:n===void 0?void 0:wi(n,e,`${t} has member 'abort' that`),close:s===void 0?void 0:vi(s,e,`${t} has member 'close' that`),start:u===void 0?void 0:Ri(u,e,`${t} has member 'start' that`),write:h===void 0?void 0:Ti(h,e,`${t} has member 'write' that`),type:f}}function wi(e,t,n){return re(e,n),s=>he(e,t,[s])}function vi(e,t,n){return re(e,n),()=>he(e,t,[])}function Ri(e,t,n){return re(e,n),s=>Oe(e,t,[s])}function Ti(e,t,n){return re(e,n),(s,u)=>he(e,t,[s,u])}function yo(e,t){if(!Me(e))throw new TypeError(`${t} is not a WritableStream.`)}function Ei(e){if(typeof e!="object"||e===null)return!1;try{return typeof e.aborted=="boolean"}catch{return!1}}let Ci=typeof AbortController=="function";function Ai(){if(Ci)return new AbortController}class mt{constructor(t={},n={}){t===void 0?t=null:Un(t,"First parameter");let s=Ht(n,"Second parameter"),u=Si(t,"First parameter");if(_o(this),u.type!==void 0)throw new RangeError("Invalid type is specified");let h=Nt(s),_=ht(s,1);ji(this,u,_,h)}get locked(){if(!Me(this))throw Jt("locked");return Ue(this)}abort(t=void 0){return Me(this)?Ue(this)?b(new TypeError("Cannot abort a stream that already has a writer")):Vt(this,t):b(Jt("abort"))}close(){return Me(this)?Ue(this)?b(new TypeError("Cannot close a stream that already has a writer")):ue(this)?b(new TypeError("Cannot close an already-closing stream")):So(this):b(Jt("close"))}getWriter(){if(!Me(this))throw Jt("getWriter");return go(this)}}Object.defineProperties(mt.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(mt.prototype,i.toStringTag,{value:"WritableStream",configurable:!0});function go(e){return new pt(e)}function Pi(e,t,n,s,u=1,f=()=>1){let h=Object.create(mt.prototype);_o(h);let _=Object.create(Ne.prototype);return Co(h,_,e,t,n,s,u,f),h}function _o(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new te,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function Me(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")?!1:e instanceof mt}function Ue(e){return e._writer!==void 0}function Vt(e,t){var n;if(e._state==="closed"||e._state==="errored")return p(void 0);e._writableStreamController._abortReason=t,(n=e._writableStreamController._abortController)===null||n===void 0||n.abort();let s=e._state;if(s==="closed"||s==="errored")return p(void 0);if(e._pendingAbortRequest!==void 0)return e._pendingAbortRequest._promise;let u=!1;s==="erroring"&&(u=!0,t=void 0);let f=S((h,_)=>{e._pendingAbortRequest={_promise:void 0,_resolve:h,_reject:_,_reason:t,_wasAlreadyErroring:u}});return e._pendingAbortRequest._promise=f,u||Yr(e,t),f}function So(e){let t=e._state;if(t==="closed"||t==="errored")return b(new TypeError(`The stream (in ${t} state) is not in the writable state and cannot be closed`));let n=S((u,f)=>{let h={_resolve:u,_reject:f};e._closeRequest=h}),s=e._writer;return s!==void 0&&e._backpressure&&t==="writable"&&rn(s),Mi(e._writableStreamController),n}function ki(e){return S((n,s)=>{let u={_resolve:n,_reject:s};e._writeRequests.push(u)})}function Vr(e,t){if(e._state==="writable"){Yr(e,t);return}Gr(e)}function Yr(e,t){let n=e._writableStreamController;e._state="erroring",e._storedError=t;let s=e._writer;s!==void 0&&vo(s,t),!Wi(e)&&n._started&&Gr(e)}function Gr(e){e._state="errored",e._writableStreamController[jn]();let t=e._storedError;if(e._writeRequests.forEach(u=>{u._reject(t)}),e._writeRequests=new te,e._pendingAbortRequest===void 0){Yt(e);return}let n=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,n._wasAlreadyErroring){n._reject(t),Yt(e);return}let s=e._writableStreamController[xn](n._reason);v(s,()=>{n._resolve(),Yt(e)},u=>{n._reject(u),Yt(e)})}function Bi(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}function Oi(e,t){e._inFlightWriteRequest._reject(t),e._inFlightWriteRequest=void 0,Vr(e,t)}function qi(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,e._state==="erroring"&&(e._storedError=void 0,e._pendingAbortRequest!==void 0&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";let n=e._writer;n!==void 0&&Bo(n)}function Di(e,t){e._inFlightCloseRequest._reject(t),e._inFlightCloseRequest=void 0,e._pendingAbortRequest!==void 0&&(e._pendingAbortRequest._reject(t),e._pendingAbortRequest=void 0),Vr(e,t)}function ue(e){return!(e._closeRequest===void 0&&e._inFlightCloseRequest===void 0)}function Wi(e){return!(e._inFlightWriteRequest===void 0&&e._inFlightCloseRequest===void 0)}function Fi(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0}function Ii(e){e._inFlightWriteRequest=e._writeRequests.shift()}function Yt(e){e._closeRequest!==void 0&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);let t=e._writer;t!==void 0&&en(t,e._storedError)}function Qr(e,t){let n=e._writer;n!==void 0&&t!==e._backpressure&&(t?Qi(n):rn(n)),e._backpressure=t}class pt{constructor(t){if(be(t,1,"WritableStreamDefaultWriter"),yo(t,"First parameter"),Ue(t))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=t,t._writer=this;let n=t._state;if(n==="writable")!ue(t)&&t._backpressure?Xt(this):Oo(this),Kt(this);else if(n==="erroring")tn(this,t._storedError),Kt(this);else if(n==="closed")Oo(this),Yi(this);else{let s=t._storedError;tn(this,s),ko(this,s)}}get closed(){return Fe(this)?this._closedPromise:b(Ie("closed"))}get desiredSize(){if(!Fe(this))throw Ie("desiredSize");if(this._ownerWritableStream===void 0)throw bt("desiredSize");return xi(this)}get ready(){return Fe(this)?this._readyPromise:b(Ie("ready"))}abort(t=void 0){return Fe(this)?this._ownerWritableStream===void 0?b(bt("abort")):Li(this,t):b(Ie("abort"))}close(){if(!Fe(this))return b(Ie("close"));let t=this._ownerWritableStream;return t===void 0?b(bt("close")):ue(t)?b(new TypeError("Cannot close an already-closing stream")):wo(this)}releaseLock(){if(!Fe(this))throw Ie("releaseLock");this._ownerWritableStream!==void 0&&Ro(this)}write(t=void 0){return Fe(this)?this._ownerWritableStream===void 0?b(bt("write to")):To(this,t):b(Ie("write"))}}Object.defineProperties(pt.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(pt.prototype,i.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});function Fe(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")?!1:e instanceof pt}function Li(e,t){let n=e._ownerWritableStream;return Vt(n,t)}function wo(e){let t=e._ownerWritableStream;return So(t)}function $i(e){let t=e._ownerWritableStream,n=t._state;return ue(t)||n==="closed"?p(void 0):n==="errored"?b(t._storedError):wo(e)}function zi(e,t){e._closedPromiseState==="pending"?en(e,t):Gi(e,t)}function vo(e,t){e._readyPromiseState==="pending"?qo(e,t):Ji(e,t)}function xi(e){let t=e._ownerWritableStream,n=t._state;return n==="errored"||n==="erroring"?null:n==="closed"?0:Ao(t._writableStreamController)}function Ro(e){let t=e._ownerWritableStream,n=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");vo(e,n),zi(e,n),t._writer=void 0,e._ownerWritableStream=void 0}function To(e,t){let n=e._ownerWritableStream,s=n._writableStreamController,u=Ui(s,t);if(n!==e._ownerWritableStream)return b(bt("write to"));let f=n._state;if(f==="errored")return b(n._storedError);if(ue(n)||f==="closed")return b(new TypeError("The stream is closing or closed and cannot be written to"));if(f==="erroring")return b(n._storedError);let h=ki(n);return Ni(s,t,u),h}let Eo={};class Ne{constructor(){throw new TypeError("Illegal constructor")}get abortReason(){if(!Jr(this))throw Zr("abortReason");return this._abortReason}get signal(){if(!Jr(this))throw Zr("signal");if(this._abortController===void 0)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(t=void 0){if(!Jr(this))throw Zr("error");this._controlledWritableStream._state==="writable"&&Po(this,t)}[xn](t){let n=this._abortAlgorithm(t);return Gt(this),n}[jn](){ve(this)}}Object.defineProperties(Ne.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(Ne.prototype,i.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});function Jr(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")?!1:e instanceof Ne}function Co(e,t,n,s,u,f,h,_){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,ve(t),t._abortReason=void 0,t._abortController=Ai(),t._started=!1,t._strategySizeAlgorithm=_,t._strategyHWM=h,t._writeAlgorithm=s,t._closeAlgorithm=u,t._abortAlgorithm=f;let E=Xr(t);Qr(e,E);let P=n(),k=p(P);v(k,()=>{t._started=!0,Qt(t)},B=>{t._started=!0,Vr(e,B)})}function ji(e,t,n,s){let u=Object.create(Ne.prototype),f=()=>{},h=()=>p(void 0),_=()=>p(void 0),E=()=>p(void 0);t.start!==void 0&&(f=()=>t.start(u)),t.write!==void 0&&(h=P=>t.write(P,u)),t.close!==void 0&&(_=()=>t.close()),t.abort!==void 0&&(E=P=>t.abort(P)),Co(e,u,f,h,_,E,n,s)}function Gt(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function Mi(e){zr(e,Eo,0),Qt(e)}function Ui(e,t){try{return e._strategySizeAlgorithm(t)}catch(n){return Kr(e,n),1}}function Ao(e){return e._strategyHWM-e._queueTotalSize}function Ni(e,t,n){try{zr(e,t,n)}catch(u){Kr(e,u);return}let s=e._controlledWritableStream;if(!ue(s)&&s._state==="writable"){let u=Xr(e);Qr(s,u)}Qt(e)}function Qt(e){let t=e._controlledWritableStream;if(!e._started||t._inFlightWriteRequest!==void 0)return;if(t._state==="erroring"){Gr(t);return}if(e._queue.length===0)return;let s=fi(e);s===Eo?Hi(e):Vi(e,s)}function Kr(e,t){e._controlledWritableStream._state==="writable"&&Po(e,t)}function Hi(e){let t=e._controlledWritableStream;Fi(t),$r(e);let n=e._closeAlgorithm();Gt(e),v(n,()=>{qi(t)},s=>{Di(t,s)})}function Vi(e,t){let n=e._controlledWritableStream;Ii(n);let s=e._writeAlgorithm(t);v(s,()=>{Bi(n);let u=n._state;if($r(e),!ue(n)&&u==="writable"){let f=Xr(e);Qr(n,f)}Qt(e)},u=>{n._state==="writable"&&Gt(e),Oi(n,u)})}function Xr(e){return Ao(e)<=0}function Po(e,t){let n=e._controlledWritableStream;Gt(e),Yr(n,t)}function Jt(e){return new TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}function Zr(e){return new TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}function Ie(e){return new TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}function bt(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function Kt(e){e._closedPromise=S((t,n)=>{e._closedPromise_resolve=t,e._closedPromise_reject=n,e._closedPromiseState="pending"})}function ko(e,t){Kt(e),en(e,t)}function Yi(e){Kt(e),Bo(e)}function en(e,t){e._closedPromise_reject!==void 0&&(L(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function Gi(e,t){ko(e,t)}function Bo(e){e._closedPromise_resolve!==void 0&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function Xt(e){e._readyPromise=S((t,n)=>{e._readyPromise_resolve=t,e._readyPromise_reject=n}),e._readyPromiseState="pending"}function tn(e,t){Xt(e),qo(e,t)}function Oo(e){Xt(e),rn(e)}function qo(e,t){e._readyPromise_reject!==void 0&&(L(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function Qi(e){Xt(e)}function Ji(e,t){tn(e,t)}function rn(e){e._readyPromise_resolve!==void 0&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}let Do=typeof DOMException<"u"?DOMException:void 0;function Ki(e){if(!(typeof e=="function"||typeof e=="object"))return!1;try{return new e,!0}catch{return!1}}function Xi(){let e=function(n,s){this.message=n||"",this.name=s||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}let Zi=Ki(Do)?Do:Xi();function Wo(e,t,n,s,u,f){let h=xe(e),_=go(t);e._disturbed=!0;let E=!1,P=p(void 0);return S((k,B)=>{let x;if(f!==void 0){if(x=()=>{let w=new Zi("Aborted","AbortError"),A=[];s||A.push(()=>t._state==="writable"?Vt(t,w):p(void 0)),u||A.push(()=>e._state==="readable"?oe(e,w):p(void 0)),G(()=>Promise.all(A.map(F=>F())),!0,w)},f.aborted){x();return}f.addEventListener("abort",x)}function ae(){return S((w,A)=>{function F(J){J?w():C(Ye(),F,A)}F(!1)})}function Ye(){return E?p(!0):C(_._readyPromise,()=>S((w,A)=>{st(h,{_chunkSteps:F=>{P=C(To(_,F),void 0,l),w(!1)},_closeSteps:()=>w(!0),_errorSteps:A})}))}if(ye(e,h._closedPromise,w=>{s?Z(!0,w):G(()=>Vt(t,w),!0,w)}),ye(t,_._closedPromise,w=>{u?Z(!0,w):G(()=>oe(e,w),!0,w)}),Y(e,h._closedPromise,()=>{n?Z():G(()=>$i(_))}),ue(t)||t._state==="closed"){let w=new TypeError("the destination writable stream closed before all data could be piped to it");u?Z(!0,w):G(()=>oe(e,w),!0,w)}L(ae());function Ae(){let w=P;return C(P,()=>w!==P?Ae():void 0)}function ye(w,A,F){w._state==="errored"?F(w._storedError):N(A,F)}function Y(w,A,F){w._state==="closed"?F():U(A,F)}function G(w,A,F){if(E)return;E=!0,t._state==="writable"&&!ue(t)?U(Ae(),J):J();function J(){v(w(),()=>ge(A,F),Ge=>ge(!0,Ge))}}function Z(w,A){E||(E=!0,t._state==="writable"&&!ue(t)?U(Ae(),()=>ge(w,A)):ge(w,A))}function ge(w,A){Ro(_),me(h),f!==void 0&&f.removeEventListener("abort",x),w?B(A):k(void 0)}})}class He{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!Zt(this))throw rr("desiredSize");return nn(this)}close(){if(!Zt(this))throw rr("close");if(!Ve(this))throw new TypeError("The stream is not in a state that permits close");gt(this)}enqueue(t=void 0){if(!Zt(this))throw rr("enqueue");if(!Ve(this))throw new TypeError("The stream is not in a state that permits enqueue");return tr(this,t)}error(t=void 0){if(!Zt(this))throw rr("error");Re(this,t)}[qr](t){ve(this);let n=this._cancelAlgorithm(t);return er(this),n}[Dr](t){let n=this._controlledReadableStream;if(this._queue.length>0){let s=$r(this);this._closeRequested&&this._queue.length===0?(er(this),_t(n)):yt(this),t._chunkSteps(s)}else Vn(n,t),yt(this)}}Object.defineProperties(He.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(He.prototype,i.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});function Zt(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")?!1:e instanceof He}function yt(e){if(!Fo(e))return;if(e._pulling){e._pullAgain=!0;return}e._pulling=!0;let n=e._pullAlgorithm();v(n,()=>{e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,yt(e))},s=>{Re(e,s)})}function Fo(e){let t=e._controlledReadableStream;return!Ve(e)||!e._started?!1:!!(Ce(t)&&Wt(t)>0||nn(e)>0)}function er(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function gt(e){if(!Ve(e))return;let t=e._controlledReadableStream;e._closeRequested=!0,e._queue.length===0&&(er(e),_t(t))}function tr(e,t){if(!Ve(e))return;let n=e._controlledReadableStream;if(Ce(n)&&Wt(n)>0)Lr(n,t,!1);else{let s;try{s=e._strategySizeAlgorithm(t)}catch(u){throw Re(e,u),u}try{zr(e,t,s)}catch(u){throw Re(e,u),u}}yt(e)}function Re(e,t){let n=e._controlledReadableStream;n._state==="readable"&&(ve(e),er(e),zo(n,t))}function nn(e){let t=e._controlledReadableStream._state;return t==="errored"?null:t==="closed"?0:e._strategyHWM-e._queueTotalSize}function es(e){return!Fo(e)}function Ve(e){let t=e._controlledReadableStream._state;return!e._closeRequested&&t==="readable"}function Io(e,t,n,s,u,f,h){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,ve(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=h,t._strategyHWM=f,t._pullAlgorithm=s,t._cancelAlgorithm=u,e._readableStreamController=t;let _=n();v(p(_),()=>{t._started=!0,yt(t)},E=>{Re(t,E)})}function ts(e,t,n,s){let u=Object.create(He.prototype),f=()=>{},h=()=>p(void 0),_=()=>p(void 0);t.start!==void 0&&(f=()=>t.start(u)),t.pull!==void 0&&(h=()=>t.pull(u)),t.cancel!==void 0&&(_=E=>t.cancel(E)),Io(e,u,f,h,_,n,s)}function rr(e){return new TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}function rs(e,t){return qe(e._readableStreamController)?os(e):ns(e)}function ns(e,t){let n=xe(e),s=!1,u=!1,f=!1,h=!1,_,E,P,k,B,x=S(Y=>{B=Y});function ae(){return s?(u=!0,p(void 0)):(s=!0,st(n,{_chunkSteps:G=>{z(()=>{u=!1;let Z=G,ge=G;f||tr(P._readableStreamController,Z),h||tr(k._readableStreamController,ge),s=!1,u&&ae()})},_closeSteps:()=>{s=!1,f||gt(P._readableStreamController),h||gt(k._readableStreamController),(!f||!h)&&B(void 0)},_errorSteps:()=>{s=!1}}),p(void 0))}function Ye(Y){if(f=!0,_=Y,h){let G=lt([_,E]),Z=oe(e,G);B(Z)}return x}function Ae(Y){if(h=!0,E=Y,f){let G=lt([_,E]),Z=oe(e,G);B(Z)}return x}function ye(){}return P=on(ye,ae,Ye),k=on(ye,ae,Ae),N(n._closedPromise,Y=>{Re(P._readableStreamController,Y),Re(k._readableStreamController,Y),(!f||!h)&&B(void 0)}),[P,k]}function os(e){let t=xe(e),n=!1,s=!1,u=!1,f=!1,h=!1,_,E,P,k,B,x=S(w=>{B=w});function ae(w){N(w._closedPromise,A=>{w===t&&(ne(P._readableStreamController,A),ne(k._readableStreamController,A),(!f||!h)&&B(void 0))})}function Ye(){We(t)&&(me(t),t=xe(e),ae(t)),st(t,{_chunkSteps:A=>{z(()=>{s=!1,u=!1;let F=A,J=A;if(!f&&!h)try{J=ro(A)}catch(Ge){ne(P._readableStreamController,Ge),ne(k._readableStreamController,Ge),B(oe(e,Ge));return}f||xt(P._readableStreamController,F),h||xt(k._readableStreamController,J),n=!1,s?ye():u&&Y()})},_closeSteps:()=>{n=!1,f||ct(P._readableStreamController),h||ct(k._readableStreamController),P._readableStreamController._pendingPullIntos.length>0&&jt(P._readableStreamController,0),k._readableStreamController._pendingPullIntos.length>0&&jt(k._readableStreamController,0),(!f||!h)&&B(void 0)},_errorSteps:()=>{n=!1}})}function Ae(w,A){we(t)&&(me(t),t=ho(e),ae(t));let F=A?k:P,J=A?P:k;bo(t,w,{_chunkSteps:Qe=>{z(()=>{s=!1,u=!1;let Je=A?h:f;if(A?f:h)Je||Mt(F._readableStreamController,Qe);else{let Xo;try{Xo=ro(Qe)}catch(sn){ne(F._readableStreamController,sn),ne(J._readableStreamController,sn),B(oe(e,sn));return}Je||Mt(F._readableStreamController,Qe),xt(J._readableStreamController,Xo)}n=!1,s?ye():u&&Y()})},_closeSteps:Qe=>{n=!1;let Je=A?h:f,fr=A?f:h;Je||ct(F._readableStreamController),fr||ct(J._readableStreamController),Qe!==void 0&&(Je||Mt(F._readableStreamController,Qe),!fr&&J._readableStreamController._pendingPullIntos.length>0&&jt(J._readableStreamController,0)),(!Je||!fr)&&B(void 0)},_errorSteps:()=>{n=!1}})}function ye(){if(n)return s=!0,p(void 0);n=!0;let w=Ur(P._readableStreamController);return w===null?Ye():Ae(w._view,!1),p(void 0)}function Y(){if(n)return u=!0,p(void 0);n=!0;let w=Ur(k._readableStreamController);return w===null?Ye():Ae(w._view,!0),p(void 0)}function G(w){if(f=!0,_=w,h){let A=lt([_,E]),F=oe(e,A);B(F)}return x}function Z(w){if(h=!0,E=w,f){let A=lt([_,E]),F=oe(e,A);B(F)}return x}function ge(){}return P=$o(ge,ye,G),k=$o(ge,Y,Z),ae(t),[P,k]}function as(e,t){pe(e,t);let n=e,s=n?.autoAllocateChunkSize,u=n?.cancel,f=n?.pull,h=n?.start,_=n?.type;return{autoAllocateChunkSize:s===void 0?void 0:Hn(s,`${t} has member 'autoAllocateChunkSize' that`),cancel:u===void 0?void 0:is(u,n,`${t} has member 'cancel' that`),pull:f===void 0?void 0:ss(f,n,`${t} has member 'pull' that`),start:h===void 0?void 0:ls(h,n,`${t} has member 'start' that`),type:_===void 0?void 0:us(_,`${t} has member 'type' that`)}}function is(e,t,n){return re(e,n),s=>he(e,t,[s])}function ss(e,t,n){return re(e,n),s=>he(e,t,[s])}function ls(e,t,n){return re(e,n),s=>Oe(e,t,[s])}function us(e,t){if(e=`${e}`,e!=="bytes")throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}function cs(e,t){pe(e,t);let n=e?.mode;return{mode:n===void 0?void 0:fs(n,`${t} has member 'mode' that`)}}function fs(e,t){if(e=`${e}`,e!=="byob")throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}function ds(e,t){return pe(e,t),{preventCancel:!!e?.preventCancel}}function Lo(e,t){pe(e,t);let n=e?.preventAbort,s=e?.preventCancel,u=e?.preventClose,f=e?.signal;return f!==void 0&&hs(f,`${t} has member 'signal' that`),{preventAbort:!!n,preventCancel:!!s,preventClose:!!u,signal:f}}function hs(e,t){if(!Ei(e))throw new TypeError(`${t} is not an AbortSignal.`)}function ms(e,t){pe(e,t);let n=e?.readable;Wr(n,"readable","ReadableWritablePair"),Ir(n,`${t} has member 'readable' that`);let s=e?.writable;return Wr(s,"writable","ReadableWritablePair"),yo(s,`${t} has member 'writable' that`),{readable:n,writable:s}}class Te{constructor(t={},n={}){t===void 0?t=null:Un(t,"First parameter");let s=Ht(n,"Second parameter"),u=as(t,"First parameter");if(an(this),u.type==="bytes"){if(s.size!==void 0)throw new RangeError("The strategy for a byte stream cannot have a size function");let f=ht(s,0);bi(this,u,f)}else{let f=Nt(s),h=ht(s,1);ts(this,u,h,f)}}get locked(){if(!Ee(this))throw Le("locked");return Ce(this)}cancel(t=void 0){return Ee(this)?Ce(this)?b(new TypeError("Cannot cancel a stream that already has a reader")):oe(this,t):b(Le("cancel"))}getReader(t=void 0){if(!Ee(this))throw Le("getReader");return cs(t,"First parameter").mode===void 0?xe(this):ho(this)}pipeThrough(t,n={}){if(!Ee(this))throw Le("pipeThrough");be(t,1,"pipeThrough");let s=ms(t,"First parameter"),u=Lo(n,"Second parameter");if(Ce(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(Ue(s.writable))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");let f=Wo(this,s.writable,u.preventClose,u.preventAbort,u.preventCancel,u.signal);return L(f),s.readable}pipeTo(t,n={}){if(!Ee(this))return b(Le("pipeTo"));if(t===void 0)return b("Parameter 1 is required in 'pipeTo'.");if(!Me(t))return b(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));let s;try{s=Lo(n,"Second parameter")}catch(u){return b(u)}return Ce(this)?b(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):Ue(t)?b(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):Wo(this,t,s.preventClose,s.preventAbort,s.preventCancel,s.signal)}tee(){if(!Ee(this))throw Le("tee");let t=rs(this);return lt(t)}values(t=void 0){if(!Ee(this))throw Le("values");let n=ds(t,"First parameter");return ui(this,n.preventCancel)}}Object.defineProperties(Te.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(Te.prototype,i.toStringTag,{value:"ReadableStream",configurable:!0}),typeof i.asyncIterator=="symbol"&&Object.defineProperty(Te.prototype,i.asyncIterator,{value:Te.prototype.values,writable:!0,configurable:!0});function on(e,t,n,s=1,u=()=>1){let f=Object.create(Te.prototype);an(f);let h=Object.create(He.prototype);return Io(f,h,e,t,n,s,u),f}function $o(e,t,n){let s=Object.create(Te.prototype);an(s);let u=Object.create(je.prototype);return fo(s,u,e,t,n,0,void 0),s}function an(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function Ee(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")?!1:e instanceof Te}function Ce(e){return e._reader!==void 0}function oe(e,t){if(e._disturbed=!0,e._state==="closed")return p(void 0);if(e._state==="errored")return b(e._storedError);_t(e);let n=e._reader;n!==void 0&&We(n)&&(n._readIntoRequests.forEach(u=>{u._closeSteps(void 0)}),n._readIntoRequests=new te);let s=e._readableStreamController[qr](t);return D(s,l)}function _t(e){e._state="closed";let t=e._reader;t!==void 0&&(zn(t),we(t)&&(t._readRequests.forEach(n=>{n._closeSteps()}),t._readRequests=new te))}function zo(e,t){e._state="errored",e._storedError=t;let n=e._reader;n!==void 0&&(Or(n,t),we(n)?(n._readRequests.forEach(s=>{s._errorSteps(t)}),n._readRequests=new te):(n._readIntoRequests.forEach(s=>{s._errorSteps(t)}),n._readIntoRequests=new te))}function Le(e){return new TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}function xo(e,t){pe(e,t);let n=e?.highWaterMark;return Wr(n,"highWaterMark","QueuingStrategyInit"),{highWaterMark:Fr(n)}}let jo=e=>e.byteLength;try{Object.defineProperty(jo,"name",{value:"size",configurable:!0})}catch{}class nr{constructor(t){be(t,1,"ByteLengthQueuingStrategy"),t=xo(t,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=t.highWaterMark}get highWaterMark(){if(!Uo(this))throw Mo("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!Uo(this))throw Mo("size");return jo}}Object.defineProperties(nr.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(nr.prototype,i.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});function Mo(e){return new TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}function Uo(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")?!1:e instanceof nr}let No=()=>1;try{Object.defineProperty(No,"name",{value:"size",configurable:!0})}catch{}class or{constructor(t){be(t,1,"CountQueuingStrategy"),t=xo(t,"First parameter"),this._countQueuingStrategyHighWaterMark=t.highWaterMark}get highWaterMark(){if(!Vo(this))throw Ho("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!Vo(this))throw Ho("size");return No}}Object.defineProperties(or.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(or.prototype,i.toStringTag,{value:"CountQueuingStrategy",configurable:!0});function Ho(e){return new TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}function Vo(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")?!1:e instanceof or}function ps(e,t){pe(e,t);let n=e?.flush,s=e?.readableType,u=e?.start,f=e?.transform,h=e?.writableType;return{flush:n===void 0?void 0:bs(n,e,`${t} has member 'flush' that`),readableType:s,start:u===void 0?void 0:ys(u,e,`${t} has member 'start' that`),transform:f===void 0?void 0:gs(f,e,`${t} has member 'transform' that`),writableType:h}}function bs(e,t,n){return re(e,n),s=>he(e,t,[s])}function ys(e,t,n){return re(e,n),s=>Oe(e,t,[s])}function gs(e,t,n){return re(e,n),(s,u)=>he(e,t,[s,u])}class ar{constructor(t={},n={},s={}){t===void 0&&(t=null);let u=Ht(n,"Second parameter"),f=Ht(s,"Third parameter"),h=ps(t,"First parameter");if(h.readableType!==void 0)throw new RangeError("Invalid readableType specified");if(h.writableType!==void 0)throw new RangeError("Invalid writableType specified");let _=ht(f,0),E=Nt(f),P=ht(u,1),k=Nt(u),B,x=S(ae=>{B=ae});_s(this,x,P,k,_,E),ws(this,h),h.start!==void 0?B(h.start(this._transformStreamController)):B(void 0)}get readable(){if(!Yo(this))throw Ko("readable");return this._readable}get writable(){if(!Yo(this))throw Ko("writable");return this._writable}}Object.defineProperties(ar.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(ar.prototype,i.toStringTag,{value:"TransformStream",configurable:!0});function _s(e,t,n,s,u,f){function h(){return t}function _(x){return Ts(e,x)}function E(x){return Es(e,x)}function P(){return Cs(e)}e._writable=Pi(h,_,P,E,n,s);function k(){return As(e)}function B(x){return sr(e,x),p(void 0)}e._readable=on(h,k,B,u,f),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,lr(e,!0),e._transformStreamController=void 0}function Yo(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")?!1:e instanceof ar}function ir(e,t){Re(e._readable._readableStreamController,t),sr(e,t)}function sr(e,t){Go(e._transformStreamController),Kr(e._writable._writableStreamController,t),e._backpressure&&lr(e,!1)}function lr(e,t){e._backpressureChangePromise!==void 0&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=S(n=>{e._backpressureChangePromise_resolve=n}),e._backpressure=t}class St{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!ur(this))throw cr("desiredSize");let t=this._controlledTransformStream._readable._readableStreamController;return nn(t)}enqueue(t=void 0){if(!ur(this))throw cr("enqueue");Qo(this,t)}error(t=void 0){if(!ur(this))throw cr("error");vs(this,t)}terminate(){if(!ur(this))throw cr("terminate");Rs(this)}}Object.defineProperties(St.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(St.prototype,i.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});function ur(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")?!1:e instanceof St}function Ss(e,t,n,s){t._controlledTransformStream=e,e._transformStreamController=t,t._transformAlgorithm=n,t._flushAlgorithm=s}function ws(e,t){let n=Object.create(St.prototype),s=f=>{try{return Qo(n,f),p(void 0)}catch(h){return b(h)}},u=()=>p(void 0);t.transform!==void 0&&(s=f=>t.transform(f,n)),t.flush!==void 0&&(u=()=>t.flush(n)),Ss(e,n,s,u)}function Go(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0}function Qo(e,t){let n=e._controlledTransformStream,s=n._readable._readableStreamController;if(!Ve(s))throw new TypeError("Readable side is not in a state that permits enqueue");try{tr(s,t)}catch(f){throw sr(n,f),n._readable._storedError}es(s)!==n._backpressure&&lr(n,!0)}function vs(e,t){ir(e._controlledTransformStream,t)}function Jo(e,t){let n=e._transformAlgorithm(t);return D(n,void 0,s=>{throw ir(e._controlledTransformStream,s),s})}function Rs(e){let t=e._controlledTransformStream,n=t._readable._readableStreamController;gt(n);let s=new TypeError("TransformStream terminated");sr(t,s)}function Ts(e,t){let n=e._transformStreamController;if(e._backpressure){let s=e._backpressureChangePromise;return D(s,()=>{let u=e._writable;if(u._state==="erroring")throw u._storedError;return Jo(n,t)})}return Jo(n,t)}function Es(e,t){return ir(e,t),p(void 0)}function Cs(e){let t=e._readable,n=e._transformStreamController,s=n._flushAlgorithm();return Go(n),D(s,()=>{if(t._state==="errored")throw t._storedError;gt(t._readableStreamController)},u=>{throw ir(e,u),t._storedError})}function As(e){return lr(e,!1),e._backpressureChangePromise}function cr(e){return new TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}function Ko(e){return new TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}a.ByteLengthQueuingStrategy=nr,a.CountQueuingStrategy=or,a.ReadableByteStreamController=je,a.ReadableStream=Te,a.ReadableStreamBYOBReader=dt,a.ReadableStreamBYOBRequest=ut,a.ReadableStreamDefaultController=He,a.ReadableStreamDefaultReader=it,a.TransformStream=ar,a.TransformStreamDefaultController=St,a.WritableStream=mt,a.WritableStreamDefaultController=Ne,a.WritableStreamDefaultWriter=pt,Object.defineProperty(a,"__esModule",{value:!0})})}}),Ms=Sn({"node_modules/fetch-blob/streams.cjs"(){var r=65536;if(!globalThis.ReadableStream)try{let o=require("process"),{emitWarning:a}=o;try{o.emitWarning=()=>{},Object.assign(globalThis,require("stream/web")),o.emitWarning=a}catch(i){throw o.emitWarning=a,i}}catch{Object.assign(globalThis,js())}try{let{Blob:o}=require("buffer");o&&!o.prototype.stream&&(o.prototype.stream=function(i){let l=0,c=this;return new ReadableStream({type:"bytes",async pull(m){let y=await c.slice(l,Math.min(c.size,l+r)).arrayBuffer();l+=y.byteLength,m.enqueue(new Uint8Array(y)),l===c.size&&m.close()}})})}catch{}}});async function*ln(r,o=!0){for(let a of r)if("stream"in a)yield*a.stream();else if(ArrayBuffer.isView(a))if(o){let i=a.byteOffset,l=a.byteOffset+a.byteLength;for(;i!==l;){let c=Math.min(l-i,pn),m=a.buffer.slice(i,i+c);i+=m.byteLength,yield new Uint8Array(m)}}else yield a;else{let i=0,l=a;for(;i!==l.size;){let m=await l.slice(i,Math.min(l.size,i+pn)).arrayBuffer();i+=m.byteLength,yield new Uint8Array(m)}}}var Us,pn,un,bn,rt,Pt=At({"node_modules/fetch-blob/index.js"(){Us=H(Ms()),pn=65536,un=class yn{#e=[];#t="";#r=0;#n="transparent";constructor(o=[],a={}){if(typeof o!="object"||o===null)throw new TypeError("Failed to construct 'Blob': The provided value cannot be converted to a sequence.");if(typeof o[Symbol.iterator]!="function")throw new TypeError("Failed to construct 'Blob': The object must have a callable @@iterator property.");if(typeof a!="object"&&typeof a!="function")throw new TypeError("Failed to construct 'Blob': parameter 2 cannot convert to dictionary.");a===null&&(a={});let i=new TextEncoder;for(let c of o){let m;ArrayBuffer.isView(c)?m=new Uint8Array(c.buffer.slice(c.byteOffset,c.byteOffset+c.byteLength)):c instanceof ArrayBuffer?m=new Uint8Array(c.slice(0)):c instanceof yn?m=c:m=i.encode(`${c}`),this.#r+=ArrayBuffer.isView(m)?m.byteLength:m.size,this.#e.push(m)}this.#n=`${a.endings===void 0?"transparent":a.endings}`;let l=a.type===void 0?"":String(a.type);this.#t=/^[\x20-\x7E]*$/.test(l)?l:""}get size(){return this.#r}get type(){return this.#t}async text(){let o=new TextDecoder,a="";for await(let i of ln(this.#e,!1))a+=o.decode(i,{stream:!0});return a+=o.decode(),a}async arrayBuffer(){let o=new Uint8Array(this.size),a=0;for await(let i of ln(this.#e,!1))o.set(i,a),a+=i.length;return o.buffer}stream(){let o=ln(this.#e,!0);return new globalThis.ReadableStream({type:"bytes",async pull(a){let i=await o.next();i.done?a.close():a.enqueue(i.value)},async cancel(){await o.return()}})}slice(o=0,a=this.size,i=""){let{size:l}=this,c=o<0?Math.max(l+o,0):Math.min(o,l),m=a<0?Math.max(l+a,0):Math.min(a,l),d=Math.max(m-c,0),y=this.#e,g=[],q=0;for(let O of y){if(q>=d)break;let S=ArrayBuffer.isView(O)?O.byteLength:O.size;if(c&&S<=c)c-=S,m-=S;else{let p;ArrayBuffer.isView(O)?(p=O.subarray(c,Math.min(S,m)),q+=p.byteLength):(p=O.slice(c,Math.min(S,m)),q+=p.size),m-=S,g.push(p),c=0}}let I=new yn([],{type:String(i).toLowerCase()});return I.#r=d,I.#e=g,I}get[Symbol.toStringTag](){return"Blob"}static[Symbol.hasInstance](o){return o&&typeof o=="object"&&typeof o.constructor=="function"&&(typeof o.stream=="function"||typeof o.arrayBuffer=="function")&&/^(Blob|File)$/.test(o[Symbol.toStringTag])}},Object.defineProperties(un.prototype,{size:{enumerable:!0},type:{enumerable:!0},slice:{enumerable:!0}}),bn=un,rt=bn}}),ea,ta,kt,ba=At({"node_modules/fetch-blob/file.js"(){Pt(),ea=class extends rt{#e=0;#t="";constructor(o,a,i={}){if(arguments.length<2)throw new TypeError(`Failed to construct 'File': 2 arguments required, but only ${arguments.length} present.`);super(o,i),i===null&&(i={});let l=i.lastModified===void 0?Date.now():Number(i.lastModified);Number.isNaN(l)||(this.#e=l),this.#t=String(a)}get name(){return this.#t}get lastModified(){return this.#e}get[Symbol.toStringTag](){return"File"}static[Symbol.hasInstance](o){return!!o&&o instanceof rt&&/^(File)$/.test(o[Symbol.toStringTag])}},ta=ea,kt=ta}});function Ns(r,o=rt){var a=`${gn()}${gn()}`.replace(/\./g,"").slice(-28).padStart(32,"-"),i=[],l=`--${a}\r
Content-Disposition: form-data; name="`;return r.forEach((c,m)=>typeof c=="string"?i.push(l+br(m)+`"\r
\r
${c.replace(/\r(?!\n)|(?<!\r)\n/g,`\r
`)}\r
`):i.push(l+br(m)+`"; filename="${br(c.name,1)}"\r
Content-Type: ${c.type||"application/octet-stream"}\r
\r
`,c,`\r
`)),i.push(`--${a}--`),new o(i,{type:"multipart/form-data; boundary="+a})}var Xe,ra,na,gn,oa,cn,br,Pe,nt,vr=At({"node_modules/formdata-polyfill/esm.min.js"(){Pt(),ba(),{toStringTag:Xe,iterator:ra,hasInstance:na}=Symbol,gn=Math.random,oa="append,set,get,getAll,delete,keys,values,entries,forEach,constructor".split(","),cn=(r,o,a)=>(r+="",/^(Blob|File)$/.test(o&&o[Xe])?[(a=a!==void 0?a+"":o[Xe]=="File"?o.name:"blob",r),o.name!==a||o[Xe]=="blob"?new kt([o],a,o):o]:[r,o+""]),br=(r,o)=>(o?r:r.replace(/\r?\n|\r/g,`\r
`)).replace(/\n/g,"%0A").replace(/\r/g,"%0D").replace(/"/g,"%22"),Pe=(r,o,a)=>{if(o.length<a)throw new TypeError(`Failed to execute '${r}' on 'FormData': ${a} arguments required, but only ${o.length} present.`)},nt=class{#e=[];constructor(...o){if(o.length)throw new TypeError("Failed to construct 'FormData': parameter 1 is not of type 'HTMLFormElement'.")}get[Xe](){return"FormData"}[ra](){return this.entries()}static[na](o){return o&&typeof o=="object"&&o[Xe]==="FormData"&&!oa.some(a=>typeof o[a]!="function")}append(...o){Pe("append",arguments,2),this.#e.push(cn(...o))}delete(o){Pe("delete",arguments,1),o+="",this.#e=this.#e.filter(([a])=>a!==o)}get(o){Pe("get",arguments,1),o+="";for(var a=this.#e,i=a.length,l=0;l<i;l++)if(a[l][0]===o)return a[l][1];return null}getAll(o,a){return Pe("getAll",arguments,1),a=[],o+="",this.#e.forEach(i=>i[0]===o&&a.push(i[1])),a}has(o){return Pe("has",arguments,1),o+="",this.#e.some(a=>a[0]===o)}forEach(o,a){Pe("forEach",arguments,1);for(var[i,l]of this)o.call(a,l,i,this)}set(...o){Pe("set",arguments,2);var a=[],i=!0;o=cn(...o),this.#e.forEach(l=>{l[0]===o[0]?i&&(i=!a.push(o)):a.push(l)}),i&&a.push(o),this.#e=a}*entries(){yield*this.#e}*keys(){for(var[o]of this)yield o}*values(){for(var[,o]of this)yield o}}}}),Hs=Sn({"node_modules/node-domexception/index.js"(r,o){if(!globalThis.DOMException)try{let{MessageChannel:a}=require("worker_threads"),i=new a().port1,l=new ArrayBuffer;i.postMessage(l,[l,l])}catch(a){a.constructor.name==="DOMException"&&(globalThis.DOMException=a.constructor)}o.exports=globalThis.DOMException}}),wt,aa,ia,hr,ya,ga,_a,Sa,fn,dn,mr,wa=At({"node_modules/fetch-blob/from.js"(){wt=H(require("fs")),aa=H(require("path")),ia=H(Hs()),ba(),Pt(),{stat:hr}=wt.promises,ya=(r,o)=>fn((0,wt.statSync)(r),r,o),ga=(r,o)=>hr(r).then(a=>fn(a,r,o)),_a=(r,o)=>hr(r).then(a=>dn(a,r,o)),Sa=(r,o)=>dn((0,wt.statSync)(r),r,o),fn=(r,o,a="")=>new rt([new mr({path:o,size:r.size,lastModified:r.mtimeMs,start:0})],{type:a}),dn=(r,o,a="")=>new kt([new mr({path:o,size:r.size,lastModified:r.mtimeMs,start:0})],(0,aa.basename)(o),{type:a,lastModified:r.mtimeMs}),mr=class{#e;#t;constructor(r){this.#e=r.path,this.#t=r.start,this.size=r.size,this.lastModified=r.lastModified}slice(r,o){return new mr({path:this.#e,lastModified:this.lastModified,size:o-r,start:this.#t+r})}async*stream(){let{mtimeMs:r}=await hr(this.#e);if(r>this.lastModified)throw new ia.default("The requested file could not be read, typically due to permission problems that have occurred after a reference to a file was acquired.","NotReadableError");yield*(0,wt.createReadStream)(this.#e,{start:this.#t,end:this.#t+this.size-1})}get[Symbol.toStringTag](){return"Blob"}}}}),va={};pa(va,{toFormData:()=>Ys});function Vs(r){let o=r.match(/\bfilename=("(.*?)"|([^()<>@,;:\\"/[\]?={}\s\t]+))($|;\s)/i);if(!o)return;let a=o[2]||o[3]||"",i=a.slice(a.lastIndexOf("\\")+1);return i=i.replace(/%22/g,'"'),i=i.replace(/&#(\d{4});/g,(l,c)=>String.fromCharCode(c)),i}async function Ys(r,o){if(!/multipart/i.test(o))throw new TypeError("Failed to fetch");let a=o.match(/boundary=(?:"([^"]+)"|([^;]+))/i);if(!a)throw new TypeError("no or bad content-type header, no multipart boundary");let i=new Ra(a[1]||a[2]),l,c,m,d,y,g,q=[],I=new nt,O=v=>{m+=C.decode(v,{stream:!0})},S=v=>{q.push(v)},p=()=>{let v=new kt(q,g,{type:y});I.append(d,v)},b=()=>{I.append(d,m)},C=new TextDecoder("utf-8");C.decode(),i.onPartBegin=function(){i.onPartData=O,i.onPartEnd=b,l="",c="",m="",d="",y="",g=null,q.length=0},i.onHeaderField=function(v){l+=C.decode(v,{stream:!0})},i.onHeaderValue=function(v){c+=C.decode(v,{stream:!0})},i.onHeaderEnd=function(){if(c+=C.decode(),l=l.toLowerCase(),l==="content-disposition"){let v=c.match(/\bname=("([^"]*)"|([^()<>@,;:\\"/[\]?={}\s\t]+))/i);v&&(d=v[2]||v[3]||""),g=Vs(c),g&&(i.onPartData=S,i.onPartEnd=p)}else l==="content-type"&&(y=c);c="",l=""};for await(let v of r)i.write(v);return i.end(),I}var ie,W,hn,_e,vt,Rt,sa,Ze,la,ua,ca,fa,ke,Ra,Gs=At({"node_modules/node-fetch/src/utils/multipart-parser.js"(){wa(),vr(),ie=0,W={START_BOUNDARY:ie++,HEADER_FIELD_START:ie++,HEADER_FIELD:ie++,HEADER_VALUE_START:ie++,HEADER_VALUE:ie++,HEADER_VALUE_ALMOST_DONE:ie++,HEADERS_ALMOST_DONE:ie++,PART_DATA_START:ie++,PART_DATA:ie++,END:ie++},hn=1,_e={PART_BOUNDARY:hn,LAST_BOUNDARY:hn*=2},vt=10,Rt=13,sa=32,Ze=45,la=58,ua=97,ca=122,fa=r=>r|32,ke=()=>{},Ra=class{constructor(r){this.index=0,this.flags=0,this.onHeaderEnd=ke,this.onHeaderField=ke,this.onHeadersEnd=ke,this.onHeaderValue=ke,this.onPartBegin=ke,this.onPartData=ke,this.onPartEnd=ke,this.boundaryChars={},r=`\r
--`+r;let o=new Uint8Array(r.length);for(let a=0;a<r.length;a++)o[a]=r.charCodeAt(a),this.boundaryChars[o[a]]=!0;this.boundary=o,this.lookbehind=new Uint8Array(this.boundary.length+8),this.state=W.START_BOUNDARY}write(r){let o=0,a=r.length,i=this.index,{lookbehind:l,boundary:c,boundaryChars:m,index:d,state:y,flags:g}=this,q=this.boundary.length,I=q-1,O=r.length,S,p,b=N=>{this[N+"Mark"]=o},C=N=>{delete this[N+"Mark"]},v=(N,D,L,z)=>{(D===void 0||D!==L)&&this[N](z&&z.subarray(D,L))},U=(N,D)=>{let L=N+"Mark";L in this&&(D?(v(N,this[L],o,r),delete this[L]):(v(N,this[L],r.length,r),this[L]=0))};for(o=0;o<a;o++)switch(S=r[o],y){case W.START_BOUNDARY:if(d===c.length-2){if(S===Ze)g|=_e.LAST_BOUNDARY;else if(S!==Rt)return;d++;break}else if(d-1===c.length-2){if(g&_e.LAST_BOUNDARY&&S===Ze)y=W.END,g=0;else if(!(g&_e.LAST_BOUNDARY)&&S===vt)d=0,v("onPartBegin"),y=W.HEADER_FIELD_START;else return;break}S!==c[d+2]&&(d=-2),S===c[d+2]&&d++;break;case W.HEADER_FIELD_START:y=W.HEADER_FIELD,b("onHeaderField"),d=0;case W.HEADER_FIELD:if(S===Rt){C("onHeaderField"),y=W.HEADERS_ALMOST_DONE;break}if(d++,S===Ze)break;if(S===la){if(d===1)return;U("onHeaderField",!0),y=W.HEADER_VALUE_START;break}if(p=fa(S),p<ua||p>ca)return;break;case W.HEADER_VALUE_START:if(S===sa)break;b("onHeaderValue"),y=W.HEADER_VALUE;case W.HEADER_VALUE:S===Rt&&(U("onHeaderValue",!0),v("onHeaderEnd"),y=W.HEADER_VALUE_ALMOST_DONE);break;case W.HEADER_VALUE_ALMOST_DONE:if(S!==vt)return;y=W.HEADER_FIELD_START;break;case W.HEADERS_ALMOST_DONE:if(S!==vt)return;v("onHeadersEnd"),y=W.PART_DATA_START;break;case W.PART_DATA_START:y=W.PART_DATA,b("onPartData");case W.PART_DATA:if(i=d,d===0){for(o+=I;o<O&&!(r[o]in m);)o+=q;o-=I,S=r[o]}if(d<c.length)c[d]===S?(d===0&&U("onPartData",!0),d++):d=0;else if(d===c.length)d++,S===Rt?g|=_e.PART_BOUNDARY:S===Ze?g|=_e.LAST_BOUNDARY:d=0;else if(d-1===c.length)if(g&_e.PART_BOUNDARY){if(d=0,S===vt){g&=~_e.PART_BOUNDARY,v("onPartEnd"),v("onPartBegin"),y=W.HEADER_FIELD_START;break}}else g&_e.LAST_BOUNDARY&&S===Ze?(v("onPartEnd"),y=W.END,g=0):d=0;if(d>0)l[d-1]=S;else if(i>0){let N=new Uint8Array(l.buffer,l.byteOffset,l.byteLength);v("onPartData",0,i,N),i=0,b("onPartData"),o--}break;case W.END:break;default:throw new Error(`Unexpected state entered: ${y}`)}U("onHeaderField"),U("onHeaderValue"),U("onPartData"),this.index=d,this.state=y,this.flags=g}end(){if(this.state===W.HEADER_FIELD_START&&this.index===0||this.state===W.PART_DATA&&this.index===this.boundary.length)this.onPartEnd();else if(this.state!==W.END)throw new Error("MultipartParser.end(): stream ended unexpectedly")}}}});pa(ka,{AbortError:()=>Aa,Blob:()=>bn,FetchError:()=>le,File:()=>kt,FormData:()=>nt,Headers:()=>Se,Request:()=>Ct,Response:()=>X,blobFrom:()=>ga,blobFromSync:()=>ya,default:()=>Pa,fileFrom:()=>_a,fileFromSync:()=>Sa,isRedirect:()=>vn});var Qs=H(require("http")),Js=H(require("https")),et=H(require("zlib")),ce=H(require("stream")),pr=H(require("buffer"));function Ks(r){if(!/^data:/i.test(r))throw new TypeError('`uri` does not appear to be a Data URI (must begin with "data:")');r=r.replace(/\r?\n/g,"");let o=r.indexOf(",");if(o===-1||o<=4)throw new TypeError("malformed data: URI");let a=r.substring(5,o).split(";"),i="",l=!1,c=a[0]||"text/plain",m=c;for(let q=1;q<a.length;q++)a[q]==="base64"?l=!0:a[q]&&(m+=`;${a[q]}`,a[q].indexOf("charset=")===0&&(i=a[q].substring(8)));!a[0]&&!i.length&&(m+=";charset=US-ASCII",i="US-ASCII");let d=l?"base64":"ascii",y=unescape(r.substring(o+1)),g=Buffer.from(y,d);return g.type=c,g.typeFull=m,g.charset=i,g}var Xs=Ks,fe=H(require("stream")),ot=H(require("util")),ee=H(require("buffer"));Pt();vr();var Rr=class extends Error{constructor(r,o){super(r),Error.captureStackTrace(this,this.constructor),this.type=o}get name(){return this.constructor.name}get[Symbol.toStringTag](){return this.constructor.name}},le=class extends Rr{constructor(r,o,a){super(r,o),a&&(this.code=this.errno=a.code,this.erroredSysCall=a.syscall)}},gr=Symbol.toStringTag,Ta=r=>typeof r=="object"&&typeof r.append=="function"&&typeof r.delete=="function"&&typeof r.get=="function"&&typeof r.getAll=="function"&&typeof r.has=="function"&&typeof r.set=="function"&&typeof r.sort=="function"&&r[gr]==="URLSearchParams",_r=r=>r&&typeof r=="object"&&typeof r.arrayBuffer=="function"&&typeof r.type=="string"&&typeof r.stream=="function"&&typeof r.constructor=="function"&&/^(Blob|File)$/.test(r[gr]),Zs=r=>typeof r=="object"&&(r[gr]==="AbortSignal"||r[gr]==="EventTarget"),el=(r,o)=>{let a=new URL(o).hostname,i=new URL(r).hostname;return a===i||a.endsWith(`.${i}`)},tl=(r,o)=>{let a=new URL(o).protocol,i=new URL(r).protocol;return a===i},rl=(0,ot.promisify)(fe.default.pipeline),Q=Symbol("Body internals"),Et=class{constructor(r,{size:o=0}={}){let a=null;r===null?r=null:Ta(r)?r=ee.Buffer.from(r.toString()):_r(r)||ee.Buffer.isBuffer(r)||(ot.types.isAnyArrayBuffer(r)?r=ee.Buffer.from(r):ArrayBuffer.isView(r)?r=ee.Buffer.from(r.buffer,r.byteOffset,r.byteLength):r instanceof fe.default||(r instanceof nt?(r=Ns(r),a=r.type.split("=")[1]):r=ee.Buffer.from(String(r))));let i=r;ee.Buffer.isBuffer(r)?i=fe.default.Readable.from(r):_r(r)&&(i=fe.default.Readable.from(r.stream())),this[Q]={body:r,stream:i,boundary:a,disturbed:!1,error:null},this.size=o,r instanceof fe.default&&r.on("error",l=>{let c=l instanceof Rr?l:new le(`Invalid response body while trying to fetch ${this.url}: ${l.message}`,"system",l);this[Q].error=c})}get body(){return this[Q].stream}get bodyUsed(){return this[Q].disturbed}async arrayBuffer(){let{buffer:r,byteOffset:o,byteLength:a}=await mn(this);return r.slice(o,o+a)}async formData(){let r=this.headers.get("content-type");if(r.startsWith("application/x-www-form-urlencoded")){let a=new nt,i=new URLSearchParams(await this.text());for(let[l,c]of i)a.append(l,c);return a}let{toFormData:o}=await Promise.resolve().then(()=>(Gs(),va));return o(this.body,r)}async blob(){let r=this.headers&&this.headers.get("content-type")||this[Q].body&&this[Q].body.type||"",o=await this.arrayBuffer();return new rt([o],{type:r})}async json(){let r=await this.text();return JSON.parse(r)}async text(){let r=await mn(this);return new TextDecoder().decode(r)}buffer(){return mn(this)}};Et.prototype.buffer=(0,ot.deprecate)(Et.prototype.buffer,"Please use 'response.arrayBuffer()' instead of 'response.buffer()'","node-fetch#buffer");Object.defineProperties(Et.prototype,{body:{enumerable:!0},bodyUsed:{enumerable:!0},arrayBuffer:{enumerable:!0},blob:{enumerable:!0},json:{enumerable:!0},text:{enumerable:!0},data:{get:(0,ot.deprecate)(()=>{},"data doesn't exist, use json(), text(), arrayBuffer(), or body instead","https://github.com/node-fetch/node-fetch/issues/1000 (response)")}});async function mn(r){if(r[Q].disturbed)throw new TypeError(`body used already for: ${r.url}`);if(r[Q].disturbed=!0,r[Q].error)throw r[Q].error;let{body:o}=r;if(o===null||!(o instanceof fe.default))return ee.Buffer.alloc(0);let a=[],i=0;try{for await(let l of o){if(r.size>0&&i+l.length>r.size){let c=new le(`content size at ${r.url} over limit: ${r.size}`,"max-size");throw o.destroy(c),c}i+=l.length,a.push(l)}}catch(l){throw l instanceof Rr?l:new le(`Invalid response body while trying to fetch ${r.url}: ${l.message}`,"system",l)}if(o.readableEnded===!0||o._readableState.ended===!0)try{return a.every(l=>typeof l=="string")?ee.Buffer.from(a.join("")):ee.Buffer.concat(a,i)}catch(l){throw new le(`Could not create Buffer from response body for ${r.url}: ${l.message}`,"system",l)}else throw new le(`Premature close of server response while trying to fetch ${r.url}`)}var wn=(r,o)=>{let a,i,{body:l}=r[Q];if(r.bodyUsed)throw new Error("cannot clone body after it is used");return l instanceof fe.default&&typeof l.getBoundary!="function"&&(a=new fe.PassThrough({highWaterMark:o}),i=new fe.PassThrough({highWaterMark:o}),l.pipe(a),l.pipe(i),r[Q].stream=a,l=i),l},nl=(0,ot.deprecate)(r=>r.getBoundary(),"form-data doesn't follow the spec and requires special treatment. Use alternative package","https://github.com/node-fetch/node-fetch/issues/1167"),Ea=(r,o)=>r===null?null:typeof r=="string"?"text/plain;charset=UTF-8":Ta(r)?"application/x-www-form-urlencoded;charset=UTF-8":_r(r)?r.type||null:ee.Buffer.isBuffer(r)||ot.types.isAnyArrayBuffer(r)||ArrayBuffer.isView(r)?null:r instanceof nt?`multipart/form-data; boundary=${o[Q].boundary}`:r&&typeof r.getBoundary=="function"?`multipart/form-data;boundary=${nl(r)}`:r instanceof fe.default?null:"text/plain;charset=UTF-8",ol=r=>{let{body:o}=r[Q];return o===null?0:_r(o)?o.size:ee.Buffer.isBuffer(o)?o.length:o&&typeof o.getLengthSync=="function"&&o.hasKnownLength&&o.hasKnownLength()?o.getLengthSync():null},al=async(r,{body:o})=>{o===null?r.end():await rl(o,r)},da=H(require("util")),Sr=H(require("http")),yr=typeof Sr.default.validateHeaderName=="function"?Sr.default.validateHeaderName:r=>{if(!/^[\^`\-\w!#$%&'*+.|~]+$/.test(r)){let o=new TypeError(`Header name must be a valid HTTP token [${r}]`);throw Object.defineProperty(o,"code",{value:"ERR_INVALID_HTTP_TOKEN"}),o}},_n=typeof Sr.default.validateHeaderValue=="function"?Sr.default.validateHeaderValue:(r,o)=>{if(/[^\t\u0020-\u007E\u0080-\u00FF]/.test(o)){let a=new TypeError(`Invalid character in header content ["${r}"]`);throw Object.defineProperty(a,"code",{value:"ERR_INVALID_CHAR"}),a}},Se=class extends URLSearchParams{constructor(r){let o=[];if(r instanceof Se){let a=r.raw();for(let[i,l]of Object.entries(a))o.push(...l.map(c=>[i,c]))}else if(r!=null)if(typeof r=="object"&&!da.types.isBoxedPrimitive(r)){let a=r[Symbol.iterator];if(a==null)o.push(...Object.entries(r));else{if(typeof a!="function")throw new TypeError("Header pairs must be iterable");o=[...r].map(i=>{if(typeof i!="object"||da.types.isBoxedPrimitive(i))throw new TypeError("Each header pair must be an iterable object");return[...i]}).map(i=>{if(i.length!==2)throw new TypeError("Each header pair must be a name/value tuple");return[...i]})}}else throw new TypeError("Failed to construct 'Headers': The provided value is not of type '(sequence<sequence<ByteString>> or record<ByteString, ByteString>)");return o=o.length>0?o.map(([a,i])=>(yr(a),_n(a,String(i)),[String(a).toLowerCase(),String(i)])):void 0,super(o),new Proxy(this,{get(a,i,l){switch(i){case"append":case"set":return(c,m)=>(yr(c),_n(c,String(m)),URLSearchParams.prototype[i].call(a,String(c).toLowerCase(),String(m)));case"delete":case"has":case"getAll":return c=>(yr(c),URLSearchParams.prototype[i].call(a,String(c).toLowerCase()));case"keys":return()=>(a.sort(),new Set(URLSearchParams.prototype.keys.call(a)).keys());default:return Reflect.get(a,i,l)}}})}get[Symbol.toStringTag](){return this.constructor.name}toString(){return Object.prototype.toString.call(this)}get(r){let o=this.getAll(r);if(o.length===0)return null;let a=o.join(", ");return/^content-encoding$/i.test(r)&&(a=a.toLowerCase()),a}forEach(r,o=void 0){for(let a of this.keys())Reflect.apply(r,o,[this.get(a),a,this])}*values(){for(let r of this.keys())yield this.get(r)}*entries(){for(let r of this.keys())yield[r,this.get(r)]}[Symbol.iterator](){return this.entries()}raw(){return[...this.keys()].reduce((r,o)=>(r[o]=this.getAll(o),r),{})}[Symbol.for("nodejs.util.inspect.custom")](){return[...this.keys()].reduce((r,o)=>{let a=this.getAll(o);return o==="host"?r[o]=a[0]:r[o]=a.length>1?a:a[0],r},{})}};Object.defineProperties(Se.prototype,["get","entries","forEach","values"].reduce((r,o)=>(r[o]={enumerable:!0},r),{}));function il(r=[]){return new Se(r.reduce((o,a,i,l)=>(i%2===0&&o.push(l.slice(i,i+2)),o),[]).filter(([o,a])=>{try{return yr(o),_n(o,String(a)),!0}catch{return!1}}))}var sl=new Set([301,302,303,307,308]),vn=r=>sl.has(r),se=Symbol("Response internals"),X=class extends Et{constructor(r=null,o={}){super(r,o);let a=o.status!=null?o.status:200,i=new Se(o.headers);if(r!==null&&!i.has("Content-Type")){let l=Ea(r,this);l&&i.append("Content-Type",l)}this[se]={type:"default",url:o.url,status:a,statusText:o.statusText||"",headers:i,counter:o.counter,highWaterMark:o.highWaterMark}}get type(){return this[se].type}get url(){return this[se].url||""}get status(){return this[se].status}get ok(){return this[se].status>=200&&this[se].status<300}get redirected(){return this[se].counter>0}get statusText(){return this[se].statusText}get headers(){return this[se].headers}get highWaterMark(){return this[se].highWaterMark}clone(){return new X(wn(this,this.highWaterMark),{type:this.type,url:this.url,status:this.status,statusText:this.statusText,headers:this.headers,ok:this.ok,redirected:this.redirected,size:this.size,highWaterMark:this.highWaterMark})}static redirect(r,o=302){if(!vn(o))throw new RangeError('Failed to execute "redirect" on "response": Invalid status code');return new X(null,{headers:{location:new URL(r).toString()},status:o})}static error(){let r=new X(null,{status:0,statusText:""});return r[se].type="error",r}static json(r=void 0,o={}){let a=JSON.stringify(r);if(a===void 0)throw new TypeError("data is not JSON serializable");let i=new Se(o&&o.headers);return i.has("content-type")||i.set("content-type","application/json"),new X(a,{...o,headers:i})}get[Symbol.toStringTag](){return"Response"}};Object.defineProperties(X.prototype,{type:{enumerable:!0},url:{enumerable:!0},status:{enumerable:!0},ok:{enumerable:!0},redirected:{enumerable:!0},statusText:{enumerable:!0},headers:{enumerable:!0},clone:{enumerable:!0}});var ll=H(require("url")),ul=H(require("util")),cl=r=>{if(r.search)return r.search;let o=r.href.length-1,a=r.hash||(r.href[o]==="#"?"#":"");return r.href[o-a.length]==="?"?"?":""},fl=H(require("net"));function ha(r,o=!1){return r==null||(r=new URL(r),/^(about|blob|data):$/.test(r.protocol))?"no-referrer":(r.username="",r.password="",r.hash="",o&&(r.pathname="",r.search=""),r)}var Ca=new Set(["","no-referrer","no-referrer-when-downgrade","same-origin","origin","strict-origin","origin-when-cross-origin","strict-origin-when-cross-origin","unsafe-url"]),dl="strict-origin-when-cross-origin";function hl(r){if(!Ca.has(r))throw new TypeError(`Invalid referrerPolicy: ${r}`);return r}function ml(r){if(/^(http|ws)s:$/.test(r.protocol))return!0;let o=r.host.replace(/(^\[)|(]$)/g,""),a=(0,fl.isIP)(o);return a===4&&/^127\./.test(o)||a===6&&/^(((0+:){7})|(::(0+:){0,6}))0*1$/.test(o)?!0:r.host==="localhost"||r.host.endsWith(".localhost")?!1:r.protocol==="file:"}function tt(r){return/^about:(blank|srcdoc)$/.test(r)||r.protocol==="data:"||/^(blob|filesystem):$/.test(r.protocol)?!0:ml(r)}function pl(r,{referrerURLCallback:o,referrerOriginCallback:a}={}){if(r.referrer==="no-referrer"||r.referrerPolicy==="")return null;let i=r.referrerPolicy;if(r.referrer==="about:client")return"no-referrer";let l=r.referrer,c=ha(l),m=ha(l,!0);c.toString().length>4096&&(c=m),o&&(c=o(c)),a&&(m=a(m));let d=new URL(r.url);switch(i){case"no-referrer":return"no-referrer";case"origin":return m;case"unsafe-url":return c;case"strict-origin":return tt(c)&&!tt(d)?"no-referrer":m.toString();case"strict-origin-when-cross-origin":return c.origin===d.origin?c:tt(c)&&!tt(d)?"no-referrer":m;case"same-origin":return c.origin===d.origin?c:"no-referrer";case"origin-when-cross-origin":return c.origin===d.origin?c:m;case"no-referrer-when-downgrade":return tt(c)&&!tt(d)?"no-referrer":c;default:throw new TypeError(`Invalid referrerPolicy: ${i}`)}}function bl(r){let o=(r.get("referrer-policy")||"").split(/[,\s]+/),a="";for(let i of o)i&&Ca.has(i)&&(a=i);return a}var M=Symbol("Request internals"),Tt=r=>typeof r=="object"&&typeof r[M]=="object",yl=(0,ul.deprecate)(()=>{},".data is not a valid RequestInit property, use .body instead","https://github.com/node-fetch/node-fetch/issues/1000 (request)"),Ct=class extends Et{constructor(r,o={}){let a;if(Tt(r)?a=new URL(r.url):(a=new URL(r),r={}),a.username!==""||a.password!=="")throw new TypeError(`${a} is an url with embedded credentials.`);let i=o.method||r.method||"GET";if(/^(delete|get|head|options|post|put)$/i.test(i)&&(i=i.toUpperCase()),!Tt(o)&&"data"in o&&yl(),(o.body!=null||Tt(r)&&r.body!==null)&&(i==="GET"||i==="HEAD"))throw new TypeError("Request with GET/HEAD method cannot have body");let l=o.body?o.body:Tt(r)&&r.body!==null?wn(r):null;super(l,{size:o.size||r.size||0});let c=new Se(o.headers||r.headers||{});if(l!==null&&!c.has("Content-Type")){let y=Ea(l,this);y&&c.set("Content-Type",y)}let m=Tt(r)?r.signal:null;if("signal"in o&&(m=o.signal),m!=null&&!Zs(m))throw new TypeError("Expected signal to be an instanceof AbortSignal or EventTarget");let d=o.referrer==null?r.referrer:o.referrer;if(d==="")d="no-referrer";else if(d){let y=new URL(d);d=/^about:(\/\/)?client$/.test(y)?"client":y}else d=void 0;this[M]={method:i,redirect:o.redirect||r.redirect||"follow",headers:c,parsedURL:a,signal:m,referrer:d},this.follow=o.follow===void 0?r.follow===void 0?20:r.follow:o.follow,this.compress=o.compress===void 0?r.compress===void 0?!0:r.compress:o.compress,this.counter=o.counter||r.counter||0,this.agent=o.agent||r.agent,this.highWaterMark=o.highWaterMark||r.highWaterMark||16384,this.insecureHTTPParser=o.insecureHTTPParser||r.insecureHTTPParser||!1,this.referrerPolicy=o.referrerPolicy||r.referrerPolicy||""}get method(){return this[M].method}get url(){return(0,ll.format)(this[M].parsedURL)}get headers(){return this[M].headers}get redirect(){return this[M].redirect}get signal(){return this[M].signal}get referrer(){if(this[M].referrer==="no-referrer")return"";if(this[M].referrer==="client")return"about:client";if(this[M].referrer)return this[M].referrer.toString()}get referrerPolicy(){return this[M].referrerPolicy}set referrerPolicy(r){this[M].referrerPolicy=hl(r)}clone(){return new Ct(this)}get[Symbol.toStringTag](){return"Request"}};Object.defineProperties(Ct.prototype,{method:{enumerable:!0},url:{enumerable:!0},headers:{enumerable:!0},redirect:{enumerable:!0},clone:{enumerable:!0},signal:{enumerable:!0},referrer:{enumerable:!0},referrerPolicy:{enumerable:!0}});var gl=r=>{let{parsedURL:o}=r[M],a=new Se(r[M].headers);a.has("Accept")||a.set("Accept","*/*");let i=null;if(r.body===null&&/^(post|put)$/i.test(r.method)&&(i="0"),r.body!==null){let d=ol(r);typeof d=="number"&&!Number.isNaN(d)&&(i=String(d))}i&&a.set("Content-Length",i),r.referrerPolicy===""&&(r.referrerPolicy=dl),r.referrer&&r.referrer!=="no-referrer"?r[M].referrer=pl(r):r[M].referrer="no-referrer",r[M].referrer instanceof URL&&a.set("Referer",r.referrer),a.has("User-Agent")||a.set("User-Agent","node-fetch"),r.compress&&!a.has("Accept-Encoding")&&a.set("Accept-Encoding","gzip, deflate, br");let{agent:l}=r;typeof l=="function"&&(l=l(o));let c=cl(o),m={path:o.pathname+c,method:r.method,headers:a[Symbol.for("nodejs.util.inspect.custom")](),insecureHTTPParser:r.insecureHTTPParser,agent:l};return{parsedURL:o,options:m}},Aa=class extends Rr{constructor(r,o="aborted"){super(r,o)}};vr();wa();var _l=new Set(["data:","http:","https:"]);async function Pa(r,o){return new Promise((a,i)=>{let l=new Ct(r,o),{parsedURL:c,options:m}=gl(l);if(!_l.has(c.protocol))throw new TypeError(`node-fetch cannot load ${r}. URL scheme "${c.protocol.replace(/:$/,"")}" is not supported.`);if(c.protocol==="data:"){let p=Xs(l.url),b=new X(p,{headers:{"Content-Type":p.typeFull}});a(b);return}let d=(c.protocol==="https:"?Js.default:Qs.default).request,{signal:y}=l,g=null,q=()=>{let p=new Aa("The operation was aborted.");i(p),l.body&&l.body instanceof ce.default.Readable&&l.body.destroy(p),!(!g||!g.body)&&g.body.emit("error",p)};if(y&&y.aborted){q();return}let I=()=>{q(),S()},O=d(c.toString(),m);y&&y.addEventListener("abort",I);let S=()=>{O.abort(),y&&y.removeEventListener("abort",I)};O.on("error",p=>{i(new le(`request to ${l.url} failed, reason: ${p.message}`,"system",p)),S()}),Sl(O,p=>{g&&g.body&&g.body.destroy(p)}),process.version<"v14"&&O.on("socket",p=>{let b;p.prependListener("end",()=>{b=p._eventsCount}),p.prependListener("close",C=>{if(g&&b<p._eventsCount&&!C){let v=new Error("Premature close");v.code="ERR_STREAM_PREMATURE_CLOSE",g.body.emit("error",v)}})}),O.on("response",p=>{O.setTimeout(0);let b=il(p.rawHeaders);if(vn(p.statusCode)){let D=b.get("Location"),L=null;try{L=D===null?null:new URL(D,l.url)}catch{if(l.redirect!=="manual"){i(new le(`uri requested responds with an invalid redirect URL: ${D}`,"invalid-redirect")),S();return}}switch(l.redirect){case"error":i(new le(`uri requested responds with a redirect, redirect mode is set to error: ${l.url}`,"no-redirect")),S();return;case"manual":break;case"follow":{if(L===null)break;if(l.counter>=l.follow){i(new le(`maximum redirect reached at: ${l.url}`,"max-redirect")),S();return}let z={headers:new Se(l.headers),follow:l.follow,counter:l.counter+1,agent:l.agent,compress:l.compress,method:l.method,body:wn(l),signal:l.signal,size:l.size,referrer:l.referrer,referrerPolicy:l.referrerPolicy};if(!el(l.url,L)||!tl(l.url,L))for(let he of["authorization","www-authenticate","cookie","cookie2"])z.headers.delete(he);if(p.statusCode!==303&&l.body&&o.body instanceof ce.default.Readable){i(new le("Cannot follow redirect with body being a readable stream","unsupported-redirect")),S();return}(p.statusCode===303||(p.statusCode===301||p.statusCode===302)&&l.method==="POST")&&(z.method="GET",z.body=void 0,z.headers.delete("content-length"));let Oe=bl(b);Oe&&(z.referrerPolicy=Oe),a(Pa(new Ct(L,z))),S();return}default:return i(new TypeError(`Redirect option '${l.redirect}' is not a valid value of RequestRedirect`))}}y&&p.once("end",()=>{y.removeEventListener("abort",I)});let C=(0,ce.pipeline)(p,new ce.PassThrough,D=>{D&&i(D)});process.version<"v12.10"&&p.on("aborted",I);let v={url:l.url,status:p.statusCode,statusText:p.statusMessage,headers:b,size:l.size,counter:l.counter,highWaterMark:l.highWaterMark},U=b.get("Content-Encoding");if(!l.compress||l.method==="HEAD"||U===null||p.statusCode===204||p.statusCode===304){g=new X(C,v),a(g);return}let N={flush:et.default.Z_SYNC_FLUSH,finishFlush:et.default.Z_SYNC_FLUSH};if(U==="gzip"||U==="x-gzip"){C=(0,ce.pipeline)(C,et.default.createGunzip(N),D=>{D&&i(D)}),g=new X(C,v),a(g);return}if(U==="deflate"||U==="x-deflate"){let D=(0,ce.pipeline)(p,new ce.PassThrough,L=>{L&&i(L)});D.once("data",L=>{(L[0]&15)===8?C=(0,ce.pipeline)(C,et.default.createInflate(),z=>{z&&i(z)}):C=(0,ce.pipeline)(C,et.default.createInflateRaw(),z=>{z&&i(z)}),g=new X(C,v),a(g)}),D.once("end",()=>{g||(g=new X(C,v),a(g))});return}if(U==="br"){C=(0,ce.pipeline)(C,et.default.createBrotliDecompress(),D=>{D&&i(D)}),g=new X(C,v),a(g);return}g=new X(C,v),a(g)}),al(O,l).catch(i)})}function Sl(r,o){let a=pr.Buffer.from(`0\r
\r
`),i=!1,l=!1,c;r.on("response",m=>{let{headers:d}=m;i=d["transfer-encoding"]==="chunked"&&!d["content-length"]}),r.on("socket",m=>{let d=()=>{if(i&&!l){let g=new Error("Premature close");g.code="ERR_STREAM_PREMATURE_CLOSE",o(g)}},y=g=>{l=pr.Buffer.compare(g.slice(-5),a)===0,!l&&c&&(l=pr.Buffer.compare(c.slice(-3),a.slice(0,3))===0&&pr.Buffer.compare(g.slice(-2),a.slice(3))===0),c=g};m.prependListener("close",d),m.on("data",y),r.on("close",()=>{m.removeListener("close",d),m.removeListener("data",y)})})}Pt();vr();});var Da=Ke((mu,qa)=>{var El=require("node:tty"),Cl=El?.WriteStream?.prototype?.hasColors?.()??!1,T=(r,o)=>{if(!Cl)return l=>l;let a=`\x1B[${r}m`,i=`\x1B[${o}m`;return l=>{let c=l+"",m=c.indexOf(i);if(m===-1)return a+c+i;let d=a,y=0;for(;m!==-1;)d+=c.slice(y,m)+a,y=m+i.length,m=c.indexOf(i,y);return d+=c.slice(y)+i,d}},R={};R.reset=T(0,0);R.bold=T(1,22);R.dim=T(2,22);R.italic=T(3,23);R.underline=T(4,24);R.overline=T(53,55);R.inverse=T(7,27);R.hidden=T(8,28);R.strikethrough=T(9,29);R.black=T(30,39);R.red=T(31,39);R.green=T(32,39);R.yellow=T(33,39);R.blue=T(34,39);R.magenta=T(35,39);R.cyan=T(36,39);R.white=T(37,39);R.gray=T(90,39);R.bgBlack=T(40,49);R.bgRed=T(41,49);R.bgGreen=T(42,49);R.bgYellow=T(43,49);R.bgBlue=T(44,49);R.bgMagenta=T(45,49);R.bgCyan=T(46,49);R.bgWhite=T(47,49);R.bgGray=T(100,49);R.redBright=T(91,39);R.greenBright=T(92,39);R.yellowBright=T(93,39);R.blueBright=T(94,39);R.magentaBright=T(95,39);R.cyanBright=T(96,39);R.whiteBright=T(97,39);R.bgRedBright=T(101,49);R.bgGreenBright=T(102,49);R.bgYellowBright=T(103,49);R.bgBlueBright=T(104,49);R.bgMagentaBright=T(105,49);R.bgCyanBright=T(106,49);R.bgWhiteBright=T(107,49);qa.exports=R});var Pn=Ke((yu,Fa)=>{"use strict";var Wa=require("fs"),An;function Pl(){try{return Wa.statSync("/.dockerenv"),!0}catch{return!1}}function kl(){try{return Wa.readFileSync("/proc/self/cgroup","utf8").includes("docker")}catch{return!1}}Fa.exports=()=>(An===void 0&&(An=Pl()||kl()),An)});var $a=Ke((gu,kn)=>{"use strict";var Bl=require("os"),Ol=require("fs"),Ia=Pn(),La=()=>{if(process.platform!=="linux")return!1;if(Bl.release().toLowerCase().includes("microsoft"))return!Ia();try{return Ol.readFileSync("/proc/version","utf8").toLowerCase().includes("microsoft")?!Ia():!1}catch{return!1}};process.env.__IS_WSL_TEST__?kn.exports=La:kn.exports=La()});var xa=Ke((_u,za)=>{"use strict";za.exports=(r,o,a)=>{let i=l=>Object.defineProperty(r,o,{value:l,enumerable:!0,writable:!0});return Object.defineProperty(r,o,{configurable:!0,enumerable:!0,get(){let l=a();return i(l),l},set(l){i(l)}}),r}});var Ya=Ke((Su,Va)=>{var ql=require("path"),Dl=require("child_process"),{promises:Er,constants:Ha}=require("fs"),Tr=$a(),Wl=Pn(),On=xa(),ja=ql.join(__dirname,"xdg-open"),{platform:at,arch:Ma}=process,Fl=()=>{try{return Er.statSync("/run/.containerenv"),!0}catch{return!1}},Bn;function Il(){return Bn===void 0&&(Bn=Fl()||Wl()),Bn}var Ll=(()=>{let r="/mnt/",o;return async function(){if(o)return o;let a="/etc/wsl.conf",i=!1;try{await Er.access(a,Ha.F_OK),i=!0}catch{}if(!i)return r;let l=await Er.readFile(a,{encoding:"utf8"}),c=/(?<!#.*)root\s*=\s*(?<mountPoint>.*)/g.exec(l);return c?(o=c.groups.mountPoint.trim(),o=o.endsWith("/")?o:`${o}/`,o):r}})(),Ua=async(r,o)=>{let a;for(let i of r)try{return await o(i)}catch(l){a=l}throw a},Cr=async r=>{if(r={wait:!1,background:!1,newInstance:!1,allowNonzeroExitCode:!1,...r},Array.isArray(r.app))return Ua(r.app,d=>Cr({...r,app:d}));let{name:o,arguments:a=[]}=r.app||{};if(a=[...a],Array.isArray(o))return Ua(o,d=>Cr({...r,app:{name:d,arguments:a}}));let i,l=[],c={};if(at==="darwin")i="open",r.wait&&l.push("--wait-apps"),r.background&&l.push("--background"),r.newInstance&&l.push("--new"),o&&l.push("-a",o);else if(at==="win32"||Tr&&!Il()&&!o){let d=await Ll();i=Tr?`${d}c/Windows/System32/WindowsPowerShell/v1.0/powershell.exe`:`${process.env.SYSTEMROOT}\\System32\\WindowsPowerShell\\v1.0\\powershell`,l.push("-NoProfile","-NonInteractive","\u2013ExecutionPolicy","Bypass","-EncodedCommand"),Tr||(c.windowsVerbatimArguments=!0);let y=["Start"];r.wait&&y.push("-Wait"),o?(y.push(`"\`"${o}\`""`,"-ArgumentList"),r.target&&a.unshift(r.target)):r.target&&y.push(`"${r.target}"`),a.length>0&&(a=a.map(g=>`"\`"${g}\`""`),y.push(a.join(","))),r.target=Buffer.from(y.join(" "),"utf16le").toString("base64")}else{if(o)i=o;else{let d=!__dirname||__dirname==="/",y=!1;try{await Er.access(ja,Ha.X_OK),y=!0}catch{}i=process.versions.electron||at==="android"||d||!y?"xdg-open":ja}a.length>0&&l.push(...a),r.wait||(c.stdio="ignore",c.detached=!0)}r.target&&l.push(r.target),at==="darwin"&&a.length>0&&l.push("--args",...a);let m=Dl.spawn(i,l,c);return r.wait?new Promise((d,y)=>{m.once("error",y),m.once("close",g=>{if(!r.allowNonzeroExitCode&&g>0){y(new Error(`Exited with code ${g}`));return}d(m)})}):(m.unref(),m)},qn=(r,o)=>{if(typeof r!="string")throw new TypeError("Expected a `target`");return Cr({...o,target:r})},$l=(r,o)=>{if(typeof r!="string")throw new TypeError("Expected a `name`");let{arguments:a=[]}=o||{};if(a!=null&&!Array.isArray(a))throw new TypeError("Expected `appArguments` as Array type");return Cr({...o,app:{name:r,arguments:a}})};function Na(r){if(typeof r=="string"||Array.isArray(r))return r;let{[Ma]:o}=r;if(!o)throw new Error(`${Ma} is not supported`);return o}function Dn({[at]:r},{wsl:o}){if(o&&Tr)return Na(o);if(!r)throw new Error(`${at} is not supported`);return Na(r)}var Ar={};On(Ar,"chrome",()=>Dn({darwin:"google chrome",win32:"chrome",linux:["google-chrome","google-chrome-stable","chromium"]},{wsl:{ia32:"/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe",x64:["/mnt/c/Program Files/Google/Chrome/Application/chrome.exe","/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe"]}}));On(Ar,"firefox",()=>Dn({darwin:"firefox",win32:"C:\\Program Files\\Mozilla Firefox\\firefox.exe",linux:"firefox"},{wsl:"/mnt/c/Program Files/Mozilla Firefox/firefox.exe"}));On(Ar,"edge",()=>Dn({darwin:"microsoft edge",win32:"msedge",linux:["microsoft-edge","microsoft-edge-dev"]},{wsl:"/mnt/c/Program Files (x86)/Microsoft/Edge/Application/msedge.exe"}));qn.apps=Ar;qn.openApp=$l;Va.exports=qn});var Ql={};Ds(Ql,{authorize:()=>jl,checkAuthorization:()=>xl,createVersion:()=>Yl,getCheckoutKey:()=>Gl,getOrganisation:()=>Hl,getOrganisations:()=>Nl,getProfile:()=>ti,getUser:()=>Ul,uploadFile:()=>ri,uploadMetadata:()=>Vl});module.exports=Ws(Ql);var Xa=K(require("http")),Za=K(require("url")),Pr=K(require("querystring")),Dt=K(require("crypto")),ei=K(require("path")),qt=K(require("fs")),Fn=K(Rn()),$e=require("@oclif/core");var Ot=K(require("node:fs")),En=K(require("node:path")),Ba=K(require("node:os"));var Bt={production:{url:"https://www.raycast.com",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},staging:{url:"https://www.rayca.st",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},development:{url:"http://localhost:3000",clientID:"rOCD_LhnXFObSLu9v7788qOGu25FZDqEzGjet4Q45ZY"}}[process.env.API_ENV||"production"],wl="e69bae0ec90f5e838555",j={},vl;function $(r){switch(r){case"raycastApiURL":return process.env.RAY_APIURL||j.APIURL||Bt.url;case"raycastAccessToken":return process.env.RAY_TOKEN||j.Token||j.AccessToken||"";case"raycastClientId":return process.env.RAY_CLIENT_ID||j.ClientID||Bt.clientID;case"githubClientId":return process.env.RAY_GithubClientID||j.GithubClientID||wl;case"githubAccessToken":return process.env.GITHUB_ACCESS_TOKEN||j.GithubAccessToken||"";case"flavorName":return process.env.RAY_Target?process.env.RAY_Target:typeof j.Target<"u"?j.Target:Tn(process.platform==="win32"?"x":"release")}}function Oa(r,o){switch(r){case"raycastApiURL":o===void 0?delete j.APIURL:j.APIURL=o;break;case"raycastAccessToken":o===void 0?delete j.Token:j.Token=o,delete j.AccessToken;break;case"raycastClientId":o===void 0?delete j.ClientID:j.ClientID=o;break;case"githubAccessToken":o===void 0?delete j.GithubAccessToken:j.GithubAccessToken=o;break;case"flavorName":o===void 0?delete j.Target:j.Target=o;break}let a=Tl();Ot.writeFileSync(En.join(a,"config.json"),JSON.stringify(j,null,"  "),"utf8")}function Tn(r){switch(r){case"debug":return"debug";case"internal":return"internal";case"release":return"";case"x":return"x";case"x-internal":return"x-internal";case"x-development":return"x-development";case void 0:return $("flavorName")}}function Rl(){let r=Tn(vl);return r==""?"raycast":`raycast-${r}`}function Tl(){let r=En.join(Ba.default.homedir(),".config",Rl());return Ot.mkdirSync(r,{recursive:!0}),r}var V=K(Da());var pu=(0,V.blue)((0,V.dim)("internal only"));function Cn(r,o,a){console.log(Al[r]+o),typeof a?.exit<"u"&&process.exit(a.exit)}var Al={wait:`\u{1F550}${(0,V.blue)("wait")}  - `,info:`${process.env.GITHUB_ACTIONS==="true"?"\u2139\uFE0F":"\u2139\uFE0F "}${(0,V.cyan)("info")}  - `,success:`\u2705${(0,V.green)("ready")}  - `,warn:`${process.env.GITHUB_ACTIONS==="true"?"\u26A0\uFE0F":"\u26A0\uFE0F "}${(0,V.yellow)("warn")}  - `,error:`\u{1F4A5}${(0,V.red)("error")}  - `,event:`\u26A1\uFE0F${(0,V.magenta)("event")}  - `,paymentPrompt:`\u{1F4B0}${(0,V.yellowBright)("plan")}  - `};var Ga=require("@oclif/core"),Qa=K(Rn()),Ja=K(Ya());async function Be(r,o){let a;try{a=await(0,Qa.default)(r,{method:o.method||"GET",headers:{"Content-Type":"application/json",Accept:"application/json",...o.token?{Authorization:`Bearer ${o.token}`}:void 0},body:o.body})}catch(i){throw new Error(`HTTP request: ${i.message}`)}if(!a.ok){switch(a.status){case 401:throw new de(a,"not authorized - please log in first using `npx ray login`");case 403:throw new de(a,"forbidden - you don't have permissions to perform the request");case 402:throw new de(a,"the limit of free commands has been reached")}let i=await a.text(),l;try{l=JSON.parse(i)}catch{throw new de(a,`HTTP error: ${a.status} - ${i}`)}throw Array.isArray(l.errors)&&l.errors.length>0?new de(a,`error: ${l.errors[0].status} - ${l.errors[0].title}`):new de(a,`HTTP error: ${a.status} - ${i}`)}return await a.json()}var de=class extends Error{constructor(o,a){let i=o.headers.get("X-Request-Id");i?super(`${a} (${o.url} RequestID: ${i})`):super(a),this.name="HTTPError"}};function Ka(r){(0,Ja.default)(r).catch(o=>{Ga.ux.error(new Error(`failed opening browser to URL ${r}: ${o.message}`),{exit:1})})}var zl=`${Bt.url}/sessions/success`,Wn=`${Bt.url}/sessions/failure`;function xl(){$("raycastAccessToken")===""&&$e.ux.error("please first log in first using `npx ray login`",{exit:1})}async function jl(){let r=$("raycastClientId"),o=$("raycastApiURL"),a=Dt.randomBytes(32).toString("hex"),l=Dt.createHash("sha256").update(a).digest("base64url").replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,""),c=Xa.createServer(),y=`http://127.0.0.1:${(await new Promise((I,O)=>{let S=c.listen(0,"127.0.0.1",()=>I(S));S.on("error",O)})).address().port}`,g=`${o}/oauth/authorize?response_type=code&client_id=${r}&code_challenge=${l}&code_challenge_method=S256&redirect_uri=${y}`;function q(I){I.close()}c.on("request",async(I,O)=>{try{let p=Pr.parse(Za.parse(I.url).query).code;p||(O.writeHead(302,{Location:Wn}),O.end(),q(c),$e.ux.error(new Error("failed authorizing app"),{exit:1}));let b=await Ml(a,p,y);b||(O.writeHead(302,{Location:Wn}),O.end(),q(c),$e.ux.error(new Error("failed getting access token"),{exit:1}));try{Oa("raycastAccessToken",b)}catch(v){console.log(v),O.writeHead(302,{Location:Wn}),O.end(),q(c),$e.ux.error(new Error("failed saving access token"),{exit:1})}O.writeHead(302,{Location:zl}),O.end();let C=await ti();C||$e.ux.error(new Error("failed fetching profile"),{exit:1}),Cn("success",`logged in as '${C.username}'`),q(c)}catch(S){q(c),$e.ux.error(S,{exit:1})}}),Cn("info","continue login in your browser"),Ka(g),await new Promise(I=>c.on("close",I))}async function Ml(r,o,a){let i=$("raycastClientId"),c=`${$("raycastApiURL")}/oauth/token`,m=Pr.stringify({grant_type:"authorization_code",client_id:i,code_verifier:r,code:o,redirect_uri:a});try{let d=await(0,Fn.default)(c,{method:"POST",body:m,headers:{"Content-Type":"application/x-www-form-urlencoded"}});if(!d.ok)throw new Error(d.statusText);return(await d.json()).access_token}catch(d){throw new Error(`failed getting access token: ${d.message}`)}}function ti(){let r=$("raycastApiURL"),o=$("raycastAccessToken");return Be(`${r}/api/v1/me`,{token:o})}function Ul(r){let o=$("raycastApiURL"),a=$("raycastAccessToken");return Be(`${o}/api/v1/users/${r}`,{token:a})}function Nl(){let r=$("raycastApiURL"),o=$("raycastAccessToken");return Be(`${r}/api/v1/me/organizations`,{token:o})}function Hl(r){let o=$("raycastApiURL"),a=$("raycastAccessToken");return Be(`${o}/api/v1/organizations/${r}`,{token:a})}function Vl(r){return Promise.all(r.map(async o=>{try{return await ri(o,"image/png")}catch(a){throw new Error(`upload: ${a.message}`)}}))}async function ri(r,o){let a=$("raycastApiURL"),i=$("raycastAccessToken"),l=qt.statSync(r),c=await Be(`${a}/api/v1/direct_uploads`,{token:i,method:"POST",body:JSON.stringify({blob:{content_type:o,filename:ei.basename(r),byte_size:l.size,checksum:Dt.createHash("md5").update(qt.readFileSync(r)).digest("base64")}})}),m=qt.readFileSync(r),d=await(0,Fn.default)(c.direct_upload.url,{method:"PUT",body:m,headers:c.direct_upload.headers});if(!d.ok)throw new de(d,d.statusText);return c.signed_id}function Yl(r){let o=$("raycastApiURL"),a=$("raycastAccessToken");return Be(`${o}/api/v1/versions`,{token:a,method:"POST",body:JSON.stringify(r)})}function Gl(r){let o=$("raycastApiURL"),a=$("raycastAccessToken");return Be(`${o}/api/v1/organizations/%${r}/billing/checkouts`,{token:a,method:"POST"}).then(i=>i.key).catch(()=>{})}0&&(module.exports={authorize,checkAuthorization,createVersion,getCheckoutKey,getOrganisation,getOrganisations,getProfile,getUser,uploadFile,uploadMetadata});
/*! Bundled license information:

node-fetch-cjs/dist/index.js:
  (*! fetch-blob. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
  (*! formdata-polyfill. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
  (*! node-domexception. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
*/
