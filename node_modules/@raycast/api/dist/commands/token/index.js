"use strict";var Ls=Object.create;var xe=Object.defineProperty;var js=Object.getOwnPropertyDescriptor;var qs=Object.getOwnPropertyNames;var Gs=Object.getPrototypeOf,Ds=Object.prototype.hasOwnProperty;var h=(e,r)=>()=>(r||e((r={exports:{}}).exports,r),r.exports),Bs=(e,r)=>{for(var t in r)xe(e,t,{get:r[t],enumerable:!0})},Yr=(e,r,t,n)=>{if(r&&typeof r=="object"||typeof r=="function")for(let s of qs(r))!Ds.call(e,s)&&s!==t&&xe(e,s,{get:()=>r[s],enumerable:!(n=js(r,s))||n.enumerable});return e};var z=(e,r,t)=>(t=e!=null?Ls(Gs(e)):{},Yr(r||!e||!e.__esModule?xe(t,"default",{value:e,enumerable:!0}):t,e)),Fs=e=>Yr(xe({},"__esModule",{value:!0}),e);var sr=h((ea,Jr)=>{"use strict";var Wr=require("fs"),nr;function Us(){try{return Wr.statSync("/.dockerenv"),!0}catch{return!1}}function Hs(){try{return Wr.readFileSync("/proc/self/cgroup","utf8").includes("docker")}catch{return!1}}Jr.exports=()=>(nr===void 0&&(nr=Us()||Hs()),nr)});var or=h((ra,ir)=>{"use strict";var Vs=require("os"),Xs=require("fs"),Kr=sr(),Mr=()=>{if(process.platform!=="linux")return!1;if(Vs.release().toLowerCase().includes("microsoft"))return!Kr();try{return Xs.readFileSync("/proc/version","utf8").toLowerCase().includes("microsoft")?!Kr():!1}catch{return!1}};process.env.__IS_WSL_TEST__?ir.exports=Mr:ir.exports=Mr()});var Qr=h((ta,Zr)=>{"use strict";Zr.exports=function(e){try{return e()}catch{}}});var st=h((na,nt)=>{nt.exports=tt;tt.sync=Ys;var et=require("fs");function zs(e,r){var t=r.pathExt!==void 0?r.pathExt:process.env.PATHEXT;if(!t||(t=t.split(";"),t.indexOf("")!==-1))return!0;for(var n=0;n<t.length;n++){var s=t[n].toLowerCase();if(s&&e.substr(-s.length).toLowerCase()===s)return!0}return!1}function rt(e,r,t){return!e.isSymbolicLink()&&!e.isFile()?!1:zs(r,t)}function tt(e,r,t){et.stat(e,function(n,s){t(n,n?!1:rt(s,e,r))})}function Ys(e,r){return rt(et.statSync(e),e,r)}});var ut=h((sa,ct)=>{ct.exports=ot;ot.sync=Ws;var it=require("fs");function ot(e,r,t){it.stat(e,function(n,s){t(n,n?!1:at(s,r))})}function Ws(e,r){return at(it.statSync(e),r)}function at(e,r){return e.isFile()&&Js(e,r)}function Js(e,r){var t=e.mode,n=e.uid,s=e.gid,i=r.uid!==void 0?r.uid:process.getuid&&process.getuid(),o=r.gid!==void 0?r.gid:process.getgid&&process.getgid(),u=parseInt("100",8),c=parseInt("010",8),f=parseInt("001",8),y=u|c,v=t&f||t&c&&s===o||t&u&&n===i||t&y&&i===0;return v}});var ft=h((oa,lt)=>{var ia=require("fs"),Ee;process.platform==="win32"||global.TESTING_WINDOWS?Ee=st():Ee=ut();lt.exports=ar;ar.sync=Ks;function ar(e,r,t){if(typeof r=="function"&&(t=r,r={}),!t){if(typeof Promise!="function")throw new TypeError("callback not provided");return new Promise(function(n,s){ar(e,r||{},function(i,o){i?s(i):n(o)})})}Ee(e,r||{},function(n,s){n&&(n.code==="EACCES"||r&&r.ignoreErrors)&&(n=null,s=!1),t(n,s)})}function Ks(e,r){try{return Ee.sync(e,r||{})}catch(t){if(r&&r.ignoreErrors||t.code==="EACCES")return!1;throw t}}});var vt=h((aa,yt)=>{yt.exports=mt;mt.sync=Zs;var cr=process.platform==="win32"||process.env.OSTYPE==="cygwin"||process.env.OSTYPE==="msys",pt=require("path"),Ms=cr?";":":",ht=ft();function dt(e){var r=new Error("not found: "+e);return r.code="ENOENT",r}function gt(e,r){var t=r.colon||Ms,n=r.path||process.env.PATH||"",s=[""];n=n.split(t);var i="";return cr&&(n.unshift(process.cwd()),i=r.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM",s=i.split(t),e.indexOf(".")!==-1&&s[0]!==""&&s.unshift("")),(e.match(/\//)||cr&&e.match(/\\/))&&(n=[""]),{env:n,ext:s,extExe:i}}function mt(e,r,t){typeof r=="function"&&(t=r,r={});var n=gt(e,r),s=n.env,i=n.ext,o=n.extExe,u=[];(function c(f,y){if(f===y)return r.all&&u.length?t(null,u):t(dt(e));var v=s[f];v.charAt(0)==='"'&&v.slice(-1)==='"'&&(v=v.slice(1,-1));var x=pt.join(v,e);!v&&/^\.[\\\/]/.test(e)&&(x=e.slice(0,2)+x),function I($,k){if($===k)return c(f+1,y);var A=i[$];ht(x+A,{pathExt:o},function(O,G){if(!O&&G)if(r.all)u.push(x+A);else return t(null,x+A);return I($+1,k)})}(0,i.length)})(0,s.length)}function Zs(e,r){r=r||{};for(var t=gt(e,r),n=t.env,s=t.ext,i=t.extExe,o=[],u=0,c=n.length;u<c;u++){var f=n[u];f.charAt(0)==='"'&&f.slice(-1)==='"'&&(f=f.slice(1,-1));var y=pt.join(f,e);!f&&/^\.[\\\/]/.test(e)&&(y=e.slice(0,2)+y);for(var v=0,x=s.length;v<x;v++){var I=y+s[v],$;try{if($=ht.sync(I,{pathExt:i}),$)if(r.all)o.push(I);else return I}catch{}}}if(r.all&&o.length)return o;if(r.nothrow)return null;throw dt(e)}});var ur=h((ca,wt)=>{"use strict";wt.exports=e=>{e=e||{};let r=e.env||process.env;return(e.platform||process.platform)!=="win32"?"PATH":Object.keys(r).find(n=>n.toUpperCase()==="PATH")||"Path"}});var St=h((ua,bt)=>{"use strict";var xt=require("path"),Qs=vt(),ei=ur()();function Et(e,r){let t=process.cwd(),n=e.options.cwd!=null;if(n)try{process.chdir(e.options.cwd)}catch{}let s;try{s=Qs.sync(e.command,{path:(e.options.env||process.env)[ei],pathExt:r?xt.delimiter:void 0})}catch{}finally{process.chdir(t)}return s&&(s=xt.resolve(n?e.options.cwd:"",s)),s}function ri(e){return Et(e)||Et(e,!0)}bt.exports=ri});var Tt=h((la,fr)=>{"use strict";var lr=/([()\][%!^"`<>&|;, *?])/g;function ti(e){return e=e.replace(lr,"^$1"),e}function ni(e,r){return e=`${e}`,e=e.replace(/(\\*)"/g,'$1$1\\"'),e=e.replace(/(\\*)$/,"$1$1"),e=`"${e}"`,e=e.replace(lr,"^$1"),r&&(e=e.replace(lr,"^$1")),e}fr.exports.command=ti;fr.exports.argument=ni});var It=h((fa,At)=>{"use strict";At.exports=/^#!.*/});var Ct=h((pa,Ot)=>{"use strict";var si=It();Ot.exports=function(e){var r=e.match(si);if(!r)return null;var t=r[0].replace(/#! ?/,"").split(" "),n=t[0].split("/").pop(),s=t[1];return n==="env"?s:n+(s?" "+s:"")}});var _t=h((ha,Rt)=>{"use strict";var pr=require("fs"),ii=Ct();function oi(e){let t;Buffer.alloc?t=Buffer.alloc(150):(t=new Buffer(150),t.fill(0));let n;try{n=pr.openSync(e,"r"),pr.readSync(n,t,0,150,0),pr.closeSync(n)}catch{}return ii(t.toString())}Rt.exports=oi});var Yt=h((l,zt)=>{l=zt.exports=m;var w;typeof process=="object"&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?w=function(){var e=Array.prototype.slice.call(arguments,0);e.unshift("SEMVER"),console.log.apply(console,e)}:w=function(){};l.SEMVER_SPEC_VERSION="2.0.0";var ie=256,be=Number.MAX_SAFE_INTEGER||9007199254740991,hr=16,ai=ie-6,oe=l.re=[],E=l.safeRe=[],a=l.src=[],g=0,vr="[a-zA-Z0-9-]",dr=[["\\s",1],["\\d",ie],[vr,ai]];function Ce(e){for(var r=0;r<dr.length;r++){var t=dr[r][0],n=dr[r][1];e=e.split(t+"*").join(t+"{0,"+n+"}").split(t+"+").join(t+"{1,"+n+"}")}return e}var Y=g++;a[Y]="0|[1-9]\\d*";var W=g++;a[W]="\\d+";var wr=g++;a[wr]="\\d*[a-zA-Z-]"+vr+"*";var $t=g++;a[$t]="("+a[Y]+")\\.("+a[Y]+")\\.("+a[Y]+")";var kt=g++;a[kt]="("+a[W]+")\\.("+a[W]+")\\.("+a[W]+")";var gr=g++;a[gr]="(?:"+a[Y]+"|"+a[wr]+")";var mr=g++;a[mr]="(?:"+a[W]+"|"+a[wr]+")";var xr=g++;a[xr]="(?:-("+a[gr]+"(?:\\."+a[gr]+")*))";var Er=g++;a[Er]="(?:-?("+a[mr]+"(?:\\."+a[mr]+")*))";var yr=g++;a[yr]=vr+"+";var ce=g++;a[ce]="(?:\\+("+a[yr]+"(?:\\."+a[yr]+")*))";var br=g++,Nt="v?"+a[$t]+a[xr]+"?"+a[ce]+"?";a[br]="^"+Nt+"$";var Sr="[v=\\s]*"+a[kt]+a[Er]+"?"+a[ce]+"?",Tr=g++;a[Tr]="^"+Sr+"$";var Q=g++;a[Q]="((?:<|>)?=?)";var Se=g++;a[Se]=a[W]+"|x|X|\\*";var Te=g++;a[Te]=a[Y]+"|x|X|\\*";var F=g++;a[F]="[v=\\s]*("+a[Te]+")(?:\\.("+a[Te]+")(?:\\.("+a[Te]+")(?:"+a[xr]+")?"+a[ce]+"?)?)?";var K=g++;a[K]="[v=\\s]*("+a[Se]+")(?:\\.("+a[Se]+")(?:\\.("+a[Se]+")(?:"+a[Er]+")?"+a[ce]+"?)?)?";var Lt=g++;a[Lt]="^"+a[Q]+"\\s*"+a[F]+"$";var jt=g++;a[jt]="^"+a[Q]+"\\s*"+a[K]+"$";var qt=g++;a[qt]="(?:^|[^\\d])(\\d{1,"+hr+"})(?:\\.(\\d{1,"+hr+"}))?(?:\\.(\\d{1,"+hr+"}))?(?:$|[^\\d])";var Re=g++;a[Re]="(?:~>?)";var M=g++;a[M]="(\\s*)"+a[Re]+"\\s+";oe[M]=new RegExp(a[M],"g");E[M]=new RegExp(Ce(a[M]),"g");var ci="$1~",Gt=g++;a[Gt]="^"+a[Re]+a[F]+"$";var Dt=g++;a[Dt]="^"+a[Re]+a[K]+"$";var _e=g++;a[_e]="(?:\\^)";var Z=g++;a[Z]="(\\s*)"+a[_e]+"\\s+";oe[Z]=new RegExp(a[Z],"g");E[Z]=new RegExp(Ce(a[Z]),"g");var ui="$1^",Bt=g++;a[Bt]="^"+a[_e]+a[F]+"$";var Ft=g++;a[Ft]="^"+a[_e]+a[K]+"$";var Ar=g++;a[Ar]="^"+a[Q]+"\\s*("+Sr+")$|^$";var Ir=g++;a[Ir]="^"+a[Q]+"\\s*("+Nt+")$|^$";var U=g++;a[U]="(\\s*)"+a[Q]+"\\s*("+Sr+"|"+a[F]+")";oe[U]=new RegExp(a[U],"g");E[U]=new RegExp(Ce(a[U]),"g");var li="$1$2$3",Ut=g++;a[Ut]="^\\s*("+a[F]+")\\s+-\\s+("+a[F]+")\\s*$";var Ht=g++;a[Ht]="^\\s*("+a[K]+")\\s+-\\s+("+a[K]+")\\s*$";var Vt=g++;a[Vt]="(<|>)?=?\\s*\\*";for(N=0;N<g;N++)w(N,a[N]),oe[N]||(oe[N]=new RegExp(a[N]),E[N]=new RegExp(Ce(a[N])));var N;l.parse=H;function H(e,r){if((!r||typeof r!="object")&&(r={loose:!!r,includePrerelease:!1}),e instanceof m)return e;if(typeof e!="string"||e.length>ie)return null;var t=r.loose?E[Tr]:E[br];if(!t.test(e))return null;try{return new m(e,r)}catch{return null}}l.valid=fi;function fi(e,r){var t=H(e,r);return t?t.version:null}l.clean=pi;function pi(e,r){var t=H(e.trim().replace(/^[=v]+/,""),r);return t?t.version:null}l.SemVer=m;function m(e,r){if((!r||typeof r!="object")&&(r={loose:!!r,includePrerelease:!1}),e instanceof m){if(e.loose===r.loose)return e;e=e.version}else if(typeof e!="string")throw new TypeError("Invalid Version: "+e);if(e.length>ie)throw new TypeError("version is longer than "+ie+" characters");if(!(this instanceof m))return new m(e,r);w("SemVer",e,r),this.options=r,this.loose=!!r.loose;var t=e.trim().match(r.loose?E[Tr]:E[br]);if(!t)throw new TypeError("Invalid Version: "+e);if(this.raw=e,this.major=+t[1],this.minor=+t[2],this.patch=+t[3],this.major>be||this.major<0)throw new TypeError("Invalid major version");if(this.minor>be||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>be||this.patch<0)throw new TypeError("Invalid patch version");t[4]?this.prerelease=t[4].split(".").map(function(n){if(/^[0-9]+$/.test(n)){var s=+n;if(s>=0&&s<be)return s}return n}):this.prerelease=[],this.build=t[5]?t[5].split("."):[],this.format()}m.prototype.format=function(){return this.version=this.major+"."+this.minor+"."+this.patch,this.prerelease.length&&(this.version+="-"+this.prerelease.join(".")),this.version};m.prototype.toString=function(){return this.version};m.prototype.compare=function(e){return w("SemVer.compare",this.version,this.options,e),e instanceof m||(e=new m(e,this.options)),this.compareMain(e)||this.comparePre(e)};m.prototype.compareMain=function(e){return e instanceof m||(e=new m(e,this.options)),J(this.major,e.major)||J(this.minor,e.minor)||J(this.patch,e.patch)};m.prototype.comparePre=function(e){if(e instanceof m||(e=new m(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;var r=0;do{var t=this.prerelease[r],n=e.prerelease[r];if(w("prerelease compare",r,t,n),t===void 0&&n===void 0)return 0;if(n===void 0)return 1;if(t===void 0)return-1;if(t===n)continue;return J(t,n)}while(++r)};m.prototype.inc=function(e,r){switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",r);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",r);break;case"prepatch":this.prerelease.length=0,this.inc("patch",r),this.inc("pre",r);break;case"prerelease":this.prerelease.length===0&&this.inc("patch",r),this.inc("pre",r);break;case"major":(this.minor!==0||this.patch!==0||this.prerelease.length===0)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(this.patch!==0||this.prerelease.length===0)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":this.prerelease.length===0&&this.patch++,this.prerelease=[];break;case"pre":if(this.prerelease.length===0)this.prerelease=[0];else{for(var t=this.prerelease.length;--t>=0;)typeof this.prerelease[t]=="number"&&(this.prerelease[t]++,t=-2);t===-1&&this.prerelease.push(0)}r&&(this.prerelease[0]===r?isNaN(this.prerelease[1])&&(this.prerelease=[r,0]):this.prerelease=[r,0]);break;default:throw new Error("invalid increment argument: "+e)}return this.format(),this.raw=this.version,this};l.inc=hi;function hi(e,r,t,n){typeof t=="string"&&(n=t,t=void 0);try{return new m(e,t).inc(r,n).version}catch{return null}}l.diff=di;function di(e,r){if(Or(e,r))return null;var t=H(e),n=H(r),s="";if(t.prerelease.length||n.prerelease.length){s="pre";var i="prerelease"}for(var o in t)if((o==="major"||o==="minor"||o==="patch")&&t[o]!==n[o])return s+o;return i}l.compareIdentifiers=J;var Pt=/^[0-9]+$/;function J(e,r){var t=Pt.test(e),n=Pt.test(r);return t&&n&&(e=+e,r=+r),e===r?0:t&&!n?-1:n&&!t?1:e<r?-1:1}l.rcompareIdentifiers=gi;function gi(e,r){return J(r,e)}l.major=mi;function mi(e,r){return new m(e,r).major}l.minor=yi;function yi(e,r){return new m(e,r).minor}l.patch=vi;function vi(e,r){return new m(e,r).patch}l.compare=L;function L(e,r,t){return new m(e,t).compare(new m(r,t))}l.compareLoose=wi;function wi(e,r){return L(e,r,!0)}l.rcompare=xi;function xi(e,r,t){return L(r,e,t)}l.sort=Ei;function Ei(e,r){return e.sort(function(t,n){return l.compare(t,n,r)})}l.rsort=bi;function bi(e,r){return e.sort(function(t,n){return l.rcompare(t,n,r)})}l.gt=ae;function ae(e,r,t){return L(e,r,t)>0}l.lt=Ae;function Ae(e,r,t){return L(e,r,t)<0}l.eq=Or;function Or(e,r,t){return L(e,r,t)===0}l.neq=Xt;function Xt(e,r,t){return L(e,r,t)!==0}l.gte=Cr;function Cr(e,r,t){return L(e,r,t)>=0}l.lte=Rr;function Rr(e,r,t){return L(e,r,t)<=0}l.cmp=Ie;function Ie(e,r,t,n){switch(r){case"===":return typeof e=="object"&&(e=e.version),typeof t=="object"&&(t=t.version),e===t;case"!==":return typeof e=="object"&&(e=e.version),typeof t=="object"&&(t=t.version),e!==t;case"":case"=":case"==":return Or(e,t,n);case"!=":return Xt(e,t,n);case">":return ae(e,t,n);case">=":return Cr(e,t,n);case"<":return Ae(e,t,n);case"<=":return Rr(e,t,n);default:throw new TypeError("Invalid operator: "+r)}}l.Comparator=_;function _(e,r){if((!r||typeof r!="object")&&(r={loose:!!r,includePrerelease:!1}),e instanceof _){if(e.loose===!!r.loose)return e;e=e.value}if(!(this instanceof _))return new _(e,r);e=e.trim().split(/\s+/).join(" "),w("comparator",e,r),this.options=r,this.loose=!!r.loose,this.parse(e),this.semver===ue?this.value="":this.value=this.operator+this.semver.version,w("comp",this)}var ue={};_.prototype.parse=function(e){var r=this.options.loose?E[Ar]:E[Ir],t=e.match(r);if(!t)throw new TypeError("Invalid comparator: "+e);this.operator=t[1],this.operator==="="&&(this.operator=""),t[2]?this.semver=new m(t[2],this.options.loose):this.semver=ue};_.prototype.toString=function(){return this.value};_.prototype.test=function(e){return w("Comparator.test",e,this.options.loose),this.semver===ue?!0:(typeof e=="string"&&(e=new m(e,this.options)),Ie(e,this.operator,this.semver,this.options))};_.prototype.intersects=function(e,r){if(!(e instanceof _))throw new TypeError("a Comparator is required");(!r||typeof r!="object")&&(r={loose:!!r,includePrerelease:!1});var t;if(this.operator==="")return t=new T(e.value,r),Oe(this.value,t,r);if(e.operator==="")return t=new T(this.value,r),Oe(e.semver,t,r);var n=(this.operator===">="||this.operator===">")&&(e.operator===">="||e.operator===">"),s=(this.operator==="<="||this.operator==="<")&&(e.operator==="<="||e.operator==="<"),i=this.semver.version===e.semver.version,o=(this.operator===">="||this.operator==="<=")&&(e.operator===">="||e.operator==="<="),u=Ie(this.semver,"<",e.semver,r)&&(this.operator===">="||this.operator===">")&&(e.operator==="<="||e.operator==="<"),c=Ie(this.semver,">",e.semver,r)&&(this.operator==="<="||this.operator==="<")&&(e.operator===">="||e.operator===">");return n||s||i&&o||u||c};l.Range=T;function T(e,r){if((!r||typeof r!="object")&&(r={loose:!!r,includePrerelease:!1}),e instanceof T)return e.loose===!!r.loose&&e.includePrerelease===!!r.includePrerelease?e:new T(e.raw,r);if(e instanceof _)return new T(e.value,r);if(!(this instanceof T))return new T(e,r);if(this.options=r,this.loose=!!r.loose,this.includePrerelease=!!r.includePrerelease,this.raw=e.trim().split(/\s+/).join(" "),this.set=this.raw.split("||").map(function(t){return this.parseRange(t.trim())},this).filter(function(t){return t.length}),!this.set.length)throw new TypeError("Invalid SemVer Range: "+this.raw);this.format()}T.prototype.format=function(){return this.range=this.set.map(function(e){return e.join(" ").trim()}).join("||").trim(),this.range};T.prototype.toString=function(){return this.range};T.prototype.parseRange=function(e){var r=this.options.loose,t=r?E[Ht]:E[Ut];e=e.replace(t,$i),w("hyphen replace",e),e=e.replace(E[U],li),w("comparator trim",e,E[U]),e=e.replace(E[M],ci),e=e.replace(E[Z],ui);var n=r?E[Ar]:E[Ir],s=e.split(" ").map(function(i){return Ti(i,this.options)},this).join(" ").split(/\s+/);return this.options.loose&&(s=s.filter(function(i){return!!i.match(n)})),s=s.map(function(i){return new _(i,this.options)},this),s};T.prototype.intersects=function(e,r){if(!(e instanceof T))throw new TypeError("a Range is required");return this.set.some(function(t){return t.every(function(n){return e.set.some(function(s){return s.every(function(i){return n.intersects(i,r)})})})})};l.toComparators=Si;function Si(e,r){return new T(e,r).set.map(function(t){return t.map(function(n){return n.value}).join(" ").trim().split(" ")})}function Ti(e,r){return w("comp",e,r),e=Oi(e,r),w("caret",e),e=Ai(e,r),w("tildes",e),e=Ri(e,r),w("xrange",e),e=Pi(e,r),w("stars",e),e}function R(e){return!e||e.toLowerCase()==="x"||e==="*"}function Ai(e,r){return e.trim().split(/\s+/).map(function(t){return Ii(t,r)}).join(" ")}function Ii(e,r){var t=r.loose?E[Dt]:E[Gt];return e.replace(t,function(n,s,i,o,u){w("tilde",e,n,s,i,o,u);var c;return R(s)?c="":R(i)?c=">="+s+".0.0 <"+(+s+1)+".0.0":R(o)?c=">="+s+"."+i+".0 <"+s+"."+(+i+1)+".0":u?(w("replaceTilde pr",u),c=">="+s+"."+i+"."+o+"-"+u+" <"+s+"."+(+i+1)+".0"):c=">="+s+"."+i+"."+o+" <"+s+"."+(+i+1)+".0",w("tilde return",c),c})}function Oi(e,r){return e.trim().split(/\s+/).map(function(t){return Ci(t,r)}).join(" ")}function Ci(e,r){w("caret",e,r);var t=r.loose?E[Ft]:E[Bt];return e.replace(t,function(n,s,i,o,u){w("caret",e,n,s,i,o,u);var c;return R(s)?c="":R(i)?c=">="+s+".0.0 <"+(+s+1)+".0.0":R(o)?s==="0"?c=">="+s+"."+i+".0 <"+s+"."+(+i+1)+".0":c=">="+s+"."+i+".0 <"+(+s+1)+".0.0":u?(w("replaceCaret pr",u),s==="0"?i==="0"?c=">="+s+"."+i+"."+o+"-"+u+" <"+s+"."+i+"."+(+o+1):c=">="+s+"."+i+"."+o+"-"+u+" <"+s+"."+(+i+1)+".0":c=">="+s+"."+i+"."+o+"-"+u+" <"+(+s+1)+".0.0"):(w("no pr"),s==="0"?i==="0"?c=">="+s+"."+i+"."+o+" <"+s+"."+i+"."+(+o+1):c=">="+s+"."+i+"."+o+" <"+s+"."+(+i+1)+".0":c=">="+s+"."+i+"."+o+" <"+(+s+1)+".0.0"),w("caret return",c),c})}function Ri(e,r){return w("replaceXRanges",e,r),e.split(/\s+/).map(function(t){return _i(t,r)}).join(" ")}function _i(e,r){e=e.trim();var t=r.loose?E[jt]:E[Lt];return e.replace(t,function(n,s,i,o,u,c){w("xRange",e,n,s,i,o,u,c);var f=R(i),y=f||R(o),v=y||R(u),x=v;return s==="="&&x&&(s=""),f?s===">"||s==="<"?n="<0.0.0":n="*":s&&x?(y&&(o=0),u=0,s===">"?(s=">=",y?(i=+i+1,o=0,u=0):(o=+o+1,u=0)):s==="<="&&(s="<",y?i=+i+1:o=+o+1),n=s+i+"."+o+"."+u):y?n=">="+i+".0.0 <"+(+i+1)+".0.0":v&&(n=">="+i+"."+o+".0 <"+i+"."+(+o+1)+".0"),w("xRange return",n),n})}function Pi(e,r){return w("replaceStars",e,r),e.trim().replace(E[Vt],"")}function $i(e,r,t,n,s,i,o,u,c,f,y,v,x){return R(t)?r="":R(n)?r=">="+t+".0.0":R(s)?r=">="+t+"."+n+".0":r=">="+r,R(c)?u="":R(f)?u="<"+(+c+1)+".0.0":R(y)?u="<"+c+"."+(+f+1)+".0":v?u="<="+c+"."+f+"."+y+"-"+v:u="<="+u,(r+" "+u).trim()}T.prototype.test=function(e){if(!e)return!1;typeof e=="string"&&(e=new m(e,this.options));for(var r=0;r<this.set.length;r++)if(ki(this.set[r],e,this.options))return!0;return!1};function ki(e,r,t){for(var n=0;n<e.length;n++)if(!e[n].test(r))return!1;if(r.prerelease.length&&!t.includePrerelease){for(n=0;n<e.length;n++)if(w(e[n].semver),e[n].semver!==ue&&e[n].semver.prerelease.length>0){var s=e[n].semver;if(s.major===r.major&&s.minor===r.minor&&s.patch===r.patch)return!0}return!1}return!0}l.satisfies=Oe;function Oe(e,r,t){try{r=new T(r,t)}catch{return!1}return r.test(e)}l.maxSatisfying=Ni;function Ni(e,r,t){var n=null,s=null;try{var i=new T(r,t)}catch{return null}return e.forEach(function(o){i.test(o)&&(!n||s.compare(o)===-1)&&(n=o,s=new m(n,t))}),n}l.minSatisfying=Li;function Li(e,r,t){var n=null,s=null;try{var i=new T(r,t)}catch{return null}return e.forEach(function(o){i.test(o)&&(!n||s.compare(o)===1)&&(n=o,s=new m(n,t))}),n}l.minVersion=ji;function ji(e,r){e=new T(e,r);var t=new m("0.0.0");if(e.test(t)||(t=new m("0.0.0-0"),e.test(t)))return t;t=null;for(var n=0;n<e.set.length;++n){var s=e.set[n];s.forEach(function(i){var o=new m(i.semver.version);switch(i.operator){case">":o.prerelease.length===0?o.patch++:o.prerelease.push(0),o.raw=o.format();case"":case">=":(!t||ae(t,o))&&(t=o);break;case"<":case"<=":break;default:throw new Error("Unexpected operation: "+i.operator)}})}return t&&e.test(t)?t:null}l.validRange=qi;function qi(e,r){try{return new T(e,r).range||"*"}catch{return null}}l.ltr=Gi;function Gi(e,r,t){return _r(e,r,"<",t)}l.gtr=Di;function Di(e,r,t){return _r(e,r,">",t)}l.outside=_r;function _r(e,r,t,n){e=new m(e,n),r=new T(r,n);var s,i,o,u,c;switch(t){case">":s=ae,i=Rr,o=Ae,u=">",c=">=";break;case"<":s=Ae,i=Cr,o=ae,u="<",c="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(Oe(e,r,n))return!1;for(var f=0;f<r.set.length;++f){var y=r.set[f],v=null,x=null;if(y.forEach(function(I){I.semver===ue&&(I=new _(">=0.0.0")),v=v||I,x=x||I,s(I.semver,v.semver,n)?v=I:o(I.semver,x.semver,n)&&(x=I)}),v.operator===u||v.operator===c||(!x.operator||x.operator===u)&&i(e,x.semver))return!1;if(x.operator===c&&o(e,x.semver))return!1}return!0}l.prerelease=Bi;function Bi(e,r){var t=H(e,r);return t&&t.prerelease.length?t.prerelease:null}l.intersects=Fi;function Fi(e,r,t){return e=new T(e,t),r=new T(r,t),e.intersects(r)}l.coerce=Ui;function Ui(e){if(e instanceof m)return e;if(typeof e!="string")return null;var r=e.match(E[qt]);return r==null?null:H(r[1]+"."+(r[2]||"0")+"."+(r[3]||"0"))}});var Zt=h((da,Mt)=>{"use strict";var Hi=require("path"),Vi=Qr(),Wt=St(),Jt=Tt(),Xi=_t(),zi=Yt(),Kt=process.platform==="win32",Yi=/\.(?:com|exe)$/i,Wi=/node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i,Ji=Vi(()=>zi.satisfies(process.version,"^4.8.0 || ^5.7.0 || >= 6.0.0",!0))||!1;function Ki(e){e.file=Wt(e);let r=e.file&&Xi(e.file);return r?(e.args.unshift(e.file),e.command=r,Wt(e)):e.file}function Mi(e){if(!Kt)return e;let r=Ki(e),t=!Yi.test(r);if(e.options.forceShell||t){let n=Wi.test(r);e.command=Hi.normalize(e.command),e.command=Jt.command(e.command),e.args=e.args.map(i=>Jt.argument(i,n));let s=[e.command].concat(e.args).join(" ");e.args=["/d","/s","/c",`"${s}"`],e.command=process.env.comspec||"cmd.exe",e.options.windowsVerbatimArguments=!0}return e}function Zi(e){if(Ji)return e;let r=[e.command].concat(e.args).join(" ");return Kt?(e.command=typeof e.options.shell=="string"?e.options.shell:process.env.comspec||"cmd.exe",e.args=["/d","/s","/c",`"${r}"`],e.options.windowsVerbatimArguments=!0):(typeof e.options.shell=="string"?e.command=e.options.shell:process.platform==="android"?e.command="/system/bin/sh":e.command="/bin/sh",e.args=["-c",r]),e}function Qi(e,r,t){r&&!Array.isArray(r)&&(t=r,r=null),r=r?r.slice(0):[],t=Object.assign({},t);let n={command:e,args:r,options:t,file:void 0,original:{command:e,args:r}};return t.shell?Zi(n):Mi(n)}Mt.exports=Qi});var rn=h((ga,en)=>{"use strict";var Pr=process.platform==="win32";function $r(e,r){return Object.assign(new Error(`${r} ${e.command} ENOENT`),{code:"ENOENT",errno:"ENOENT",syscall:`${r} ${e.command}`,path:e.command,spawnargs:e.args})}function eo(e,r){if(!Pr)return;let t=e.emit;e.emit=function(n,s){if(n==="exit"){let i=Qt(s,r,"spawn");if(i)return t.call(e,"error",i)}return t.apply(e,arguments)}}function Qt(e,r){return Pr&&e===1&&!r.file?$r(r.original,"spawn"):null}function ro(e,r){return Pr&&e===1&&!r.file?$r(r.original,"spawnSync"):null}en.exports={hookChildProcess:eo,verifyENOENT:Qt,verifyENOENTSync:ro,notFoundError:$r}});var sn=h((ma,ee)=>{"use strict";var tn=require("child_process"),kr=Zt(),Nr=rn();function nn(e,r,t){let n=kr(e,r,t),s=tn.spawn(n.command,n.args,n.options);return Nr.hookChildProcess(s,n),s}function to(e,r,t){let n=kr(e,r,t),s=tn.spawnSync(n.command,n.args,n.options);return s.error=s.error||Nr.verifyENOENTSync(s.status,n),s}ee.exports=nn;ee.exports.spawn=nn;ee.exports.sync=to;ee.exports._parse=kr;ee.exports._enoent=Nr});var an=h((ya,on)=>{"use strict";on.exports=function(e){var r=typeof e=="string"?`
`:10,t=typeof e=="string"?"\r":13;return e[e.length-1]===r&&(e=e.slice(0,e.length-1)),e[e.length-1]===t&&(e=e.slice(0,e.length-1)),e}});var un=h((va,Pe)=>{"use strict";var le=require("path"),cn=ur();Pe.exports=e=>{e=Object.assign({cwd:process.cwd(),path:process.env[cn()]},e);let r,t=le.resolve(e.cwd),n=[];for(;r!==t;)n.push(le.join(t,"node_modules/.bin")),r=t,t=le.resolve(t,"..");return n.push(le.dirname(process.execPath)),n.concat(e.path).join(le.delimiter)};Pe.exports.env=e=>{e=Object.assign({env:process.env},e);let r=Object.assign({},e.env),t=cn({env:r});return e.path=r[t],r[t]=Pe.exports(e),r}});var fn=h((wa,ln)=>{"use strict";var j=ln.exports=function(e){return e!==null&&typeof e=="object"&&typeof e.pipe=="function"};j.writable=function(e){return j(e)&&e.writable!==!1&&typeof e._write=="function"&&typeof e._writableState=="object"};j.readable=function(e){return j(e)&&e.readable!==!1&&typeof e._read=="function"&&typeof e._readableState=="object"};j.duplex=function(e){return j.writable(e)&&j.readable(e)};j.transform=function(e){return j.duplex(e)&&typeof e._transform=="function"&&typeof e._transformState=="object"}});var dn=h((xa,hn)=>{hn.exports=pn;function pn(e,r){if(e&&r)return pn(e)(r);if(typeof e!="function")throw new TypeError("need wrapper function");return Object.keys(e).forEach(function(n){t[n]=e[n]}),t;function t(){for(var n=new Array(arguments.length),s=0;s<n.length;s++)n[s]=arguments[s];var i=e.apply(this,n),o=n[n.length-1];return typeof i=="function"&&i!==o&&Object.keys(o).forEach(function(u){i[u]=o[u]}),i}}});var jr=h((Ea,Lr)=>{var gn=dn();Lr.exports=gn($e);Lr.exports.strict=gn(mn);$e.proto=$e(function(){Object.defineProperty(Function.prototype,"once",{value:function(){return $e(this)},configurable:!0}),Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return mn(this)},configurable:!0})});function $e(e){var r=function(){return r.called?r.value:(r.called=!0,r.value=e.apply(this,arguments))};return r.called=!1,r}function mn(e){var r=function(){if(r.called)throw new Error(r.onceError);return r.called=!0,r.value=e.apply(this,arguments)},t=e.name||"Function wrapped with `once`";return r.onceError=t+" shouldn't be called more than once",r.called=!1,r}});var wn=h((ba,vn)=>{var no=jr(),so=function(){},io=function(e){return e.setHeader&&typeof e.abort=="function"},oo=function(e){return e.stdio&&Array.isArray(e.stdio)&&e.stdio.length===3},yn=function(e,r,t){if(typeof r=="function")return yn(e,null,r);r||(r={}),t=no(t||so);var n=e._writableState,s=e._readableState,i=r.readable||r.readable!==!1&&e.readable,o=r.writable||r.writable!==!1&&e.writable,u=!1,c=function(){e.writable||f()},f=function(){o=!1,i||t.call(e)},y=function(){i=!1,o||t.call(e)},v=function(A){t.call(e,A?new Error("exited with error code: "+A):null)},x=function(A){t.call(e,A)},I=function(){process.nextTick($)},$=function(){if(!u){if(i&&!(s&&s.ended&&!s.destroyed))return t.call(e,new Error("premature close"));if(o&&!(n&&n.ended&&!n.destroyed))return t.call(e,new Error("premature close"))}},k=function(){e.req.on("finish",f)};return io(e)?(e.on("complete",f),e.on("abort",I),e.req?k():e.on("request",k)):o&&!n&&(e.on("end",c),e.on("close",c)),oo(e)&&e.on("exit",v),e.on("end",y),e.on("finish",f),r.error!==!1&&e.on("error",x),e.on("close",I),function(){u=!0,e.removeListener("complete",f),e.removeListener("abort",I),e.removeListener("request",k),e.req&&e.req.removeListener("finish",f),e.removeListener("end",c),e.removeListener("close",c),e.removeListener("finish",f),e.removeListener("exit",v),e.removeListener("end",y),e.removeListener("error",x),e.removeListener("close",I)}};vn.exports=yn});var bn=h((Sa,En)=>{var ao=jr(),co=wn(),ke;try{ke=require("fs")}catch{}var fe=function(){},uo=/^v?\.0/.test(process.version),Ne=function(e){return typeof e=="function"},lo=function(e){return!uo||!ke?!1:(e instanceof(ke.ReadStream||fe)||e instanceof(ke.WriteStream||fe))&&Ne(e.close)},fo=function(e){return e.setHeader&&Ne(e.abort)},po=function(e,r,t,n){n=ao(n);var s=!1;e.on("close",function(){s=!0}),co(e,{readable:r,writable:t},function(o){if(o)return n(o);s=!0,n()});var i=!1;return function(o){if(!s&&!i){if(i=!0,lo(e))return e.close(fe);if(fo(e))return e.abort();if(Ne(e.destroy))return e.destroy();n(o||new Error("stream was destroyed"))}}},xn=function(e){e()},ho=function(e,r){return e.pipe(r)},go=function(){var e=Array.prototype.slice.call(arguments),r=Ne(e[e.length-1]||fe)&&e.pop()||fe;if(Array.isArray(e[0])&&(e=e[0]),e.length<2)throw new Error("pump requires two streams per minimum");var t,n=e.map(function(s,i){var o=i<e.length-1,u=i>0;return po(s,o,u,function(c){t||(t=c),c&&n.forEach(xn),!o&&(n.forEach(xn),r(t))})});return e.reduce(ho)};En.exports=go});var Tn=h((Ta,Sn)=>{"use strict";var{PassThrough:mo}=require("stream");Sn.exports=e=>{e=Object.assign({},e);let{array:r}=e,{encoding:t}=e,n=t==="buffer",s=!1;r?s=!(t||n):t=t||"utf8",n&&(t=null);let i=0,o=[],u=new mo({objectMode:s});return t&&u.setEncoding(t),u.on("data",c=>{o.push(c),s?i=o.length:i+=c.length}),u.getBufferedValue=()=>r?o:n?Buffer.concat(o,i):o.join(""),u.getBufferedLength=()=>i,u}});var An=h((Aa,pe)=>{"use strict";var yo=bn(),vo=Tn(),Le=class extends Error{constructor(){super("maxBuffer exceeded"),this.name="MaxBufferError"}};function qr(e,r){if(!e)return Promise.reject(new Error("Expected a stream"));r=Object.assign({maxBuffer:1/0},r);let{maxBuffer:t}=r,n;return new Promise((s,i)=>{let o=u=>{u&&(u.bufferedData=n.getBufferedValue()),i(u)};n=yo(e,vo(r),u=>{if(u){o(u);return}s()}),n.on("data",()=>{n.getBufferedLength()>t&&o(new Le)})}).then(()=>n.getBufferedValue())}pe.exports=qr;pe.exports.buffer=(e,r)=>qr(e,Object.assign({},r,{encoding:"buffer"}));pe.exports.array=(e,r)=>qr(e,Object.assign({},r,{array:!0}));pe.exports.MaxBufferError=Le});var On=h((Ia,In)=>{"use strict";In.exports=(e,r)=>(r=r||(()=>{}),e.then(t=>new Promise(n=>{n(r())}).then(()=>t),t=>new Promise(n=>{n(r())}).then(()=>{throw t})))});var Cn=h((Oa,je)=>{je.exports=["SIGABRT","SIGALRM","SIGHUP","SIGINT","SIGTERM"];process.platform!=="win32"&&je.exports.push("SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");process.platform==="linux"&&je.exports.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT","SIGUNUSED")});var kn=h((Ca,ne)=>{var S=global.process,V=function(e){return e&&typeof e=="object"&&typeof e.removeListener=="function"&&typeof e.emit=="function"&&typeof e.reallyExit=="function"&&typeof e.listeners=="function"&&typeof e.kill=="function"&&typeof e.pid=="number"&&typeof e.on=="function"};V(S)?(Rn=require("assert"),re=Cn(),_n=/^win/i.test(S.platform),he=require("events"),typeof he!="function"&&(he=he.EventEmitter),S.__signal_exit_emitter__?C=S.__signal_exit_emitter__:(C=S.__signal_exit_emitter__=new he,C.count=0,C.emitted={}),C.infinite||(C.setMaxListeners(1/0),C.infinite=!0),ne.exports=function(e,r){if(!V(global.process))return function(){};Rn.equal(typeof e,"function","a callback must be provided for exit handler"),te===!1&&Gr();var t="exit";r&&r.alwaysLast&&(t="afterexit");var n=function(){C.removeListener(t,e),C.listeners("exit").length===0&&C.listeners("afterexit").length===0&&qe()};return C.on(t,e),n},qe=function(){!te||!V(global.process)||(te=!1,re.forEach(function(r){try{S.removeListener(r,Ge[r])}catch{}}),S.emit=De,S.reallyExit=Dr,C.count-=1)},ne.exports.unload=qe,X=function(r,t,n){C.emitted[r]||(C.emitted[r]=!0,C.emit(r,t,n))},Ge={},re.forEach(function(e){Ge[e]=function(){if(V(global.process)){var t=S.listeners(e);t.length===C.count&&(qe(),X("exit",null,e),X("afterexit",null,e),_n&&e==="SIGHUP"&&(e="SIGINT"),S.kill(S.pid,e))}}}),ne.exports.signals=function(){return re},te=!1,Gr=function(){te||!V(global.process)||(te=!0,C.count+=1,re=re.filter(function(r){try{return S.on(r,Ge[r]),!0}catch{return!1}}),S.emit=$n,S.reallyExit=Pn)},ne.exports.load=Gr,Dr=S.reallyExit,Pn=function(r){V(global.process)&&(S.exitCode=r||0,X("exit",S.exitCode,null),X("afterexit",S.exitCode,null),Dr.call(S,S.exitCode))},De=S.emit,$n=function(r,t){if(r==="exit"&&V(global.process)){t!==void 0&&(S.exitCode=t);var n=De.apply(this,arguments);return X("exit",S.exitCode,null),X("afterexit",S.exitCode,null),n}else return De.apply(this,arguments)}):ne.exports=function(){return function(){}};var Rn,re,_n,he,C,qe,X,Ge,te,Gr,Dr,Pn,De,$n});var jn=h((Ra,Fe)=>{"use strict";var Nn=require("util"),Be;if(typeof Nn.getSystemErrorName=="function")Fe.exports=Nn.getSystemErrorName;else{try{if(Be=process.binding("uv"),typeof Be.errname!="function")throw new TypeError("uv.errname is not a function")}catch(e){console.error("execa/lib/errname: unable to establish process.binding('uv')",e),Be=null}Fe.exports=e=>Ln(Be,e)}Fe.exports.__test__=Ln;function Ln(e,r){if(e)return e.errname(r);if(!(r<0))throw new Error("err >= 0");return`Unknown system error ${r}`}});var Gn=h((_a,qn)=>{"use strict";var de=["stdin","stdout","stderr"],wo=e=>de.some(r=>!!e[r]);qn.exports=e=>{if(!e)return null;if(e.stdio&&wo(e))throw new Error(`It's not possible to provide \`stdio\` in combination with one of ${de.map(s=>`\`${s}\``).join(", ")}`);if(typeof e.stdio=="string")return e.stdio;let r=e.stdio||[];if(!Array.isArray(r))throw new TypeError(`Expected \`stdio\` to be of type \`string\` or \`Array\`, got \`${typeof r}\``);let t=[],n=Math.max(r.length,de.length);for(let s=0;s<n;s++){let i=null;r[s]!==void 0?i=r[s]:e[de[s]]!==void 0&&(i=e[de[s]]),t[s]=i}return t}});var ge=h((Pa,P)=>{"use strict";var xo=require("path"),Fn=require("child_process"),Un=sn(),Eo=an(),bo=un(),Hn=fn(),Dn=An(),So=On(),To=kn(),Ao=jn(),Io=Gn(),Oo=1e3*1e3*10;function Vn(e,r,t){let n;return t=Object.assign({extendEnv:!0,env:{}},t),t.extendEnv&&(t.env=Object.assign({},process.env,t.env)),t.__winShell===!0?(delete t.__winShell,n={command:e,args:r,options:t,file:e,original:{cmd:e,args:r}}):n=Un._parse(e,r,t),t=Object.assign({maxBuffer:Oo,buffer:!0,stripEof:!0,preferLocal:!0,localDir:n.options.cwd||process.cwd(),encoding:"utf8",reject:!0,cleanup:!0},n.options),t.stdio=Io(t),t.preferLocal&&(t.env=bo.env(Object.assign({},t,{cwd:t.localDir}))),t.detached&&(t.cleanup=!1),process.platform==="win32"&&xo.basename(n.command)==="cmd.exe"&&n.args.unshift("/q"),{cmd:n.command,args:n.args,opts:t,parsed:n}}function Co(e,r){r!=null&&(Hn(r)?r.pipe(e.stdin):e.stdin.end(r))}function Ue(e,r){return r&&e.stripEof&&(r=Eo(r)),r}function Xn(e,r,t){let n="/bin/sh",s=["-c",r];return t=Object.assign({},t),process.platform==="win32"&&(t.__winShell=!0,n=process.env.comspec||"cmd.exe",s=["/s","/c",`"${r}"`],t.windowsVerbatimArguments=!0),t.shell&&(n=t.shell,delete t.shell),e(n,s,t)}function Bn(e,r,{encoding:t,buffer:n,maxBuffer:s}){if(!e[r])return null;let i;return n?t?i=Dn(e[r],{encoding:t,maxBuffer:s}):i=Dn.buffer(e[r],{maxBuffer:s}):i=new Promise((o,u)=>{e[r].once("end",o).once("error",u)}),i.catch(o=>{throw o.stream=r,o.message=`${r} ${o.message}`,o})}function zn(e,r){let{stdout:t,stderr:n}=e,s=e.error,{code:i,signal:o}=e,{parsed:u,joinedCmd:c}=r,f=r.timedOut||!1;if(!s){let y="";Array.isArray(u.opts.stdio)?(u.opts.stdio[2]!=="inherit"&&(y+=y.length>0?n:`
${n}`),u.opts.stdio[1]!=="inherit"&&(y+=`
${t}`)):u.opts.stdio!=="inherit"&&(y=`
${n}${t}`),s=new Error(`Command failed: ${c}${y}`),s.code=i<0?Ao(i):i}return s.stdout=t,s.stderr=n,s.failed=!0,s.signal=o||null,s.cmd=c,s.timedOut=f,s}function Yn(e,r){let t=e;return Array.isArray(r)&&r.length>0&&(t+=" "+r.join(" ")),t}P.exports=(e,r,t)=>{let n=Vn(e,r,t),{encoding:s,buffer:i,maxBuffer:o}=n.opts,u=Yn(e,r),c;try{c=Fn.spawn(n.cmd,n.args,n.opts)}catch(A){return Promise.reject(A)}let f;n.opts.cleanup&&(f=To(()=>{c.kill()}));let y=null,v=!1,x=()=>{y&&(clearTimeout(y),y=null),f&&f()};n.opts.timeout>0&&(y=setTimeout(()=>{y=null,v=!0,c.kill(n.opts.killSignal)},n.opts.timeout));let I=new Promise(A=>{c.on("exit",(O,G)=>{x(),A({code:O,signal:G})}),c.on("error",O=>{x(),A({error:O})}),c.stdin&&c.stdin.on("error",O=>{x(),A({error:O})})});function $(){c.stdout&&c.stdout.destroy(),c.stderr&&c.stderr.destroy()}let k=()=>So(Promise.all([I,Bn(c,"stdout",{encoding:s,buffer:i,maxBuffer:o}),Bn(c,"stderr",{encoding:s,buffer:i,maxBuffer:o})]).then(A=>{let O=A[0];if(O.stdout=A[1],O.stderr=A[2],O.error||O.code!==0||O.signal!==null){let G=zn(O,{joinedCmd:u,parsed:n,timedOut:v});if(G.killed=G.killed||c.killed,!n.opts.reject)return G;throw G}return{stdout:Ue(n.opts,O.stdout),stderr:Ue(n.opts,O.stderr),code:0,failed:!1,killed:!1,signal:null,cmd:u,timedOut:!1}}),$);return Un._enoent.hookChildProcess(c,n.parsed),Co(c,n.opts.input),c.then=(A,O)=>k().then(A,O),c.catch=A=>k().catch(A),c};P.exports.stdout=(...e)=>P.exports(...e).then(r=>r.stdout);P.exports.stderr=(...e)=>P.exports(...e).then(r=>r.stderr);P.exports.shell=(e,r)=>Xn(P.exports,e,r);P.exports.sync=(e,r,t)=>{let n=Vn(e,r,t),s=Yn(e,r);if(Hn(n.opts.input))throw new TypeError("The `input` option cannot be a stream in sync mode");let i=Fn.spawnSync(n.cmd,n.args,n.opts);if(i.code=i.status,i.error||i.status!==0||i.signal!==null){let o=zn(i,{joinedCmd:s,parsed:n});if(!n.opts.reject)return o;throw o}return{stdout:Ue(n.opts,i.stdout),stderr:Ue(n.opts,i.stderr),code:0,failed:!1,signal:null,cmd:s,timedOut:!1}};P.exports.shellSync=(e,r)=>Xn(P.exports.sync,e,r)});var Jn=h(($a,Wn)=>{"use strict";var He=ge(),Ve=e=>{throw e.code==="ENOENT"?new Error("Couldn't find the termux-api scripts. You can install them with: apt install termux-api"):e};Wn.exports={copy:async e=>{try{await He("termux-clipboard-set",e)}catch(r){Ve(r)}},paste:async e=>{try{return await He.stdout("termux-clipboard-get",e)}catch(r){Ve(r)}},copySync:e=>{try{He.sync("termux-clipboard-set",e)}catch(r){Ve(r)}},pasteSync:e=>{try{return He.sync("termux-clipboard-get",e)}catch(r){Ve(r)}}}});var ss=h((ka,ns)=>{"use strict";var Ro=require("path"),Xe=ge(),es="xsel",rs=Ro.join(__dirname,"../fallbacks/linux/xsel"),Kn=["--clipboard","--input"],Mn=["--clipboard","--output"],ts=(e,r)=>{let t;return e.code==="ENOENT"?t=new Error("Couldn't find the `xsel` binary and fallback didn't work. On Debian/Ubuntu you can install xsel with: sudo apt install xsel"):(t=new Error("Both xsel and fallback failed"),t.xselError=e),t.fallbackError=r,t},Zn=async(e,r)=>{try{return await Xe.stdout(es,e,r)}catch(t){try{return await Xe.stdout(rs,e,r)}catch(n){throw ts(t,n)}}},Qn=(e,r)=>{try{return Xe.sync(es,e,r)}catch(t){try{return Xe.sync(rs,e,r)}catch(n){throw ts(t,n)}}};ns.exports={copy:async e=>{await Zn(Kn,e)},copySync:e=>{Qn(Kn,e)},paste:e=>Zn(Mn,e),pasteSync:e=>Qn(Mn,e)}});var os=h((Na,is)=>{"use strict";var ze=ge(),Ye={...process.env,LC_CTYPE:"UTF-8"};is.exports={copy:async e=>ze("pbcopy",{...e,env:Ye}),paste:async e=>ze.stdout("pbpaste",{...e,env:Ye}),copySync:e=>ze.sync("pbcopy",{...e,env:Ye}),pasteSync:e=>ze.sync("pbpaste",{...e,env:Ye})}});var us=h((La,cs)=>{var _o=require("child_process"),as=require("fs"),Po=require("path");cs.exports=function(){if(process.arch==="x64"||process.platform==="darwin")return"x64";if(process.platform==="win32"){var r=!1;try{r=!!(process.env.SYSTEMROOT&&as.statSync(process.env.SYSTEMROOT))}catch{}var t=r?process.env.SYSTEMROOT:"C:\\Windows",n=!1;try{n=!!as.statSync(Po.join(t,"sysnative"))}catch{}return n?"x64":"x86"}if(process.platform==="linux"){var s=_o.execSync("getconf LONG_BIT",{encoding:"utf8"});return s===`64
`?"x64":"x86"}return"x86"}});var ps=h((ja,fs)=>{"use strict";var ls=require("path"),We=ge(),$o=us(),Je=$o()==="x64"?ls.join(__dirname,"../fallbacks/windows/clipboard_x86_64.exe"):ls.join(__dirname,"../fallbacks/windows/clipboard_i686.exe");fs.exports={copy:async e=>We(Je,["--copy"],e),paste:async e=>We.stdout(Je,["--paste"],e),copySync:e=>We.sync(Je,["--copy"],e),pasteSync:e=>We.sync(Je,["--paste"],e)}});var ds=h(me=>{"use strict";var ko=or(),No=Jn(),Lo=ss(),jo=os(),hs=ps(),Ke=(()=>{switch(process.platform){case"darwin":return jo;case"win32":return hs;case"android":if(process.env.PREFIX!=="/data/data/com.termux/files/usr")throw new Error("You need to install Termux for this module to work on Android: https://termux.com");return No;default:return ko?hs:Lo}})();me.write=async e=>{if(typeof e!="string")throw new TypeError(`Expected a string, got ${typeof e}`);await Ke.copy({input:e})};me.read=async()=>Ke.paste({stripEof:!1});me.writeSync=e=>{if(typeof e!="string")throw new TypeError(`Expected a string, got ${typeof e}`);Ke.copySync({input:e})};me.readSync=()=>Ke.pasteSync({stripEof:!1}).stdout});var ws=h((Ua,vs)=>{var Bo=require("node:tty"),Fo=Bo?.WriteStream?.prototype?.hasColors?.()??!1,d=(e,r)=>{if(!Fo)return s=>s;let t=`\x1B[${e}m`,n=`\x1B[${r}m`;return s=>{let i=s+"",o=i.indexOf(n);if(o===-1)return t+i+n;let u=t,c=0;for(;o!==-1;)u+=i.slice(c,o)+t,c=o+n.length,o=i.indexOf(n,c);return u+=i.slice(c)+n,u}},p={};p.reset=d(0,0);p.bold=d(1,22);p.dim=d(2,22);p.italic=d(3,23);p.underline=d(4,24);p.overline=d(53,55);p.inverse=d(7,27);p.hidden=d(8,28);p.strikethrough=d(9,29);p.black=d(30,39);p.red=d(31,39);p.green=d(32,39);p.yellow=d(33,39);p.blue=d(34,39);p.magenta=d(35,39);p.cyan=d(36,39);p.white=d(37,39);p.gray=d(90,39);p.bgBlack=d(40,49);p.bgRed=d(41,49);p.bgGreen=d(42,49);p.bgYellow=d(43,49);p.bgBlue=d(44,49);p.bgMagenta=d(45,49);p.bgCyan=d(46,49);p.bgWhite=d(47,49);p.bgGray=d(100,49);p.redBright=d(91,39);p.greenBright=d(92,39);p.yellowBright=d(93,39);p.blueBright=d(94,39);p.magentaBright=d(95,39);p.cyanBright=d(96,39);p.whiteBright=d(97,39);p.bgRedBright=d(101,49);p.bgGreenBright=d(102,49);p.bgYellowBright=d(103,49);p.bgBlueBright=d(104,49);p.bgMagentaBright=d(105,49);p.bgCyanBright=d(106,49);p.bgWhiteBright=d(107,49);vs.exports=p});var Ss=h((Ja,bs)=>{"use strict";bs.exports=(e,r,t)=>{let n=s=>Object.defineProperty(e,r,{value:s,enumerable:!0,writable:!0});return Object.defineProperty(e,r,{configurable:!0,enumerable:!0,get(){let s=t();return n(s),s},set(s){n(s)}}),e}});var _s=h((Ka,Rs)=>{var Ho=require("path"),Vo=require("child_process"),{promises:Qe,constants:Cs}=require("fs"),Ze=or(),Xo=sr(),Vr=Ss(),Ts=Ho.join(__dirname,"xdg-open"),{platform:se,arch:As}=process,zo=()=>{try{return Qe.statSync("/run/.containerenv"),!0}catch{return!1}},Hr;function Yo(){return Hr===void 0&&(Hr=zo()||Xo()),Hr}var Wo=(()=>{let e="/mnt/",r;return async function(){if(r)return r;let t="/etc/wsl.conf",n=!1;try{await Qe.access(t,Cs.F_OK),n=!0}catch{}if(!n)return e;let s=await Qe.readFile(t,{encoding:"utf8"}),i=/(?<!#.*)root\s*=\s*(?<mountPoint>.*)/g.exec(s);return i?(r=i.groups.mountPoint.trim(),r=r.endsWith("/")?r:`${r}/`,r):e}})(),Is=async(e,r)=>{let t;for(let n of e)try{return await r(n)}catch(s){t=s}throw t},er=async e=>{if(e={wait:!1,background:!1,newInstance:!1,allowNonzeroExitCode:!1,...e},Array.isArray(e.app))return Is(e.app,u=>er({...e,app:u}));let{name:r,arguments:t=[]}=e.app||{};if(t=[...t],Array.isArray(r))return Is(r,u=>er({...e,app:{name:u,arguments:t}}));let n,s=[],i={};if(se==="darwin")n="open",e.wait&&s.push("--wait-apps"),e.background&&s.push("--background"),e.newInstance&&s.push("--new"),r&&s.push("-a",r);else if(se==="win32"||Ze&&!Yo()&&!r){let u=await Wo();n=Ze?`${u}c/Windows/System32/WindowsPowerShell/v1.0/powershell.exe`:`${process.env.SYSTEMROOT}\\System32\\WindowsPowerShell\\v1.0\\powershell`,s.push("-NoProfile","-NonInteractive","\u2013ExecutionPolicy","Bypass","-EncodedCommand"),Ze||(i.windowsVerbatimArguments=!0);let c=["Start"];e.wait&&c.push("-Wait"),r?(c.push(`"\`"${r}\`""`,"-ArgumentList"),e.target&&t.unshift(e.target)):e.target&&c.push(`"${e.target}"`),t.length>0&&(t=t.map(f=>`"\`"${f}\`""`),c.push(t.join(","))),e.target=Buffer.from(c.join(" "),"utf16le").toString("base64")}else{if(r)n=r;else{let u=!__dirname||__dirname==="/",c=!1;try{await Qe.access(Ts,Cs.X_OK),c=!0}catch{}n=process.versions.electron||se==="android"||u||!c?"xdg-open":Ts}t.length>0&&s.push(...t),e.wait||(i.stdio="ignore",i.detached=!0)}e.target&&s.push(e.target),se==="darwin"&&t.length>0&&s.push("--args",...t);let o=Vo.spawn(n,s,i);return e.wait?new Promise((u,c)=>{o.once("error",c),o.once("close",f=>{if(!e.allowNonzeroExitCode&&f>0){c(new Error(`Exited with code ${f}`));return}u(o)})}):(o.unref(),o)},Xr=(e,r)=>{if(typeof e!="string")throw new TypeError("Expected a `target`");return er({...r,target:e})},Jo=(e,r)=>{if(typeof e!="string")throw new TypeError("Expected a `name`");let{arguments:t=[]}=r||{};if(t!=null&&!Array.isArray(t))throw new TypeError("Expected `appArguments` as Array type");return er({...r,app:{name:e,arguments:t}})};function Os(e){if(typeof e=="string"||Array.isArray(e))return e;let{[As]:r}=e;if(!r)throw new Error(`${As} is not supported`);return r}function zr({[se]:e},{wsl:r}){if(r&&Ze)return Os(r);if(!e)throw new Error(`${se} is not supported`);return Os(e)}var rr={};Vr(rr,"chrome",()=>zr({darwin:"google chrome",win32:"chrome",linux:["google-chrome","google-chrome-stable","chromium"]},{wsl:{ia32:"/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe",x64:["/mnt/c/Program Files/Google/Chrome/Application/chrome.exe","/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe"]}}));Vr(rr,"firefox",()=>zr({darwin:"firefox",win32:"C:\\Program Files\\Mozilla Firefox\\firefox.exe",linux:"firefox"},{wsl:"/mnt/c/Program Files/Mozilla Firefox/firefox.exe"}));Vr(rr,"edge",()=>zr({darwin:"microsoft edge",win32:"msedge",linux:["microsoft-edge","microsoft-edge-dev"]},{wsl:"/mnt/c/Program Files (x86)/Microsoft/Edge/Application/msedge.exe"}));Xr.apps=rr;Xr.openApp=Jo;Rs.exports=Xr});var Zo={};Bs(Zo,{default:()=>tr});module.exports=Fs(Zo);var ks=require("@oclif/core"),Ns=z(ds());var B=require("@oclif/core");var ve=z(require("node:fs")),Fr=z(require("node:path")),gs=z(require("node:os"));var ye={production:{url:"https://www.raycast.com",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},staging:{url:"https://www.rayca.st",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},development:{url:"http://localhost:3000",clientID:"rOCD_LhnXFObSLu9v7788qOGu25FZDqEzGjet4Q45ZY"}}[process.env.API_ENV||"production"],qo="e69bae0ec90f5e838555",q={},ms;function ys(e){ms=e;try{q=JSON.parse(ve.readFileSync(Fr.join(Do(),"config.json"),"utf8"))}catch(r){if(r instanceof Error&&r.code==="ENOENT")return;throw new Error(`Failed to read config file: ${r}`)}}function we(e){switch(e){case"raycastApiURL":return process.env.RAY_APIURL||q.APIURL||ye.url;case"raycastAccessToken":return process.env.RAY_TOKEN||q.Token||q.AccessToken||"";case"raycastClientId":return process.env.RAY_CLIENT_ID||q.ClientID||ye.clientID;case"githubClientId":return process.env.RAY_GithubClientID||q.GithubClientID||qo;case"githubAccessToken":return process.env.GITHUB_ACCESS_TOKEN||q.GithubAccessToken||"";case"flavorName":return process.env.RAY_Target?process.env.RAY_Target:typeof q.Target<"u"?q.Target:Br(process.platform==="win32"?"x":"release")}}function Br(e){switch(e){case"debug":return"debug";case"internal":return"internal";case"release":return"";case"x":return"x";case"x-internal":return"x-internal";case"x-development":return"x-development";case void 0:return we("flavorName")}}function Go(){let e=Br(ms);return e==""?"raycast":`raycast-${e}`}function Do(){let e=Fr.join(gs.default.homedir(),".config",Go());return ve.mkdirSync(e,{recursive:!0}),e}var b=z(ws());var Ha=(0,b.blue)((0,b.dim)("internal only"));function Ur(e,r,t){console.log(D[e]+r),typeof t?.exit<"u"&&process.exit(t.exit)}function xs(e){Object.entries(e).forEach(([r,t])=>{console.log(`${(0,b.blue)(`- ${r}: `)}${t}`)})}var D={wait:`\u{1F550}${(0,b.blue)("wait")}  - `,info:`${process.env.GITHUB_ACTIONS==="true"?"\u2139\uFE0F":"\u2139\uFE0F "}${(0,b.cyan)("info")}  - `,success:`\u2705${(0,b.green)("ready")}  - `,warn:`${process.env.GITHUB_ACTIONS==="true"?"\u26A0\uFE0F":"\u26A0\uFE0F "}${(0,b.yellow)("warn")}  - `,error:`\u{1F4A5}${(0,b.red)("error")}  - `,event:`\u26A1\uFE0F${(0,b.magenta)("event")}  - `,paymentPrompt:`\u{1F4B0}${(0,b.yellowBright)("plan")}  - `},Uo=!0;function Es(e,r){e||(D.wait=`${(0,b.blue)("wait")}  - `,D.info=`${(0,b.cyan)("info")}  - `,D.success=`${(0,b.green)("ready")}  - `,D.warn=`${(0,b.yellow)("warn")}  - `,D.error=`${(0,b.red)("error")}  - `,D.event=`${(0,b.magenta)("event")}  - `,D.paymentPrompt=`${(0,b.yellowBright)("plan")}  - `),r&&(Uo=!1)}var Me=class extends B.Command{static baseFlags={"exit-on-error":B.Flags.boolean({default:!0,helpGroup:"GLOBAL",aliases:["exitOnError"],deprecateAliases:!0,summary:"Always exit with non-zero code on error",allowNo:!0}),emoji:B.Flags.boolean({default:!1,helpGroup:"GLOBAL",summary:"Prefix output with emojis \u{1F308}"}),help:B.Flags.boolean({default:!1,helpGroup:"GLOBAL",summary:"Show the help message for the command"}),"non-interactive":B.Flags.boolean({char:"I",default:!1,helpGroup:"GLOBAL",summary:"Disable interactive outputs, useful for CI"}),target:B.Flags.option({char:"t",description:"Raycast app target",helpGroup:"GLOBAL",multiple:!1,options:["debug","internal","release","x","x-development","x-internal"],hidden:!0})()};flags;args;async init(){await super.init(),process.on("SIGINT",()=>process.exit(1));let{args:r,flags:t}=await this.parse({flags:this.ctor.flags,baseFlags:super.ctor.baseFlags,enableJsonFlag:this.ctor.enableJsonFlag,args:this.ctor.args,strict:this.ctor.strict});this.flags=t,this.args=r,ys(this.flags.target),Es(this.flags.emoji,this.flags["non-interactive"])}error(r,t){return t?.message&&r instanceof Error&&(r.message=`${t.message} (${r.message})`,delete t.message),super.error(r,t)}async catch(r){return super.catch(r)}async finally(r){return super.finally(r)}};var Ps=require("@oclif/core");var Ko=require("@oclif/core");var Mo=z(_s());var ic=`${ye.url}/sessions/success`,oc=`${ye.url}/sessions/failure`;function $s(){we("raycastAccessToken")===""&&Ps.ux.error("please first log in first using `npx ray login`",{exit:1})}var tr=class e extends Me{static enableJsonFlag=!0;static description="Display the access token";static flags={clipboard:ks.Flags.boolean({char:"C",description:"Copy the token to the clipboard",default:!1})};async run(){let{flags:r}=await this.parse(e);$s();let t=we("raycastAccessToken");r.json?this.logJson({Token:t}):xs({Token:t}),r.clipboard&&(await Ns.default.write(t),r.json||Ur("success","copied token to clipboard"))}};
/*! Bundled license information:

arch/index.js:
  (*! arch. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> *)
*/
