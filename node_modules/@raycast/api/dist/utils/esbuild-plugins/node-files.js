"use strict";var c=Object.create;var r=Object.defineProperty;var x=Object.getOwnPropertyDescriptor;var h=Object.getOwnPropertyNames;var m=Object.getPrototypeOf,j=Object.prototype.hasOwnProperty;var u=(s,e)=>{for(var t in e)r(s,t,{get:e[t],enumerable:!0})},f=(s,e,t,l)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of h(e))!j.call(s,n)&&n!==t&&r(s,n,{get:()=>e[n],enumerable:!(l=x(e,n))||l.enumerable});return s};var p=(s,e,t)=>(t=s!=null?c(m(s)):{},f(e||!s||!s.__esModule?r(t,"default",{value:s,enumerable:!0}):t,s)),v=s=>f(r({},"__esModule",{value:!0}),s);var d={};u(d,{nodeFiles:()=>a});module.exports=v(d);var i=p(require("node:fs")),o=p(require("node:path")),a={name:"node-files",setup(s){s.onResolve({filter:/\.node$/},e=>i.existsSync(o.default.join(e.resolveDir,e.path+".js"))||i.existsSync(o.default.join(e.resolveDir,e.path+".ts"))||i.existsSync(o.default.join(e.resolveDir,e.path+".tsx"))||i.existsSync(o.default.join(e.resolveDir,e.path+".jsx"))||i.existsSync(o.default.join(e.resolveDir,e.path+".json"))||i.existsSync(o.default.join(e.resolveDir,e.path+".css"))||i.existsSync(o.default.join(e.resolveDir,e.path+".wasm"))||i.existsSync(o.default.join(e.resolveDir,e.path+".swift"))?{}:{errors:[{text:"native bindings (e.g., .node files) are not supported because they are neither cross-platform nor cross-architecture; for example, native bindings built on a M1 mac would not work on an Intel mac"}]})}};0&&(module.exports={nodeFiles});
