"use strict";var C=Object.create;var f=Object.defineProperty;var v=Object.getOwnPropertyDescriptor;var _=Object.getOwnPropertyNames;var k=Object.getPrototypeOf,P=Object.prototype.hasOwnProperty;var H=(r,t)=>()=>(t||r((t={exports:{}}).exports,t),t.exports),R=(r,t)=>{for(var i in t)f(r,i,{get:t[i],enumerable:!0})},m=(r,t,i,u)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of _(t))!P.call(r,a)&&a!==i&&f(r,a,{get:()=>t[a],enumerable:!(u=v(t,a))||u.enumerable});return r};var d=(r,t,i)=>(i=r!=null?C(k(r)):{},m(t||!r||!r.__esModule?f(i,"default",{value:r,enumerable:!0}):i,r)),A=r=>m(f({},"__esModule",{value:!0}),r);var x=H((mr,$)=>{var T=require("node:tty"),D=T?.WriteStream?.prototype?.hasColors?.()??!1,n=(r,t)=>{if(!D)return a=>a;let i=`\x1B[${r}m`,u=`\x1B[${t}m`;return a=>{let g=a+"",c=g.indexOf(u);if(c===-1)return i+g+u;let h=i,l=0;for(;c!==-1;)h+=g.slice(l,c)+i,l=c+u.length,c=g.indexOf(u,l);return h+=g.slice(l)+u,h}},e={};e.reset=n(0,0);e.bold=n(1,22);e.dim=n(2,22);e.italic=n(3,23);e.underline=n(4,24);e.overline=n(53,55);e.inverse=n(7,27);e.hidden=n(8,28);e.strikethrough=n(9,29);e.black=n(30,39);e.red=n(31,39);e.green=n(32,39);e.yellow=n(33,39);e.blue=n(34,39);e.magenta=n(35,39);e.cyan=n(36,39);e.white=n(37,39);e.gray=n(90,39);e.bgBlack=n(40,49);e.bgRed=n(41,49);e.bgGreen=n(42,49);e.bgYellow=n(43,49);e.bgBlue=n(44,49);e.bgMagenta=n(45,49);e.bgCyan=n(46,49);e.bgWhite=n(47,49);e.bgGray=n(100,49);e.redBright=n(91,39);e.greenBright=n(92,39);e.yellowBright=n(93,39);e.blueBright=n(94,39);e.magentaBright=n(95,39);e.cyanBright=n(96,39);e.whiteBright=n(97,39);e.bgRedBright=n(101,49);e.bgGreenBright=n(102,49);e.bgYellowBright=n(103,49);e.bgBlueBright=n(104,49);e.bgMagentaBright=n(105,49);e.bgCyanBright=n(106,49);e.bgWhiteBright=n(107,49);$.exports=e});var pr={};R(pr,{CurrentBranch:()=>I,FirstCommit:()=>W,PreviousBranch:()=>S,changeWorkingDirectory:()=>L,checkout:()=>K,checkoutOrCreate:()=>Q,clean:()=>ir,clone:()=>Y,commitEverything:()=>gr,commitPath:()=>ur,deleteBranch:()=>b,deleteLocalAndRemoteBranch:()=>V,didFileChangedSince:()=>or,fetch:()=>rr,getCommitSha:()=>N,getRelativePath:()=>M,getRemote:()=>F,hardReset:()=>tr,hasRemoteBranch:()=>X,hasTag:()=>nr,hasUncommittedChanges:()=>j,isGitInstalled:()=>O,isInsideWorkTree:()=>U,isLocalHeadAheadOfRemote:()=>er,listCommits:()=>hr,merge:()=>lr,pull:()=>cr,push:()=>sr,readConfig:()=>z,setLocalConfig:()=>Z,setRemote:()=>q,tagCurrentCommit:()=>ar,tags:()=>fr,useSparseCheckoutCone:()=>J});module.exports=A(pr);var p=require("child_process");var s=d(x());var dr=(0,s.blue)((0,s.dim)("internal only"));function E(r,t,i){console.log(G[r]+t),typeof i?.exit<"u"&&process.exit(i.exit)}var G={wait:`\u{1F550}${(0,s.blue)("wait")}  - `,info:`${process.env.GITHUB_ACTIONS==="true"?"\u2139\uFE0F":"\u2139\uFE0F "}${(0,s.cyan)("info")}  - `,success:`\u2705${(0,s.green)("ready")}  - `,warn:`${process.env.GITHUB_ACTIONS==="true"?"\u26A0\uFE0F":"\u26A0\uFE0F "}${(0,s.yellow)("warn")}  - `,error:`\u{1F4A5}${(0,s.red)("error")}  - `,event:`\u26A1\uFE0F${(0,s.magenta)("event")}  - `,paymentPrompt:`\u{1F4B0}${(0,s.yellowBright)("plan")}  - `};var y=d(require("path")),I=".",S="-",W="`git rev-list --max-parents=0 HEAD | tail -n 1`",w;function L(r){w=r}function o(r,t){return new Promise((i,u)=>{(0,p.exec)(r,{cwd:w},(a,g,c)=>{if(t.throwOnError&&a){u(new Error(`failed running git ${a.message.replace(/oauth2:gho_[a-zA-Z0-9]+@/g,"oauth2:gho_xxxxx")}
${g.trim()}
${c.trim()}`));return}i(g.trim())})})}function O(){return new Promise(r=>{(0,p.exec)("git --version",t=>{r(t===null)})})}async function U(){return await O()?await o("git rev-parse --is-inside-work-tree",{throwOnError:!1})==="true":(E("info","git is not installed"),!1)}async function j(){return await o("git status -s",{throwOnError:!0})!==""}async function F(){return await B()?(await o("git remote get-url origin",{throwOnError:!1})).replace(/\.[^./]+$/,""):""}function M(){return o("git rev-parse --show-prefix",{throwOnError:!0})}async function N(){return await B()?o("git rev-parse HEAD",{throwOnError:!0}):""}async function Y(r,t){w=y.default.dirname(t),await o(`git clone --filter=blob:none --no-checkout ${r} "${t}"`,{throwOnError:!0})}async function q(r,t){try{await o(`git remote set-url ${r} ${t}`,{throwOnError:!0})}catch{await o(`git remote add ${r} ${t}`,{throwOnError:!0})}}function z(r){return o(`git config --get ${r}`,{throwOnError:!0})}function Z(r,t){return o(`git config --local ${r} "${t}"`,{throwOnError:!0})}function J(r){return o(`git sparse-checkout set ${r}`,{throwOnError:!0})}function K(r){return o(`git checkout ${r}`,{throwOnError:!0})}function Q(r){return o(`git checkout -b ${r} || git checkout ${r}`,{throwOnError:!0})}function b(r){return o(`git branch -D ${r}`,{throwOnError:!1})}async function V(r){return await b(r),o(`git push origin -d ${r}`,{throwOnError:!1})}async function X(r){return await o(`git ls-remote --heads origin ${r}`,{throwOnError:!1})!==""}function rr(r,t){return o(`git fetch ${r} ${t} --depth=1`,{throwOnError:!1})}function tr(r){return o(`git reset --hard ${r}`,{throwOnError:!0})}async function er(r){let t=await o(`git rev-parse ${r}`,{throwOnError:!0}),i=await o(`git rev-parse origin/${r}`,{throwOnError:!0});return await o(`git merge-base --is-ancestor ${i} ${t} && echo "success"`,{throwOnError:!1})==="success"}async function nr(r){return await o(`git tag -l ${r}`,{throwOnError:!1})!==""}async function or(r,t){return await o(`git diff HEAD..${t} -- "${r}"`,{throwOnError:!0})!==""}function ir(){return o("git clean -fd",{throwOnError:!0})}function sr(r){return o(`git push -u origin ${r}`,{throwOnError:!0})}function ar(r,t){return o(`git tag -f -a ${r} -m "${t}"`,{throwOnError:!0})}function ur(r,t){return o(`git add "${r}" && git commit -m "${t}"`,{throwOnError:!0})}function gr(r){return o(`git add . && git commit -m "${r}"`,{throwOnError:!0})}function cr(r){return o(`git pull origin ${r} --ff`,{throwOnError:!0})}function lr(r){return o(`git merge ${r}`,{throwOnError:!0})}function fr(r){return{latestPullTag:"__raycast_latest_pull_"+r+"__",latestPublishTag:"__raycast_latest_publish_"+r+"__"}}async function hr(r){return(await o(`git log --pretty=format:%s${r?` HEAD...${r}`:""}`,{throwOnError:!0})).split(`
`)}async function B(){try{return await o("git remote get-url origin",{throwOnError:!1}),!0}catch{return!1}}0&&(module.exports={CurrentBranch,FirstCommit,PreviousBranch,changeWorkingDirectory,checkout,checkoutOrCreate,clean,clone,commitEverything,commitPath,deleteBranch,deleteLocalAndRemoteBranch,didFileChangedSince,fetch,getCommitSha,getRelativePath,getRemote,hardReset,hasRemoteBranch,hasTag,hasUncommittedChanges,isGitInstalled,isInsideWorkTree,isLocalHeadAheadOfRemote,listCommits,merge,pull,push,readConfig,setLocalConfig,setRemote,tagCurrentCommit,tags,useSparseCheckoutCone});
