"use strict";var m=Object.defineProperty;var t=Object.getOwnPropertyDescriptor;var u=Object.getOwnPropertyNames;var x=Object.prototype.hasOwnProperty;var a=(o,r)=>{for(var f in r)m(o,f,{get:r[f],enumerable:!0})},b=(o,r,f,n)=>{if(r&&typeof r=="object"||typeof r=="function")for(let e of u(r))!x.call(o,e)&&e!==f&&m(o,e,{get:()=>r[e],enumerable:!(n=t(r,e))||n.enumerable});return o};var c=o=>b(m({},"__esModule",{value:!0}),o);var d={};a(d,{run:()=>p.run});module.exports=c(d);var p=require("@oclif/core");0&&(module.exports={run});
