"use strict";var de=Object.create;var A=Object.defineProperty;var ge=Object.getOwnPropertyDescriptor;var me=Object.getOwnPropertyNames;var he=Object.getPrototypeOf,ye=Object.prototype.hasOwnProperty;var y=(e,r)=>()=>(r||e((r={exports:{}}).exports,r),r.exports),we=(e,r)=>{for(var t in r)A(e,t,{get:r[t],enumerable:!0})},U=(e,r,t,i)=>{if(r&&typeof r=="object"||typeof r=="function")for(let s of me(r))!ye.call(e,s)&&s!==t&&A(e,s,{get:()=>r[s],enumerable:!(i=ge(r,s))||i.enumerable});return e};var d=(e,r,t)=>(t=e!=null?de(he(e)):{},U(r||!e||!e.__esModule?A(t,"default",{value:e,enumerable:!0}):t,e)),xe=e=>U(A({},"__esModule",{value:!0}),e);var J=y((ze,H)=>{var ke=require("node:tty"),Ce=ke?.WriteStream?.prototype?.hasColors?.()??!1,o=(e,r)=>{if(!Ce)return s=>s;let t=`\x1B[${e}m`,i=`\x1B[${r}m`;return s=>{let a=s+"",c=a.indexOf(i);if(c===-1)return t+a+i;let f=t,u=0;for(;c!==-1;)f+=a.slice(u,c)+t,u=c+i.length,c=a.indexOf(i,u);return f+=a.slice(u)+i,f}},n={};n.reset=o(0,0);n.bold=o(1,22);n.dim=o(2,22);n.italic=o(3,23);n.underline=o(4,24);n.overline=o(53,55);n.inverse=o(7,27);n.hidden=o(8,28);n.strikethrough=o(9,29);n.black=o(30,39);n.red=o(31,39);n.green=o(32,39);n.yellow=o(33,39);n.blue=o(34,39);n.magenta=o(35,39);n.cyan=o(36,39);n.white=o(37,39);n.gray=o(90,39);n.bgBlack=o(40,49);n.bgRed=o(41,49);n.bgGreen=o(42,49);n.bgYellow=o(43,49);n.bgBlue=o(44,49);n.bgMagenta=o(45,49);n.bgCyan=o(46,49);n.bgWhite=o(47,49);n.bgGray=o(100,49);n.redBright=o(91,39);n.greenBright=o(92,39);n.yellowBright=o(93,39);n.blueBright=o(94,39);n.magentaBright=o(95,39);n.cyanBright=o(96,39);n.whiteBright=o(97,39);n.bgRedBright=o(101,49);n.bgGreenBright=o(102,49);n.bgYellowBright=o(103,49);n.bgBlueBright=o(104,49);n.bgMagentaBright=o(105,49);n.bgCyanBright=o(106,49);n.bgWhiteBright=o(107,49);H.exports=n});var O=y((er,Z)=>{"use strict";var z=require("fs"),B;function $e(){try{return z.statSync("/.dockerenv"),!0}catch{return!1}}function Pe(){try{return z.readFileSync("/proc/self/cgroup","utf8").includes("docker")}catch{return!1}}Z.exports=()=>(B===void 0&&(B=$e()||Pe()),B)});var re=y((rr,F)=>{"use strict";var Se=require("os"),Te=require("fs"),Q=O(),ee=()=>{if(process.platform!=="linux")return!1;if(Se.release().toLowerCase().includes("microsoft"))return!Q();try{return Te.readFileSync("/proc/version","utf8").toLowerCase().includes("microsoft")?!Q():!1}catch{return!1}};process.env.__IS_WSL_TEST__?F.exports=ee:F.exports=ee()});var ne=y((tr,te)=>{"use strict";te.exports=(e,r,t)=>{let i=s=>Object.defineProperty(e,r,{value:s,enumerable:!0,writable:!0});return Object.defineProperty(e,r,{configurable:!0,enumerable:!0,get(){let s=t();return i(s),s},set(s){i(s)}}),e}});var le=y((nr,ue)=>{var De=require("path"),Ne=require("child_process"),{promises:$,constants:ce}=require("fs"),v=re(),Be=O(),_=ne(),oe=De.join(__dirname,"xdg-open"),{platform:h,arch:se}=process,Oe=()=>{try{return $.statSync("/run/.containerenv"),!0}catch{return!1}},R;function Fe(){return R===void 0&&(R=Oe()||Be()),R}var Re=(()=>{let e="/mnt/",r;return async function(){if(r)return r;let t="/etc/wsl.conf",i=!1;try{await $.access(t,ce.F_OK),i=!0}catch{}if(!i)return e;let s=await $.readFile(t,{encoding:"utf8"}),a=/(?<!#.*)root\s*=\s*(?<mountPoint>.*)/g.exec(s);return a?(r=a.groups.mountPoint.trim(),r=r.endsWith("/")?r:`${r}/`,r):e}})(),ie=async(e,r)=>{let t;for(let i of e)try{return await r(i)}catch(s){t=s}throw t},P=async e=>{if(e={wait:!1,background:!1,newInstance:!1,allowNonzeroExitCode:!1,...e},Array.isArray(e.app))return ie(e.app,f=>P({...e,app:f}));let{name:r,arguments:t=[]}=e.app||{};if(t=[...t],Array.isArray(r))return ie(r,f=>P({...e,app:{name:f,arguments:t}}));let i,s=[],a={};if(h==="darwin")i="open",e.wait&&s.push("--wait-apps"),e.background&&s.push("--background"),e.newInstance&&s.push("--new"),r&&s.push("-a",r);else if(h==="win32"||v&&!Fe()&&!r){let f=await Re();i=v?`${f}c/Windows/System32/WindowsPowerShell/v1.0/powershell.exe`:`${process.env.SYSTEMROOT}\\System32\\WindowsPowerShell\\v1.0\\powershell`,s.push("-NoProfile","-NonInteractive","\u2013ExecutionPolicy","Bypass","-EncodedCommand"),v||(a.windowsVerbatimArguments=!0);let u=["Start"];e.wait&&u.push("-Wait"),r?(u.push(`"\`"${r}\`""`,"-ArgumentList"),e.target&&t.unshift(e.target)):e.target&&u.push(`"${e.target}"`),t.length>0&&(t=t.map(m=>`"\`"${m}\`""`),u.push(t.join(","))),e.target=Buffer.from(u.join(" "),"utf16le").toString("base64")}else{if(r)i=r;else{let f=!__dirname||__dirname==="/",u=!1;try{await $.access(oe,ce.X_OK),u=!0}catch{}i=process.versions.electron||h==="android"||f||!u?"xdg-open":oe}t.length>0&&s.push(...t),e.wait||(a.stdio="ignore",a.detached=!0)}e.target&&s.push(e.target),h==="darwin"&&t.length>0&&s.push("--args",...t);let c=Ne.spawn(i,s,a);return e.wait?new Promise((f,u)=>{c.once("error",u),c.once("close",m=>{if(!e.allowNonzeroExitCode&&m>0){u(new Error(`Exited with code ${m}`));return}f(c)})}):(c.unref(),c)},G=(e,r)=>{if(typeof e!="string")throw new TypeError("Expected a `target`");return P({...r,target:e})},_e=(e,r)=>{if(typeof e!="string")throw new TypeError("Expected a `name`");let{arguments:t=[]}=r||{};if(t!=null&&!Array.isArray(t))throw new TypeError("Expected `appArguments` as Array type");return P({...r,app:{name:e,arguments:t}})};function ae(e){if(typeof e=="string"||Array.isArray(e))return e;let{[se]:r}=e;if(!r)throw new Error(`${se} is not supported`);return r}function M({[h]:e},{wsl:r}){if(r&&v)return ae(r);if(!e)throw new Error(`${h} is not supported`);return ae(e)}var S={};_(S,"chrome",()=>M({darwin:"google chrome",win32:"chrome",linux:["google-chrome","google-chrome-stable","chromium"]},{wsl:{ia32:"/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe",x64:["/mnt/c/Program Files/Google/Chrome/Application/chrome.exe","/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe"]}}));_(S,"firefox",()=>M({darwin:"firefox",win32:"C:\\Program Files\\Mozilla Firefox\\firefox.exe",linux:"firefox"},{wsl:"/mnt/c/Program Files/Mozilla Firefox/firefox.exe"}));_(S,"edge",()=>M({darwin:"microsoft edge",win32:"msedge",linux:["microsoft-edge","microsoft-edge-dev"]},{wsl:"/mnt/c/Program Files (x86)/Microsoft/Edge/Application/msedge.exe"}));G.apps=S;G.openApp=_e;ue.exports=G});var qe={};we(qe,{commFilePath:()=>L,notifyRaycast:()=>Ge,updatePid:()=>pe});module.exports=xe(qe);var I=require("node:child_process"),p=d(require("node:fs")),E=d(require("node:path"));var C=d(require("node:fs")),T=d(require("node:path")),K=d(require("node:os"));var q=d(require("node:path")),W=d(require("node:fs"));function k(){let e;try{e=q.resolve("package.json")}catch(t){throw new Error(`cannot resolve package manifest path: ${t}`)}let r;try{r=JSON.parse(W.readFileSync(e,"utf8"))}catch(t){throw new Error(`cannot read package manifest: ${t}`)}return r.name=r.name.replace(/^@workaround/g,""),r}var Y={production:{url:"https://www.raycast.com",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},staging:{url:"https://www.rayca.st",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},development:{url:"http://localhost:3000",clientID:"rOCD_LhnXFObSLu9v7788qOGu25FZDqEzGjet4Q45ZY"}}[process.env.API_ENV||"production"],be="e69bae0ec90f5e838555",g={},D;function x(){let e=k();if(!e.name)throw new Error("extension name in manifest cannot be empty");return T.join(Ae(),"extensions",e.name)}function V(){let e=w(D);return e?.startsWith("x")?e==="x"?"com.raycast-x.macos":`com.raycast-x.macos.${e.replace("x-","")}`:e?`com.raycast.macos.${e}`:"com.raycast.macos"}function X(){let e=w(D);return e==="x"?"raycast":`raycast${e}`}function Ie(e){switch(e){case"raycastApiURL":return process.env.RAY_APIURL||g.APIURL||Y.url;case"raycastAccessToken":return process.env.RAY_TOKEN||g.Token||g.AccessToken||"";case"raycastClientId":return process.env.RAY_CLIENT_ID||g.ClientID||Y.clientID;case"githubClientId":return process.env.RAY_GithubClientID||g.GithubClientID||be;case"githubAccessToken":return process.env.GITHUB_ACCESS_TOKEN||g.GithubAccessToken||"";case"flavorName":return process.env.RAY_Target?process.env.RAY_Target:typeof g.Target<"u"?g.Target:w(process.platform==="win32"?"x":"release")}}function w(e){switch(e){case"debug":return"debug";case"internal":return"internal";case"release":return"";case"x":return"x";case"x-internal":return"x-internal";case"x-development":return"x-development";case void 0:return Ie("flavorName")}}function Ee(){let e=w(D);return e==""?"raycast":`raycast-${e}`}function Ae(){let e=T.join(K.default.homedir(),".config",Ee());return C.mkdirSync(e,{recursive:!0}),e}var l=d(J());var Ze=(0,l.blue)((0,l.dim)("internal only"));function N(e,r,t){console.log(ve[e]+r),typeof t?.exit<"u"&&process.exit(t.exit)}var ve={wait:`\u{1F550}${(0,l.blue)("wait")}  - `,info:`${process.env.GITHUB_ACTIONS==="true"?"\u2139\uFE0F":"\u2139\uFE0F "}${(0,l.cyan)("info")}  - `,success:`\u2705${(0,l.green)("ready")}  - `,warn:`${process.env.GITHUB_ACTIONS==="true"?"\u26A0\uFE0F":"\u26A0\uFE0F "}${(0,l.yellow)("warn")}  - `,error:`\u{1F4A5}${(0,l.red)("error")}  - `,event:`\u26A1\uFE0F${(0,l.magenta)("event")}  - `,paymentPrompt:`\u{1F4B0}${(0,l.yellowBright)("plan")}  - `};var fe=d(le());async function Ge(e,r){let t=!1;switch(e){case"start":{Le(),pe(),Me();break}case"stop":{try{p.unlinkSync(L("log"))}catch{}try{p.unlinkSync(L("pid"))}catch{}break}default:t=!0}let i;try{i=k()}catch{return}Ue()||N("warn","Raycast is not running"),t?je(i.name,e,r):await j(i.name,e,r)}function L(e){return E.join(x(),e==="log"?"dev.log":"cli.pid")}function pe(){let e=x();try{p.mkdirSync(e,{recursive:!0})}catch(t){throw new Error(`cannot create output directory (${t.message})`)}let r=process.pid.toString();try{p.writeFileSync(E.join(e,"cli.pid"),r,{mode:420})}catch(t){throw new Error(`cannot write pid file (${t.message})`)}}function Me(){let e=x();try{p.mkdirSync(e,{recursive:!0})}catch(r){throw new Error(`cannot create output directory (${r.message})`)}try{p.writeFileSync(E.join(e,"dev.log"),"")}catch(r){throw new Error(`cannot write log file (${r.message})`)}}function Le(){let e=E.join(x(),"cli.pid");try{let t=p.readFileSync(e,"utf8").trim();process.platform==="win32"?(0,I.execSync)(`taskkill /F /PID ${t}`):(0,I.execSync)(`kill -9 ${t}`,{shell:"/bin/sh"})}catch{}}var b=null;function je(e,r,t){b?(clearTimeout(b),b=setTimeout(()=>{j(e,r,t)},300)):(j(e,r,t),b=setTimeout(()=>{b=null},300))}async function j(e,r,t){let i=process.cwd(),s=`${X()}://cli/${e}/${r}?cwd=${encodeURIComponent(i)}&info=${encodeURIComponent(t||"")}`;try{await(0,fe.default)(s,{background:!0})}catch(a){N("error",`cannot notify Raycast (${a.message})`)}}function Ue(){if(process.platform==="win32")return(0,I.execSync)('tasklist /FI "IMAGENAME eq Raycast.exe" /FO CSV /NH',{encoding:"utf8"}).toLowerCase().includes("raycast.exe");{let r=V(),t="ps -e -o args | grep -v grep | grep -i contents/macos/raycast",i;try{i=(0,I.execSync)(t,{shell:"/bin/sh",encoding:"utf8"})}catch{return!1}let s=i.trim();if(s==="")return!1;let a=s.split(`
`);for(let c of a){let f=c.indexOf("/Contents/MacOS/Raycast");if(f>-1&&(c=c.substring(0,f)),!!p.existsSync(c))return!0}}return!1}0&&(module.exports={commFilePath,notifyRaycast,updatePid});
