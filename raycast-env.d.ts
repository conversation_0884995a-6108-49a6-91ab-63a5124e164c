/// <reference types="@raycast/api">

/* 🚧 🚧 🚧
 * This file is auto-generated from the extension's manifest.
 * Do not modify manually. Instead, update the `package.json` file.
 * 🚧 🚧 🚧 */

/* eslint-disable @typescript-eslint/ban-types */

type ExtensionPreferences = {}

/** Preferences accessible in all the extension's commands */
declare type Preferences = ExtensionPreferences

declare namespace Preferences {
  /** Preferences accessible in the `record` command */
  export type Record = ExtensionPreferences & {}
  /** Preferences accessible in the `show-records` command */
  export type ShowRecords = ExtensionPreferences & {}
}

declare namespace Arguments {
  /** Arguments passed to the `record` command */
  export type Record = {
  /** What are you working on? */
  "content": string
}
  /** Arguments passed to the `show-records` command */
  export type ShowRecords = {}
}

