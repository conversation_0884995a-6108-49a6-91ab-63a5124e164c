"use strict";var FF=Object.create;var us=Object.defineProperty;var CF=Object.getOwnPropertyDescriptor;var TF=Object.getOwnPropertyNames;var RF=Object.getPrototypeOf,AF=Object.prototype.hasOwnProperty;var C=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),$F=(e,t)=>{for(var r in t)us(e,r,{get:t[r],enumerable:!0})},Wm=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of TF(t))!AF.call(e,o)&&o!==r&&us(e,o,{get:()=>t[o],enumerable:!(n=CF(t,o))||n.enumerable});return e};var N=(e,t,r)=>(r=e!=null?FF(RF(e)):{},Wm(t||!e||!e.__esModule?us(r,"default",{value:e,enumerable:!0}):r,e)),xF=e=>Wm(us({},"__esModule",{value:!0}),e);var Rh=C((ZM,Th)=>{Th.exports=Ch;Ch.sync=vC;var vh=require("fs");function EC(e,t){var r=t.pathExt!==void 0?t.pathExt:process.env.PATHEXT;if(!r||(r=r.split(";"),r.indexOf("")!==-1))return!0;for(var n=0;n<r.length;n++){var o=r[n].toLowerCase();if(o&&e.substr(-o.length).toLowerCase()===o)return!0}return!1}function Fh(e,t,r){return!e.isSymbolicLink()&&!e.isFile()?!1:EC(t,r)}function Ch(e,t,r){vh.stat(e,function(n,o){r(n,n?!1:Fh(o,e,t))})}function vC(e,t){return Fh(vh.statSync(e),e,t)}});var Oh=C((eN,Ph)=>{Ph.exports=$h;$h.sync=FC;var Ah=require("fs");function $h(e,t,r){Ah.stat(e,function(n,o){r(n,n?!1:xh(o,t))})}function FC(e,t){return xh(Ah.statSync(e),t)}function xh(e,t){return e.isFile()&&CC(e,t)}function CC(e,t){var r=e.mode,n=e.uid,o=e.gid,i=t.uid!==void 0?t.uid:process.getuid&&process.getuid(),a=t.gid!==void 0?t.gid:process.getgid&&process.getgid(),l=parseInt("100",8),c=parseInt("010",8),d=parseInt("001",8),p=l|c,m=r&d||r&c&&o===a||r&l&&n===i||r&p&&i===0;return m}});var Ih=C((rN,Bh)=>{var tN=require("fs"),Ss;process.platform==="win32"||global.TESTING_WINDOWS?Ss=Rh():Ss=Oh();Bh.exports=Sl;Sl.sync=TC;function Sl(e,t,r){if(typeof t=="function"&&(r=t,t={}),!r){if(typeof Promise!="function")throw new TypeError("callback not provided");return new Promise(function(n,o){Sl(e,t||{},function(i,a){i?o(i):n(a)})})}Ss(e,t||{},function(n,o){n&&(n.code==="EACCES"||t&&t.ignoreErrors)&&(n=null,o=!1),r(n,o)})}function TC(e,t){try{return Ss.sync(e,t||{})}catch(r){if(t&&t.ignoreErrors||r.code==="EACCES")return!1;throw r}}});var Uh=C((nN,qh)=>{var ln=process.platform==="win32"||process.env.OSTYPE==="cygwin"||process.env.OSTYPE==="msys",kh=require("path"),RC=ln?";":":",Mh=Ih(),Nh=e=>Object.assign(new Error(`not found: ${e}`),{code:"ENOENT"}),jh=(e,t)=>{let r=t.colon||RC,n=e.match(/\//)||ln&&e.match(/\\/)?[""]:[...ln?[process.cwd()]:[],...(t.path||process.env.PATH||"").split(r)],o=ln?t.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM":"",i=ln?o.split(r):[""];return ln&&e.indexOf(".")!==-1&&i[0]!==""&&i.unshift(""),{pathEnv:n,pathExt:i,pathExtExe:o}},Lh=(e,t,r)=>{typeof t=="function"&&(r=t,t={}),t||(t={});let{pathEnv:n,pathExt:o,pathExtExe:i}=jh(e,t),a=[],l=d=>new Promise((p,m)=>{if(d===n.length)return t.all&&a.length?p(a):m(Nh(e));let b=n[d],D=/^".*"$/.test(b)?b.slice(1,-1):b,g=kh.join(D,e),y=!D&&/^\.[\\\/]/.test(e)?e.slice(0,2)+g:g;p(c(y,d,0))}),c=(d,p,m)=>new Promise((b,D)=>{if(m===o.length)return b(l(p+1));let g=o[m];Mh(d+g,{pathExt:i},(y,S)=>{if(!y&&S)if(t.all)a.push(d+g);else return b(d+g);return b(c(d,p,m+1))})});return r?l(0).then(d=>r(null,d),r):l(0)},AC=(e,t)=>{t=t||{};let{pathEnv:r,pathExt:n,pathExtExe:o}=jh(e,t),i=[];for(let a=0;a<r.length;a++){let l=r[a],c=/^".*"$/.test(l)?l.slice(1,-1):l,d=kh.join(c,e),p=!c&&/^\.[\\\/]/.test(e)?e.slice(0,2)+d:d;for(let m=0;m<n.length;m++){let b=p+n[m];try{if(Mh.sync(b,{pathExt:o}))if(t.all)i.push(b);else return b}catch{}}}if(t.all&&i.length)return i;if(t.nothrow)return null;throw Nh(e)};qh.exports=Lh;Lh.sync=AC});var Wh=C((oN,El)=>{"use strict";var zh=(e={})=>{let t=e.env||process.env;return(e.platform||process.platform)!=="win32"?"PATH":Object.keys(t).reverse().find(n=>n.toUpperCase()==="PATH")||"Path"};El.exports=zh;El.exports.default=zh});var Yh=C((iN,Hh)=>{"use strict";var Vh=require("path"),$C=Uh(),xC=Wh();function Gh(e,t){let r=e.options.env||process.env,n=process.cwd(),o=e.options.cwd!=null,i=o&&process.chdir!==void 0&&!process.chdir.disabled;if(i)try{process.chdir(e.options.cwd)}catch{}let a;try{a=$C.sync(e.command,{path:r[xC({env:r})],pathExt:t?Vh.delimiter:void 0})}catch{}finally{i&&process.chdir(n)}return a&&(a=Vh.resolve(o?e.options.cwd:"",a)),a}function PC(e){return Gh(e)||Gh(e,!0)}Hh.exports=PC});var Kh=C((sN,Fl)=>{"use strict";var vl=/([()\][%!^"`<>&|;, *?])/g;function OC(e){return e=e.replace(vl,"^$1"),e}function BC(e,t){return e=`${e}`,e=e.replace(/(?=(\\+?)?)\1"/g,'$1$1\\"'),e=e.replace(/(?=(\\+?)?)\1$/,"$1$1"),e=`"${e}"`,e=e.replace(vl,"^$1"),t&&(e=e.replace(vl,"^$1")),e}Fl.exports.command=OC;Fl.exports.argument=BC});var Qh=C((aN,Jh)=>{"use strict";Jh.exports=/^#!(.*)/});var Zh=C((uN,Xh)=>{"use strict";var IC=Qh();Xh.exports=(e="")=>{let t=e.match(IC);if(!t)return null;let[r,n]=t[0].replace(/#! ?/,"").split(" "),o=r.split("/").pop();return o==="env"?n:n?`${o} ${n}`:o}});var tg=C((lN,eg)=>{"use strict";var Cl=require("fs"),kC=Zh();function MC(e){let r=Buffer.alloc(150),n;try{n=Cl.openSync(e,"r"),Cl.readSync(n,r,0,150,0),Cl.closeSync(n)}catch{}return kC(r.toString())}eg.exports=MC});var ig=C((cN,og)=>{"use strict";var NC=require("path"),rg=Yh(),ng=Kh(),jC=tg(),LC=process.platform==="win32",qC=/\.(?:com|exe)$/i,UC=/node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;function zC(e){e.file=rg(e);let t=e.file&&jC(e.file);return t?(e.args.unshift(e.file),e.command=t,rg(e)):e.file}function WC(e){if(!LC)return e;let t=zC(e),r=!qC.test(t);if(e.options.forceShell||r){let n=UC.test(t);e.command=NC.normalize(e.command),e.command=ng.command(e.command),e.args=e.args.map(i=>ng.argument(i,n));let o=[e.command].concat(e.args).join(" ");e.args=["/d","/s","/c",`"${o}"`],e.command=process.env.comspec||"cmd.exe",e.options.windowsVerbatimArguments=!0}return e}function VC(e,t,r){t&&!Array.isArray(t)&&(r=t,t=null),t=t?t.slice(0):[],r=Object.assign({},r);let n={command:e,args:t,options:r,file:void 0,original:{command:e,args:t}};return r.shell?n:WC(n)}og.exports=VC});var ug=C((fN,ag)=>{"use strict";var Tl=process.platform==="win32";function Rl(e,t){return Object.assign(new Error(`${t} ${e.command} ENOENT`),{code:"ENOENT",errno:"ENOENT",syscall:`${t} ${e.command}`,path:e.command,spawnargs:e.args})}function GC(e,t){if(!Tl)return;let r=e.emit;e.emit=function(n,o){if(n==="exit"){let i=sg(o,t);if(i)return r.call(e,"error",i)}return r.apply(e,arguments)}}function sg(e,t){return Tl&&e===1&&!t.file?Rl(t.original,"spawn"):null}function HC(e,t){return Tl&&e===1&&!t.file?Rl(t.original,"spawnSync"):null}ag.exports={hookChildProcess:GC,verifyENOENT:sg,verifyENOENTSync:HC,notFoundError:Rl}});var fg=C((dN,cn)=>{"use strict";var lg=require("child_process"),Al=ig(),$l=ug();function cg(e,t,r){let n=Al(e,t,r),o=lg.spawn(n.command,n.args,n.options);return $l.hookChildProcess(o,n),o}function YC(e,t,r){let n=Al(e,t,r),o=lg.spawnSync(n.command,n.args,n.options);return o.error=o.error||$l.verifyENOENTSync(o.status,n),o}cn.exports=cg;cn.exports.spawn=cg;cn.exports.sync=YC;cn.exports._parse=Al;cn.exports._enoent=$l});var xb=C(Xc=>{"use strict";var $b={b:"\b",f:"\f",n:`
`,r:"\r",t:"	",'"':'"',"/":"/","\\":"\\"},q1=97;Xc.parse=function(e,t,r){var n={},o=0,i=0,a=0,l=r&&r.bigint&&typeof BigInt<"u";return{data:c("",!0),pointers:n};function c(P,x){d();var k;R(P,"value");var oe=y();switch(oe){case"t":g("rue"),k=!0;break;case"f":g("alse"),k=!1;break;case"n":g("ull"),k=null;break;case'"':k=p();break;case"[":k=b(P);break;case"{":k=D(P);break;default:S(),"-0123456789".indexOf(oe)>=0?k=m():B()}return R(P,"valueEnd"),d(),x&&a<e.length&&B(),k}function d(){e:for(;a<e.length;){switch(e[a]){case" ":i++;break;case"	":i+=4;break;case"\r":i=0;break;case`
`:i=0,o++;break;default:break e}a++}}function p(){for(var P="",x;x=y(),x!='"';)x=="\\"?(x=y(),x in $b?P+=$b[x]:x=="u"?P+=w():W()):P+=x;return P}function m(){var P="",x=!0;e[a]=="-"&&(P+=y()),P+=e[a]=="0"?y():T(),e[a]=="."&&(P+=y()+T(),x=!1),(e[a]=="e"||e[a]=="E")&&(P+=y(),(e[a]=="+"||e[a]=="-")&&(P+=y()),P+=T(),x=!1);var k=+P;return l&&x&&(k>Number.MAX_SAFE_INTEGER||k<Number.MIN_SAFE_INTEGER)?BigInt(P):k}function b(P){d();var x=[],k=0;if(y()=="]")return x;for(S();;){var oe=P+"/"+k;x.push(c(oe)),d();var re=y();if(re=="]")break;re!=","&&W(),d(),k++}return x}function D(P){d();var x={};if(y()=="}")return x;for(S();;){var k=v();y()!='"'&&W();var oe=p(),re=P+"/"+Qc(oe);A(re,"key",k),R(re,"keyEnd"),d(),y()!=":"&&W(),d(),x[oe]=c(re),d();var De=y();if(De=="}")break;De!=","&&W(),d()}return x}function g(P){for(var x=0;x<P.length;x++)y()!==P[x]&&W()}function y(){ae();var P=e[a];return a++,i++,P}function S(){a--,i--}function w(){for(var P=4,x=0;P--;){x<<=4;var k=y().toLowerCase();k>="a"&&k<="f"?x+=k.charCodeAt()-q1+10:k>="0"&&k<="9"?x+=+k:W()}return String.fromCharCode(x)}function T(){for(var P="";e[a]>="0"&&e[a]<="9";)P+=y();if(P.length)return P;ae(),B()}function R(P,x){A(P,x,v())}function A(P,x,k){n[P]=n[P]||{},n[P][x]=k}function v(){return{line:o,column:i,pos:a}}function B(){throw new SyntaxError("Unexpected token "+e[a]+" in JSON at position "+a)}function W(){S(),B()}function ae(){if(a>=e.length)throw new SyntaxError("Unexpected end of JSON input")}};Xc.stringify=function(e,t,r){if(!Fa(e))return;var n=0,o,i,a=typeof r=="object"?r.space:r;switch(typeof a){case"number":var l=a>10?10:a<0?0:Math.floor(a);a=l&&A(l," "),o=l,i=l;break;case"string":a=a.slice(0,10),o=0,i=0;for(var c=0;c<a.length;c++){var d=a[c];switch(d){case" ":i++;break;case"	":i+=4;break;case"\r":i=0;break;case`
`:i=0,n++;break;default:throw new Error("whitespace characters not allowed in JSON")}o++}break;default:a=void 0}var p="",m={},b=0,D=0,g=0,y=r&&r.es6&&typeof Map=="function";return S(e,0,""),{json:p,pointers:m};function S(v,B,W){switch(R(W,"value"),typeof v){case"number":case"bigint":case"boolean":w(""+v);break;case"string":w(Ca(v));break;case"object":v===null?w("null"):typeof v.toJSON=="function"?w(Ca(v.toJSON())):Array.isArray(v)?ae():y?v.constructor.BYTES_PER_ELEMENT?ae():v instanceof Map?x():v instanceof Set?x(!0):P():P()}R(W,"valueEnd");function ae(){if(v.length){w("[");for(var k=B+1,oe=0;oe<v.length;oe++){oe&&w(","),T(k);var re=Fa(v[oe])?v[oe]:null,De=W+"/"+oe;S(re,k,De)}T(B),w("]")}else w("[]")}function P(){var k=Object.keys(v);if(k.length){w("{");for(var oe=B+1,re=0;re<k.length;re++){var De=k[re],yt=v[De];if(Fa(yt)){re&&w(",");var xt=W+"/"+Qc(De);T(oe),R(xt,"key"),w(Ca(De)),R(xt,"keyEnd"),w(":"),a&&w(" "),S(yt,oe,xt)}}T(B),w("}")}else w("{}")}function x(k){if(v.size){w("{");for(var oe=B+1,re=!0,De=v.entries(),yt=De.next();!yt.done;){var xt=yt.value,Ai=xt[0],Hr=k?!0:xt[1];if(Fa(Hr)){re||w(","),re=!1;var eo=W+"/"+Qc(Ai);T(oe),R(eo,"key"),w(Ca(Ai)),R(eo,"keyEnd"),w(":"),a&&w(" "),S(Hr,oe,eo)}yt=De.next()}T(B),w("}")}else w("{}")}}function w(v){D+=v.length,g+=v.length,p+=v}function T(v){if(a){for(p+=`
`+A(v,a),b++,D=0;v--;)n?(b+=n,D=i):D+=i,g+=o;g+=1}}function R(v,B){m[v]=m[v]||{},m[v][B]={line:b,column:D,pos:g}}function A(v,B){return Array(v+1).join(B)}};var U1=["number","bigint","boolean","string","object"];function Fa(e){return U1.indexOf(typeof e)>=0}var z1=/"|\\/g,W1=/[\b]/g,V1=/\f/g,G1=/\n/g,H1=/\r/g,Y1=/\t/g;function Ca(e){return e=e.replace(z1,"\\$&").replace(V1,"\\f").replace(W1,"\\b").replace(G1,"\\n").replace(H1,"\\r").replace(Y1,"\\t"),'"'+e+'"'}var K1=/~/g,J1=/\//g;function Qc(e){return e.replace(K1,"~0").replace(J1,"~1")}});var Zc=C((n4,Pb)=>{var Q1=require("node:tty"),X1=Q1?.WriteStream?.prototype?.hasColors?.()??!1,q=(e,t)=>{if(!X1)return o=>o;let r=`\x1B[${e}m`,n=`\x1B[${t}m`;return o=>{let i=o+"",a=i.indexOf(n);if(a===-1)return r+i+n;let l=r,c=0;for(;a!==-1;)l+=i.slice(c,a)+r,c=a+n.length,a=i.indexOf(n,c);return l+=i.slice(c)+n,l}},j={};j.reset=q(0,0);j.bold=q(1,22);j.dim=q(2,22);j.italic=q(3,23);j.underline=q(4,24);j.overline=q(53,55);j.inverse=q(7,27);j.hidden=q(8,28);j.strikethrough=q(9,29);j.black=q(30,39);j.red=q(31,39);j.green=q(32,39);j.yellow=q(33,39);j.blue=q(34,39);j.magenta=q(35,39);j.cyan=q(36,39);j.white=q(37,39);j.gray=q(90,39);j.bgBlack=q(40,49);j.bgRed=q(41,49);j.bgGreen=q(42,49);j.bgYellow=q(43,49);j.bgBlue=q(44,49);j.bgMagenta=q(45,49);j.bgCyan=q(46,49);j.bgWhite=q(47,49);j.bgGray=q(100,49);j.redBright=q(91,39);j.greenBright=q(92,39);j.yellowBright=q(93,39);j.blueBright=q(94,39);j.magentaBright=q(95,39);j.cyanBright=q(96,39);j.whiteBright=q(97,39);j.bgRedBright=q(101,49);j.bgGreenBright=q(102,49);j.bgYellowBright=q(103,49);j.bgBlueBright=q(104,49);j.bgMagentaBright=q(105,49);j.bgCyanBright=q(106,49);j.bgWhiteBright=q(107,49);Pb.exports=j});var gf=C(u_=>{var Z1=Object.create,Ia=Object.defineProperty,ex=Object.getOwnPropertyDescriptor,tx=Object.getOwnPropertyNames,rx=Object.getPrototypeOf,nx=Object.prototype.hasOwnProperty,Hb=e=>Ia(e,"__esModule",{value:!0}),Uo=(e,t)=>function(){return e&&(t=(0,e[Object.keys(e)[0]])(e=0)),t},pf=(e,t)=>function(){return t||(0,e[Object.keys(e)[0]])((t={exports:{}}).exports,t),t.exports},Yb=(e,t)=>{Hb(e);for(var r in t)Ia(e,r,{get:t[r],enumerable:!0})},ox=(e,t,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of tx(t))!nx.call(e,n)&&n!=="default"&&Ia(e,n,{get:()=>t[n],enumerable:!(r=ex(t,n))||r.enumerable});return e},Ee=e=>ox(Hb(Ia(e!=null?Z1(rx(e)):{},"default",e&&e.__esModule&&"default"in e?{get:()=>e.default,enumerable:!0}:{value:e,enumerable:!0})),e),ix=pf({"node_modules/web-streams-polyfill/dist/ponyfill.es2018.js"(e,t){(function(r,n){typeof e=="object"&&typeof t<"u"?n(e):typeof define=="function"&&define.amd?define(["exports"],n):(r=typeof globalThis<"u"?globalThis:r||self,n(r.WebStreamsPolyfill={}))})(e,function(r){"use strict";let n=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Symbol:s=>`Symbol(${s})`;function o(){}function i(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global}let a=i();function l(s){return typeof s=="object"&&s!==null||typeof s=="function"}let c=o,d=Promise,p=Promise.prototype.then,m=Promise.resolve.bind(d),b=Promise.reject.bind(d);function D(s){return new d(s)}function g(s){return m(s)}function y(s){return b(s)}function S(s,u,f){return p.call(s,u,f)}function w(s,u,f){S(S(s,u,f),void 0,c)}function T(s,u){w(s,u)}function R(s,u){w(s,void 0,u)}function A(s,u,f){return S(s,u,f)}function v(s){S(s,void 0,c)}let B=(()=>{let s=a&&a.queueMicrotask;if(typeof s=="function")return s;let u=g(void 0);return f=>S(u,f)})();function W(s,u,f){if(typeof s!="function")throw new TypeError("Argument is not a function");return Function.prototype.apply.call(s,u,f)}function ae(s,u,f){try{return g(W(s,u,f))}catch(h){return y(h)}}let P=16384;class x{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(u){let f=this._back,h=f;f._elements.length===P-1&&(h={_elements:[],_next:void 0}),f._elements.push(u),h!==f&&(this._back=h,f._next=h),++this._size}shift(){let u=this._front,f=u,h=this._cursor,_=h+1,E=u._elements,F=E[h];return _===P&&(f=u._next,_=0),--this._size,this._cursor=_,u!==f&&(this._front=f),E[h]=void 0,F}forEach(u){let f=this._cursor,h=this._front,_=h._elements;for(;(f!==_.length||h._next!==void 0)&&!(f===_.length&&(h=h._next,_=h._elements,f=0,_.length===0));)u(_[f]),++f}peek(){let u=this._front,f=this._cursor;return u._elements[f]}}function k(s,u){s._ownerReadableStream=u,u._reader=s,u._state==="readable"?yt(s):u._state==="closed"?Ai(s):xt(s,u._storedError)}function oe(s,u){let f=s._ownerReadableStream;return tt(f,u)}function re(s){s._ownerReadableStream._state==="readable"?Hr(s,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):eo(s,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),s._ownerReadableStream._reader=void 0,s._ownerReadableStream=void 0}function De(s){return new TypeError("Cannot "+s+" a stream using a released reader")}function yt(s){s._closedPromise=D((u,f)=>{s._closedPromise_resolve=u,s._closedPromise_reject=f})}function xt(s,u){yt(s),Hr(s,u)}function Ai(s){yt(s),xp(s)}function Hr(s,u){s._closedPromise_reject!==void 0&&(v(s._closedPromise),s._closedPromise_reject(u),s._closedPromise_resolve=void 0,s._closedPromise_reject=void 0)}function eo(s,u){xt(s,u)}function xp(s){s._closedPromise_resolve!==void 0&&(s._closedPromise_resolve(void 0),s._closedPromise_resolve=void 0,s._closedPromise_reject=void 0)}let Pp=n("[[AbortSteps]]"),Op=n("[[ErrorSteps]]"),Mu=n("[[CancelSteps]]"),Nu=n("[[PullSteps]]"),Bp=Number.isFinite||function(s){return typeof s=="number"&&isFinite(s)},rv=Math.trunc||function(s){return s<0?Math.ceil(s):Math.floor(s)};function nv(s){return typeof s=="object"||typeof s=="function"}function Pt(s,u){if(s!==void 0&&!nv(s))throw new TypeError(`${u} is not an object.`)}function Ze(s,u){if(typeof s!="function")throw new TypeError(`${u} is not a function.`)}function ov(s){return typeof s=="object"&&s!==null||typeof s=="function"}function Ip(s,u){if(!ov(s))throw new TypeError(`${u} is not an object.`)}function Ot(s,u,f){if(s===void 0)throw new TypeError(`Parameter ${u} is required in '${f}'.`)}function ju(s,u,f){if(s===void 0)throw new TypeError(`${u} is required in '${f}'.`)}function Lu(s){return Number(s)}function kp(s){return s===0?0:s}function iv(s){return kp(rv(s))}function Mp(s,u){let h=Number.MAX_SAFE_INTEGER,_=Number(s);if(_=kp(_),!Bp(_))throw new TypeError(`${u} is not a finite number`);if(_=iv(_),_<0||_>h)throw new TypeError(`${u} is outside the accepted range of 0 to ${h}, inclusive`);return!Bp(_)||_===0?0:_}function qu(s,u){if(!rr(s))throw new TypeError(`${u} is not a ReadableStream.`)}function Yr(s){return new to(s)}function Np(s,u){s._reader._readRequests.push(u)}function Uu(s,u,f){let _=s._reader._readRequests.shift();f?_._closeSteps():_._chunkSteps(u)}function $i(s){return s._reader._readRequests.length}function jp(s){let u=s._reader;return!(u===void 0||!Xt(u))}class to{constructor(u){if(Ot(u,1,"ReadableStreamDefaultReader"),qu(u,"First parameter"),nr(u))throw new TypeError("This stream has already been locked for exclusive reading by another reader");k(this,u),this._readRequests=new x}get closed(){return Xt(this)?this._closedPromise:y(xi("closed"))}cancel(u=void 0){return Xt(this)?this._ownerReadableStream===void 0?y(De("cancel")):oe(this,u):y(xi("cancel"))}read(){if(!Xt(this))return y(xi("read"));if(this._ownerReadableStream===void 0)return y(De("read from"));let u,f,h=D((E,F)=>{u=E,f=F});return ro(this,{_chunkSteps:E=>u({value:E,done:!1}),_closeSteps:()=>u({value:void 0,done:!0}),_errorSteps:E=>f(E)}),h}releaseLock(){if(!Xt(this))throw xi("releaseLock");if(this._ownerReadableStream!==void 0){if(this._readRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");re(this)}}}Object.defineProperties(to.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(to.prototype,n.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});function Xt(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_readRequests")?!1:s instanceof to}function ro(s,u){let f=s._ownerReadableStream;f._disturbed=!0,f._state==="closed"?u._closeSteps():f._state==="errored"?u._errorSteps(f._storedError):f._readableStreamController[Nu](u)}function xi(s){return new TypeError(`ReadableStreamDefaultReader.prototype.${s} can only be used on a ReadableStreamDefaultReader`)}let Lp=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype);class qp{constructor(u,f){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=u,this._preventCancel=f}next(){let u=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?A(this._ongoingPromise,u,u):u(),this._ongoingPromise}return(u){let f=()=>this._returnSteps(u);return this._ongoingPromise?A(this._ongoingPromise,f,f):f()}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});let u=this._reader;if(u._ownerReadableStream===void 0)return y(De("iterate"));let f,h,_=D((F,$)=>{f=F,h=$});return ro(u,{_chunkSteps:F=>{this._ongoingPromise=void 0,B(()=>f({value:F,done:!1}))},_closeSteps:()=>{this._ongoingPromise=void 0,this._isFinished=!0,re(u),f({value:void 0,done:!0})},_errorSteps:F=>{this._ongoingPromise=void 0,this._isFinished=!0,re(u),h(F)}}),_}_returnSteps(u){if(this._isFinished)return Promise.resolve({value:u,done:!0});this._isFinished=!0;let f=this._reader;if(f._ownerReadableStream===void 0)return y(De("finish iterating"));if(!this._preventCancel){let h=oe(f,u);return re(f),A(h,()=>({value:u,done:!0}))}return re(f),g({value:u,done:!0})}}let Up={next(){return zp(this)?this._asyncIteratorImpl.next():y(Wp("next"))},return(s){return zp(this)?this._asyncIteratorImpl.return(s):y(Wp("return"))}};Lp!==void 0&&Object.setPrototypeOf(Up,Lp);function sv(s,u){let f=Yr(s),h=new qp(f,u),_=Object.create(Up);return _._asyncIteratorImpl=h,_}function zp(s){if(!l(s)||!Object.prototype.hasOwnProperty.call(s,"_asyncIteratorImpl"))return!1;try{return s._asyncIteratorImpl instanceof qp}catch{return!1}}function Wp(s){return new TypeError(`ReadableStreamAsyncIterator.${s} can only be used on a ReadableSteamAsyncIterator`)}let Vp=Number.isNaN||function(s){return s!==s};function no(s){return s.slice()}function Gp(s,u,f,h,_){new Uint8Array(s).set(new Uint8Array(f,h,_),u)}function C3(s){return s}function Pi(s){return!1}function Hp(s,u,f){if(s.slice)return s.slice(u,f);let h=f-u,_=new ArrayBuffer(h);return Gp(_,0,s,u,h),_}function av(s){return!(typeof s!="number"||Vp(s)||s<0)}function Yp(s){let u=Hp(s.buffer,s.byteOffset,s.byteOffset+s.byteLength);return new Uint8Array(u)}function zu(s){let u=s._queue.shift();return s._queueTotalSize-=u.size,s._queueTotalSize<0&&(s._queueTotalSize=0),u.value}function Wu(s,u,f){if(!av(f)||f===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");s._queue.push({value:u,size:f}),s._queueTotalSize+=f}function uv(s){return s._queue.peek().value}function Zt(s){s._queue=new x,s._queueTotalSize=0}class oo{constructor(){throw new TypeError("Illegal constructor")}get view(){if(!Vu(this))throw Ku("view");return this._view}respond(u){if(!Vu(this))throw Ku("respond");if(Ot(u,1,"respond"),u=Mp(u,"First parameter"),this._associatedReadableByteStreamController===void 0)throw new TypeError("This BYOB request has been invalidated");Pi(this._view.buffer),Mi(this._associatedReadableByteStreamController,u)}respondWithNewView(u){if(!Vu(this))throw Ku("respondWithNewView");if(Ot(u,1,"respondWithNewView"),!ArrayBuffer.isView(u))throw new TypeError("You can only respond with array buffer views");if(this._associatedReadableByteStreamController===void 0)throw new TypeError("This BYOB request has been invalidated");Pi(u.buffer),Ni(this._associatedReadableByteStreamController,u)}}Object.defineProperties(oo.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(oo.prototype,n.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class Kr{constructor(){throw new TypeError("Illegal constructor")}get byobRequest(){if(!Sr(this))throw so("byobRequest");return Yu(this)}get desiredSize(){if(!Sr(this))throw so("desiredSize");return rm(this)}close(){if(!Sr(this))throw so("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");let u=this._controlledReadableByteStream._state;if(u!=="readable")throw new TypeError(`The stream (in ${u} state) is not in the readable state and cannot be closed`);io(this)}enqueue(u){if(!Sr(this))throw so("enqueue");if(Ot(u,1,"enqueue"),!ArrayBuffer.isView(u))throw new TypeError("chunk must be an array buffer view");if(u.byteLength===0)throw new TypeError("chunk must have non-zero byteLength");if(u.buffer.byteLength===0)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");let f=this._controlledReadableByteStream._state;if(f!=="readable")throw new TypeError(`The stream (in ${f} state) is not in the readable state and cannot be enqueued to`);ki(this,u)}error(u=void 0){if(!Sr(this))throw so("error");et(this,u)}[Mu](u){Kp(this),Zt(this);let f=this._cancelAlgorithm(u);return Ii(this),f}[Nu](u){let f=this._controlledReadableByteStream;if(this._queueTotalSize>0){let _=this._queue.shift();this._queueTotalSize-=_.byteLength,Zp(this);let E=new Uint8Array(_.buffer,_.byteOffset,_.byteLength);u._chunkSteps(E);return}let h=this._autoAllocateChunkSize;if(h!==void 0){let _;try{_=new ArrayBuffer(h)}catch(F){u._errorSteps(F);return}let E={buffer:_,bufferByteLength:h,byteOffset:0,byteLength:h,bytesFilled:0,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(E)}Np(f,u),Er(this)}}Object.defineProperties(Kr.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(Kr.prototype,n.toStringTag,{value:"ReadableByteStreamController",configurable:!0});function Sr(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_controlledReadableByteStream")?!1:s instanceof Kr}function Vu(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_associatedReadableByteStreamController")?!1:s instanceof oo}function Er(s){if(!dv(s))return;if(s._pulling){s._pullAgain=!0;return}s._pulling=!0;let f=s._pullAlgorithm();w(f,()=>{s._pulling=!1,s._pullAgain&&(s._pullAgain=!1,Er(s))},h=>{et(s,h)})}function Kp(s){Hu(s),s._pendingPullIntos=new x}function Gu(s,u){let f=!1;s._state==="closed"&&(f=!0);let h=Jp(u);u.readerType==="default"?Uu(s,h,f):hv(s,h,f)}function Jp(s){let u=s.bytesFilled,f=s.elementSize;return new s.viewConstructor(s.buffer,s.byteOffset,u/f)}function Oi(s,u,f,h){s._queue.push({buffer:u,byteOffset:f,byteLength:h}),s._queueTotalSize+=h}function Qp(s,u){let f=u.elementSize,h=u.bytesFilled-u.bytesFilled%f,_=Math.min(s._queueTotalSize,u.byteLength-u.bytesFilled),E=u.bytesFilled+_,F=E-E%f,$=_,z=!1;F>h&&($=F-u.bytesFilled,z=!0);let K=s._queue;for(;$>0;){let ee=K.peek(),te=Math.min($,ee.byteLength),be=u.byteOffset+u.bytesFilled;Gp(u.buffer,be,ee.buffer,ee.byteOffset,te),ee.byteLength===te?K.shift():(ee.byteOffset+=te,ee.byteLength-=te),s._queueTotalSize-=te,Xp(s,te,u),$-=te}return z}function Xp(s,u,f){f.bytesFilled+=u}function Zp(s){s._queueTotalSize===0&&s._closeRequested?(Ii(s),ho(s._controlledReadableByteStream)):Er(s)}function Hu(s){s._byobRequest!==null&&(s._byobRequest._associatedReadableByteStreamController=void 0,s._byobRequest._view=null,s._byobRequest=null)}function em(s){for(;s._pendingPullIntos.length>0;){if(s._queueTotalSize===0)return;let u=s._pendingPullIntos.peek();Qp(s,u)&&(Bi(s),Gu(s._controlledReadableByteStream,u))}}function lv(s,u,f){let h=s._controlledReadableByteStream,_=1;u.constructor!==DataView&&(_=u.constructor.BYTES_PER_ELEMENT);let E=u.constructor,F=u.buffer,$={buffer:F,bufferByteLength:F.byteLength,byteOffset:u.byteOffset,byteLength:u.byteLength,bytesFilled:0,elementSize:_,viewConstructor:E,readerType:"byob"};if(s._pendingPullIntos.length>0){s._pendingPullIntos.push($),im(h,f);return}if(h._state==="closed"){let z=new E($.buffer,$.byteOffset,0);f._closeSteps(z);return}if(s._queueTotalSize>0){if(Qp(s,$)){let z=Jp($);Zp(s),f._chunkSteps(z);return}if(s._closeRequested){let z=new TypeError("Insufficient bytes to fill elements in the given buffer");et(s,z),f._errorSteps(z);return}}s._pendingPullIntos.push($),im(h,f),Er(s)}function cv(s,u){let f=s._controlledReadableByteStream;if(Ju(f))for(;sm(f)>0;){let h=Bi(s);Gu(f,h)}}function fv(s,u,f){if(Xp(s,u,f),f.bytesFilled<f.elementSize)return;Bi(s);let h=f.bytesFilled%f.elementSize;if(h>0){let _=f.byteOffset+f.bytesFilled,E=Hp(f.buffer,_-h,_);Oi(s,E,0,E.byteLength)}f.bytesFilled-=h,Gu(s._controlledReadableByteStream,f),em(s)}function tm(s,u){let f=s._pendingPullIntos.peek();Hu(s),s._controlledReadableByteStream._state==="closed"?cv(s):fv(s,u,f),Er(s)}function Bi(s){return s._pendingPullIntos.shift()}function dv(s){let u=s._controlledReadableByteStream;return u._state!=="readable"||s._closeRequested||!s._started?!1:!!(jp(u)&&$i(u)>0||Ju(u)&&sm(u)>0||rm(s)>0)}function Ii(s){s._pullAlgorithm=void 0,s._cancelAlgorithm=void 0}function io(s){let u=s._controlledReadableByteStream;if(!(s._closeRequested||u._state!=="readable")){if(s._queueTotalSize>0){s._closeRequested=!0;return}if(s._pendingPullIntos.length>0&&s._pendingPullIntos.peek().bytesFilled>0){let h=new TypeError("Insufficient bytes to fill elements in the given buffer");throw et(s,h),h}Ii(s),ho(u)}}function ki(s,u){let f=s._controlledReadableByteStream;if(s._closeRequested||f._state!=="readable")return;let h=u.buffer,_=u.byteOffset,E=u.byteLength,F=h;if(s._pendingPullIntos.length>0){let $=s._pendingPullIntos.peek();Pi($.buffer),$.buffer=$.buffer}if(Hu(s),jp(f))if($i(f)===0)Oi(s,F,_,E);else{s._pendingPullIntos.length>0&&Bi(s);let $=new Uint8Array(F,_,E);Uu(f,$,!1)}else Ju(f)?(Oi(s,F,_,E),em(s)):Oi(s,F,_,E);Er(s)}function et(s,u){let f=s._controlledReadableByteStream;f._state==="readable"&&(Kp(s),Zt(s),Ii(s),$m(f,u))}function Yu(s){if(s._byobRequest===null&&s._pendingPullIntos.length>0){let u=s._pendingPullIntos.peek(),f=new Uint8Array(u.buffer,u.byteOffset+u.bytesFilled,u.byteLength-u.bytesFilled),h=Object.create(oo.prototype);mv(h,s,f),s._byobRequest=h}return s._byobRequest}function rm(s){let u=s._controlledReadableByteStream._state;return u==="errored"?null:u==="closed"?0:s._strategyHWM-s._queueTotalSize}function Mi(s,u){let f=s._pendingPullIntos.peek();if(s._controlledReadableByteStream._state==="closed"){if(u!==0)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(u===0)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(f.bytesFilled+u>f.byteLength)throw new RangeError("bytesWritten out of range")}f.buffer=f.buffer,tm(s,u)}function Ni(s,u){let f=s._pendingPullIntos.peek();if(s._controlledReadableByteStream._state==="closed"){if(u.byteLength!==0)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(u.byteLength===0)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(f.byteOffset+f.bytesFilled!==u.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(f.bufferByteLength!==u.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(f.bytesFilled+u.byteLength>f.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");let _=u.byteLength;f.buffer=u.buffer,tm(s,_)}function nm(s,u,f,h,_,E,F){u._controlledReadableByteStream=s,u._pullAgain=!1,u._pulling=!1,u._byobRequest=null,u._queue=u._queueTotalSize=void 0,Zt(u),u._closeRequested=!1,u._started=!1,u._strategyHWM=E,u._pullAlgorithm=h,u._cancelAlgorithm=_,u._autoAllocateChunkSize=F,u._pendingPullIntos=new x,s._readableStreamController=u;let $=f();w(g($),()=>{u._started=!0,Er(u)},z=>{et(u,z)})}function pv(s,u,f){let h=Object.create(Kr.prototype),_=()=>{},E=()=>g(void 0),F=()=>g(void 0);u.start!==void 0&&(_=()=>u.start(h)),u.pull!==void 0&&(E=()=>u.pull(h)),u.cancel!==void 0&&(F=z=>u.cancel(z));let $=u.autoAllocateChunkSize;if($===0)throw new TypeError("autoAllocateChunkSize must be greater than 0");nm(s,h,_,E,F,f,$)}function mv(s,u,f){s._associatedReadableByteStreamController=u,s._view=f}function Ku(s){return new TypeError(`ReadableStreamBYOBRequest.prototype.${s} can only be used on a ReadableStreamBYOBRequest`)}function so(s){return new TypeError(`ReadableByteStreamController.prototype.${s} can only be used on a ReadableByteStreamController`)}function om(s){return new ao(s)}function im(s,u){s._reader._readIntoRequests.push(u)}function hv(s,u,f){let _=s._reader._readIntoRequests.shift();f?_._closeSteps(u):_._chunkSteps(u)}function sm(s){return s._reader._readIntoRequests.length}function Ju(s){let u=s._reader;return!(u===void 0||!vr(u))}class ao{constructor(u){if(Ot(u,1,"ReadableStreamBYOBReader"),qu(u,"First parameter"),nr(u))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!Sr(u._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");k(this,u),this._readIntoRequests=new x}get closed(){return vr(this)?this._closedPromise:y(ji("closed"))}cancel(u=void 0){return vr(this)?this._ownerReadableStream===void 0?y(De("cancel")):oe(this,u):y(ji("cancel"))}read(u){if(!vr(this))return y(ji("read"));if(!ArrayBuffer.isView(u))return y(new TypeError("view must be an array buffer view"));if(u.byteLength===0)return y(new TypeError("view must have non-zero byteLength"));if(u.buffer.byteLength===0)return y(new TypeError("view's buffer must have non-zero byteLength"));if(Pi(u.buffer),this._ownerReadableStream===void 0)return y(De("read from"));let f,h,_=D((F,$)=>{f=F,h=$});return am(this,u,{_chunkSteps:F=>f({value:F,done:!1}),_closeSteps:F=>f({value:F,done:!0}),_errorSteps:F=>h(F)}),_}releaseLock(){if(!vr(this))throw ji("releaseLock");if(this._ownerReadableStream!==void 0){if(this._readIntoRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");re(this)}}}Object.defineProperties(ao.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(ao.prototype,n.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});function vr(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_readIntoRequests")?!1:s instanceof ao}function am(s,u,f){let h=s._ownerReadableStream;h._disturbed=!0,h._state==="errored"?f._errorSteps(h._storedError):lv(h._readableStreamController,u,f)}function ji(s){return new TypeError(`ReadableStreamBYOBReader.prototype.${s} can only be used on a ReadableStreamBYOBReader`)}function uo(s,u){let{highWaterMark:f}=s;if(f===void 0)return u;if(Vp(f)||f<0)throw new RangeError("Invalid highWaterMark");return f}function Li(s){let{size:u}=s;return u||(()=>1)}function qi(s,u){Pt(s,u);let f=s?.highWaterMark,h=s?.size;return{highWaterMark:f===void 0?void 0:Lu(f),size:h===void 0?void 0:gv(h,`${u} has member 'size' that`)}}function gv(s,u){return Ze(s,u),f=>Lu(s(f))}function yv(s,u){Pt(s,u);let f=s?.abort,h=s?.close,_=s?.start,E=s?.type,F=s?.write;return{abort:f===void 0?void 0:Dv(f,s,`${u} has member 'abort' that`),close:h===void 0?void 0:bv(h,s,`${u} has member 'close' that`),start:_===void 0?void 0:_v(_,s,`${u} has member 'start' that`),write:F===void 0?void 0:wv(F,s,`${u} has member 'write' that`),type:E}}function Dv(s,u,f){return Ze(s,f),h=>ae(s,u,[h])}function bv(s,u,f){return Ze(s,f),()=>ae(s,u,[])}function _v(s,u,f){return Ze(s,f),h=>W(s,u,[h])}function wv(s,u,f){return Ze(s,f),(h,_)=>ae(s,u,[h,_])}function um(s,u){if(!Jr(s))throw new TypeError(`${u} is not a WritableStream.`)}function Sv(s){if(typeof s!="object"||s===null)return!1;try{return typeof s.aborted=="boolean"}catch{return!1}}let Ev=typeof AbortController=="function";function vv(){if(Ev)return new AbortController}class lo{constructor(u={},f={}){u===void 0?u=null:Ip(u,"First parameter");let h=qi(f,"Second parameter"),_=yv(u,"First parameter");if(cm(this),_.type!==void 0)throw new RangeError("Invalid type is specified");let F=Li(h),$=uo(h,1);Nv(this,_,$,F)}get locked(){if(!Jr(this))throw Gi("locked");return Qr(this)}abort(u=void 0){return Jr(this)?Qr(this)?y(new TypeError("Cannot abort a stream that already has a writer")):Ui(this,u):y(Gi("abort"))}close(){return Jr(this)?Qr(this)?y(new TypeError("Cannot close a stream that already has a writer")):Dt(this)?y(new TypeError("Cannot close an already-closing stream")):fm(this):y(Gi("close"))}getWriter(){if(!Jr(this))throw Gi("getWriter");return lm(this)}}Object.defineProperties(lo.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(lo.prototype,n.toStringTag,{value:"WritableStream",configurable:!0});function lm(s){return new co(s)}function Fv(s,u,f,h,_=1,E=()=>1){let F=Object.create(lo.prototype);cm(F);let $=Object.create(Xr.prototype);return ym(F,$,s,u,f,h,_,E),F}function cm(s){s._state="writable",s._storedError=void 0,s._writer=void 0,s._writableStreamController=void 0,s._writeRequests=new x,s._inFlightWriteRequest=void 0,s._closeRequest=void 0,s._inFlightCloseRequest=void 0,s._pendingAbortRequest=void 0,s._backpressure=!1}function Jr(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_writableStreamController")?!1:s instanceof lo}function Qr(s){return s._writer!==void 0}function Ui(s,u){var f;if(s._state==="closed"||s._state==="errored")return g(void 0);s._writableStreamController._abortReason=u,(f=s._writableStreamController._abortController)===null||f===void 0||f.abort();let h=s._state;if(h==="closed"||h==="errored")return g(void 0);if(s._pendingAbortRequest!==void 0)return s._pendingAbortRequest._promise;let _=!1;h==="erroring"&&(_=!0,u=void 0);let E=D((F,$)=>{s._pendingAbortRequest={_promise:void 0,_resolve:F,_reject:$,_reason:u,_wasAlreadyErroring:_}});return s._pendingAbortRequest._promise=E,_||Xu(s,u),E}function fm(s){let u=s._state;if(u==="closed"||u==="errored")return y(new TypeError(`The stream (in ${u} state) is not in the writable state and cannot be closed`));let f=D((_,E)=>{let F={_resolve:_,_reject:E};s._closeRequest=F}),h=s._writer;return h!==void 0&&s._backpressure&&u==="writable"&&al(h),jv(s._writableStreamController),f}function Cv(s){return D((f,h)=>{let _={_resolve:f,_reject:h};s._writeRequests.push(_)})}function Qu(s,u){if(s._state==="writable"){Xu(s,u);return}Zu(s)}function Xu(s,u){let f=s._writableStreamController;s._state="erroring",s._storedError=u;let h=s._writer;h!==void 0&&pm(h,u),!xv(s)&&f._started&&Zu(s)}function Zu(s){s._state="errored",s._writableStreamController[Op]();let u=s._storedError;if(s._writeRequests.forEach(_=>{_._reject(u)}),s._writeRequests=new x,s._pendingAbortRequest===void 0){zi(s);return}let f=s._pendingAbortRequest;if(s._pendingAbortRequest=void 0,f._wasAlreadyErroring){f._reject(u),zi(s);return}let h=s._writableStreamController[Pp](f._reason);w(h,()=>{f._resolve(),zi(s)},_=>{f._reject(_),zi(s)})}function Tv(s){s._inFlightWriteRequest._resolve(void 0),s._inFlightWriteRequest=void 0}function Rv(s,u){s._inFlightWriteRequest._reject(u),s._inFlightWriteRequest=void 0,Qu(s,u)}function Av(s){s._inFlightCloseRequest._resolve(void 0),s._inFlightCloseRequest=void 0,s._state==="erroring"&&(s._storedError=void 0,s._pendingAbortRequest!==void 0&&(s._pendingAbortRequest._resolve(),s._pendingAbortRequest=void 0)),s._state="closed";let f=s._writer;f!==void 0&&wm(f)}function $v(s,u){s._inFlightCloseRequest._reject(u),s._inFlightCloseRequest=void 0,s._pendingAbortRequest!==void 0&&(s._pendingAbortRequest._reject(u),s._pendingAbortRequest=void 0),Qu(s,u)}function Dt(s){return!(s._closeRequest===void 0&&s._inFlightCloseRequest===void 0)}function xv(s){return!(s._inFlightWriteRequest===void 0&&s._inFlightCloseRequest===void 0)}function Pv(s){s._inFlightCloseRequest=s._closeRequest,s._closeRequest=void 0}function Ov(s){s._inFlightWriteRequest=s._writeRequests.shift()}function zi(s){s._closeRequest!==void 0&&(s._closeRequest._reject(s._storedError),s._closeRequest=void 0);let u=s._writer;u!==void 0&&il(u,s._storedError)}function el(s,u){let f=s._writer;f!==void 0&&u!==s._backpressure&&(u?Gv(f):al(f)),s._backpressure=u}class co{constructor(u){if(Ot(u,1,"WritableStreamDefaultWriter"),um(u,"First parameter"),Qr(u))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=u,u._writer=this;let f=u._state;if(f==="writable")!Dt(u)&&u._backpressure?Yi(this):Sm(this),Hi(this);else if(f==="erroring")sl(this,u._storedError),Hi(this);else if(f==="closed")Sm(this),Wv(this);else{let h=u._storedError;sl(this,h),_m(this,h)}}get closed(){return Fr(this)?this._closedPromise:y(Cr("closed"))}get desiredSize(){if(!Fr(this))throw Cr("desiredSize");if(this._ownerWritableStream===void 0)throw fo("desiredSize");return Mv(this)}get ready(){return Fr(this)?this._readyPromise:y(Cr("ready"))}abort(u=void 0){return Fr(this)?this._ownerWritableStream===void 0?y(fo("abort")):Bv(this,u):y(Cr("abort"))}close(){if(!Fr(this))return y(Cr("close"));let u=this._ownerWritableStream;return u===void 0?y(fo("close")):Dt(u)?y(new TypeError("Cannot close an already-closing stream")):dm(this)}releaseLock(){if(!Fr(this))throw Cr("releaseLock");this._ownerWritableStream!==void 0&&mm(this)}write(u=void 0){return Fr(this)?this._ownerWritableStream===void 0?y(fo("write to")):hm(this,u):y(Cr("write"))}}Object.defineProperties(co.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(co.prototype,n.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});function Fr(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_ownerWritableStream")?!1:s instanceof co}function Bv(s,u){let f=s._ownerWritableStream;return Ui(f,u)}function dm(s){let u=s._ownerWritableStream;return fm(u)}function Iv(s){let u=s._ownerWritableStream,f=u._state;return Dt(u)||f==="closed"?g(void 0):f==="errored"?y(u._storedError):dm(s)}function kv(s,u){s._closedPromiseState==="pending"?il(s,u):Vv(s,u)}function pm(s,u){s._readyPromiseState==="pending"?Em(s,u):Hv(s,u)}function Mv(s){let u=s._ownerWritableStream,f=u._state;return f==="errored"||f==="erroring"?null:f==="closed"?0:Dm(u._writableStreamController)}function mm(s){let u=s._ownerWritableStream,f=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");pm(s,f),kv(s,f),u._writer=void 0,s._ownerWritableStream=void 0}function hm(s,u){let f=s._ownerWritableStream,h=f._writableStreamController,_=Lv(h,u);if(f!==s._ownerWritableStream)return y(fo("write to"));let E=f._state;if(E==="errored")return y(f._storedError);if(Dt(f)||E==="closed")return y(new TypeError("The stream is closing or closed and cannot be written to"));if(E==="erroring")return y(f._storedError);let F=Cv(f);return qv(h,u,_),F}let gm={};class Xr{constructor(){throw new TypeError("Illegal constructor")}get abortReason(){if(!tl(this))throw ol("abortReason");return this._abortReason}get signal(){if(!tl(this))throw ol("signal");if(this._abortController===void 0)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(u=void 0){if(!tl(this))throw ol("error");this._controlledWritableStream._state==="writable"&&bm(this,u)}[Pp](u){let f=this._abortAlgorithm(u);return Wi(this),f}[Op](){Zt(this)}}Object.defineProperties(Xr.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(Xr.prototype,n.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});function tl(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_controlledWritableStream")?!1:s instanceof Xr}function ym(s,u,f,h,_,E,F,$){u._controlledWritableStream=s,s._writableStreamController=u,u._queue=void 0,u._queueTotalSize=void 0,Zt(u),u._abortReason=void 0,u._abortController=vv(),u._started=!1,u._strategySizeAlgorithm=$,u._strategyHWM=F,u._writeAlgorithm=h,u._closeAlgorithm=_,u._abortAlgorithm=E;let z=nl(u);el(s,z);let K=f(),ee=g(K);w(ee,()=>{u._started=!0,Vi(u)},te=>{u._started=!0,Qu(s,te)})}function Nv(s,u,f,h){let _=Object.create(Xr.prototype),E=()=>{},F=()=>g(void 0),$=()=>g(void 0),z=()=>g(void 0);u.start!==void 0&&(E=()=>u.start(_)),u.write!==void 0&&(F=K=>u.write(K,_)),u.close!==void 0&&($=()=>u.close()),u.abort!==void 0&&(z=K=>u.abort(K)),ym(s,_,E,F,$,z,f,h)}function Wi(s){s._writeAlgorithm=void 0,s._closeAlgorithm=void 0,s._abortAlgorithm=void 0,s._strategySizeAlgorithm=void 0}function jv(s){Wu(s,gm,0),Vi(s)}function Lv(s,u){try{return s._strategySizeAlgorithm(u)}catch(f){return rl(s,f),1}}function Dm(s){return s._strategyHWM-s._queueTotalSize}function qv(s,u,f){try{Wu(s,u,f)}catch(_){rl(s,_);return}let h=s._controlledWritableStream;if(!Dt(h)&&h._state==="writable"){let _=nl(s);el(h,_)}Vi(s)}function Vi(s){let u=s._controlledWritableStream;if(!s._started||u._inFlightWriteRequest!==void 0)return;if(u._state==="erroring"){Zu(u);return}if(s._queue.length===0)return;let h=uv(s);h===gm?Uv(s):zv(s,h)}function rl(s,u){s._controlledWritableStream._state==="writable"&&bm(s,u)}function Uv(s){let u=s._controlledWritableStream;Pv(u),zu(s);let f=s._closeAlgorithm();Wi(s),w(f,()=>{Av(u)},h=>{$v(u,h)})}function zv(s,u){let f=s._controlledWritableStream;Ov(f);let h=s._writeAlgorithm(u);w(h,()=>{Tv(f);let _=f._state;if(zu(s),!Dt(f)&&_==="writable"){let E=nl(s);el(f,E)}Vi(s)},_=>{f._state==="writable"&&Wi(s),Rv(f,_)})}function nl(s){return Dm(s)<=0}function bm(s,u){let f=s._controlledWritableStream;Wi(s),Xu(f,u)}function Gi(s){return new TypeError(`WritableStream.prototype.${s} can only be used on a WritableStream`)}function ol(s){return new TypeError(`WritableStreamDefaultController.prototype.${s} can only be used on a WritableStreamDefaultController`)}function Cr(s){return new TypeError(`WritableStreamDefaultWriter.prototype.${s} can only be used on a WritableStreamDefaultWriter`)}function fo(s){return new TypeError("Cannot "+s+" a stream using a released writer")}function Hi(s){s._closedPromise=D((u,f)=>{s._closedPromise_resolve=u,s._closedPromise_reject=f,s._closedPromiseState="pending"})}function _m(s,u){Hi(s),il(s,u)}function Wv(s){Hi(s),wm(s)}function il(s,u){s._closedPromise_reject!==void 0&&(v(s._closedPromise),s._closedPromise_reject(u),s._closedPromise_resolve=void 0,s._closedPromise_reject=void 0,s._closedPromiseState="rejected")}function Vv(s,u){_m(s,u)}function wm(s){s._closedPromise_resolve!==void 0&&(s._closedPromise_resolve(void 0),s._closedPromise_resolve=void 0,s._closedPromise_reject=void 0,s._closedPromiseState="resolved")}function Yi(s){s._readyPromise=D((u,f)=>{s._readyPromise_resolve=u,s._readyPromise_reject=f}),s._readyPromiseState="pending"}function sl(s,u){Yi(s),Em(s,u)}function Sm(s){Yi(s),al(s)}function Em(s,u){s._readyPromise_reject!==void 0&&(v(s._readyPromise),s._readyPromise_reject(u),s._readyPromise_resolve=void 0,s._readyPromise_reject=void 0,s._readyPromiseState="rejected")}function Gv(s){Yi(s)}function Hv(s,u){sl(s,u)}function al(s){s._readyPromise_resolve!==void 0&&(s._readyPromise_resolve(void 0),s._readyPromise_resolve=void 0,s._readyPromise_reject=void 0,s._readyPromiseState="fulfilled")}let vm=typeof DOMException<"u"?DOMException:void 0;function Yv(s){if(!(typeof s=="function"||typeof s=="object"))return!1;try{return new s,!0}catch{return!1}}function Kv(){let s=function(f,h){this.message=f||"",this.name=h||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return s.prototype=Object.create(Error.prototype),Object.defineProperty(s.prototype,"constructor",{value:s,writable:!0,configurable:!0}),s}let Jv=Yv(vm)?vm:Kv();function Fm(s,u,f,h,_,E){let F=Yr(s),$=lm(u);s._disturbed=!0;let z=!1,K=g(void 0);return D((ee,te)=>{let be;if(E!==void 0){if(be=()=>{let I=new Jv("Aborted","AbortError"),Y=[];h||Y.push(()=>u._state==="writable"?Ui(u,I):g(void 0)),_||Y.push(()=>s._state==="readable"?tt(s,I):g(void 0)),Pe(()=>Promise.all(Y.map(ue=>ue())),!0,I)},E.aborted){be();return}E.addEventListener("abort",be)}function rt(){return D((I,Y)=>{function ue(Ne){Ne?I():S(tn(),ue,Y)}ue(!1)})}function tn(){return z?g(!0):S($._readyPromise,()=>D((I,Y)=>{ro(F,{_chunkSteps:ue=>{K=S(hm($,ue),void 0,o),I(!1)},_closeSteps:()=>I(!0),_errorSteps:Y})}))}if(Bt(s,F._closedPromise,I=>{h?qe(!0,I):Pe(()=>Ui(u,I),!0,I)}),Bt(u,$._closedPromise,I=>{_?qe(!0,I):Pe(()=>tt(s,I),!0,I)}),Ae(s,F._closedPromise,()=>{f?qe():Pe(()=>Iv($))}),Dt(u)||u._state==="closed"){let I=new TypeError("the destination writable stream closed before all data could be piped to it");_?qe(!0,I):Pe(()=>tt(s,I),!0,I)}v(rt());function or(){let I=K;return S(K,()=>I!==K?or():void 0)}function Bt(I,Y,ue){I._state==="errored"?ue(I._storedError):R(Y,ue)}function Ae(I,Y,ue){I._state==="closed"?ue():T(Y,ue)}function Pe(I,Y,ue){if(z)return;z=!0,u._state==="writable"&&!Dt(u)?T(or(),Ne):Ne();function Ne(){w(I(),()=>It(Y,ue),rn=>It(!0,rn))}}function qe(I,Y){z||(z=!0,u._state==="writable"&&!Dt(u)?T(or(),()=>It(I,Y)):It(I,Y))}function It(I,Y){mm($),re(F),E!==void 0&&E.removeEventListener("abort",be),I?te(Y):ee(void 0)}})}class Zr{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!Ki(this))throw Xi("desiredSize");return ul(this)}close(){if(!Ki(this))throw Xi("close");if(!en(this))throw new TypeError("The stream is not in a state that permits close");mo(this)}enqueue(u=void 0){if(!Ki(this))throw Xi("enqueue");if(!en(this))throw new TypeError("The stream is not in a state that permits enqueue");return Qi(this,u)}error(u=void 0){if(!Ki(this))throw Xi("error");er(this,u)}[Mu](u){Zt(this);let f=this._cancelAlgorithm(u);return Ji(this),f}[Nu](u){let f=this._controlledReadableStream;if(this._queue.length>0){let h=zu(this);this._closeRequested&&this._queue.length===0?(Ji(this),ho(f)):po(this),u._chunkSteps(h)}else Np(f,u),po(this)}}Object.defineProperties(Zr.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(Zr.prototype,n.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});function Ki(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_controlledReadableStream")?!1:s instanceof Zr}function po(s){if(!Cm(s))return;if(s._pulling){s._pullAgain=!0;return}s._pulling=!0;let f=s._pullAlgorithm();w(f,()=>{s._pulling=!1,s._pullAgain&&(s._pullAgain=!1,po(s))},h=>{er(s,h)})}function Cm(s){let u=s._controlledReadableStream;return!en(s)||!s._started?!1:!!(nr(u)&&$i(u)>0||ul(s)>0)}function Ji(s){s._pullAlgorithm=void 0,s._cancelAlgorithm=void 0,s._strategySizeAlgorithm=void 0}function mo(s){if(!en(s))return;let u=s._controlledReadableStream;s._closeRequested=!0,s._queue.length===0&&(Ji(s),ho(u))}function Qi(s,u){if(!en(s))return;let f=s._controlledReadableStream;if(nr(f)&&$i(f)>0)Uu(f,u,!1);else{let h;try{h=s._strategySizeAlgorithm(u)}catch(_){throw er(s,_),_}try{Wu(s,u,h)}catch(_){throw er(s,_),_}}po(s)}function er(s,u){let f=s._controlledReadableStream;f._state==="readable"&&(Zt(s),Ji(s),$m(f,u))}function ul(s){let u=s._controlledReadableStream._state;return u==="errored"?null:u==="closed"?0:s._strategyHWM-s._queueTotalSize}function Qv(s){return!Cm(s)}function en(s){let u=s._controlledReadableStream._state;return!s._closeRequested&&u==="readable"}function Tm(s,u,f,h,_,E,F){u._controlledReadableStream=s,u._queue=void 0,u._queueTotalSize=void 0,Zt(u),u._started=!1,u._closeRequested=!1,u._pullAgain=!1,u._pulling=!1,u._strategySizeAlgorithm=F,u._strategyHWM=E,u._pullAlgorithm=h,u._cancelAlgorithm=_,s._readableStreamController=u;let $=f();w(g($),()=>{u._started=!0,po(u)},z=>{er(u,z)})}function Xv(s,u,f,h){let _=Object.create(Zr.prototype),E=()=>{},F=()=>g(void 0),$=()=>g(void 0);u.start!==void 0&&(E=()=>u.start(_)),u.pull!==void 0&&(F=()=>u.pull(_)),u.cancel!==void 0&&($=z=>u.cancel(z)),Tm(s,_,E,F,$,f,h)}function Xi(s){return new TypeError(`ReadableStreamDefaultController.prototype.${s} can only be used on a ReadableStreamDefaultController`)}function Zv(s,u){return Sr(s._readableStreamController)?tF(s):eF(s)}function eF(s,u){let f=Yr(s),h=!1,_=!1,E=!1,F=!1,$,z,K,ee,te,be=D(Ae=>{te=Ae});function rt(){return h?(_=!0,g(void 0)):(h=!0,ro(f,{_chunkSteps:Pe=>{B(()=>{_=!1;let qe=Pe,It=Pe;E||Qi(K._readableStreamController,qe),F||Qi(ee._readableStreamController,It),h=!1,_&&rt()})},_closeSteps:()=>{h=!1,E||mo(K._readableStreamController),F||mo(ee._readableStreamController),(!E||!F)&&te(void 0)},_errorSteps:()=>{h=!1}}),g(void 0))}function tn(Ae){if(E=!0,$=Ae,F){let Pe=no([$,z]),qe=tt(s,Pe);te(qe)}return be}function or(Ae){if(F=!0,z=Ae,E){let Pe=no([$,z]),qe=tt(s,Pe);te(qe)}return be}function Bt(){}return K=ll(Bt,rt,tn),ee=ll(Bt,rt,or),R(f._closedPromise,Ae=>{er(K._readableStreamController,Ae),er(ee._readableStreamController,Ae),(!E||!F)&&te(void 0)}),[K,ee]}function tF(s){let u=Yr(s),f=!1,h=!1,_=!1,E=!1,F=!1,$,z,K,ee,te,be=D(I=>{te=I});function rt(I){R(I._closedPromise,Y=>{I===u&&(et(K._readableStreamController,Y),et(ee._readableStreamController,Y),(!E||!F)&&te(void 0))})}function tn(){vr(u)&&(re(u),u=Yr(s),rt(u)),ro(u,{_chunkSteps:Y=>{B(()=>{h=!1,_=!1;let ue=Y,Ne=Y;if(!E&&!F)try{Ne=Yp(Y)}catch(rn){et(K._readableStreamController,rn),et(ee._readableStreamController,rn),te(tt(s,rn));return}E||ki(K._readableStreamController,ue),F||ki(ee._readableStreamController,Ne),f=!1,h?Bt():_&&Ae()})},_closeSteps:()=>{f=!1,E||io(K._readableStreamController),F||io(ee._readableStreamController),K._readableStreamController._pendingPullIntos.length>0&&Mi(K._readableStreamController,0),ee._readableStreamController._pendingPullIntos.length>0&&Mi(ee._readableStreamController,0),(!E||!F)&&te(void 0)},_errorSteps:()=>{f=!1}})}function or(I,Y){Xt(u)&&(re(u),u=om(s),rt(u));let ue=Y?ee:K,Ne=Y?K:ee;am(u,I,{_chunkSteps:nn=>{B(()=>{h=!1,_=!1;let on=Y?F:E;if(Y?E:F)on||Ni(ue._readableStreamController,nn);else{let zm;try{zm=Yp(nn)}catch(fl){et(ue._readableStreamController,fl),et(Ne._readableStreamController,fl),te(tt(s,fl));return}on||Ni(ue._readableStreamController,nn),ki(Ne._readableStreamController,zm)}f=!1,h?Bt():_&&Ae()})},_closeSteps:nn=>{f=!1;let on=Y?F:E,as=Y?E:F;on||io(ue._readableStreamController),as||io(Ne._readableStreamController),nn!==void 0&&(on||Ni(ue._readableStreamController,nn),!as&&Ne._readableStreamController._pendingPullIntos.length>0&&Mi(Ne._readableStreamController,0)),(!on||!as)&&te(void 0)},_errorSteps:()=>{f=!1}})}function Bt(){if(f)return h=!0,g(void 0);f=!0;let I=Yu(K._readableStreamController);return I===null?tn():or(I._view,!1),g(void 0)}function Ae(){if(f)return _=!0,g(void 0);f=!0;let I=Yu(ee._readableStreamController);return I===null?tn():or(I._view,!0),g(void 0)}function Pe(I){if(E=!0,$=I,F){let Y=no([$,z]),ue=tt(s,Y);te(ue)}return be}function qe(I){if(F=!0,z=I,E){let Y=no([$,z]),ue=tt(s,Y);te(ue)}return be}function It(){}return K=Am(It,Bt,Pe),ee=Am(It,Ae,qe),rt(u),[K,ee]}function rF(s,u){Pt(s,u);let f=s,h=f?.autoAllocateChunkSize,_=f?.cancel,E=f?.pull,F=f?.start,$=f?.type;return{autoAllocateChunkSize:h===void 0?void 0:Mp(h,`${u} has member 'autoAllocateChunkSize' that`),cancel:_===void 0?void 0:nF(_,f,`${u} has member 'cancel' that`),pull:E===void 0?void 0:oF(E,f,`${u} has member 'pull' that`),start:F===void 0?void 0:iF(F,f,`${u} has member 'start' that`),type:$===void 0?void 0:sF($,`${u} has member 'type' that`)}}function nF(s,u,f){return Ze(s,f),h=>ae(s,u,[h])}function oF(s,u,f){return Ze(s,f),h=>ae(s,u,[h])}function iF(s,u,f){return Ze(s,f),h=>W(s,u,[h])}function sF(s,u){if(s=`${s}`,s!=="bytes")throw new TypeError(`${u} '${s}' is not a valid enumeration value for ReadableStreamType`);return s}function aF(s,u){Pt(s,u);let f=s?.mode;return{mode:f===void 0?void 0:uF(f,`${u} has member 'mode' that`)}}function uF(s,u){if(s=`${s}`,s!=="byob")throw new TypeError(`${u} '${s}' is not a valid enumeration value for ReadableStreamReaderMode`);return s}function lF(s,u){return Pt(s,u),{preventCancel:!!s?.preventCancel}}function Rm(s,u){Pt(s,u);let f=s?.preventAbort,h=s?.preventCancel,_=s?.preventClose,E=s?.signal;return E!==void 0&&cF(E,`${u} has member 'signal' that`),{preventAbort:!!f,preventCancel:!!h,preventClose:!!_,signal:E}}function cF(s,u){if(!Sv(s))throw new TypeError(`${u} is not an AbortSignal.`)}function fF(s,u){Pt(s,u);let f=s?.readable;ju(f,"readable","ReadableWritablePair"),qu(f,`${u} has member 'readable' that`);let h=s?.writable;return ju(h,"writable","ReadableWritablePair"),um(h,`${u} has member 'writable' that`),{readable:f,writable:h}}class tr{constructor(u={},f={}){u===void 0?u=null:Ip(u,"First parameter");let h=qi(f,"Second parameter"),_=rF(u,"First parameter");if(cl(this),_.type==="bytes"){if(h.size!==void 0)throw new RangeError("The strategy for a byte stream cannot have a size function");let E=uo(h,0);pv(this,_,E)}else{let E=Li(h),F=uo(h,1);Xv(this,_,F,E)}}get locked(){if(!rr(this))throw Tr("locked");return nr(this)}cancel(u=void 0){return rr(this)?nr(this)?y(new TypeError("Cannot cancel a stream that already has a reader")):tt(this,u):y(Tr("cancel"))}getReader(u=void 0){if(!rr(this))throw Tr("getReader");return aF(u,"First parameter").mode===void 0?Yr(this):om(this)}pipeThrough(u,f={}){if(!rr(this))throw Tr("pipeThrough");Ot(u,1,"pipeThrough");let h=fF(u,"First parameter"),_=Rm(f,"Second parameter");if(nr(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(Qr(h.writable))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");let E=Fm(this,h.writable,_.preventClose,_.preventAbort,_.preventCancel,_.signal);return v(E),h.readable}pipeTo(u,f={}){if(!rr(this))return y(Tr("pipeTo"));if(u===void 0)return y("Parameter 1 is required in 'pipeTo'.");if(!Jr(u))return y(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));let h;try{h=Rm(f,"Second parameter")}catch(_){return y(_)}return nr(this)?y(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):Qr(u)?y(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):Fm(this,u,h.preventClose,h.preventAbort,h.preventCancel,h.signal)}tee(){if(!rr(this))throw Tr("tee");let u=Zv(this);return no(u)}values(u=void 0){if(!rr(this))throw Tr("values");let f=lF(u,"First parameter");return sv(this,f.preventCancel)}}Object.defineProperties(tr.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(tr.prototype,n.toStringTag,{value:"ReadableStream",configurable:!0}),typeof n.asyncIterator=="symbol"&&Object.defineProperty(tr.prototype,n.asyncIterator,{value:tr.prototype.values,writable:!0,configurable:!0});function ll(s,u,f,h=1,_=()=>1){let E=Object.create(tr.prototype);cl(E);let F=Object.create(Zr.prototype);return Tm(E,F,s,u,f,h,_),E}function Am(s,u,f){let h=Object.create(tr.prototype);cl(h);let _=Object.create(Kr.prototype);return nm(h,_,s,u,f,0,void 0),h}function cl(s){s._state="readable",s._reader=void 0,s._storedError=void 0,s._disturbed=!1}function rr(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_readableStreamController")?!1:s instanceof tr}function nr(s){return s._reader!==void 0}function tt(s,u){if(s._disturbed=!0,s._state==="closed")return g(void 0);if(s._state==="errored")return y(s._storedError);ho(s);let f=s._reader;f!==void 0&&vr(f)&&(f._readIntoRequests.forEach(_=>{_._closeSteps(void 0)}),f._readIntoRequests=new x);let h=s._readableStreamController[Mu](u);return A(h,o)}function ho(s){s._state="closed";let u=s._reader;u!==void 0&&(xp(u),Xt(u)&&(u._readRequests.forEach(f=>{f._closeSteps()}),u._readRequests=new x))}function $m(s,u){s._state="errored",s._storedError=u;let f=s._reader;f!==void 0&&(Hr(f,u),Xt(f)?(f._readRequests.forEach(h=>{h._errorSteps(u)}),f._readRequests=new x):(f._readIntoRequests.forEach(h=>{h._errorSteps(u)}),f._readIntoRequests=new x))}function Tr(s){return new TypeError(`ReadableStream.prototype.${s} can only be used on a ReadableStream`)}function xm(s,u){Pt(s,u);let f=s?.highWaterMark;return ju(f,"highWaterMark","QueuingStrategyInit"),{highWaterMark:Lu(f)}}let Pm=s=>s.byteLength;try{Object.defineProperty(Pm,"name",{value:"size",configurable:!0})}catch{}class Zi{constructor(u){Ot(u,1,"ByteLengthQueuingStrategy"),u=xm(u,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=u.highWaterMark}get highWaterMark(){if(!Bm(this))throw Om("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!Bm(this))throw Om("size");return Pm}}Object.defineProperties(Zi.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(Zi.prototype,n.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});function Om(s){return new TypeError(`ByteLengthQueuingStrategy.prototype.${s} can only be used on a ByteLengthQueuingStrategy`)}function Bm(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_byteLengthQueuingStrategyHighWaterMark")?!1:s instanceof Zi}let Im=()=>1;try{Object.defineProperty(Im,"name",{value:"size",configurable:!0})}catch{}class es{constructor(u){Ot(u,1,"CountQueuingStrategy"),u=xm(u,"First parameter"),this._countQueuingStrategyHighWaterMark=u.highWaterMark}get highWaterMark(){if(!Mm(this))throw km("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!Mm(this))throw km("size");return Im}}Object.defineProperties(es.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(es.prototype,n.toStringTag,{value:"CountQueuingStrategy",configurable:!0});function km(s){return new TypeError(`CountQueuingStrategy.prototype.${s} can only be used on a CountQueuingStrategy`)}function Mm(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_countQueuingStrategyHighWaterMark")?!1:s instanceof es}function dF(s,u){Pt(s,u);let f=s?.flush,h=s?.readableType,_=s?.start,E=s?.transform,F=s?.writableType;return{flush:f===void 0?void 0:pF(f,s,`${u} has member 'flush' that`),readableType:h,start:_===void 0?void 0:mF(_,s,`${u} has member 'start' that`),transform:E===void 0?void 0:hF(E,s,`${u} has member 'transform' that`),writableType:F}}function pF(s,u,f){return Ze(s,f),h=>ae(s,u,[h])}function mF(s,u,f){return Ze(s,f),h=>W(s,u,[h])}function hF(s,u,f){return Ze(s,f),(h,_)=>ae(s,u,[h,_])}class ts{constructor(u={},f={},h={}){u===void 0&&(u=null);let _=qi(f,"Second parameter"),E=qi(h,"Third parameter"),F=dF(u,"First parameter");if(F.readableType!==void 0)throw new RangeError("Invalid readableType specified");if(F.writableType!==void 0)throw new RangeError("Invalid writableType specified");let $=uo(E,0),z=Li(E),K=uo(_,1),ee=Li(_),te,be=D(rt=>{te=rt});gF(this,be,K,ee,$,z),DF(this,F),F.start!==void 0?te(F.start(this._transformStreamController)):te(void 0)}get readable(){if(!Nm(this))throw Um("readable");return this._readable}get writable(){if(!Nm(this))throw Um("writable");return this._writable}}Object.defineProperties(ts.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(ts.prototype,n.toStringTag,{value:"TransformStream",configurable:!0});function gF(s,u,f,h,_,E){function F(){return u}function $(be){return wF(s,be)}function z(be){return SF(s,be)}function K(){return EF(s)}s._writable=Fv(F,$,K,z,f,h);function ee(){return vF(s)}function te(be){return ns(s,be),g(void 0)}s._readable=ll(F,ee,te,_,E),s._backpressure=void 0,s._backpressureChangePromise=void 0,s._backpressureChangePromise_resolve=void 0,os(s,!0),s._transformStreamController=void 0}function Nm(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_transformStreamController")?!1:s instanceof ts}function rs(s,u){er(s._readable._readableStreamController,u),ns(s,u)}function ns(s,u){jm(s._transformStreamController),rl(s._writable._writableStreamController,u),s._backpressure&&os(s,!1)}function os(s,u){s._backpressureChangePromise!==void 0&&s._backpressureChangePromise_resolve(),s._backpressureChangePromise=D(f=>{s._backpressureChangePromise_resolve=f}),s._backpressure=u}class go{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!is(this))throw ss("desiredSize");let u=this._controlledTransformStream._readable._readableStreamController;return ul(u)}enqueue(u=void 0){if(!is(this))throw ss("enqueue");Lm(this,u)}error(u=void 0){if(!is(this))throw ss("error");bF(this,u)}terminate(){if(!is(this))throw ss("terminate");_F(this)}}Object.defineProperties(go.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(go.prototype,n.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});function is(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_controlledTransformStream")?!1:s instanceof go}function yF(s,u,f,h){u._controlledTransformStream=s,s._transformStreamController=u,u._transformAlgorithm=f,u._flushAlgorithm=h}function DF(s,u){let f=Object.create(go.prototype),h=E=>{try{return Lm(f,E),g(void 0)}catch(F){return y(F)}},_=()=>g(void 0);u.transform!==void 0&&(h=E=>u.transform(E,f)),u.flush!==void 0&&(_=()=>u.flush(f)),yF(s,f,h,_)}function jm(s){s._transformAlgorithm=void 0,s._flushAlgorithm=void 0}function Lm(s,u){let f=s._controlledTransformStream,h=f._readable._readableStreamController;if(!en(h))throw new TypeError("Readable side is not in a state that permits enqueue");try{Qi(h,u)}catch(E){throw ns(f,E),f._readable._storedError}Qv(h)!==f._backpressure&&os(f,!0)}function bF(s,u){rs(s._controlledTransformStream,u)}function qm(s,u){let f=s._transformAlgorithm(u);return A(f,void 0,h=>{throw rs(s._controlledTransformStream,h),h})}function _F(s){let u=s._controlledTransformStream,f=u._readable._readableStreamController;mo(f);let h=new TypeError("TransformStream terminated");ns(u,h)}function wF(s,u){let f=s._transformStreamController;if(s._backpressure){let h=s._backpressureChangePromise;return A(h,()=>{let _=s._writable;if(_._state==="erroring")throw _._storedError;return qm(f,u)})}return qm(f,u)}function SF(s,u){return rs(s,u),g(void 0)}function EF(s){let u=s._readable,f=s._transformStreamController,h=f._flushAlgorithm();return jm(f),A(h,()=>{if(u._state==="errored")throw u._storedError;mo(u._readableStreamController)},_=>{throw rs(s,_),u._storedError})}function vF(s){return os(s,!1),s._backpressureChangePromise}function ss(s){return new TypeError(`TransformStreamDefaultController.prototype.${s} can only be used on a TransformStreamDefaultController`)}function Um(s){return new TypeError(`TransformStream.prototype.${s} can only be used on a TransformStream`)}r.ByteLengthQueuingStrategy=Zi,r.CountQueuingStrategy=es,r.ReadableByteStreamController=Kr,r.ReadableStream=tr,r.ReadableStreamBYOBReader=ao,r.ReadableStreamBYOBRequest=oo,r.ReadableStreamDefaultController=Zr,r.ReadableStreamDefaultReader=to,r.TransformStream=ts,r.TransformStreamDefaultController=go,r.WritableStream=lo,r.WritableStreamDefaultController=Xr,r.WritableStreamDefaultWriter=co,Object.defineProperty(r,"__esModule",{value:!0})})}}),sx=pf({"node_modules/fetch-blob/streams.cjs"(){var e=65536;if(!globalThis.ReadableStream)try{let t=require("process"),{emitWarning:r}=t;try{t.emitWarning=()=>{},Object.assign(globalThis,require("stream/web")),t.emitWarning=r}catch(n){throw t.emitWarning=r,n}}catch{Object.assign(globalThis,ix())}try{let{Blob:t}=require("buffer");t&&!t.prototype.stream&&(t.prototype.stream=function(n){let o=0,i=this;return new ReadableStream({type:"bytes",async pull(a){let c=await i.slice(o,Math.min(i.size,o+e)).arrayBuffer();o+=c.byteLength,a.enqueue(new Uint8Array(c)),o===i.size&&a.close()}})})}catch{}}});async function*ef(e,t=!0){for(let r of e)if("stream"in r)yield*r.stream();else if(ArrayBuffer.isView(r))if(t){let n=r.byteOffset,o=r.byteOffset+r.byteLength;for(;n!==o;){let i=Math.min(o-n,uf),a=r.buffer.slice(n,n+i);n+=a.byteLength,yield new Uint8Array(a)}}else yield r;else{let n=0,o=r;for(;n!==o.size;){let a=await o.slice(n,Math.min(o.size,n+uf)).arrayBuffer();n+=a.byteLength,yield new Uint8Array(a)}}}var ax,uf,tf,lf,Rn,zo=Uo({"node_modules/fetch-blob/index.js"(){ax=Ee(sx()),uf=65536,tf=class cf{#e=[];#r="";#t=0;#n="transparent";constructor(t=[],r={}){if(typeof t!="object"||t===null)throw new TypeError("Failed to construct 'Blob': The provided value cannot be converted to a sequence.");if(typeof t[Symbol.iterator]!="function")throw new TypeError("Failed to construct 'Blob': The object must have a callable @@iterator property.");if(typeof r!="object"&&typeof r!="function")throw new TypeError("Failed to construct 'Blob': parameter 2 cannot convert to dictionary.");r===null&&(r={});let n=new TextEncoder;for(let i of t){let a;ArrayBuffer.isView(i)?a=new Uint8Array(i.buffer.slice(i.byteOffset,i.byteOffset+i.byteLength)):i instanceof ArrayBuffer?a=new Uint8Array(i.slice(0)):i instanceof cf?a=i:a=n.encode(`${i}`),this.#t+=ArrayBuffer.isView(a)?a.byteLength:a.size,this.#e.push(a)}this.#n=`${r.endings===void 0?"transparent":r.endings}`;let o=r.type===void 0?"":String(r.type);this.#r=/^[\x20-\x7E]*$/.test(o)?o:""}get size(){return this.#t}get type(){return this.#r}async text(){let t=new TextDecoder,r="";for await(let n of ef(this.#e,!1))r+=t.decode(n,{stream:!0});return r+=t.decode(),r}async arrayBuffer(){let t=new Uint8Array(this.size),r=0;for await(let n of ef(this.#e,!1))t.set(n,r),r+=n.length;return t.buffer}stream(){let t=ef(this.#e,!0);return new globalThis.ReadableStream({type:"bytes",async pull(r){let n=await t.next();n.done?r.close():r.enqueue(n.value)},async cancel(){await t.return()}})}slice(t=0,r=this.size,n=""){let{size:o}=this,i=t<0?Math.max(o+t,0):Math.min(t,o),a=r<0?Math.max(o+r,0):Math.min(r,o),l=Math.max(a-i,0),c=this.#e,d=[],p=0;for(let b of c){if(p>=l)break;let D=ArrayBuffer.isView(b)?b.byteLength:b.size;if(i&&D<=i)i-=D,a-=D;else{let g;ArrayBuffer.isView(b)?(g=b.subarray(i,Math.min(D,a)),p+=g.byteLength):(g=b.slice(i,Math.min(D,a)),p+=g.size),a-=D,d.push(g),i=0}}let m=new cf([],{type:String(n).toLowerCase()});return m.#t=l,m.#e=d,m}get[Symbol.toStringTag](){return"Blob"}static[Symbol.hasInstance](t){return t&&typeof t=="object"&&typeof t.constructor=="function"&&(typeof t.stream=="function"||typeof t.arrayBuffer=="function")&&/^(Blob|File)$/.test(t[Symbol.toStringTag])}},Object.defineProperties(tf.prototype,{size:{enumerable:!0},type:{enumerable:!0},slice:{enumerable:!0}}),lf=tf,Rn=lf}}),Ob,Bb,Wo,Kb=Uo({"node_modules/fetch-blob/file.js"(){zo(),Ob=class extends Rn{#e=0;#r="";constructor(t,r,n={}){if(arguments.length<2)throw new TypeError(`Failed to construct 'File': 2 arguments required, but only ${arguments.length} present.`);super(t,n),n===null&&(n={});let o=n.lastModified===void 0?Date.now():Number(n.lastModified);Number.isNaN(o)||(this.#e=o),this.#r=String(r)}get name(){return this.#r}get lastModified(){return this.#e}get[Symbol.toStringTag](){return"File"}static[Symbol.hasInstance](t){return!!t&&t instanceof Rn&&/^(File)$/.test(t[Symbol.toStringTag])}},Bb=Ob,Wo=Bb}});function ux(e,t=Rn){var r=`${ff()}${ff()}`.replace(/\./g,"").slice(-28).padStart(32,"-"),n=[],o=`--${r}\r
Content-Disposition: form-data; name="`;return e.forEach((i,a)=>typeof i=="string"?n.push(o+$a(a)+`"\r
\r
${i.replace(/\r(?!\n)|(?<!\r)\n/g,`\r
`)}\r
`):n.push(o+$a(a)+`"; filename="${$a(i.name,1)}"\r
Content-Type: ${i.type||"application/octet-stream"}\r
\r
`,i,`\r
`)),n.push(`--${r}--`),new t(n,{type:"multipart/form-data; boundary="+r})}var vn,Ib,kb,ff,Mb,rf,$a,fr,An,ka=Uo({"node_modules/formdata-polyfill/esm.min.js"(){zo(),Kb(),{toStringTag:vn,iterator:Ib,hasInstance:kb}=Symbol,ff=Math.random,Mb="append,set,get,getAll,delete,keys,values,entries,forEach,constructor".split(","),rf=(e,t,r)=>(e+="",/^(Blob|File)$/.test(t&&t[vn])?[(r=r!==void 0?r+"":t[vn]=="File"?t.name:"blob",e),t.name!==r||t[vn]=="blob"?new Wo([t],r,t):t]:[e,t+""]),$a=(e,t)=>(t?e:e.replace(/\r?\n|\r/g,`\r
`)).replace(/\n/g,"%0A").replace(/\r/g,"%0D").replace(/"/g,"%22"),fr=(e,t,r)=>{if(t.length<r)throw new TypeError(`Failed to execute '${e}' on 'FormData': ${r} arguments required, but only ${t.length} present.`)},An=class{#e=[];constructor(...t){if(t.length)throw new TypeError("Failed to construct 'FormData': parameter 1 is not of type 'HTMLFormElement'.")}get[vn](){return"FormData"}[Ib](){return this.entries()}static[kb](t){return t&&typeof t=="object"&&t[vn]==="FormData"&&!Mb.some(r=>typeof t[r]!="function")}append(...t){fr("append",arguments,2),this.#e.push(rf(...t))}delete(t){fr("delete",arguments,1),t+="",this.#e=this.#e.filter(([r])=>r!==t)}get(t){fr("get",arguments,1),t+="";for(var r=this.#e,n=r.length,o=0;o<n;o++)if(r[o][0]===t)return r[o][1];return null}getAll(t,r){return fr("getAll",arguments,1),r=[],t+="",this.#e.forEach(n=>n[0]===t&&r.push(n[1])),r}has(t){return fr("has",arguments,1),t+="",this.#e.some(r=>r[0]===t)}forEach(t,r){fr("forEach",arguments,1);for(var[n,o]of this)t.call(r,o,n,this)}set(...t){fr("set",arguments,2);var r=[],n=!0;t=rf(...t),this.#e.forEach(o=>{o[0]===t[0]?n&&(n=!r.push(t)):r.push(o)}),n&&r.push(t),this.#e=r}*entries(){yield*this.#e}*keys(){for(var[t]of this)yield t}*values(){for(var[,t]of this)yield t}}}}),lx=pf({"node_modules/node-domexception/index.js"(e,t){if(!globalThis.DOMException)try{let{MessageChannel:r}=require("worker_threads"),n=new r().port1,o=new ArrayBuffer;n.postMessage(o,[o,o])}catch(r){r.constructor.name==="DOMException"&&(globalThis.DOMException=r.constructor)}t.exports=globalThis.DOMException}}),ko,Nb,jb,Ta,Jb,Qb,Xb,Zb,nf,of,Ra,e_=Uo({"node_modules/fetch-blob/from.js"(){ko=Ee(require("fs")),Nb=Ee(require("path")),jb=Ee(lx()),Kb(),zo(),{stat:Ta}=ko.promises,Jb=(e,t)=>nf((0,ko.statSync)(e),e,t),Qb=(e,t)=>Ta(e).then(r=>nf(r,e,t)),Xb=(e,t)=>Ta(e).then(r=>of(r,e,t)),Zb=(e,t)=>of((0,ko.statSync)(e),e,t),nf=(e,t,r="")=>new Rn([new Ra({path:t,size:e.size,lastModified:e.mtimeMs,start:0})],{type:r}),of=(e,t,r="")=>new Wo([new Ra({path:t,size:e.size,lastModified:e.mtimeMs,start:0})],(0,Nb.basename)(t),{type:r,lastModified:e.mtimeMs}),Ra=class{#e;#r;constructor(e){this.#e=e.path,this.#r=e.start,this.size=e.size,this.lastModified=e.lastModified}slice(e,t){return new Ra({path:this.#e,lastModified:this.lastModified,size:t-e,start:this.#r+e})}async*stream(){let{mtimeMs:e}=await Ta(this.#e);if(e>this.lastModified)throw new jb.default("The requested file could not be read, typically due to permission problems that have occurred after a reference to a file was acquired.","NotReadableError");yield*(0,ko.createReadStream)(this.#e,{start:this.#r,end:this.#r+this.size-1})}get[Symbol.toStringTag](){return"Blob"}}}}),t_={};Yb(t_,{toFormData:()=>fx});function cx(e){let t=e.match(/\bfilename=("(.*?)"|([^()<>@,;:\\"/[\]?={}\s\t]+))($|;\s)/i);if(!t)return;let r=t[2]||t[3]||"",n=r.slice(r.lastIndexOf("\\")+1);return n=n.replace(/%22/g,'"'),n=n.replace(/&#(\d{4});/g,(o,i)=>String.fromCharCode(i)),n}async function fx(e,t){if(!/multipart/i.test(t))throw new TypeError("Failed to fetch");let r=t.match(/boundary=(?:"([^"]+)"|([^;]+))/i);if(!r)throw new TypeError("no or bad content-type header, no multipart boundary");let n=new r_(r[1]||r[2]),o,i,a,l,c,d,p=[],m=new An,b=w=>{a+=S.decode(w,{stream:!0})},D=w=>{p.push(w)},g=()=>{let w=new Wo(p,d,{type:c});m.append(l,w)},y=()=>{m.append(l,a)},S=new TextDecoder("utf-8");S.decode(),n.onPartBegin=function(){n.onPartData=b,n.onPartEnd=y,o="",i="",a="",l="",c="",d=null,p.length=0},n.onHeaderField=function(w){o+=S.decode(w,{stream:!0})},n.onHeaderValue=function(w){i+=S.decode(w,{stream:!0})},n.onHeaderEnd=function(){if(i+=S.decode(),o=o.toLowerCase(),o==="content-disposition"){let w=i.match(/\bname=("([^"]*)"|([^()<>@,;:\\"/[\]?={}\s\t]+))/i);w&&(l=w[2]||w[3]||""),d=cx(i),d&&(n.onPartData=D,n.onPartEnd=g)}else o==="content-type"&&(c=i);i="",o=""};for await(let w of e)n.write(w);return n.end(),m}var at,ie,sf,Lt,Mo,No,Lb,Fn,qb,Ub,zb,Wb,dr,r_,dx=Uo({"node_modules/node-fetch/src/utils/multipart-parser.js"(){e_(),ka(),at=0,ie={START_BOUNDARY:at++,HEADER_FIELD_START:at++,HEADER_FIELD:at++,HEADER_VALUE_START:at++,HEADER_VALUE:at++,HEADER_VALUE_ALMOST_DONE:at++,HEADERS_ALMOST_DONE:at++,PART_DATA_START:at++,PART_DATA:at++,END:at++},sf=1,Lt={PART_BOUNDARY:sf,LAST_BOUNDARY:sf*=2},Mo=10,No=13,Lb=32,Fn=45,qb=58,Ub=97,zb=122,Wb=e=>e|32,dr=()=>{},r_=class{constructor(e){this.index=0,this.flags=0,this.onHeaderEnd=dr,this.onHeaderField=dr,this.onHeadersEnd=dr,this.onHeaderValue=dr,this.onPartBegin=dr,this.onPartData=dr,this.onPartEnd=dr,this.boundaryChars={},e=`\r
--`+e;let t=new Uint8Array(e.length);for(let r=0;r<e.length;r++)t[r]=e.charCodeAt(r),this.boundaryChars[t[r]]=!0;this.boundary=t,this.lookbehind=new Uint8Array(this.boundary.length+8),this.state=ie.START_BOUNDARY}write(e){let t=0,r=e.length,n=this.index,{lookbehind:o,boundary:i,boundaryChars:a,index:l,state:c,flags:d}=this,p=this.boundary.length,m=p-1,b=e.length,D,g,y=R=>{this[R+"Mark"]=t},S=R=>{delete this[R+"Mark"]},w=(R,A,v,B)=>{(A===void 0||A!==v)&&this[R](B&&B.subarray(A,v))},T=(R,A)=>{let v=R+"Mark";v in this&&(A?(w(R,this[v],t,e),delete this[v]):(w(R,this[v],e.length,e),this[v]=0))};for(t=0;t<r;t++)switch(D=e[t],c){case ie.START_BOUNDARY:if(l===i.length-2){if(D===Fn)d|=Lt.LAST_BOUNDARY;else if(D!==No)return;l++;break}else if(l-1===i.length-2){if(d&Lt.LAST_BOUNDARY&&D===Fn)c=ie.END,d=0;else if(!(d&Lt.LAST_BOUNDARY)&&D===Mo)l=0,w("onPartBegin"),c=ie.HEADER_FIELD_START;else return;break}D!==i[l+2]&&(l=-2),D===i[l+2]&&l++;break;case ie.HEADER_FIELD_START:c=ie.HEADER_FIELD,y("onHeaderField"),l=0;case ie.HEADER_FIELD:if(D===No){S("onHeaderField"),c=ie.HEADERS_ALMOST_DONE;break}if(l++,D===Fn)break;if(D===qb){if(l===1)return;T("onHeaderField",!0),c=ie.HEADER_VALUE_START;break}if(g=Wb(D),g<Ub||g>zb)return;break;case ie.HEADER_VALUE_START:if(D===Lb)break;y("onHeaderValue"),c=ie.HEADER_VALUE;case ie.HEADER_VALUE:D===No&&(T("onHeaderValue",!0),w("onHeaderEnd"),c=ie.HEADER_VALUE_ALMOST_DONE);break;case ie.HEADER_VALUE_ALMOST_DONE:if(D!==Mo)return;c=ie.HEADER_FIELD_START;break;case ie.HEADERS_ALMOST_DONE:if(D!==Mo)return;w("onHeadersEnd"),c=ie.PART_DATA_START;break;case ie.PART_DATA_START:c=ie.PART_DATA,y("onPartData");case ie.PART_DATA:if(n=l,l===0){for(t+=m;t<b&&!(e[t]in a);)t+=p;t-=m,D=e[t]}if(l<i.length)i[l]===D?(l===0&&T("onPartData",!0),l++):l=0;else if(l===i.length)l++,D===No?d|=Lt.PART_BOUNDARY:D===Fn?d|=Lt.LAST_BOUNDARY:l=0;else if(l-1===i.length)if(d&Lt.PART_BOUNDARY){if(l=0,D===Mo){d&=~Lt.PART_BOUNDARY,w("onPartEnd"),w("onPartBegin"),c=ie.HEADER_FIELD_START;break}}else d&Lt.LAST_BOUNDARY&&D===Fn?(w("onPartEnd"),c=ie.END,d=0):l=0;if(l>0)o[l-1]=D;else if(n>0){let R=new Uint8Array(o.buffer,o.byteOffset,o.byteLength);w("onPartData",0,n,R),n=0,y("onPartData"),t--}break;case ie.END:break;default:throw new Error(`Unexpected state entered: ${c}`)}T("onHeaderField"),T("onHeaderValue"),T("onPartData"),this.index=l,this.state=c,this.flags=d}end(){if(this.state===ie.HEADER_FIELD_START&&this.index===0||this.state===ie.PART_DATA&&this.index===this.boundary.length)this.onPartEnd();else if(this.state!==ie.END)throw new Error("MultipartParser.end(): stream ended unexpectedly")}}}});Yb(u_,{AbortError:()=>s_,Blob:()=>lf,FetchError:()=>lt,File:()=>Wo,FormData:()=>An,Headers:()=>qt,Request:()=>qo,Response:()=>Le,blobFrom:()=>Qb,blobFromSync:()=>Jb,default:()=>a_,fileFrom:()=>Xb,fileFromSync:()=>Zb,isRedirect:()=>hf});var px=Ee(require("http")),mx=Ee(require("https")),Cn=Ee(require("zlib")),Ft=Ee(require("stream")),Aa=Ee(require("buffer"));function hx(e){if(!/^data:/i.test(e))throw new TypeError('`uri` does not appear to be a Data URI (must begin with "data:")');e=e.replace(/\r?\n/g,"");let t=e.indexOf(",");if(t===-1||t<=4)throw new TypeError("malformed data: URI");let r=e.substring(5,t).split(";"),n="",o=!1,i=r[0]||"text/plain",a=i;for(let p=1;p<r.length;p++)r[p]==="base64"?o=!0:r[p]&&(a+=`;${r[p]}`,r[p].indexOf("charset=")===0&&(n=r[p].substring(8)));!r[0]&&!n.length&&(a+=";charset=US-ASCII",n="US-ASCII");let l=o?"base64":"ascii",c=unescape(e.substring(t+1)),d=Buffer.from(c,l);return d.type=i,d.typeFull=a,d.charset=n,d}var gx=hx,Ct=Ee(require("stream")),$n=Ee(require("util")),We=Ee(require("buffer"));zo();ka();var Ma=class extends Error{constructor(e,t){super(e),Error.captureStackTrace(this,this.constructor),this.type=t}get name(){return this.constructor.name}get[Symbol.toStringTag](){return this.constructor.name}},lt=class extends Ma{constructor(e,t,r){super(e,t),r&&(this.code=this.errno=r.code,this.erroredSysCall=r.syscall)}},Pa=Symbol.toStringTag,n_=e=>typeof e=="object"&&typeof e.append=="function"&&typeof e.delete=="function"&&typeof e.get=="function"&&typeof e.getAll=="function"&&typeof e.has=="function"&&typeof e.set=="function"&&typeof e.sort=="function"&&e[Pa]==="URLSearchParams",Oa=e=>e&&typeof e=="object"&&typeof e.arrayBuffer=="function"&&typeof e.type=="string"&&typeof e.stream=="function"&&typeof e.constructor=="function"&&/^(Blob|File)$/.test(e[Pa]),yx=e=>typeof e=="object"&&(e[Pa]==="AbortSignal"||e[Pa]==="EventTarget"),Dx=(e,t)=>{let r=new URL(t).hostname,n=new URL(e).hostname;return r===n||r.endsWith(`.${n}`)},bx=(e,t)=>{let r=new URL(t).protocol,n=new URL(e).protocol;return r===n},_x=(0,$n.promisify)(Ct.default.pipeline),Oe=Symbol("Body internals"),Lo=class{constructor(e,{size:t=0}={}){let r=null;e===null?e=null:n_(e)?e=We.Buffer.from(e.toString()):Oa(e)||We.Buffer.isBuffer(e)||($n.types.isAnyArrayBuffer(e)?e=We.Buffer.from(e):ArrayBuffer.isView(e)?e=We.Buffer.from(e.buffer,e.byteOffset,e.byteLength):e instanceof Ct.default||(e instanceof An?(e=ux(e),r=e.type.split("=")[1]):e=We.Buffer.from(String(e))));let n=e;We.Buffer.isBuffer(e)?n=Ct.default.Readable.from(e):Oa(e)&&(n=Ct.default.Readable.from(e.stream())),this[Oe]={body:e,stream:n,boundary:r,disturbed:!1,error:null},this.size=t,e instanceof Ct.default&&e.on("error",o=>{let i=o instanceof Ma?o:new lt(`Invalid response body while trying to fetch ${this.url}: ${o.message}`,"system",o);this[Oe].error=i})}get body(){return this[Oe].stream}get bodyUsed(){return this[Oe].disturbed}async arrayBuffer(){let{buffer:e,byteOffset:t,byteLength:r}=await af(this);return e.slice(t,t+r)}async formData(){let e=this.headers.get("content-type");if(e.startsWith("application/x-www-form-urlencoded")){let r=new An,n=new URLSearchParams(await this.text());for(let[o,i]of n)r.append(o,i);return r}let{toFormData:t}=await Promise.resolve().then(()=>(dx(),t_));return t(this.body,e)}async blob(){let e=this.headers&&this.headers.get("content-type")||this[Oe].body&&this[Oe].body.type||"",t=await this.arrayBuffer();return new Rn([t],{type:e})}async json(){let e=await this.text();return JSON.parse(e)}async text(){let e=await af(this);return new TextDecoder().decode(e)}buffer(){return af(this)}};Lo.prototype.buffer=(0,$n.deprecate)(Lo.prototype.buffer,"Please use 'response.arrayBuffer()' instead of 'response.buffer()'","node-fetch#buffer");Object.defineProperties(Lo.prototype,{body:{enumerable:!0},bodyUsed:{enumerable:!0},arrayBuffer:{enumerable:!0},blob:{enumerable:!0},json:{enumerable:!0},text:{enumerable:!0},data:{get:(0,$n.deprecate)(()=>{},"data doesn't exist, use json(), text(), arrayBuffer(), or body instead","https://github.com/node-fetch/node-fetch/issues/1000 (response)")}});async function af(e){if(e[Oe].disturbed)throw new TypeError(`body used already for: ${e.url}`);if(e[Oe].disturbed=!0,e[Oe].error)throw e[Oe].error;let{body:t}=e;if(t===null||!(t instanceof Ct.default))return We.Buffer.alloc(0);let r=[],n=0;try{for await(let o of t){if(e.size>0&&n+o.length>e.size){let i=new lt(`content size at ${e.url} over limit: ${e.size}`,"max-size");throw t.destroy(i),i}n+=o.length,r.push(o)}}catch(o){throw o instanceof Ma?o:new lt(`Invalid response body while trying to fetch ${e.url}: ${o.message}`,"system",o)}if(t.readableEnded===!0||t._readableState.ended===!0)try{return r.every(o=>typeof o=="string")?We.Buffer.from(r.join("")):We.Buffer.concat(r,n)}catch(o){throw new lt(`Could not create Buffer from response body for ${e.url}: ${o.message}`,"system",o)}else throw new lt(`Premature close of server response while trying to fetch ${e.url}`)}var mf=(e,t)=>{let r,n,{body:o}=e[Oe];if(e.bodyUsed)throw new Error("cannot clone body after it is used");return o instanceof Ct.default&&typeof o.getBoundary!="function"&&(r=new Ct.PassThrough({highWaterMark:t}),n=new Ct.PassThrough({highWaterMark:t}),o.pipe(r),o.pipe(n),e[Oe].stream=r,o=n),o},wx=(0,$n.deprecate)(e=>e.getBoundary(),"form-data doesn't follow the spec and requires special treatment. Use alternative package","https://github.com/node-fetch/node-fetch/issues/1167"),o_=(e,t)=>e===null?null:typeof e=="string"?"text/plain;charset=UTF-8":n_(e)?"application/x-www-form-urlencoded;charset=UTF-8":Oa(e)?e.type||null:We.Buffer.isBuffer(e)||$n.types.isAnyArrayBuffer(e)||ArrayBuffer.isView(e)?null:e instanceof An?`multipart/form-data; boundary=${t[Oe].boundary}`:e&&typeof e.getBoundary=="function"?`multipart/form-data;boundary=${wx(e)}`:e instanceof Ct.default?null:"text/plain;charset=UTF-8",Sx=e=>{let{body:t}=e[Oe];return t===null?0:Oa(t)?t.size:We.Buffer.isBuffer(t)?t.length:t&&typeof t.getLengthSync=="function"&&t.hasKnownLength&&t.hasKnownLength()?t.getLengthSync():null},Ex=async(e,{body:t})=>{t===null?e.end():await _x(t,e)},Vb=Ee(require("util")),Ba=Ee(require("http")),xa=typeof Ba.default.validateHeaderName=="function"?Ba.default.validateHeaderName:e=>{if(!/^[\^`\-\w!#$%&'*+.|~]+$/.test(e)){let t=new TypeError(`Header name must be a valid HTTP token [${e}]`);throw Object.defineProperty(t,"code",{value:"ERR_INVALID_HTTP_TOKEN"}),t}},df=typeof Ba.default.validateHeaderValue=="function"?Ba.default.validateHeaderValue:(e,t)=>{if(/[^\t\u0020-\u007E\u0080-\u00FF]/.test(t)){let r=new TypeError(`Invalid character in header content ["${e}"]`);throw Object.defineProperty(r,"code",{value:"ERR_INVALID_CHAR"}),r}},qt=class extends URLSearchParams{constructor(e){let t=[];if(e instanceof qt){let r=e.raw();for(let[n,o]of Object.entries(r))t.push(...o.map(i=>[n,i]))}else if(e!=null)if(typeof e=="object"&&!Vb.types.isBoxedPrimitive(e)){let r=e[Symbol.iterator];if(r==null)t.push(...Object.entries(e));else{if(typeof r!="function")throw new TypeError("Header pairs must be iterable");t=[...e].map(n=>{if(typeof n!="object"||Vb.types.isBoxedPrimitive(n))throw new TypeError("Each header pair must be an iterable object");return[...n]}).map(n=>{if(n.length!==2)throw new TypeError("Each header pair must be a name/value tuple");return[...n]})}}else throw new TypeError("Failed to construct 'Headers': The provided value is not of type '(sequence<sequence<ByteString>> or record<ByteString, ByteString>)");return t=t.length>0?t.map(([r,n])=>(xa(r),df(r,String(n)),[String(r).toLowerCase(),String(n)])):void 0,super(t),new Proxy(this,{get(r,n,o){switch(n){case"append":case"set":return(i,a)=>(xa(i),df(i,String(a)),URLSearchParams.prototype[n].call(r,String(i).toLowerCase(),String(a)));case"delete":case"has":case"getAll":return i=>(xa(i),URLSearchParams.prototype[n].call(r,String(i).toLowerCase()));case"keys":return()=>(r.sort(),new Set(URLSearchParams.prototype.keys.call(r)).keys());default:return Reflect.get(r,n,o)}}})}get[Symbol.toStringTag](){return this.constructor.name}toString(){return Object.prototype.toString.call(this)}get(e){let t=this.getAll(e);if(t.length===0)return null;let r=t.join(", ");return/^content-encoding$/i.test(e)&&(r=r.toLowerCase()),r}forEach(e,t=void 0){for(let r of this.keys())Reflect.apply(e,t,[this.get(r),r,this])}*values(){for(let e of this.keys())yield this.get(e)}*entries(){for(let e of this.keys())yield[e,this.get(e)]}[Symbol.iterator](){return this.entries()}raw(){return[...this.keys()].reduce((e,t)=>(e[t]=this.getAll(t),e),{})}[Symbol.for("nodejs.util.inspect.custom")](){return[...this.keys()].reduce((e,t)=>{let r=this.getAll(t);return t==="host"?e[t]=r[0]:e[t]=r.length>1?r:r[0],e},{})}};Object.defineProperties(qt.prototype,["get","entries","forEach","values"].reduce((e,t)=>(e[t]={enumerable:!0},e),{}));function vx(e=[]){return new qt(e.reduce((t,r,n,o)=>(n%2===0&&t.push(o.slice(n,n+2)),t),[]).filter(([t,r])=>{try{return xa(t),df(t,String(r)),!0}catch{return!1}}))}var Fx=new Set([301,302,303,307,308]),hf=e=>Fx.has(e),ut=Symbol("Response internals"),Le=class extends Lo{constructor(e=null,t={}){super(e,t);let r=t.status!=null?t.status:200,n=new qt(t.headers);if(e!==null&&!n.has("Content-Type")){let o=o_(e,this);o&&n.append("Content-Type",o)}this[ut]={type:"default",url:t.url,status:r,statusText:t.statusText||"",headers:n,counter:t.counter,highWaterMark:t.highWaterMark}}get type(){return this[ut].type}get url(){return this[ut].url||""}get status(){return this[ut].status}get ok(){return this[ut].status>=200&&this[ut].status<300}get redirected(){return this[ut].counter>0}get statusText(){return this[ut].statusText}get headers(){return this[ut].headers}get highWaterMark(){return this[ut].highWaterMark}clone(){return new Le(mf(this,this.highWaterMark),{type:this.type,url:this.url,status:this.status,statusText:this.statusText,headers:this.headers,ok:this.ok,redirected:this.redirected,size:this.size,highWaterMark:this.highWaterMark})}static redirect(e,t=302){if(!hf(t))throw new RangeError('Failed to execute "redirect" on "response": Invalid status code');return new Le(null,{headers:{location:new URL(e).toString()},status:t})}static error(){let e=new Le(null,{status:0,statusText:""});return e[ut].type="error",e}static json(e=void 0,t={}){let r=JSON.stringify(e);if(r===void 0)throw new TypeError("data is not JSON serializable");let n=new qt(t&&t.headers);return n.has("content-type")||n.set("content-type","application/json"),new Le(r,{...t,headers:n})}get[Symbol.toStringTag](){return"Response"}};Object.defineProperties(Le.prototype,{type:{enumerable:!0},url:{enumerable:!0},status:{enumerable:!0},ok:{enumerable:!0},redirected:{enumerable:!0},statusText:{enumerable:!0},headers:{enumerable:!0},clone:{enumerable:!0}});var Cx=Ee(require("url")),Tx=Ee(require("util")),Rx=e=>{if(e.search)return e.search;let t=e.href.length-1,r=e.hash||(e.href[t]==="#"?"#":"");return e.href[t-r.length]==="?"?"?":""},Ax=Ee(require("net"));function Gb(e,t=!1){return e==null||(e=new URL(e),/^(about|blob|data):$/.test(e.protocol))?"no-referrer":(e.username="",e.password="",e.hash="",t&&(e.pathname="",e.search=""),e)}var i_=new Set(["","no-referrer","no-referrer-when-downgrade","same-origin","origin","strict-origin","origin-when-cross-origin","strict-origin-when-cross-origin","unsafe-url"]),$x="strict-origin-when-cross-origin";function xx(e){if(!i_.has(e))throw new TypeError(`Invalid referrerPolicy: ${e}`);return e}function Px(e){if(/^(http|ws)s:$/.test(e.protocol))return!0;let t=e.host.replace(/(^\[)|(]$)/g,""),r=(0,Ax.isIP)(t);return r===4&&/^127\./.test(t)||r===6&&/^(((0+:){7})|(::(0+:){0,6}))0*1$/.test(t)?!0:e.host==="localhost"||e.host.endsWith(".localhost")?!1:e.protocol==="file:"}function Tn(e){return/^about:(blank|srcdoc)$/.test(e)||e.protocol==="data:"||/^(blob|filesystem):$/.test(e.protocol)?!0:Px(e)}function Ox(e,{referrerURLCallback:t,referrerOriginCallback:r}={}){if(e.referrer==="no-referrer"||e.referrerPolicy==="")return null;let n=e.referrerPolicy;if(e.referrer==="about:client")return"no-referrer";let o=e.referrer,i=Gb(o),a=Gb(o,!0);i.toString().length>4096&&(i=a),t&&(i=t(i)),r&&(a=r(a));let l=new URL(e.url);switch(n){case"no-referrer":return"no-referrer";case"origin":return a;case"unsafe-url":return i;case"strict-origin":return Tn(i)&&!Tn(l)?"no-referrer":a.toString();case"strict-origin-when-cross-origin":return i.origin===l.origin?i:Tn(i)&&!Tn(l)?"no-referrer":a;case"same-origin":return i.origin===l.origin?i:"no-referrer";case"origin-when-cross-origin":return i.origin===l.origin?i:a;case"no-referrer-when-downgrade":return Tn(i)&&!Tn(l)?"no-referrer":i;default:throw new TypeError(`Invalid referrerPolicy: ${n}`)}}function Bx(e){let t=(e.get("referrer-policy")||"").split(/[,\s]+/),r="";for(let n of t)n&&i_.has(n)&&(r=n);return r}var Se=Symbol("Request internals"),jo=e=>typeof e=="object"&&typeof e[Se]=="object",Ix=(0,Tx.deprecate)(()=>{},".data is not a valid RequestInit property, use .body instead","https://github.com/node-fetch/node-fetch/issues/1000 (request)"),qo=class extends Lo{constructor(e,t={}){let r;if(jo(e)?r=new URL(e.url):(r=new URL(e),e={}),r.username!==""||r.password!=="")throw new TypeError(`${r} is an url with embedded credentials.`);let n=t.method||e.method||"GET";if(/^(delete|get|head|options|post|put)$/i.test(n)&&(n=n.toUpperCase()),!jo(t)&&"data"in t&&Ix(),(t.body!=null||jo(e)&&e.body!==null)&&(n==="GET"||n==="HEAD"))throw new TypeError("Request with GET/HEAD method cannot have body");let o=t.body?t.body:jo(e)&&e.body!==null?mf(e):null;super(o,{size:t.size||e.size||0});let i=new qt(t.headers||e.headers||{});if(o!==null&&!i.has("Content-Type")){let c=o_(o,this);c&&i.set("Content-Type",c)}let a=jo(e)?e.signal:null;if("signal"in t&&(a=t.signal),a!=null&&!yx(a))throw new TypeError("Expected signal to be an instanceof AbortSignal or EventTarget");let l=t.referrer==null?e.referrer:t.referrer;if(l==="")l="no-referrer";else if(l){let c=new URL(l);l=/^about:(\/\/)?client$/.test(c)?"client":c}else l=void 0;this[Se]={method:n,redirect:t.redirect||e.redirect||"follow",headers:i,parsedURL:r,signal:a,referrer:l},this.follow=t.follow===void 0?e.follow===void 0?20:e.follow:t.follow,this.compress=t.compress===void 0?e.compress===void 0?!0:e.compress:t.compress,this.counter=t.counter||e.counter||0,this.agent=t.agent||e.agent,this.highWaterMark=t.highWaterMark||e.highWaterMark||16384,this.insecureHTTPParser=t.insecureHTTPParser||e.insecureHTTPParser||!1,this.referrerPolicy=t.referrerPolicy||e.referrerPolicy||""}get method(){return this[Se].method}get url(){return(0,Cx.format)(this[Se].parsedURL)}get headers(){return this[Se].headers}get redirect(){return this[Se].redirect}get signal(){return this[Se].signal}get referrer(){if(this[Se].referrer==="no-referrer")return"";if(this[Se].referrer==="client")return"about:client";if(this[Se].referrer)return this[Se].referrer.toString()}get referrerPolicy(){return this[Se].referrerPolicy}set referrerPolicy(e){this[Se].referrerPolicy=xx(e)}clone(){return new qo(this)}get[Symbol.toStringTag](){return"Request"}};Object.defineProperties(qo.prototype,{method:{enumerable:!0},url:{enumerable:!0},headers:{enumerable:!0},redirect:{enumerable:!0},clone:{enumerable:!0},signal:{enumerable:!0},referrer:{enumerable:!0},referrerPolicy:{enumerable:!0}});var kx=e=>{let{parsedURL:t}=e[Se],r=new qt(e[Se].headers);r.has("Accept")||r.set("Accept","*/*");let n=null;if(e.body===null&&/^(post|put)$/i.test(e.method)&&(n="0"),e.body!==null){let l=Sx(e);typeof l=="number"&&!Number.isNaN(l)&&(n=String(l))}n&&r.set("Content-Length",n),e.referrerPolicy===""&&(e.referrerPolicy=$x),e.referrer&&e.referrer!=="no-referrer"?e[Se].referrer=Ox(e):e[Se].referrer="no-referrer",e[Se].referrer instanceof URL&&r.set("Referer",e.referrer),r.has("User-Agent")||r.set("User-Agent","node-fetch"),e.compress&&!r.has("Accept-Encoding")&&r.set("Accept-Encoding","gzip, deflate, br");let{agent:o}=e;typeof o=="function"&&(o=o(t));let i=Rx(t),a={path:t.pathname+i,method:e.method,headers:r[Symbol.for("nodejs.util.inspect.custom")](),insecureHTTPParser:e.insecureHTTPParser,agent:o};return{parsedURL:t,options:a}},s_=class extends Ma{constructor(e,t="aborted"){super(e,t)}};ka();e_();var Mx=new Set(["data:","http:","https:"]);async function a_(e,t){return new Promise((r,n)=>{let o=new qo(e,t),{parsedURL:i,options:a}=kx(o);if(!Mx.has(i.protocol))throw new TypeError(`node-fetch cannot load ${e}. URL scheme "${i.protocol.replace(/:$/,"")}" is not supported.`);if(i.protocol==="data:"){let g=gx(o.url),y=new Le(g,{headers:{"Content-Type":g.typeFull}});r(y);return}let l=(i.protocol==="https:"?mx.default:px.default).request,{signal:c}=o,d=null,p=()=>{let g=new s_("The operation was aborted.");n(g),o.body&&o.body instanceof Ft.default.Readable&&o.body.destroy(g),!(!d||!d.body)&&d.body.emit("error",g)};if(c&&c.aborted){p();return}let m=()=>{p(),D()},b=l(i.toString(),a);c&&c.addEventListener("abort",m);let D=()=>{b.abort(),c&&c.removeEventListener("abort",m)};b.on("error",g=>{n(new lt(`request to ${o.url} failed, reason: ${g.message}`,"system",g)),D()}),Nx(b,g=>{d&&d.body&&d.body.destroy(g)}),process.version<"v14"&&b.on("socket",g=>{let y;g.prependListener("end",()=>{y=g._eventsCount}),g.prependListener("close",S=>{if(d&&y<g._eventsCount&&!S){let w=new Error("Premature close");w.code="ERR_STREAM_PREMATURE_CLOSE",d.body.emit("error",w)}})}),b.on("response",g=>{b.setTimeout(0);let y=vx(g.rawHeaders);if(hf(g.statusCode)){let A=y.get("Location"),v=null;try{v=A===null?null:new URL(A,o.url)}catch{if(o.redirect!=="manual"){n(new lt(`uri requested responds with an invalid redirect URL: ${A}`,"invalid-redirect")),D();return}}switch(o.redirect){case"error":n(new lt(`uri requested responds with a redirect, redirect mode is set to error: ${o.url}`,"no-redirect")),D();return;case"manual":break;case"follow":{if(v===null)break;if(o.counter>=o.follow){n(new lt(`maximum redirect reached at: ${o.url}`,"max-redirect")),D();return}let B={headers:new qt(o.headers),follow:o.follow,counter:o.counter+1,agent:o.agent,compress:o.compress,method:o.method,body:mf(o),signal:o.signal,size:o.size,referrer:o.referrer,referrerPolicy:o.referrerPolicy};if(!Dx(o.url,v)||!bx(o.url,v))for(let ae of["authorization","www-authenticate","cookie","cookie2"])B.headers.delete(ae);if(g.statusCode!==303&&o.body&&t.body instanceof Ft.default.Readable){n(new lt("Cannot follow redirect with body being a readable stream","unsupported-redirect")),D();return}(g.statusCode===303||(g.statusCode===301||g.statusCode===302)&&o.method==="POST")&&(B.method="GET",B.body=void 0,B.headers.delete("content-length"));let W=Bx(y);W&&(B.referrerPolicy=W),r(a_(new qo(v,B))),D();return}default:return n(new TypeError(`Redirect option '${o.redirect}' is not a valid value of RequestRedirect`))}}c&&g.once("end",()=>{c.removeEventListener("abort",m)});let S=(0,Ft.pipeline)(g,new Ft.PassThrough,A=>{A&&n(A)});process.version<"v12.10"&&g.on("aborted",m);let w={url:o.url,status:g.statusCode,statusText:g.statusMessage,headers:y,size:o.size,counter:o.counter,highWaterMark:o.highWaterMark},T=y.get("Content-Encoding");if(!o.compress||o.method==="HEAD"||T===null||g.statusCode===204||g.statusCode===304){d=new Le(S,w),r(d);return}let R={flush:Cn.default.Z_SYNC_FLUSH,finishFlush:Cn.default.Z_SYNC_FLUSH};if(T==="gzip"||T==="x-gzip"){S=(0,Ft.pipeline)(S,Cn.default.createGunzip(R),A=>{A&&n(A)}),d=new Le(S,w),r(d);return}if(T==="deflate"||T==="x-deflate"){let A=(0,Ft.pipeline)(g,new Ft.PassThrough,v=>{v&&n(v)});A.once("data",v=>{(v[0]&15)===8?S=(0,Ft.pipeline)(S,Cn.default.createInflate(),B=>{B&&n(B)}):S=(0,Ft.pipeline)(S,Cn.default.createInflateRaw(),B=>{B&&n(B)}),d=new Le(S,w),r(d)}),A.once("end",()=>{d||(d=new Le(S,w),r(d))});return}if(T==="br"){S=(0,Ft.pipeline)(S,Cn.default.createBrotliDecompress(),A=>{A&&n(A)}),d=new Le(S,w),r(d);return}d=new Le(S,w),r(d)}),Ex(b,o).catch(n)})}function Nx(e,t){let r=Aa.Buffer.from(`0\r
\r
`),n=!1,o=!1,i;e.on("response",a=>{let{headers:l}=a;n=l["transfer-encoding"]==="chunked"&&!l["content-length"]}),e.on("socket",a=>{let l=()=>{if(n&&!o){let d=new Error("Premature close");d.code="ERR_STREAM_PREMATURE_CLOSE",t(d)}},c=d=>{o=Aa.Buffer.compare(d.slice(-5),r)===0,!o&&i&&(o=Aa.Buffer.compare(i.slice(-3),r.slice(0,3))===0&&Aa.Buffer.compare(d.slice(-2),r.slice(3))===0),i=d};a.prependListener("close",l),a.on("data",c),e.on("close",()=>{a.removeListener("close",l),a.removeListener("data",c)})})}zo();ka();});var Ho=C(X=>{"use strict";Object.defineProperty(X,"__esModule",{value:!0});X.regexpCode=X.getEsmExportName=X.getProperty=X.safeStringify=X.stringify=X.strConcat=X.addCodeArg=X.str=X._=X.nil=X._Code=X.Name=X.IDENTIFIER=X._CodeOrName=void 0;var Vo=class{};X._CodeOrName=Vo;X.IDENTIFIER=/^[a-z$_][a-z$_0-9]*$/i;var Br=class extends Vo{constructor(t){if(super(),!X.IDENTIFIER.test(t))throw new Error("CodeGen: name must be a valid identifier");this.str=t}toString(){return this.str}emptyStr(){return!1}get names(){return{[this.str]:1}}};X.Name=Br;var Ve=class extends Vo{constructor(t){super(),this._items=typeof t=="string"?[t]:t}toString(){return this.str}emptyStr(){if(this._items.length>1)return!1;let t=this._items[0];return t===""||t==='""'}get str(){var t;return(t=this._str)!==null&&t!==void 0?t:this._str=this._items.reduce((r,n)=>`${r}${n}`,"")}get names(){var t;return(t=this._names)!==null&&t!==void 0?t:this._names=this._items.reduce((r,n)=>(n instanceof Br&&(r[n.str]=(r[n.str]||0)+1),r),{})}};X._Code=Ve;X.nil=new Ve("");function l_(e,...t){let r=[e[0]],n=0;for(;n<t.length;)Df(r,t[n]),r.push(e[++n]);return new Ve(r)}X._=l_;var yf=new Ve("+");function c_(e,...t){let r=[Go(e[0])],n=0;for(;n<t.length;)r.push(yf),Df(r,t[n]),r.push(yf,Go(e[++n]));return jx(r),new Ve(r)}X.str=c_;function Df(e,t){t instanceof Ve?e.push(...t._items):t instanceof Br?e.push(t):e.push(Ux(t))}X.addCodeArg=Df;function jx(e){let t=1;for(;t<e.length-1;){if(e[t]===yf){let r=Lx(e[t-1],e[t+1]);if(r!==void 0){e.splice(t-1,3,r);continue}e[t++]="+"}t++}}function Lx(e,t){if(t==='""')return e;if(e==='""')return t;if(typeof e=="string")return t instanceof Br||e[e.length-1]!=='"'?void 0:typeof t!="string"?`${e.slice(0,-1)}${t}"`:t[0]==='"'?e.slice(0,-1)+t.slice(1):void 0;if(typeof t=="string"&&t[0]==='"'&&!(e instanceof Br))return`"${e}${t.slice(1)}`}function qx(e,t){return t.emptyStr()?e:e.emptyStr()?t:c_`${e}${t}`}X.strConcat=qx;function Ux(e){return typeof e=="number"||typeof e=="boolean"||e===null?e:Go(Array.isArray(e)?e.join(","):e)}function zx(e){return new Ve(Go(e))}X.stringify=zx;function Go(e){return JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")}X.safeStringify=Go;function Wx(e){return typeof e=="string"&&X.IDENTIFIER.test(e)?new Ve(`.${e}`):l_`[${e}]`}X.getProperty=Wx;function Vx(e){if(typeof e=="string"&&X.IDENTIFIER.test(e))return new Ve(`${e}`);throw new Error(`CodeGen: invalid export name: ${e}, use explicit $id name mapping`)}X.getEsmExportName=Vx;function Gx(e){return new Ve(e.toString())}X.regexpCode=Gx});var wf=C(Ie=>{"use strict";Object.defineProperty(Ie,"__esModule",{value:!0});Ie.ValueScope=Ie.ValueScopeName=Ie.Scope=Ie.varKinds=Ie.UsedValueState=void 0;var Be=Ho(),bf=class extends Error{constructor(t){super(`CodeGen: "code" for ${t} not defined`),this.value=t.value}},Na;(function(e){e[e.Started=0]="Started",e[e.Completed=1]="Completed"})(Na||(Ie.UsedValueState=Na={}));Ie.varKinds={const:new Be.Name("const"),let:new Be.Name("let"),var:new Be.Name("var")};var ja=class{constructor({prefixes:t,parent:r}={}){this._names={},this._prefixes=t,this._parent=r}toName(t){return t instanceof Be.Name?t:this.name(t)}name(t){return new Be.Name(this._newName(t))}_newName(t){let r=this._names[t]||this._nameGroup(t);return`${t}${r.index++}`}_nameGroup(t){var r,n;if(!((n=(r=this._parent)===null||r===void 0?void 0:r._prefixes)===null||n===void 0)&&n.has(t)||this._prefixes&&!this._prefixes.has(t))throw new Error(`CodeGen: prefix "${t}" is not allowed in this scope`);return this._names[t]={prefix:t,index:0}}};Ie.Scope=ja;var La=class extends Be.Name{constructor(t,r){super(r),this.prefix=t}setValue(t,{property:r,itemIndex:n}){this.value=t,this.scopePath=(0,Be._)`.${new Be.Name(r)}[${n}]`}};Ie.ValueScopeName=La;var Hx=(0,Be._)`\n`,_f=class extends ja{constructor(t){super(t),this._values={},this._scope=t.scope,this.opts={...t,_n:t.lines?Hx:Be.nil}}get(){return this._scope}name(t){return new La(t,this._newName(t))}value(t,r){var n;if(r.ref===void 0)throw new Error("CodeGen: ref must be passed in value");let o=this.toName(t),{prefix:i}=o,a=(n=r.key)!==null&&n!==void 0?n:r.ref,l=this._values[i];if(l){let p=l.get(a);if(p)return p}else l=this._values[i]=new Map;l.set(a,o);let c=this._scope[i]||(this._scope[i]=[]),d=c.length;return c[d]=r.ref,o.setValue(r,{property:i,itemIndex:d}),o}getValue(t,r){let n=this._values[t];if(n)return n.get(r)}scopeRefs(t,r=this._values){return this._reduceValues(r,n=>{if(n.scopePath===void 0)throw new Error(`CodeGen: name "${n}" has no value`);return(0,Be._)`${t}${n.scopePath}`})}scopeCode(t=this._values,r,n){return this._reduceValues(t,o=>{if(o.value===void 0)throw new Error(`CodeGen: name "${o}" has no value`);return o.value.code},r,n)}_reduceValues(t,r,n={},o){let i=Be.nil;for(let a in t){let l=t[a];if(!l)continue;let c=n[a]=n[a]||new Map;l.forEach(d=>{if(c.has(d))return;c.set(d,Na.Started);let p=r(d);if(p){let m=this.opts.es5?Ie.varKinds.var:Ie.varKinds.const;i=(0,Be._)`${i}${m} ${d} = ${p};${this.opts._n}`}else if(p=o?.(d))i=(0,Be._)`${i}${p}${this.opts._n}`;else throw new bf(d);c.set(d,Na.Completed)})}return i}};Ie.ValueScope=_f});var G=C(V=>{"use strict";Object.defineProperty(V,"__esModule",{value:!0});V.or=V.and=V.not=V.CodeGen=V.operators=V.varKinds=V.ValueScopeName=V.ValueScope=V.Scope=V.Name=V.regexpCode=V.stringify=V.getProperty=V.nil=V.strConcat=V.str=V._=void 0;var J=Ho(),ct=wf(),pr=Ho();Object.defineProperty(V,"_",{enumerable:!0,get:function(){return pr._}});Object.defineProperty(V,"str",{enumerable:!0,get:function(){return pr.str}});Object.defineProperty(V,"strConcat",{enumerable:!0,get:function(){return pr.strConcat}});Object.defineProperty(V,"nil",{enumerable:!0,get:function(){return pr.nil}});Object.defineProperty(V,"getProperty",{enumerable:!0,get:function(){return pr.getProperty}});Object.defineProperty(V,"stringify",{enumerable:!0,get:function(){return pr.stringify}});Object.defineProperty(V,"regexpCode",{enumerable:!0,get:function(){return pr.regexpCode}});Object.defineProperty(V,"Name",{enumerable:!0,get:function(){return pr.Name}});var Wa=wf();Object.defineProperty(V,"Scope",{enumerable:!0,get:function(){return Wa.Scope}});Object.defineProperty(V,"ValueScope",{enumerable:!0,get:function(){return Wa.ValueScope}});Object.defineProperty(V,"ValueScopeName",{enumerable:!0,get:function(){return Wa.ValueScopeName}});Object.defineProperty(V,"varKinds",{enumerable:!0,get:function(){return Wa.varKinds}});V.operators={GT:new J._Code(">"),GTE:new J._Code(">="),LT:new J._Code("<"),LTE:new J._Code("<="),EQ:new J._Code("==="),NEQ:new J._Code("!=="),NOT:new J._Code("!"),OR:new J._Code("||"),AND:new J._Code("&&"),ADD:new J._Code("+")};var Ut=class{optimizeNodes(){return this}optimizeNames(t,r){return this}},Sf=class extends Ut{constructor(t,r,n){super(),this.varKind=t,this.name=r,this.rhs=n}render({es5:t,_n:r}){let n=t?ct.varKinds.var:this.varKind,o=this.rhs===void 0?"":` = ${this.rhs}`;return`${n} ${this.name}${o};`+r}optimizeNames(t,r){if(t[this.name.str])return this.rhs&&(this.rhs=Pn(this.rhs,t,r)),this}get names(){return this.rhs instanceof J._CodeOrName?this.rhs.names:{}}},qa=class extends Ut{constructor(t,r,n){super(),this.lhs=t,this.rhs=r,this.sideEffects=n}render({_n:t}){return`${this.lhs} = ${this.rhs};`+t}optimizeNames(t,r){if(!(this.lhs instanceof J.Name&&!t[this.lhs.str]&&!this.sideEffects))return this.rhs=Pn(this.rhs,t,r),this}get names(){let t=this.lhs instanceof J.Name?{}:{...this.lhs.names};return za(t,this.rhs)}},Ef=class extends qa{constructor(t,r,n,o){super(t,n,o),this.op=r}render({_n:t}){return`${this.lhs} ${this.op}= ${this.rhs};`+t}},vf=class extends Ut{constructor(t){super(),this.label=t,this.names={}}render({_n:t}){return`${this.label}:`+t}},Ff=class extends Ut{constructor(t){super(),this.label=t,this.names={}}render({_n:t}){return`break${this.label?` ${this.label}`:""};`+t}},Cf=class extends Ut{constructor(t){super(),this.error=t}render({_n:t}){return`throw ${this.error};`+t}get names(){return this.error.names}},Tf=class extends Ut{constructor(t){super(),this.code=t}render({_n:t}){return`${this.code};`+t}optimizeNodes(){return`${this.code}`?this:void 0}optimizeNames(t,r){return this.code=Pn(this.code,t,r),this}get names(){return this.code instanceof J._CodeOrName?this.code.names:{}}},Yo=class extends Ut{constructor(t=[]){super(),this.nodes=t}render(t){return this.nodes.reduce((r,n)=>r+n.render(t),"")}optimizeNodes(){let{nodes:t}=this,r=t.length;for(;r--;){let n=t[r].optimizeNodes();Array.isArray(n)?t.splice(r,1,...n):n?t[r]=n:t.splice(r,1)}return t.length>0?this:void 0}optimizeNames(t,r){let{nodes:n}=this,o=n.length;for(;o--;){let i=n[o];i.optimizeNames(t,r)||(Yx(t,i.names),n.splice(o,1))}return n.length>0?this:void 0}get names(){return this.nodes.reduce((t,r)=>Mr(t,r.names),{})}},zt=class extends Yo{render(t){return"{"+t._n+super.render(t)+"}"+t._n}},Rf=class extends Yo{},xn=class extends zt{};xn.kind="else";var Ir=class e extends zt{constructor(t,r){super(r),this.condition=t}render(t){let r=`if(${this.condition})`+super.render(t);return this.else&&(r+="else "+this.else.render(t)),r}optimizeNodes(){super.optimizeNodes();let t=this.condition;if(t===!0)return this.nodes;let r=this.else;if(r){let n=r.optimizeNodes();r=this.else=Array.isArray(n)?new xn(n):n}if(r)return t===!1?r instanceof e?r:r.nodes:this.nodes.length?this:new e(f_(t),r instanceof e?[r]:r.nodes);if(!(t===!1||!this.nodes.length))return this}optimizeNames(t,r){var n;if(this.else=(n=this.else)===null||n===void 0?void 0:n.optimizeNames(t,r),!!(super.optimizeNames(t,r)||this.else))return this.condition=Pn(this.condition,t,r),this}get names(){let t=super.names;return za(t,this.condition),this.else&&Mr(t,this.else.names),t}};Ir.kind="if";var kr=class extends zt{};kr.kind="for";var Af=class extends kr{constructor(t){super(),this.iteration=t}render(t){return`for(${this.iteration})`+super.render(t)}optimizeNames(t,r){if(super.optimizeNames(t,r))return this.iteration=Pn(this.iteration,t,r),this}get names(){return Mr(super.names,this.iteration.names)}},$f=class extends kr{constructor(t,r,n,o){super(),this.varKind=t,this.name=r,this.from=n,this.to=o}render(t){let r=t.es5?ct.varKinds.var:this.varKind,{name:n,from:o,to:i}=this;return`for(${r} ${n}=${o}; ${n}<${i}; ${n}++)`+super.render(t)}get names(){let t=za(super.names,this.from);return za(t,this.to)}},Ua=class extends kr{constructor(t,r,n,o){super(),this.loop=t,this.varKind=r,this.name=n,this.iterable=o}render(t){return`for(${this.varKind} ${this.name} ${this.loop} ${this.iterable})`+super.render(t)}optimizeNames(t,r){if(super.optimizeNames(t,r))return this.iterable=Pn(this.iterable,t,r),this}get names(){return Mr(super.names,this.iterable.names)}},Ko=class extends zt{constructor(t,r,n){super(),this.name=t,this.args=r,this.async=n}render(t){return`${this.async?"async ":""}function ${this.name}(${this.args})`+super.render(t)}};Ko.kind="func";var Jo=class extends Yo{render(t){return"return "+super.render(t)}};Jo.kind="return";var xf=class extends zt{render(t){let r="try"+super.render(t);return this.catch&&(r+=this.catch.render(t)),this.finally&&(r+=this.finally.render(t)),r}optimizeNodes(){var t,r;return super.optimizeNodes(),(t=this.catch)===null||t===void 0||t.optimizeNodes(),(r=this.finally)===null||r===void 0||r.optimizeNodes(),this}optimizeNames(t,r){var n,o;return super.optimizeNames(t,r),(n=this.catch)===null||n===void 0||n.optimizeNames(t,r),(o=this.finally)===null||o===void 0||o.optimizeNames(t,r),this}get names(){let t=super.names;return this.catch&&Mr(t,this.catch.names),this.finally&&Mr(t,this.finally.names),t}},Qo=class extends zt{constructor(t){super(),this.error=t}render(t){return`catch(${this.error})`+super.render(t)}};Qo.kind="catch";var Xo=class extends zt{render(t){return"finally"+super.render(t)}};Xo.kind="finally";var Pf=class{constructor(t,r={}){this._values={},this._blockStarts=[],this._constants={},this.opts={...r,_n:r.lines?`
`:""},this._extScope=t,this._scope=new ct.Scope({parent:t}),this._nodes=[new Rf]}toString(){return this._root.render(this.opts)}name(t){return this._scope.name(t)}scopeName(t){return this._extScope.name(t)}scopeValue(t,r){let n=this._extScope.value(t,r);return(this._values[n.prefix]||(this._values[n.prefix]=new Set)).add(n),n}getScopeValue(t,r){return this._extScope.getValue(t,r)}scopeRefs(t){return this._extScope.scopeRefs(t,this._values)}scopeCode(){return this._extScope.scopeCode(this._values)}_def(t,r,n,o){let i=this._scope.toName(r);return n!==void 0&&o&&(this._constants[i.str]=n),this._leafNode(new Sf(t,i,n)),i}const(t,r,n){return this._def(ct.varKinds.const,t,r,n)}let(t,r,n){return this._def(ct.varKinds.let,t,r,n)}var(t,r,n){return this._def(ct.varKinds.var,t,r,n)}assign(t,r,n){return this._leafNode(new qa(t,r,n))}add(t,r){return this._leafNode(new Ef(t,V.operators.ADD,r))}code(t){return typeof t=="function"?t():t!==J.nil&&this._leafNode(new Tf(t)),this}object(...t){let r=["{"];for(let[n,o]of t)r.length>1&&r.push(","),r.push(n),(n!==o||this.opts.es5)&&(r.push(":"),(0,J.addCodeArg)(r,o));return r.push("}"),new J._Code(r)}if(t,r,n){if(this._blockNode(new Ir(t)),r&&n)this.code(r).else().code(n).endIf();else if(r)this.code(r).endIf();else if(n)throw new Error('CodeGen: "else" body without "then" body');return this}elseIf(t){return this._elseNode(new Ir(t))}else(){return this._elseNode(new xn)}endIf(){return this._endBlockNode(Ir,xn)}_for(t,r){return this._blockNode(t),r&&this.code(r).endFor(),this}for(t,r){return this._for(new Af(t),r)}forRange(t,r,n,o,i=this.opts.es5?ct.varKinds.var:ct.varKinds.let){let a=this._scope.toName(t);return this._for(new $f(i,a,r,n),()=>o(a))}forOf(t,r,n,o=ct.varKinds.const){let i=this._scope.toName(t);if(this.opts.es5){let a=r instanceof J.Name?r:this.var("_arr",r);return this.forRange("_i",0,(0,J._)`${a}.length`,l=>{this.var(i,(0,J._)`${a}[${l}]`),n(i)})}return this._for(new Ua("of",o,i,r),()=>n(i))}forIn(t,r,n,o=this.opts.es5?ct.varKinds.var:ct.varKinds.const){if(this.opts.ownProperties)return this.forOf(t,(0,J._)`Object.keys(${r})`,n);let i=this._scope.toName(t);return this._for(new Ua("in",o,i,r),()=>n(i))}endFor(){return this._endBlockNode(kr)}label(t){return this._leafNode(new vf(t))}break(t){return this._leafNode(new Ff(t))}return(t){let r=new Jo;if(this._blockNode(r),this.code(t),r.nodes.length!==1)throw new Error('CodeGen: "return" should have one node');return this._endBlockNode(Jo)}try(t,r,n){if(!r&&!n)throw new Error('CodeGen: "try" without "catch" and "finally"');let o=new xf;if(this._blockNode(o),this.code(t),r){let i=this.name("e");this._currNode=o.catch=new Qo(i),r(i)}return n&&(this._currNode=o.finally=new Xo,this.code(n)),this._endBlockNode(Qo,Xo)}throw(t){return this._leafNode(new Cf(t))}block(t,r){return this._blockStarts.push(this._nodes.length),t&&this.code(t).endBlock(r),this}endBlock(t){let r=this._blockStarts.pop();if(r===void 0)throw new Error("CodeGen: not in self-balancing block");let n=this._nodes.length-r;if(n<0||t!==void 0&&n!==t)throw new Error(`CodeGen: wrong number of nodes: ${n} vs ${t} expected`);return this._nodes.length=r,this}func(t,r=J.nil,n,o){return this._blockNode(new Ko(t,r,n)),o&&this.code(o).endFunc(),this}endFunc(){return this._endBlockNode(Ko)}optimize(t=1){for(;t-- >0;)this._root.optimizeNodes(),this._root.optimizeNames(this._root.names,this._constants)}_leafNode(t){return this._currNode.nodes.push(t),this}_blockNode(t){this._currNode.nodes.push(t),this._nodes.push(t)}_endBlockNode(t,r){let n=this._currNode;if(n instanceof t||r&&n instanceof r)return this._nodes.pop(),this;throw new Error(`CodeGen: not in block "${r?`${t.kind}/${r.kind}`:t.kind}"`)}_elseNode(t){let r=this._currNode;if(!(r instanceof Ir))throw new Error('CodeGen: "else" without "if"');return this._currNode=r.else=t,this}get _root(){return this._nodes[0]}get _currNode(){let t=this._nodes;return t[t.length-1]}set _currNode(t){let r=this._nodes;r[r.length-1]=t}};V.CodeGen=Pf;function Mr(e,t){for(let r in t)e[r]=(e[r]||0)+(t[r]||0);return e}function za(e,t){return t instanceof J._CodeOrName?Mr(e,t.names):e}function Pn(e,t,r){if(e instanceof J.Name)return n(e);if(!o(e))return e;return new J._Code(e._items.reduce((i,a)=>(a instanceof J.Name&&(a=n(a)),a instanceof J._Code?i.push(...a._items):i.push(a),i),[]));function n(i){let a=r[i.str];return a===void 0||t[i.str]!==1?i:(delete t[i.str],a)}function o(i){return i instanceof J._Code&&i._items.some(a=>a instanceof J.Name&&t[a.str]===1&&r[a.str]!==void 0)}}function Yx(e,t){for(let r in t)e[r]=(e[r]||0)-(t[r]||0)}function f_(e){return typeof e=="boolean"||typeof e=="number"||e===null?!e:(0,J._)`!${Of(e)}`}V.not=f_;var Kx=d_(V.operators.AND);function Jx(...e){return e.reduce(Kx)}V.and=Jx;var Qx=d_(V.operators.OR);function Xx(...e){return e.reduce(Qx)}V.or=Xx;function d_(e){return(t,r)=>t===J.nil?r:r===J.nil?t:(0,J._)`${Of(t)} ${e} ${Of(r)}`}function Of(e){return e instanceof J.Name?e:(0,J._)`(${e})`}});var Z=C(H=>{"use strict";Object.defineProperty(H,"__esModule",{value:!0});H.checkStrictMode=H.getErrorPath=H.Type=H.useFunc=H.setEvaluated=H.evaluatedPropsToName=H.mergeEvaluated=H.eachItem=H.unescapeJsonPointer=H.escapeJsonPointer=H.escapeFragment=H.unescapeFragment=H.schemaRefOrVal=H.schemaHasRulesButRef=H.schemaHasRules=H.checkUnknownRules=H.alwaysValidSchema=H.toHash=void 0;var se=G(),Zx=Ho();function e2(e){let t={};for(let r of e)t[r]=!0;return t}H.toHash=e2;function t2(e,t){return typeof t=="boolean"?t:Object.keys(t).length===0?!0:(h_(e,t),!g_(t,e.self.RULES.all))}H.alwaysValidSchema=t2;function h_(e,t=e.schema){let{opts:r,self:n}=e;if(!r.strictSchema||typeof t=="boolean")return;let o=n.RULES.keywords;for(let i in t)o[i]||b_(e,`unknown keyword: "${i}"`)}H.checkUnknownRules=h_;function g_(e,t){if(typeof e=="boolean")return!e;for(let r in e)if(t[r])return!0;return!1}H.schemaHasRules=g_;function r2(e,t){if(typeof e=="boolean")return!e;for(let r in e)if(r!=="$ref"&&t.all[r])return!0;return!1}H.schemaHasRulesButRef=r2;function n2({topSchemaRef:e,schemaPath:t},r,n,o){if(!o){if(typeof r=="number"||typeof r=="boolean")return r;if(typeof r=="string")return(0,se._)`${r}`}return(0,se._)`${e}${t}${(0,se.getProperty)(n)}`}H.schemaRefOrVal=n2;function o2(e){return y_(decodeURIComponent(e))}H.unescapeFragment=o2;function i2(e){return encodeURIComponent(If(e))}H.escapeFragment=i2;function If(e){return typeof e=="number"?`${e}`:e.replace(/~/g,"~0").replace(/\//g,"~1")}H.escapeJsonPointer=If;function y_(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}H.unescapeJsonPointer=y_;function s2(e,t){if(Array.isArray(e))for(let r of e)t(r);else t(e)}H.eachItem=s2;function p_({mergeNames:e,mergeToName:t,mergeValues:r,resultToName:n}){return(o,i,a,l)=>{let c=a===void 0?i:a instanceof se.Name?(i instanceof se.Name?e(o,i,a):t(o,i,a),a):i instanceof se.Name?(t(o,a,i),i):r(i,a);return l===se.Name&&!(c instanceof se.Name)?n(o,c):c}}H.mergeEvaluated={props:p_({mergeNames:(e,t,r)=>e.if((0,se._)`${r} !== true && ${t} !== undefined`,()=>{e.if((0,se._)`${t} === true`,()=>e.assign(r,!0),()=>e.assign(r,(0,se._)`${r} || {}`).code((0,se._)`Object.assign(${r}, ${t})`))}),mergeToName:(e,t,r)=>e.if((0,se._)`${r} !== true`,()=>{t===!0?e.assign(r,!0):(e.assign(r,(0,se._)`${r} || {}`),kf(e,r,t))}),mergeValues:(e,t)=>e===!0?!0:{...e,...t},resultToName:D_}),items:p_({mergeNames:(e,t,r)=>e.if((0,se._)`${r} !== true && ${t} !== undefined`,()=>e.assign(r,(0,se._)`${t} === true ? true : ${r} > ${t} ? ${r} : ${t}`)),mergeToName:(e,t,r)=>e.if((0,se._)`${r} !== true`,()=>e.assign(r,t===!0?!0:(0,se._)`${r} > ${t} ? ${r} : ${t}`)),mergeValues:(e,t)=>e===!0?!0:Math.max(e,t),resultToName:(e,t)=>e.var("items",t)})};function D_(e,t){if(t===!0)return e.var("props",!0);let r=e.var("props",(0,se._)`{}`);return t!==void 0&&kf(e,r,t),r}H.evaluatedPropsToName=D_;function kf(e,t,r){Object.keys(r).forEach(n=>e.assign((0,se._)`${t}${(0,se.getProperty)(n)}`,!0))}H.setEvaluated=kf;var m_={};function a2(e,t){return e.scopeValue("func",{ref:t,code:m_[t.code]||(m_[t.code]=new Zx._Code(t.code))})}H.useFunc=a2;var Bf;(function(e){e[e.Num=0]="Num",e[e.Str=1]="Str"})(Bf||(H.Type=Bf={}));function u2(e,t,r){if(e instanceof se.Name){let n=t===Bf.Num;return r?n?(0,se._)`"[" + ${e} + "]"`:(0,se._)`"['" + ${e} + "']"`:n?(0,se._)`"/" + ${e}`:(0,se._)`"/" + ${e}.replace(/~/g, "~0").replace(/\\//g, "~1")`}return r?(0,se.getProperty)(e).toString():"/"+If(e)}H.getErrorPath=u2;function b_(e,t,r=e.opts.strictSchema){if(r){if(t=`strict mode: ${t}`,r===!0)throw new Error(t);e.self.logger.warn(t)}}H.checkStrictMode=b_});var Wt=C(Mf=>{"use strict";Object.defineProperty(Mf,"__esModule",{value:!0});var Ce=G(),l2={data:new Ce.Name("data"),valCxt:new Ce.Name("valCxt"),instancePath:new Ce.Name("instancePath"),parentData:new Ce.Name("parentData"),parentDataProperty:new Ce.Name("parentDataProperty"),rootData:new Ce.Name("rootData"),dynamicAnchors:new Ce.Name("dynamicAnchors"),vErrors:new Ce.Name("vErrors"),errors:new Ce.Name("errors"),this:new Ce.Name("this"),self:new Ce.Name("self"),scope:new Ce.Name("scope"),json:new Ce.Name("json"),jsonPos:new Ce.Name("jsonPos"),jsonLen:new Ce.Name("jsonLen"),jsonPart:new Ce.Name("jsonPart")};Mf.default=l2});var Zo=C(Te=>{"use strict";Object.defineProperty(Te,"__esModule",{value:!0});Te.extendErrors=Te.resetErrorsCount=Te.reportExtraError=Te.reportError=Te.keyword$DataError=Te.keywordError=void 0;var Q=G(),Va=Z(),$e=Wt();Te.keywordError={message:({keyword:e})=>(0,Q.str)`must pass "${e}" keyword validation`};Te.keyword$DataError={message:({keyword:e,schemaType:t})=>t?(0,Q.str)`"${e}" keyword must be ${t} ($data)`:(0,Q.str)`"${e}" keyword is invalid ($data)`};function c2(e,t=Te.keywordError,r,n){let{it:o}=e,{gen:i,compositeRule:a,allErrors:l}=o,c=S_(e,t,r);n??(a||l)?__(i,c):w_(o,(0,Q._)`[${c}]`)}Te.reportError=c2;function f2(e,t=Te.keywordError,r){let{it:n}=e,{gen:o,compositeRule:i,allErrors:a}=n,l=S_(e,t,r);__(o,l),i||a||w_(n,$e.default.vErrors)}Te.reportExtraError=f2;function d2(e,t){e.assign($e.default.errors,t),e.if((0,Q._)`${$e.default.vErrors} !== null`,()=>e.if(t,()=>e.assign((0,Q._)`${$e.default.vErrors}.length`,t),()=>e.assign($e.default.vErrors,null)))}Te.resetErrorsCount=d2;function p2({gen:e,keyword:t,schemaValue:r,data:n,errsCount:o,it:i}){if(o===void 0)throw new Error("ajv implementation error");let a=e.name("err");e.forRange("i",o,$e.default.errors,l=>{e.const(a,(0,Q._)`${$e.default.vErrors}[${l}]`),e.if((0,Q._)`${a}.instancePath === undefined`,()=>e.assign((0,Q._)`${a}.instancePath`,(0,Q.strConcat)($e.default.instancePath,i.errorPath))),e.assign((0,Q._)`${a}.schemaPath`,(0,Q.str)`${i.errSchemaPath}/${t}`),i.opts.verbose&&(e.assign((0,Q._)`${a}.schema`,r),e.assign((0,Q._)`${a}.data`,n))})}Te.extendErrors=p2;function __(e,t){let r=e.const("err",t);e.if((0,Q._)`${$e.default.vErrors} === null`,()=>e.assign($e.default.vErrors,(0,Q._)`[${r}]`),(0,Q._)`${$e.default.vErrors}.push(${r})`),e.code((0,Q._)`${$e.default.errors}++`)}function w_(e,t){let{gen:r,validateName:n,schemaEnv:o}=e;o.$async?r.throw((0,Q._)`new ${e.ValidationError}(${t})`):(r.assign((0,Q._)`${n}.errors`,t),r.return(!1))}var Nr={keyword:new Q.Name("keyword"),schemaPath:new Q.Name("schemaPath"),params:new Q.Name("params"),propertyName:new Q.Name("propertyName"),message:new Q.Name("message"),schema:new Q.Name("schema"),parentSchema:new Q.Name("parentSchema")};function S_(e,t,r){let{createErrors:n}=e.it;return n===!1?(0,Q._)`{}`:m2(e,t,r)}function m2(e,t,r={}){let{gen:n,it:o}=e,i=[h2(o,r),g2(e,r)];return y2(e,t,i),n.object(...i)}function h2({errorPath:e},{instancePath:t}){let r=t?(0,Q.str)`${e}${(0,Va.getErrorPath)(t,Va.Type.Str)}`:e;return[$e.default.instancePath,(0,Q.strConcat)($e.default.instancePath,r)]}function g2({keyword:e,it:{errSchemaPath:t}},{schemaPath:r,parentSchema:n}){let o=n?t:(0,Q.str)`${t}/${e}`;return r&&(o=(0,Q.str)`${o}${(0,Va.getErrorPath)(r,Va.Type.Str)}`),[Nr.schemaPath,o]}function y2(e,{params:t,message:r},n){let{keyword:o,data:i,schemaValue:a,it:l}=e,{opts:c,propertyName:d,topSchemaRef:p,schemaPath:m}=l;n.push([Nr.keyword,o],[Nr.params,typeof t=="function"?t(e):t||(0,Q._)`{}`]),c.messages&&n.push([Nr.message,typeof r=="function"?r(e):r]),c.verbose&&n.push([Nr.schema,a],[Nr.parentSchema,(0,Q._)`${p}${m}`],[$e.default.data,i]),d&&n.push([Nr.propertyName,d])}});var v_=C(On=>{"use strict";Object.defineProperty(On,"__esModule",{value:!0});On.boolOrEmptySchema=On.topBoolOrEmptySchema=void 0;var D2=Zo(),b2=G(),_2=Wt(),w2={message:"boolean schema is false"};function S2(e){let{gen:t,schema:r,validateName:n}=e;r===!1?E_(e,!1):typeof r=="object"&&r.$async===!0?t.return(_2.default.data):(t.assign((0,b2._)`${n}.errors`,null),t.return(!0))}On.topBoolOrEmptySchema=S2;function E2(e,t){let{gen:r,schema:n}=e;n===!1?(r.var(t,!1),E_(e)):r.var(t,!0)}On.boolOrEmptySchema=E2;function E_(e,t){let{gen:r,data:n}=e,o={gen:r,keyword:"false schema",data:n,schema:!1,schemaCode:!1,schemaValue:!1,params:{},it:e};(0,D2.reportError)(o,w2,void 0,t)}});var Nf=C(Bn=>{"use strict";Object.defineProperty(Bn,"__esModule",{value:!0});Bn.getRules=Bn.isJSONType=void 0;var v2=["string","number","integer","boolean","null","object","array"],F2=new Set(v2);function C2(e){return typeof e=="string"&&F2.has(e)}Bn.isJSONType=C2;function T2(){let e={number:{type:"number",rules:[]},string:{type:"string",rules:[]},array:{type:"array",rules:[]},object:{type:"object",rules:[]}};return{types:{...e,integer:!0,boolean:!0,null:!0},rules:[{rules:[]},e.number,e.string,e.array,e.object],post:{rules:[]},all:{},keywords:{}}}Bn.getRules=T2});var jf=C(mr=>{"use strict";Object.defineProperty(mr,"__esModule",{value:!0});mr.shouldUseRule=mr.shouldUseGroup=mr.schemaHasRulesForType=void 0;function R2({schema:e,self:t},r){let n=t.RULES.types[r];return n&&n!==!0&&F_(e,n)}mr.schemaHasRulesForType=R2;function F_(e,t){return t.rules.some(r=>C_(e,r))}mr.shouldUseGroup=F_;function C_(e,t){var r;return e[t.keyword]!==void 0||((r=t.definition.implements)===null||r===void 0?void 0:r.some(n=>e[n]!==void 0))}mr.shouldUseRule=C_});var ei=C(Re=>{"use strict";Object.defineProperty(Re,"__esModule",{value:!0});Re.reportTypeError=Re.checkDataTypes=Re.checkDataType=Re.coerceAndCheckDataType=Re.getJSONTypes=Re.getSchemaTypes=Re.DataType=void 0;var A2=Nf(),$2=jf(),x2=Zo(),U=G(),T_=Z(),In;(function(e){e[e.Correct=0]="Correct",e[e.Wrong=1]="Wrong"})(In||(Re.DataType=In={}));function P2(e){let t=R_(e.type);if(t.includes("null")){if(e.nullable===!1)throw new Error("type: null contradicts nullable: false")}else{if(!t.length&&e.nullable!==void 0)throw new Error('"nullable" cannot be used without "type"');e.nullable===!0&&t.push("null")}return t}Re.getSchemaTypes=P2;function R_(e){let t=Array.isArray(e)?e:e?[e]:[];if(t.every(A2.isJSONType))return t;throw new Error("type must be JSONType or JSONType[]: "+t.join(","))}Re.getJSONTypes=R_;function O2(e,t){let{gen:r,data:n,opts:o}=e,i=B2(t,o.coerceTypes),a=t.length>0&&!(i.length===0&&t.length===1&&(0,$2.schemaHasRulesForType)(e,t[0]));if(a){let l=qf(t,n,o.strictNumbers,In.Wrong);r.if(l,()=>{i.length?I2(e,t,i):Uf(e)})}return a}Re.coerceAndCheckDataType=O2;var A_=new Set(["string","number","integer","boolean","null"]);function B2(e,t){return t?e.filter(r=>A_.has(r)||t==="array"&&r==="array"):[]}function I2(e,t,r){let{gen:n,data:o,opts:i}=e,a=n.let("dataType",(0,U._)`typeof ${o}`),l=n.let("coerced",(0,U._)`undefined`);i.coerceTypes==="array"&&n.if((0,U._)`${a} == 'object' && Array.isArray(${o}) && ${o}.length == 1`,()=>n.assign(o,(0,U._)`${o}[0]`).assign(a,(0,U._)`typeof ${o}`).if(qf(t,o,i.strictNumbers),()=>n.assign(l,o))),n.if((0,U._)`${l} !== undefined`);for(let d of r)(A_.has(d)||d==="array"&&i.coerceTypes==="array")&&c(d);n.else(),Uf(e),n.endIf(),n.if((0,U._)`${l} !== undefined`,()=>{n.assign(o,l),k2(e,l)});function c(d){switch(d){case"string":n.elseIf((0,U._)`${a} == "number" || ${a} == "boolean"`).assign(l,(0,U._)`"" + ${o}`).elseIf((0,U._)`${o} === null`).assign(l,(0,U._)`""`);return;case"number":n.elseIf((0,U._)`${a} == "boolean" || ${o} === null
              || (${a} == "string" && ${o} && ${o} == +${o})`).assign(l,(0,U._)`+${o}`);return;case"integer":n.elseIf((0,U._)`${a} === "boolean" || ${o} === null
              || (${a} === "string" && ${o} && ${o} == +${o} && !(${o} % 1))`).assign(l,(0,U._)`+${o}`);return;case"boolean":n.elseIf((0,U._)`${o} === "false" || ${o} === 0 || ${o} === null`).assign(l,!1).elseIf((0,U._)`${o} === "true" || ${o} === 1`).assign(l,!0);return;case"null":n.elseIf((0,U._)`${o} === "" || ${o} === 0 || ${o} === false`),n.assign(l,null);return;case"array":n.elseIf((0,U._)`${a} === "string" || ${a} === "number"
              || ${a} === "boolean" || ${o} === null`).assign(l,(0,U._)`[${o}]`)}}}function k2({gen:e,parentData:t,parentDataProperty:r},n){e.if((0,U._)`${t} !== undefined`,()=>e.assign((0,U._)`${t}[${r}]`,n))}function Lf(e,t,r,n=In.Correct){let o=n===In.Correct?U.operators.EQ:U.operators.NEQ,i;switch(e){case"null":return(0,U._)`${t} ${o} null`;case"array":i=(0,U._)`Array.isArray(${t})`;break;case"object":i=(0,U._)`${t} && typeof ${t} == "object" && !Array.isArray(${t})`;break;case"integer":i=a((0,U._)`!(${t} % 1) && !isNaN(${t})`);break;case"number":i=a();break;default:return(0,U._)`typeof ${t} ${o} ${e}`}return n===In.Correct?i:(0,U.not)(i);function a(l=U.nil){return(0,U.and)((0,U._)`typeof ${t} == "number"`,l,r?(0,U._)`isFinite(${t})`:U.nil)}}Re.checkDataType=Lf;function qf(e,t,r,n){if(e.length===1)return Lf(e[0],t,r,n);let o,i=(0,T_.toHash)(e);if(i.array&&i.object){let a=(0,U._)`typeof ${t} != "object"`;o=i.null?a:(0,U._)`!${t} || ${a}`,delete i.null,delete i.array,delete i.object}else o=U.nil;i.number&&delete i.integer;for(let a in i)o=(0,U.and)(o,Lf(a,t,r,n));return o}Re.checkDataTypes=qf;var M2={message:({schema:e})=>`must be ${e}`,params:({schema:e,schemaValue:t})=>typeof e=="string"?(0,U._)`{type: ${e}}`:(0,U._)`{type: ${t}}`};function Uf(e){let t=N2(e);(0,x2.reportError)(t,M2)}Re.reportTypeError=Uf;function N2(e){let{gen:t,data:r,schema:n}=e,o=(0,T_.schemaRefOrVal)(e,n,"type");return{gen:t,keyword:"type",data:r,schema:n.type,schemaCode:o,schemaValue:o,parentSchema:n,params:{},it:e}}});var x_=C(Ga=>{"use strict";Object.defineProperty(Ga,"__esModule",{value:!0});Ga.assignDefaults=void 0;var kn=G(),j2=Z();function L2(e,t){let{properties:r,items:n}=e.schema;if(t==="object"&&r)for(let o in r)$_(e,o,r[o].default);else t==="array"&&Array.isArray(n)&&n.forEach((o,i)=>$_(e,i,o.default))}Ga.assignDefaults=L2;function $_(e,t,r){let{gen:n,compositeRule:o,data:i,opts:a}=e;if(r===void 0)return;let l=(0,kn._)`${i}${(0,kn.getProperty)(t)}`;if(o){(0,j2.checkStrictMode)(e,`default is ignored for: ${l}`);return}let c=(0,kn._)`${l} === undefined`;a.useDefaults==="empty"&&(c=(0,kn._)`${c} || ${l} === null || ${l} === ""`),n.if(c,(0,kn._)`${l} = ${(0,kn.stringify)(r)}`)}});var Ge=C(ne=>{"use strict";Object.defineProperty(ne,"__esModule",{value:!0});ne.validateUnion=ne.validateArray=ne.usePattern=ne.callValidateCode=ne.schemaProperties=ne.allSchemaProperties=ne.noPropertyInData=ne.propertyInData=ne.isOwnProperty=ne.hasPropFunc=ne.reportMissingProp=ne.checkMissingProp=ne.checkReportMissingProp=void 0;var le=G(),zf=Z(),hr=Wt(),q2=Z();function U2(e,t){let{gen:r,data:n,it:o}=e;r.if(Vf(r,n,t,o.opts.ownProperties),()=>{e.setParams({missingProperty:(0,le._)`${t}`},!0),e.error()})}ne.checkReportMissingProp=U2;function z2({gen:e,data:t,it:{opts:r}},n,o){return(0,le.or)(...n.map(i=>(0,le.and)(Vf(e,t,i,r.ownProperties),(0,le._)`${o} = ${i}`)))}ne.checkMissingProp=z2;function W2(e,t){e.setParams({missingProperty:t},!0),e.error()}ne.reportMissingProp=W2;function P_(e){return e.scopeValue("func",{ref:Object.prototype.hasOwnProperty,code:(0,le._)`Object.prototype.hasOwnProperty`})}ne.hasPropFunc=P_;function Wf(e,t,r){return(0,le._)`${P_(e)}.call(${t}, ${r})`}ne.isOwnProperty=Wf;function V2(e,t,r,n){let o=(0,le._)`${t}${(0,le.getProperty)(r)} !== undefined`;return n?(0,le._)`${o} && ${Wf(e,t,r)}`:o}ne.propertyInData=V2;function Vf(e,t,r,n){let o=(0,le._)`${t}${(0,le.getProperty)(r)} === undefined`;return n?(0,le.or)(o,(0,le.not)(Wf(e,t,r))):o}ne.noPropertyInData=Vf;function O_(e){return e?Object.keys(e).filter(t=>t!=="__proto__"):[]}ne.allSchemaProperties=O_;function G2(e,t){return O_(t).filter(r=>!(0,zf.alwaysValidSchema)(e,t[r]))}ne.schemaProperties=G2;function H2({schemaCode:e,data:t,it:{gen:r,topSchemaRef:n,schemaPath:o,errorPath:i},it:a},l,c,d){let p=d?(0,le._)`${e}, ${t}, ${n}${o}`:t,m=[[hr.default.instancePath,(0,le.strConcat)(hr.default.instancePath,i)],[hr.default.parentData,a.parentData],[hr.default.parentDataProperty,a.parentDataProperty],[hr.default.rootData,hr.default.rootData]];a.opts.dynamicRef&&m.push([hr.default.dynamicAnchors,hr.default.dynamicAnchors]);let b=(0,le._)`${p}, ${r.object(...m)}`;return c!==le.nil?(0,le._)`${l}.call(${c}, ${b})`:(0,le._)`${l}(${b})`}ne.callValidateCode=H2;var Y2=(0,le._)`new RegExp`;function K2({gen:e,it:{opts:t}},r){let n=t.unicodeRegExp?"u":"",{regExp:o}=t.code,i=o(r,n);return e.scopeValue("pattern",{key:i.toString(),ref:i,code:(0,le._)`${o.code==="new RegExp"?Y2:(0,q2.useFunc)(e,o)}(${r}, ${n})`})}ne.usePattern=K2;function J2(e){let{gen:t,data:r,keyword:n,it:o}=e,i=t.name("valid");if(o.allErrors){let l=t.let("valid",!0);return a(()=>t.assign(l,!1)),l}return t.var(i,!0),a(()=>t.break()),i;function a(l){let c=t.const("len",(0,le._)`${r}.length`);t.forRange("i",0,c,d=>{e.subschema({keyword:n,dataProp:d,dataPropType:zf.Type.Num},i),t.if((0,le.not)(i),l)})}}ne.validateArray=J2;function Q2(e){let{gen:t,schema:r,keyword:n,it:o}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");if(r.some(c=>(0,zf.alwaysValidSchema)(o,c))&&!o.opts.unevaluated)return;let a=t.let("valid",!1),l=t.name("_valid");t.block(()=>r.forEach((c,d)=>{let p=e.subschema({keyword:n,schemaProp:d,compositeRule:!0},l);t.assign(a,(0,le._)`${a} || ${l}`),e.mergeValidEvaluated(p,l)||t.if((0,le.not)(a))})),e.result(a,()=>e.reset(),()=>e.error(!0))}ne.validateUnion=Q2});var k_=C(Tt=>{"use strict";Object.defineProperty(Tt,"__esModule",{value:!0});Tt.validateKeywordUsage=Tt.validSchemaType=Tt.funcKeywordCode=Tt.macroKeywordCode=void 0;var xe=G(),jr=Wt(),X2=Ge(),Z2=Zo();function eP(e,t){let{gen:r,keyword:n,schema:o,parentSchema:i,it:a}=e,l=t.macro.call(a.self,o,i,a),c=I_(r,n,l);a.opts.validateSchema!==!1&&a.self.validateSchema(l,!0);let d=r.name("valid");e.subschema({schema:l,schemaPath:xe.nil,errSchemaPath:`${a.errSchemaPath}/${n}`,topSchemaRef:c,compositeRule:!0},d),e.pass(d,()=>e.error(!0))}Tt.macroKeywordCode=eP;function tP(e,t){var r;let{gen:n,keyword:o,schema:i,parentSchema:a,$data:l,it:c}=e;nP(c,t);let d=!l&&t.compile?t.compile.call(c.self,i,a,c):t.validate,p=I_(n,o,d),m=n.let("valid");e.block$data(m,b),e.ok((r=t.valid)!==null&&r!==void 0?r:m);function b(){if(t.errors===!1)y(),t.modifying&&B_(e),S(()=>e.error());else{let w=t.async?D():g();t.modifying&&B_(e),S(()=>rP(e,w))}}function D(){let w=n.let("ruleErrs",null);return n.try(()=>y((0,xe._)`await `),T=>n.assign(m,!1).if((0,xe._)`${T} instanceof ${c.ValidationError}`,()=>n.assign(w,(0,xe._)`${T}.errors`),()=>n.throw(T))),w}function g(){let w=(0,xe._)`${p}.errors`;return n.assign(w,null),y(xe.nil),w}function y(w=t.async?(0,xe._)`await `:xe.nil){let T=c.opts.passContext?jr.default.this:jr.default.self,R=!("compile"in t&&!l||t.schema===!1);n.assign(m,(0,xe._)`${w}${(0,X2.callValidateCode)(e,p,T,R)}`,t.modifying)}function S(w){var T;n.if((0,xe.not)((T=t.valid)!==null&&T!==void 0?T:m),w)}}Tt.funcKeywordCode=tP;function B_(e){let{gen:t,data:r,it:n}=e;t.if(n.parentData,()=>t.assign(r,(0,xe._)`${n.parentData}[${n.parentDataProperty}]`))}function rP(e,t){let{gen:r}=e;r.if((0,xe._)`Array.isArray(${t})`,()=>{r.assign(jr.default.vErrors,(0,xe._)`${jr.default.vErrors} === null ? ${t} : ${jr.default.vErrors}.concat(${t})`).assign(jr.default.errors,(0,xe._)`${jr.default.vErrors}.length`),(0,Z2.extendErrors)(e)},()=>e.error())}function nP({schemaEnv:e},t){if(t.async&&!e.$async)throw new Error("async keyword in sync schema")}function I_(e,t,r){if(r===void 0)throw new Error(`keyword "${t}" failed to compile`);return e.scopeValue("keyword",typeof r=="function"?{ref:r}:{ref:r,code:(0,xe.stringify)(r)})}function oP(e,t,r=!1){return!t.length||t.some(n=>n==="array"?Array.isArray(e):n==="object"?e&&typeof e=="object"&&!Array.isArray(e):typeof e==n||r&&typeof e>"u")}Tt.validSchemaType=oP;function iP({schema:e,opts:t,self:r,errSchemaPath:n},o,i){if(Array.isArray(o.keyword)?!o.keyword.includes(i):o.keyword!==i)throw new Error("ajv implementation error");let a=o.dependencies;if(a?.some(l=>!Object.prototype.hasOwnProperty.call(e,l)))throw new Error(`parent schema must have dependencies of ${i}: ${a.join(",")}`);if(o.validateSchema&&!o.validateSchema(e[i])){let c=`keyword "${i}" value is invalid at path "${n}": `+r.errorsText(o.validateSchema.errors);if(t.validateSchema==="log")r.logger.error(c);else throw new Error(c)}}Tt.validateKeywordUsage=iP});var N_=C(gr=>{"use strict";Object.defineProperty(gr,"__esModule",{value:!0});gr.extendSubschemaMode=gr.extendSubschemaData=gr.getSubschema=void 0;var Rt=G(),M_=Z();function sP(e,{keyword:t,schemaProp:r,schema:n,schemaPath:o,errSchemaPath:i,topSchemaRef:a}){if(t!==void 0&&n!==void 0)throw new Error('both "keyword" and "schema" passed, only one allowed');if(t!==void 0){let l=e.schema[t];return r===void 0?{schema:l,schemaPath:(0,Rt._)`${e.schemaPath}${(0,Rt.getProperty)(t)}`,errSchemaPath:`${e.errSchemaPath}/${t}`}:{schema:l[r],schemaPath:(0,Rt._)`${e.schemaPath}${(0,Rt.getProperty)(t)}${(0,Rt.getProperty)(r)}`,errSchemaPath:`${e.errSchemaPath}/${t}/${(0,M_.escapeFragment)(r)}`}}if(n!==void 0){if(o===void 0||i===void 0||a===void 0)throw new Error('"schemaPath", "errSchemaPath" and "topSchemaRef" are required with "schema"');return{schema:n,schemaPath:o,topSchemaRef:a,errSchemaPath:i}}throw new Error('either "keyword" or "schema" must be passed')}gr.getSubschema=sP;function aP(e,t,{dataProp:r,dataPropType:n,data:o,dataTypes:i,propertyName:a}){if(o!==void 0&&r!==void 0)throw new Error('both "data" and "dataProp" passed, only one allowed');let{gen:l}=t;if(r!==void 0){let{errorPath:d,dataPathArr:p,opts:m}=t,b=l.let("data",(0,Rt._)`${t.data}${(0,Rt.getProperty)(r)}`,!0);c(b),e.errorPath=(0,Rt.str)`${d}${(0,M_.getErrorPath)(r,n,m.jsPropertySyntax)}`,e.parentDataProperty=(0,Rt._)`${r}`,e.dataPathArr=[...p,e.parentDataProperty]}if(o!==void 0){let d=o instanceof Rt.Name?o:l.let("data",o,!0);c(d),a!==void 0&&(e.propertyName=a)}i&&(e.dataTypes=i);function c(d){e.data=d,e.dataLevel=t.dataLevel+1,e.dataTypes=[],t.definedProperties=new Set,e.parentData=t.data,e.dataNames=[...t.dataNames,d]}}gr.extendSubschemaData=aP;function uP(e,{jtdDiscriminator:t,jtdMetadata:r,compositeRule:n,createErrors:o,allErrors:i}){n!==void 0&&(e.compositeRule=n),o!==void 0&&(e.createErrors=o),i!==void 0&&(e.allErrors=i),e.jtdDiscriminator=t,e.jtdMetadata=r}gr.extendSubschemaMode=uP});var Gf=C(($4,j_)=>{"use strict";j_.exports=function e(t,r){if(t===r)return!0;if(t&&r&&typeof t=="object"&&typeof r=="object"){if(t.constructor!==r.constructor)return!1;var n,o,i;if(Array.isArray(t)){if(n=t.length,n!=r.length)return!1;for(o=n;o--!==0;)if(!e(t[o],r[o]))return!1;return!0}if(t.constructor===RegExp)return t.source===r.source&&t.flags===r.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===r.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===r.toString();if(i=Object.keys(t),n=i.length,n!==Object.keys(r).length)return!1;for(o=n;o--!==0;)if(!Object.prototype.hasOwnProperty.call(r,i[o]))return!1;for(o=n;o--!==0;){var a=i[o];if(!e(t[a],r[a]))return!1}return!0}return t!==t&&r!==r}});var q_=C((x4,L_)=>{"use strict";var yr=L_.exports=function(e,t,r){typeof t=="function"&&(r=t,t={}),r=t.cb||r;var n=typeof r=="function"?r:r.pre||function(){},o=r.post||function(){};Ha(t,n,o,e,"",e)};yr.keywords={additionalItems:!0,items:!0,contains:!0,additionalProperties:!0,propertyNames:!0,not:!0,if:!0,then:!0,else:!0};yr.arrayKeywords={items:!0,allOf:!0,anyOf:!0,oneOf:!0};yr.propsKeywords={$defs:!0,definitions:!0,properties:!0,patternProperties:!0,dependencies:!0};yr.skipKeywords={default:!0,enum:!0,const:!0,required:!0,maximum:!0,minimum:!0,exclusiveMaximum:!0,exclusiveMinimum:!0,multipleOf:!0,maxLength:!0,minLength:!0,pattern:!0,format:!0,maxItems:!0,minItems:!0,uniqueItems:!0,maxProperties:!0,minProperties:!0};function Ha(e,t,r,n,o,i,a,l,c,d){if(n&&typeof n=="object"&&!Array.isArray(n)){t(n,o,i,a,l,c,d);for(var p in n){var m=n[p];if(Array.isArray(m)){if(p in yr.arrayKeywords)for(var b=0;b<m.length;b++)Ha(e,t,r,m[b],o+"/"+p+"/"+b,i,o,p,n,b)}else if(p in yr.propsKeywords){if(m&&typeof m=="object")for(var D in m)Ha(e,t,r,m[D],o+"/"+p+"/"+lP(D),i,o,p,n,D)}else(p in yr.keywords||e.allKeys&&!(p in yr.skipKeywords))&&Ha(e,t,r,m,o+"/"+p,i,o,p,n)}r(n,o,i,a,l,c,d)}}function lP(e){return e.replace(/~/g,"~0").replace(/\//g,"~1")}});var ti=C(ke=>{"use strict";Object.defineProperty(ke,"__esModule",{value:!0});ke.getSchemaRefs=ke.resolveUrl=ke.normalizeId=ke._getFullPath=ke.getFullPath=ke.inlineRef=void 0;var cP=Z(),fP=Gf(),dP=q_(),pP=new Set(["type","format","pattern","maxLength","minLength","maxProperties","minProperties","maxItems","minItems","maximum","minimum","uniqueItems","multipleOf","required","enum","const"]);function mP(e,t=!0){return typeof e=="boolean"?!0:t===!0?!Hf(e):t?U_(e)<=t:!1}ke.inlineRef=mP;var hP=new Set(["$ref","$recursiveRef","$recursiveAnchor","$dynamicRef","$dynamicAnchor"]);function Hf(e){for(let t in e){if(hP.has(t))return!0;let r=e[t];if(Array.isArray(r)&&r.some(Hf)||typeof r=="object"&&Hf(r))return!0}return!1}function U_(e){let t=0;for(let r in e){if(r==="$ref")return 1/0;if(t++,!pP.has(r)&&(typeof e[r]=="object"&&(0,cP.eachItem)(e[r],n=>t+=U_(n)),t===1/0))return 1/0}return t}function z_(e,t="",r){r!==!1&&(t=Mn(t));let n=e.parse(t);return W_(e,n)}ke.getFullPath=z_;function W_(e,t){return e.serialize(t).split("#")[0]+"#"}ke._getFullPath=W_;var gP=/#\/?$/;function Mn(e){return e?e.replace(gP,""):""}ke.normalizeId=Mn;function yP(e,t,r){return r=Mn(r),e.resolve(t,r)}ke.resolveUrl=yP;var DP=/^[a-z_][-a-z0-9._]*$/i;function bP(e,t){if(typeof e=="boolean")return{};let{schemaId:r,uriResolver:n}=this.opts,o=Mn(e[r]||t),i={"":o},a=z_(n,o,!1),l={},c=new Set;return dP(e,{allKeys:!0},(m,b,D,g)=>{if(g===void 0)return;let y=a+b,S=i[g];typeof m[r]=="string"&&(S=w.call(this,m[r])),T.call(this,m.$anchor),T.call(this,m.$dynamicAnchor),i[b]=S;function w(R){let A=this.opts.uriResolver.resolve;if(R=Mn(S?A(S,R):R),c.has(R))throw p(R);c.add(R);let v=this.refs[R];return typeof v=="string"&&(v=this.refs[v]),typeof v=="object"?d(m,v.schema,R):R!==Mn(y)&&(R[0]==="#"?(d(m,l[R],R),l[R]=m):this.refs[R]=y),R}function T(R){if(typeof R=="string"){if(!DP.test(R))throw new Error(`invalid anchor "${R}"`);w.call(this,`#${R}`)}}}),l;function d(m,b,D){if(b!==void 0&&!fP(m,b))throw p(D)}function p(m){return new Error(`reference "${m}" resolves to more than one schema`)}}ke.getSchemaRefs=bP});var oi=C(Dr=>{"use strict";Object.defineProperty(Dr,"__esModule",{value:!0});Dr.getData=Dr.KeywordCxt=Dr.validateFunctionCode=void 0;var K_=v_(),V_=ei(),Kf=jf(),Ya=ei(),_P=x_(),ni=k_(),Yf=N_(),O=G(),M=Wt(),wP=ti(),Vt=Z(),ri=Zo();function SP(e){if(X_(e)&&(Z_(e),Q_(e))){FP(e);return}J_(e,()=>(0,K_.topBoolOrEmptySchema)(e))}Dr.validateFunctionCode=SP;function J_({gen:e,validateName:t,schema:r,schemaEnv:n,opts:o},i){o.code.es5?e.func(t,(0,O._)`${M.default.data}, ${M.default.valCxt}`,n.$async,()=>{e.code((0,O._)`"use strict"; ${G_(r,o)}`),vP(e,o),e.code(i)}):e.func(t,(0,O._)`${M.default.data}, ${EP(o)}`,n.$async,()=>e.code(G_(r,o)).code(i))}function EP(e){return(0,O._)`{${M.default.instancePath}="", ${M.default.parentData}, ${M.default.parentDataProperty}, ${M.default.rootData}=${M.default.data}${e.dynamicRef?(0,O._)`, ${M.default.dynamicAnchors}={}`:O.nil}}={}`}function vP(e,t){e.if(M.default.valCxt,()=>{e.var(M.default.instancePath,(0,O._)`${M.default.valCxt}.${M.default.instancePath}`),e.var(M.default.parentData,(0,O._)`${M.default.valCxt}.${M.default.parentData}`),e.var(M.default.parentDataProperty,(0,O._)`${M.default.valCxt}.${M.default.parentDataProperty}`),e.var(M.default.rootData,(0,O._)`${M.default.valCxt}.${M.default.rootData}`),t.dynamicRef&&e.var(M.default.dynamicAnchors,(0,O._)`${M.default.valCxt}.${M.default.dynamicAnchors}`)},()=>{e.var(M.default.instancePath,(0,O._)`""`),e.var(M.default.parentData,(0,O._)`undefined`),e.var(M.default.parentDataProperty,(0,O._)`undefined`),e.var(M.default.rootData,M.default.data),t.dynamicRef&&e.var(M.default.dynamicAnchors,(0,O._)`{}`)})}function FP(e){let{schema:t,opts:r,gen:n}=e;J_(e,()=>{r.$comment&&t.$comment&&tw(e),$P(e),n.let(M.default.vErrors,null),n.let(M.default.errors,0),r.unevaluated&&CP(e),ew(e),OP(e)})}function CP(e){let{gen:t,validateName:r}=e;e.evaluated=t.const("evaluated",(0,O._)`${r}.evaluated`),t.if((0,O._)`${e.evaluated}.dynamicProps`,()=>t.assign((0,O._)`${e.evaluated}.props`,(0,O._)`undefined`)),t.if((0,O._)`${e.evaluated}.dynamicItems`,()=>t.assign((0,O._)`${e.evaluated}.items`,(0,O._)`undefined`))}function G_(e,t){let r=typeof e=="object"&&e[t.schemaId];return r&&(t.code.source||t.code.process)?(0,O._)`/*# sourceURL=${r} */`:O.nil}function TP(e,t){if(X_(e)&&(Z_(e),Q_(e))){RP(e,t);return}(0,K_.boolOrEmptySchema)(e,t)}function Q_({schema:e,self:t}){if(typeof e=="boolean")return!e;for(let r in e)if(t.RULES.all[r])return!0;return!1}function X_(e){return typeof e.schema!="boolean"}function RP(e,t){let{schema:r,gen:n,opts:o}=e;o.$comment&&r.$comment&&tw(e),xP(e),PP(e);let i=n.const("_errs",M.default.errors);ew(e,i),n.var(t,(0,O._)`${i} === ${M.default.errors}`)}function Z_(e){(0,Vt.checkUnknownRules)(e),AP(e)}function ew(e,t){if(e.opts.jtd)return H_(e,[],!1,t);let r=(0,V_.getSchemaTypes)(e.schema),n=(0,V_.coerceAndCheckDataType)(e,r);H_(e,r,!n,t)}function AP(e){let{schema:t,errSchemaPath:r,opts:n,self:o}=e;t.$ref&&n.ignoreKeywordsWithRef&&(0,Vt.schemaHasRulesButRef)(t,o.RULES)&&o.logger.warn(`$ref: keywords ignored in schema at path "${r}"`)}function $P(e){let{schema:t,opts:r}=e;t.default!==void 0&&r.useDefaults&&r.strictSchema&&(0,Vt.checkStrictMode)(e,"default is ignored in the schema root")}function xP(e){let t=e.schema[e.opts.schemaId];t&&(e.baseId=(0,wP.resolveUrl)(e.opts.uriResolver,e.baseId,t))}function PP(e){if(e.schema.$async&&!e.schemaEnv.$async)throw new Error("async schema in sync schema")}function tw({gen:e,schemaEnv:t,schema:r,errSchemaPath:n,opts:o}){let i=r.$comment;if(o.$comment===!0)e.code((0,O._)`${M.default.self}.logger.log(${i})`);else if(typeof o.$comment=="function"){let a=(0,O.str)`${n}/$comment`,l=e.scopeValue("root",{ref:t.root});e.code((0,O._)`${M.default.self}.opts.$comment(${i}, ${a}, ${l}.schema)`)}}function OP(e){let{gen:t,schemaEnv:r,validateName:n,ValidationError:o,opts:i}=e;r.$async?t.if((0,O._)`${M.default.errors} === 0`,()=>t.return(M.default.data),()=>t.throw((0,O._)`new ${o}(${M.default.vErrors})`)):(t.assign((0,O._)`${n}.errors`,M.default.vErrors),i.unevaluated&&BP(e),t.return((0,O._)`${M.default.errors} === 0`))}function BP({gen:e,evaluated:t,props:r,items:n}){r instanceof O.Name&&e.assign((0,O._)`${t}.props`,r),n instanceof O.Name&&e.assign((0,O._)`${t}.items`,n)}function H_(e,t,r,n){let{gen:o,schema:i,data:a,allErrors:l,opts:c,self:d}=e,{RULES:p}=d;if(i.$ref&&(c.ignoreKeywordsWithRef||!(0,Vt.schemaHasRulesButRef)(i,p))){o.block(()=>nw(e,"$ref",p.all.$ref.definition));return}c.jtd||IP(e,t),o.block(()=>{for(let b of p.rules)m(b);m(p.post)});function m(b){(0,Kf.shouldUseGroup)(i,b)&&(b.type?(o.if((0,Ya.checkDataType)(b.type,a,c.strictNumbers)),Y_(e,b),t.length===1&&t[0]===b.type&&r&&(o.else(),(0,Ya.reportTypeError)(e)),o.endIf()):Y_(e,b),l||o.if((0,O._)`${M.default.errors} === ${n||0}`))}}function Y_(e,t){let{gen:r,schema:n,opts:{useDefaults:o}}=e;o&&(0,_P.assignDefaults)(e,t.type),r.block(()=>{for(let i of t.rules)(0,Kf.shouldUseRule)(n,i)&&nw(e,i.keyword,i.definition,t.type)})}function IP(e,t){e.schemaEnv.meta||!e.opts.strictTypes||(kP(e,t),e.opts.allowUnionTypes||MP(e,t),NP(e,e.dataTypes))}function kP(e,t){if(t.length){if(!e.dataTypes.length){e.dataTypes=t;return}t.forEach(r=>{rw(e.dataTypes,r)||Jf(e,`type "${r}" not allowed by context "${e.dataTypes.join(",")}"`)}),LP(e,t)}}function MP(e,t){t.length>1&&!(t.length===2&&t.includes("null"))&&Jf(e,"use allowUnionTypes to allow union type keyword")}function NP(e,t){let r=e.self.RULES.all;for(let n in r){let o=r[n];if(typeof o=="object"&&(0,Kf.shouldUseRule)(e.schema,o)){let{type:i}=o.definition;i.length&&!i.some(a=>jP(t,a))&&Jf(e,`missing type "${i.join(",")}" for keyword "${n}"`)}}}function jP(e,t){return e.includes(t)||t==="number"&&e.includes("integer")}function rw(e,t){return e.includes(t)||t==="integer"&&e.includes("number")}function LP(e,t){let r=[];for(let n of e.dataTypes)rw(t,n)?r.push(n):t.includes("integer")&&n==="number"&&r.push("integer");e.dataTypes=r}function Jf(e,t){let r=e.schemaEnv.baseId+e.errSchemaPath;t+=` at "${r}" (strictTypes)`,(0,Vt.checkStrictMode)(e,t,e.opts.strictTypes)}var Ka=class{constructor(t,r,n){if((0,ni.validateKeywordUsage)(t,r,n),this.gen=t.gen,this.allErrors=t.allErrors,this.keyword=n,this.data=t.data,this.schema=t.schema[n],this.$data=r.$data&&t.opts.$data&&this.schema&&this.schema.$data,this.schemaValue=(0,Vt.schemaRefOrVal)(t,this.schema,n,this.$data),this.schemaType=r.schemaType,this.parentSchema=t.schema,this.params={},this.it=t,this.def=r,this.$data)this.schemaCode=t.gen.const("vSchema",ow(this.$data,t));else if(this.schemaCode=this.schemaValue,!(0,ni.validSchemaType)(this.schema,r.schemaType,r.allowUndefined))throw new Error(`${n} value must be ${JSON.stringify(r.schemaType)}`);("code"in r?r.trackErrors:r.errors!==!1)&&(this.errsCount=t.gen.const("_errs",M.default.errors))}result(t,r,n){this.failResult((0,O.not)(t),r,n)}failResult(t,r,n){this.gen.if(t),n?n():this.error(),r?(this.gen.else(),r(),this.allErrors&&this.gen.endIf()):this.allErrors?this.gen.endIf():this.gen.else()}pass(t,r){this.failResult((0,O.not)(t),void 0,r)}fail(t){if(t===void 0){this.error(),this.allErrors||this.gen.if(!1);return}this.gen.if(t),this.error(),this.allErrors?this.gen.endIf():this.gen.else()}fail$data(t){if(!this.$data)return this.fail(t);let{schemaCode:r}=this;this.fail((0,O._)`${r} !== undefined && (${(0,O.or)(this.invalid$data(),t)})`)}error(t,r,n){if(r){this.setParams(r),this._error(t,n),this.setParams({});return}this._error(t,n)}_error(t,r){(t?ri.reportExtraError:ri.reportError)(this,this.def.error,r)}$dataError(){(0,ri.reportError)(this,this.def.$dataError||ri.keyword$DataError)}reset(){if(this.errsCount===void 0)throw new Error('add "trackErrors" to keyword definition');(0,ri.resetErrorsCount)(this.gen,this.errsCount)}ok(t){this.allErrors||this.gen.if(t)}setParams(t,r){r?Object.assign(this.params,t):this.params=t}block$data(t,r,n=O.nil){this.gen.block(()=>{this.check$data(t,n),r()})}check$data(t=O.nil,r=O.nil){if(!this.$data)return;let{gen:n,schemaCode:o,schemaType:i,def:a}=this;n.if((0,O.or)((0,O._)`${o} === undefined`,r)),t!==O.nil&&n.assign(t,!0),(i.length||a.validateSchema)&&(n.elseIf(this.invalid$data()),this.$dataError(),t!==O.nil&&n.assign(t,!1)),n.else()}invalid$data(){let{gen:t,schemaCode:r,schemaType:n,def:o,it:i}=this;return(0,O.or)(a(),l());function a(){if(n.length){if(!(r instanceof O.Name))throw new Error("ajv implementation error");let c=Array.isArray(n)?n:[n];return(0,O._)`${(0,Ya.checkDataTypes)(c,r,i.opts.strictNumbers,Ya.DataType.Wrong)}`}return O.nil}function l(){if(o.validateSchema){let c=t.scopeValue("validate$data",{ref:o.validateSchema});return(0,O._)`!${c}(${r})`}return O.nil}}subschema(t,r){let n=(0,Yf.getSubschema)(this.it,t);(0,Yf.extendSubschemaData)(n,this.it,t),(0,Yf.extendSubschemaMode)(n,t);let o={...this.it,...n,items:void 0,props:void 0};return TP(o,r),o}mergeEvaluated(t,r){let{it:n,gen:o}=this;n.opts.unevaluated&&(n.props!==!0&&t.props!==void 0&&(n.props=Vt.mergeEvaluated.props(o,t.props,n.props,r)),n.items!==!0&&t.items!==void 0&&(n.items=Vt.mergeEvaluated.items(o,t.items,n.items,r)))}mergeValidEvaluated(t,r){let{it:n,gen:o}=this;if(n.opts.unevaluated&&(n.props!==!0||n.items!==!0))return o.if(r,()=>this.mergeEvaluated(t,O.Name)),!0}};Dr.KeywordCxt=Ka;function nw(e,t,r,n){let o=new Ka(e,r,t);"code"in r?r.code(o,n):o.$data&&r.validate?(0,ni.funcKeywordCode)(o,r):"macro"in r?(0,ni.macroKeywordCode)(o,r):(r.compile||r.validate)&&(0,ni.funcKeywordCode)(o,r)}var qP=/^\/(?:[^~]|~0|~1)*$/,UP=/^([0-9]+)(#|\/(?:[^~]|~0|~1)*)?$/;function ow(e,{dataLevel:t,dataNames:r,dataPathArr:n}){let o,i;if(e==="")return M.default.rootData;if(e[0]==="/"){if(!qP.test(e))throw new Error(`Invalid JSON-pointer: ${e}`);o=e,i=M.default.rootData}else{let d=UP.exec(e);if(!d)throw new Error(`Invalid JSON-pointer: ${e}`);let p=+d[1];if(o=d[2],o==="#"){if(p>=t)throw new Error(c("property/index",p));return n[t-p]}if(p>t)throw new Error(c("data",p));if(i=r[t-p],!o)return i}let a=i,l=o.split("/");for(let d of l)d&&(i=(0,O._)`${i}${(0,O.getProperty)((0,Vt.unescapeJsonPointer)(d))}`,a=(0,O._)`${a} && ${i}`);return a;function c(d,p){return`Cannot access ${d} ${p} levels up, current level is ${t}`}}Dr.getData=ow});var Ja=C(Xf=>{"use strict";Object.defineProperty(Xf,"__esModule",{value:!0});var Qf=class extends Error{constructor(t){super("validation failed"),this.errors=t,this.ajv=this.validation=!0}};Xf.default=Qf});var ii=C(td=>{"use strict";Object.defineProperty(td,"__esModule",{value:!0});var Zf=ti(),ed=class extends Error{constructor(t,r,n,o){super(o||`can't resolve reference ${n} from id ${r}`),this.missingRef=(0,Zf.resolveUrl)(t,r,n),this.missingSchema=(0,Zf.normalizeId)((0,Zf.getFullPath)(t,this.missingRef))}};td.default=ed});var Xa=C(He=>{"use strict";Object.defineProperty(He,"__esModule",{value:!0});He.resolveSchema=He.getCompilingSchema=He.resolveRef=He.compileSchema=He.SchemaEnv=void 0;var ft=G(),zP=Ja(),Lr=Wt(),dt=ti(),iw=Z(),WP=oi(),Nn=class{constructor(t){var r;this.refs={},this.dynamicAnchors={};let n;typeof t.schema=="object"&&(n=t.schema),this.schema=t.schema,this.schemaId=t.schemaId,this.root=t.root||this,this.baseId=(r=t.baseId)!==null&&r!==void 0?r:(0,dt.normalizeId)(n?.[t.schemaId||"$id"]),this.schemaPath=t.schemaPath,this.localRefs=t.localRefs,this.meta=t.meta,this.$async=n?.$async,this.refs={}}};He.SchemaEnv=Nn;function nd(e){let t=sw.call(this,e);if(t)return t;let r=(0,dt.getFullPath)(this.opts.uriResolver,e.root.baseId),{es5:n,lines:o}=this.opts.code,{ownProperties:i}=this.opts,a=new ft.CodeGen(this.scope,{es5:n,lines:o,ownProperties:i}),l;e.$async&&(l=a.scopeValue("Error",{ref:zP.default,code:(0,ft._)`require("ajv/dist/runtime/validation_error").default`}));let c=a.scopeName("validate");e.validateName=c;let d={gen:a,allErrors:this.opts.allErrors,data:Lr.default.data,parentData:Lr.default.parentData,parentDataProperty:Lr.default.parentDataProperty,dataNames:[Lr.default.data],dataPathArr:[ft.nil],dataLevel:0,dataTypes:[],definedProperties:new Set,topSchemaRef:a.scopeValue("schema",this.opts.code.source===!0?{ref:e.schema,code:(0,ft.stringify)(e.schema)}:{ref:e.schema}),validateName:c,ValidationError:l,schema:e.schema,schemaEnv:e,rootId:r,baseId:e.baseId||r,schemaPath:ft.nil,errSchemaPath:e.schemaPath||(this.opts.jtd?"":"#"),errorPath:(0,ft._)`""`,opts:this.opts,self:this},p;try{this._compilations.add(e),(0,WP.validateFunctionCode)(d),a.optimize(this.opts.code.optimize);let m=a.toString();p=`${a.scopeRefs(Lr.default.scope)}return ${m}`,this.opts.code.process&&(p=this.opts.code.process(p,e));let D=new Function(`${Lr.default.self}`,`${Lr.default.scope}`,p)(this,this.scope.get());if(this.scope.value(c,{ref:D}),D.errors=null,D.schema=e.schema,D.schemaEnv=e,e.$async&&(D.$async=!0),this.opts.code.source===!0&&(D.source={validateName:c,validateCode:m,scopeValues:a._values}),this.opts.unevaluated){let{props:g,items:y}=d;D.evaluated={props:g instanceof ft.Name?void 0:g,items:y instanceof ft.Name?void 0:y,dynamicProps:g instanceof ft.Name,dynamicItems:y instanceof ft.Name},D.source&&(D.source.evaluated=(0,ft.stringify)(D.evaluated))}return e.validate=D,e}catch(m){throw delete e.validate,delete e.validateName,p&&this.logger.error("Error compiling schema, function code:",p),m}finally{this._compilations.delete(e)}}He.compileSchema=nd;function VP(e,t,r){var n;r=(0,dt.resolveUrl)(this.opts.uriResolver,t,r);let o=e.refs[r];if(o)return o;let i=YP.call(this,e,r);if(i===void 0){let a=(n=e.localRefs)===null||n===void 0?void 0:n[r],{schemaId:l}=this.opts;a&&(i=new Nn({schema:a,schemaId:l,root:e,baseId:t}))}if(i!==void 0)return e.refs[r]=GP.call(this,i)}He.resolveRef=VP;function GP(e){return(0,dt.inlineRef)(e.schema,this.opts.inlineRefs)?e.schema:e.validate?e:nd.call(this,e)}function sw(e){for(let t of this._compilations)if(HP(t,e))return t}He.getCompilingSchema=sw;function HP(e,t){return e.schema===t.schema&&e.root===t.root&&e.baseId===t.baseId}function YP(e,t){let r;for(;typeof(r=this.refs[t])=="string";)t=r;return r||this.schemas[t]||Qa.call(this,e,t)}function Qa(e,t){let r=this.opts.uriResolver.parse(t),n=(0,dt._getFullPath)(this.opts.uriResolver,r),o=(0,dt.getFullPath)(this.opts.uriResolver,e.baseId,void 0);if(Object.keys(e.schema).length>0&&n===o)return rd.call(this,r,e);let i=(0,dt.normalizeId)(n),a=this.refs[i]||this.schemas[i];if(typeof a=="string"){let l=Qa.call(this,e,a);return typeof l?.schema!="object"?void 0:rd.call(this,r,l)}if(typeof a?.schema=="object"){if(a.validate||nd.call(this,a),i===(0,dt.normalizeId)(t)){let{schema:l}=a,{schemaId:c}=this.opts,d=l[c];return d&&(o=(0,dt.resolveUrl)(this.opts.uriResolver,o,d)),new Nn({schema:l,schemaId:c,root:e,baseId:o})}return rd.call(this,r,a)}}He.resolveSchema=Qa;var KP=new Set(["properties","patternProperties","enum","dependencies","definitions"]);function rd(e,{baseId:t,schema:r,root:n}){var o;if(((o=e.fragment)===null||o===void 0?void 0:o[0])!=="/")return;for(let l of e.fragment.slice(1).split("/")){if(typeof r=="boolean")return;let c=r[(0,iw.unescapeFragment)(l)];if(c===void 0)return;r=c;let d=typeof r=="object"&&r[this.opts.schemaId];!KP.has(l)&&d&&(t=(0,dt.resolveUrl)(this.opts.uriResolver,t,d))}let i;if(typeof r!="boolean"&&r.$ref&&!(0,iw.schemaHasRulesButRef)(r,this.RULES)){let l=(0,dt.resolveUrl)(this.opts.uriResolver,t,r.$ref);i=Qa.call(this,n,l)}let{schemaId:a}=this.opts;if(i=i||new Nn({schema:r,schemaId:a,root:n,baseId:t}),i.schema!==i.root.schema)return i}});var aw=C((M4,JP)=>{JP.exports={$id:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#",description:"Meta-schema for $data reference (JSON AnySchema extension proposal)",type:"object",required:["$data"],properties:{$data:{type:"string",anyOf:[{format:"relative-json-pointer"},{format:"json-pointer"}]}},additionalProperties:!1}});var lw=C((N4,uw)=>{"use strict";var QP={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15};uw.exports={HEX:QP}});var yw=C((j4,gw)=>{"use strict";var{HEX:XP}=lw();function pw(e){if(hw(e,".")<3)return{host:e,isIPV4:!1};let t=e.match(/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/u)||[],[r]=t;return r?{host:eO(r,"."),isIPV4:!0}:{host:e,isIPV4:!1}}function od(e,t=!1){let r="",n=!0;for(let o of e){if(XP[o]===void 0)return;o!=="0"&&n===!0&&(n=!1),n||(r+=o)}return t&&r.length===0&&(r="0"),r}function ZP(e){let t=0,r={error:!1,address:"",zone:""},n=[],o=[],i=!1,a=!1,l=!1;function c(){if(o.length){if(i===!1){let d=od(o);if(d!==void 0)n.push(d);else return r.error=!0,!1}o.length=0}return!0}for(let d=0;d<e.length;d++){let p=e[d];if(!(p==="["||p==="]"))if(p===":"){if(a===!0&&(l=!0),!c())break;if(t++,n.push(":"),t>7){r.error=!0;break}d-1>=0&&e[d-1]===":"&&(a=!0);continue}else if(p==="%"){if(!c())break;i=!0}else{o.push(p);continue}}return o.length&&(i?r.zone=o.join(""):l?n.push(o.join("")):n.push(od(o))),r.address=n.join(""),r}function mw(e,t={}){if(hw(e,":")<2)return{host:e,isIPV6:!1};let r=ZP(e);if(r.error)return{host:e,isIPV6:!1};{let n=r.address,o=r.address;return r.zone&&(n+="%"+r.zone,o+="%25"+r.zone),{host:n,escapedHost:o,isIPV6:!0}}}function eO(e,t){let r="",n=!0,o=e.length;for(let i=0;i<o;i++){let a=e[i];a==="0"&&n?(i+1<=o&&e[i+1]===t||i+1===o)&&(r+=a,n=!1):(a===t?n=!0:n=!1,r+=a)}return r}function hw(e,t){let r=0;for(let n=0;n<e.length;n++)e[n]===t&&r++;return r}var cw=/^\.\.?\//u,fw=/^\/\.(?:\/|$)/u,dw=/^\/\.\.(?:\/|$)/u,tO=/^\/?(?:.|\n)*?(?=\/|$)/u;function rO(e){let t=[];for(;e.length;)if(e.match(cw))e=e.replace(cw,"");else if(e.match(fw))e=e.replace(fw,"/");else if(e.match(dw))e=e.replace(dw,"/"),t.pop();else if(e==="."||e==="..")e="";else{let r=e.match(tO);if(r){let n=r[0];e=e.slice(n.length),t.push(n)}else throw new Error("Unexpected dot segment condition")}return t.join("")}function nO(e,t){let r=t!==!0?escape:unescape;return e.scheme!==void 0&&(e.scheme=r(e.scheme)),e.userinfo!==void 0&&(e.userinfo=r(e.userinfo)),e.host!==void 0&&(e.host=r(e.host)),e.path!==void 0&&(e.path=r(e.path)),e.query!==void 0&&(e.query=r(e.query)),e.fragment!==void 0&&(e.fragment=r(e.fragment)),e}function oO(e,t){let r=[];if(e.userinfo!==void 0&&(r.push(e.userinfo),r.push("@")),e.host!==void 0){let n=unescape(e.host),o=pw(n);if(o.isIPV4)n=o.host;else{let i=mw(o.host,{isIPV4:!1});i.isIPV6===!0?n=`[${i.escapedHost}]`:n=e.host}r.push(n)}return(typeof e.port=="number"||typeof e.port=="string")&&(r.push(":"),r.push(String(e.port))),r.length?r.join(""):void 0}gw.exports={recomposeAuthority:oO,normalizeComponentEncoding:nO,removeDotSegments:rO,normalizeIPv4:pw,normalizeIPv6:mw,stringArrayToHexStripped:od}});var Ew=C((L4,Sw)=>{"use strict";var iO=/^[\da-f]{8}\b-[\da-f]{4}\b-[\da-f]{4}\b-[\da-f]{4}\b-[\da-f]{12}$/iu,sO=/([\da-z][\d\-a-z]{0,31}):((?:[\w!$'()*+,\-.:;=@]|%[\da-f]{2})+)/iu;function Dw(e){return typeof e.secure=="boolean"?e.secure:String(e.scheme).toLowerCase()==="wss"}function bw(e){return e.host||(e.error=e.error||"HTTP URIs must have a host."),e}function _w(e){let t=String(e.scheme).toLowerCase()==="https";return(e.port===(t?443:80)||e.port==="")&&(e.port=void 0),e.path||(e.path="/"),e}function aO(e){return e.secure=Dw(e),e.resourceName=(e.path||"/")+(e.query?"?"+e.query:""),e.path=void 0,e.query=void 0,e}function uO(e){if((e.port===(Dw(e)?443:80)||e.port==="")&&(e.port=void 0),typeof e.secure=="boolean"&&(e.scheme=e.secure?"wss":"ws",e.secure=void 0),e.resourceName){let[t,r]=e.resourceName.split("?");e.path=t&&t!=="/"?t:void 0,e.query=r,e.resourceName=void 0}return e.fragment=void 0,e}function lO(e,t){if(!e.path)return e.error="URN can not be parsed",e;let r=e.path.match(sO);if(r){let n=t.scheme||e.scheme||"urn";e.nid=r[1].toLowerCase(),e.nss=r[2];let o=`${n}:${t.nid||e.nid}`,i=id[o];e.path=void 0,i&&(e=i.parse(e,t))}else e.error=e.error||"URN can not be parsed.";return e}function cO(e,t){let r=t.scheme||e.scheme||"urn",n=e.nid.toLowerCase(),o=`${r}:${t.nid||n}`,i=id[o];i&&(e=i.serialize(e,t));let a=e,l=e.nss;return a.path=`${n||t.nid}:${l}`,t.skipEscape=!0,a}function fO(e,t){let r=e;return r.uuid=r.nss,r.nss=void 0,!t.tolerant&&(!r.uuid||!iO.test(r.uuid))&&(r.error=r.error||"UUID is not valid."),r}function dO(e){let t=e;return t.nss=(e.uuid||"").toLowerCase(),t}var ww={scheme:"http",domainHost:!0,parse:bw,serialize:_w},pO={scheme:"https",domainHost:ww.domainHost,parse:bw,serialize:_w},Za={scheme:"ws",domainHost:!0,parse:aO,serialize:uO},mO={scheme:"wss",domainHost:Za.domainHost,parse:Za.parse,serialize:Za.serialize},hO={scheme:"urn",parse:lO,serialize:cO,skipNormalize:!0},gO={scheme:"urn:uuid",parse:fO,serialize:dO,skipNormalize:!0},id={http:ww,https:pO,ws:Za,wss:mO,urn:hO,"urn:uuid":gO};Sw.exports=id});var Fw=C((q4,tu)=>{"use strict";var{normalizeIPv6:yO,normalizeIPv4:DO,removeDotSegments:si,recomposeAuthority:bO,normalizeComponentEncoding:eu}=yw(),sd=Ew();function _O(e,t){return typeof e=="string"?e=At(Gt(e,t),t):typeof e=="object"&&(e=Gt(At(e,t),t)),e}function wO(e,t,r){let n=Object.assign({scheme:"null"},r),o=vw(Gt(e,n),Gt(t,n),n,!0);return At(o,{...n,skipEscape:!0})}function vw(e,t,r,n){let o={};return n||(e=Gt(At(e,r),r),t=Gt(At(t,r),r)),r=r||{},!r.tolerant&&t.scheme?(o.scheme=t.scheme,o.userinfo=t.userinfo,o.host=t.host,o.port=t.port,o.path=si(t.path||""),o.query=t.query):(t.userinfo!==void 0||t.host!==void 0||t.port!==void 0?(o.userinfo=t.userinfo,o.host=t.host,o.port=t.port,o.path=si(t.path||""),o.query=t.query):(t.path?(t.path.charAt(0)==="/"?o.path=si(t.path):((e.userinfo!==void 0||e.host!==void 0||e.port!==void 0)&&!e.path?o.path="/"+t.path:e.path?o.path=e.path.slice(0,e.path.lastIndexOf("/")+1)+t.path:o.path=t.path,o.path=si(o.path)),o.query=t.query):(o.path=e.path,t.query!==void 0?o.query=t.query:o.query=e.query),o.userinfo=e.userinfo,o.host=e.host,o.port=e.port),o.scheme=e.scheme),o.fragment=t.fragment,o}function SO(e,t,r){return typeof e=="string"?(e=unescape(e),e=At(eu(Gt(e,r),!0),{...r,skipEscape:!0})):typeof e=="object"&&(e=At(eu(e,!0),{...r,skipEscape:!0})),typeof t=="string"?(t=unescape(t),t=At(eu(Gt(t,r),!0),{...r,skipEscape:!0})):typeof t=="object"&&(t=At(eu(t,!0),{...r,skipEscape:!0})),e.toLowerCase()===t.toLowerCase()}function At(e,t){let r={host:e.host,scheme:e.scheme,userinfo:e.userinfo,port:e.port,path:e.path,query:e.query,nid:e.nid,nss:e.nss,uuid:e.uuid,fragment:e.fragment,reference:e.reference,resourceName:e.resourceName,secure:e.secure,error:""},n=Object.assign({},t),o=[],i=sd[(n.scheme||r.scheme||"").toLowerCase()];i&&i.serialize&&i.serialize(r,n),r.path!==void 0&&(n.skipEscape?r.path=unescape(r.path):(r.path=escape(r.path),r.scheme!==void 0&&(r.path=r.path.split("%3A").join(":")))),n.reference!=="suffix"&&r.scheme&&o.push(r.scheme,":");let a=bO(r,n);if(a!==void 0&&(n.reference!=="suffix"&&o.push("//"),o.push(a),r.path&&r.path.charAt(0)!=="/"&&o.push("/")),r.path!==void 0){let l=r.path;!n.absolutePath&&(!i||!i.absolutePath)&&(l=si(l)),a===void 0&&(l=l.replace(/^\/\//u,"/%2F")),o.push(l)}return r.query!==void 0&&o.push("?",r.query),r.fragment!==void 0&&o.push("#",r.fragment),o.join("")}var EO=Array.from({length:127},(e,t)=>/[^!"$&'()*+,\-.;=_`a-z{}~]/u.test(String.fromCharCode(t)));function vO(e){let t=0;for(let r=0,n=e.length;r<n;++r)if(t=e.charCodeAt(r),t>126||EO[t])return!0;return!1}var FO=/^(?:([^#/:?]+):)?(?:\/\/((?:([^#/?@]*)@)?(\[[^#/?\]]+\]|[^#/:?]*)(?::(\d*))?))?([^#?]*)(?:\?([^#]*))?(?:#((?:.|[\n\r])*))?/u;function Gt(e,t){let r=Object.assign({},t),n={scheme:void 0,userinfo:void 0,host:"",port:void 0,path:"",query:void 0,fragment:void 0},o=e.indexOf("%")!==-1,i=!1;r.reference==="suffix"&&(e=(r.scheme?r.scheme+":":"")+"//"+e);let a=e.match(FO);if(a){if(n.scheme=a[1],n.userinfo=a[3],n.host=a[4],n.port=parseInt(a[5],10),n.path=a[6]||"",n.query=a[7],n.fragment=a[8],isNaN(n.port)&&(n.port=a[5]),n.host){let c=DO(n.host);if(c.isIPV4===!1){let d=yO(c.host,{isIPV4:!1});n.host=d.host.toLowerCase(),i=d.isIPV6}else n.host=c.host,i=!0}n.scheme===void 0&&n.userinfo===void 0&&n.host===void 0&&n.port===void 0&&!n.path&&n.query===void 0?n.reference="same-document":n.scheme===void 0?n.reference="relative":n.fragment===void 0?n.reference="absolute":n.reference="uri",r.reference&&r.reference!=="suffix"&&r.reference!==n.reference&&(n.error=n.error||"URI is not a "+r.reference+" reference.");let l=sd[(r.scheme||n.scheme||"").toLowerCase()];if(!r.unicodeSupport&&(!l||!l.unicodeSupport)&&n.host&&(r.domainHost||l&&l.domainHost)&&i===!1&&vO(n.host))try{n.host=URL.domainToASCII(n.host.toLowerCase())}catch(c){n.error=n.error||"Host's domain name can not be converted to ASCII: "+c}(!l||l&&!l.skipNormalize)&&(o&&n.scheme!==void 0&&(n.scheme=unescape(n.scheme)),o&&n.host!==void 0&&(n.host=unescape(n.host)),n.path!==void 0&&n.path.length&&(n.path=escape(unescape(n.path))),n.fragment!==void 0&&n.fragment.length&&(n.fragment=encodeURI(decodeURIComponent(n.fragment)))),l&&l.parse&&l.parse(n,r)}else n.error=n.error||"URI can not be parsed.";return n}var ad={SCHEMES:sd,normalize:_O,resolve:wO,resolveComponents:vw,equal:SO,serialize:At,parse:Gt};tu.exports=ad;tu.exports.default=ad;tu.exports.fastUri=ad});var Tw=C(ud=>{"use strict";Object.defineProperty(ud,"__esModule",{value:!0});var Cw=Fw();Cw.code='require("ajv/dist/runtime/uri").default';ud.default=Cw});var Iw=C(ve=>{"use strict";Object.defineProperty(ve,"__esModule",{value:!0});ve.CodeGen=ve.Name=ve.nil=ve.stringify=ve.str=ve._=ve.KeywordCxt=void 0;var CO=oi();Object.defineProperty(ve,"KeywordCxt",{enumerable:!0,get:function(){return CO.KeywordCxt}});var jn=G();Object.defineProperty(ve,"_",{enumerable:!0,get:function(){return jn._}});Object.defineProperty(ve,"str",{enumerable:!0,get:function(){return jn.str}});Object.defineProperty(ve,"stringify",{enumerable:!0,get:function(){return jn.stringify}});Object.defineProperty(ve,"nil",{enumerable:!0,get:function(){return jn.nil}});Object.defineProperty(ve,"Name",{enumerable:!0,get:function(){return jn.Name}});Object.defineProperty(ve,"CodeGen",{enumerable:!0,get:function(){return jn.CodeGen}});var TO=Ja(),Pw=ii(),RO=Nf(),ai=Xa(),AO=G(),ui=ti(),ru=ei(),cd=Z(),Rw=aw(),$O=Tw(),Ow=(e,t)=>new RegExp(e,t);Ow.code="new RegExp";var xO=["removeAdditional","useDefaults","coerceTypes"],PO=new Set(["validate","serialize","parse","wrapper","root","schema","keyword","pattern","formats","validate$data","func","obj","Error"]),OO={errorDataPath:"",format:"`validateFormats: false` can be used instead.",nullable:'"nullable" keyword is supported by default.',jsonPointers:"Deprecated jsPropertySyntax can be used instead.",extendRefs:"Deprecated ignoreKeywordsWithRef can be used instead.",missingRefs:"Pass empty schema with $id that should be ignored to ajv.addSchema.",processCode:"Use option `code: {process: (code, schemaEnv: object) => string}`",sourceCode:"Use option `code: {source: true}`",strictDefaults:"It is default now, see option `strict`.",strictKeywords:"It is default now, see option `strict`.",uniqueItems:'"uniqueItems" keyword is always validated.',unknownFormats:"Disable strict mode or pass `true` to `ajv.addFormat` (or `formats` option).",cache:"Map is used as cache, schema object as key.",serialize:"Map is used as cache, schema object as key.",ajvErrors:"It is default now."},BO={ignoreKeywordsWithRef:"",jsPropertySyntax:"",unicode:'"minLength"/"maxLength" account for unicode characters by default.'},Aw=200;function IO(e){var t,r,n,o,i,a,l,c,d,p,m,b,D,g,y,S,w,T,R,A,v,B,W,ae,P;let x=e.strict,k=(t=e.code)===null||t===void 0?void 0:t.optimize,oe=k===!0||k===void 0?1:k||0,re=(n=(r=e.code)===null||r===void 0?void 0:r.regExp)!==null&&n!==void 0?n:Ow,De=(o=e.uriResolver)!==null&&o!==void 0?o:$O.default;return{strictSchema:(a=(i=e.strictSchema)!==null&&i!==void 0?i:x)!==null&&a!==void 0?a:!0,strictNumbers:(c=(l=e.strictNumbers)!==null&&l!==void 0?l:x)!==null&&c!==void 0?c:!0,strictTypes:(p=(d=e.strictTypes)!==null&&d!==void 0?d:x)!==null&&p!==void 0?p:"log",strictTuples:(b=(m=e.strictTuples)!==null&&m!==void 0?m:x)!==null&&b!==void 0?b:"log",strictRequired:(g=(D=e.strictRequired)!==null&&D!==void 0?D:x)!==null&&g!==void 0?g:!1,code:e.code?{...e.code,optimize:oe,regExp:re}:{optimize:oe,regExp:re},loopRequired:(y=e.loopRequired)!==null&&y!==void 0?y:Aw,loopEnum:(S=e.loopEnum)!==null&&S!==void 0?S:Aw,meta:(w=e.meta)!==null&&w!==void 0?w:!0,messages:(T=e.messages)!==null&&T!==void 0?T:!0,inlineRefs:(R=e.inlineRefs)!==null&&R!==void 0?R:!0,schemaId:(A=e.schemaId)!==null&&A!==void 0?A:"$id",addUsedSchema:(v=e.addUsedSchema)!==null&&v!==void 0?v:!0,validateSchema:(B=e.validateSchema)!==null&&B!==void 0?B:!0,validateFormats:(W=e.validateFormats)!==null&&W!==void 0?W:!0,unicodeRegExp:(ae=e.unicodeRegExp)!==null&&ae!==void 0?ae:!0,int32range:(P=e.int32range)!==null&&P!==void 0?P:!0,uriResolver:De}}var li=class{constructor(t={}){this.schemas={},this.refs={},this.formats={},this._compilations=new Set,this._loading={},this._cache=new Map,t=this.opts={...t,...IO(t)};let{es5:r,lines:n}=this.opts.code;this.scope=new AO.ValueScope({scope:{},prefixes:PO,es5:r,lines:n}),this.logger=qO(t.logger);let o=t.validateFormats;t.validateFormats=!1,this.RULES=(0,RO.getRules)(),$w.call(this,OO,t,"NOT SUPPORTED"),$w.call(this,BO,t,"DEPRECATED","warn"),this._metaOpts=jO.call(this),t.formats&&MO.call(this),this._addVocabularies(),this._addDefaultMetaSchema(),t.keywords&&NO.call(this,t.keywords),typeof t.meta=="object"&&this.addMetaSchema(t.meta),kO.call(this),t.validateFormats=o}_addVocabularies(){this.addKeyword("$async")}_addDefaultMetaSchema(){let{$data:t,meta:r,schemaId:n}=this.opts,o=Rw;n==="id"&&(o={...Rw},o.id=o.$id,delete o.$id),r&&t&&this.addMetaSchema(o,o[n],!1)}defaultMeta(){let{meta:t,schemaId:r}=this.opts;return this.opts.defaultMeta=typeof t=="object"?t[r]||t:void 0}validate(t,r){let n;if(typeof t=="string"){if(n=this.getSchema(t),!n)throw new Error(`no schema with key or ref "${t}"`)}else n=this.compile(t);let o=n(r);return"$async"in n||(this.errors=n.errors),o}compile(t,r){let n=this._addSchema(t,r);return n.validate||this._compileSchemaEnv(n)}compileAsync(t,r){if(typeof this.opts.loadSchema!="function")throw new Error("options.loadSchema should be a function");let{loadSchema:n}=this.opts;return o.call(this,t,r);async function o(p,m){await i.call(this,p.$schema);let b=this._addSchema(p,m);return b.validate||a.call(this,b)}async function i(p){p&&!this.getSchema(p)&&await o.call(this,{$ref:p},!0)}async function a(p){try{return this._compileSchemaEnv(p)}catch(m){if(!(m instanceof Pw.default))throw m;return l.call(this,m),await c.call(this,m.missingSchema),a.call(this,p)}}function l({missingSchema:p,missingRef:m}){if(this.refs[p])throw new Error(`AnySchema ${p} is loaded but ${m} cannot be resolved`)}async function c(p){let m=await d.call(this,p);this.refs[p]||await i.call(this,m.$schema),this.refs[p]||this.addSchema(m,p,r)}async function d(p){let m=this._loading[p];if(m)return m;try{return await(this._loading[p]=n(p))}finally{delete this._loading[p]}}}addSchema(t,r,n,o=this.opts.validateSchema){if(Array.isArray(t)){for(let a of t)this.addSchema(a,void 0,n,o);return this}let i;if(typeof t=="object"){let{schemaId:a}=this.opts;if(i=t[a],i!==void 0&&typeof i!="string")throw new Error(`schema ${a} must be string`)}return r=(0,ui.normalizeId)(r||i),this._checkUnique(r),this.schemas[r]=this._addSchema(t,n,r,o,!0),this}addMetaSchema(t,r,n=this.opts.validateSchema){return this.addSchema(t,r,!0,n),this}validateSchema(t,r){if(typeof t=="boolean")return!0;let n;if(n=t.$schema,n!==void 0&&typeof n!="string")throw new Error("$schema must be a string");if(n=n||this.opts.defaultMeta||this.defaultMeta(),!n)return this.logger.warn("meta-schema not available"),this.errors=null,!0;let o=this.validate(n,t);if(!o&&r){let i="schema is invalid: "+this.errorsText();if(this.opts.validateSchema==="log")this.logger.error(i);else throw new Error(i)}return o}getSchema(t){let r;for(;typeof(r=xw.call(this,t))=="string";)t=r;if(r===void 0){let{schemaId:n}=this.opts,o=new ai.SchemaEnv({schema:{},schemaId:n});if(r=ai.resolveSchema.call(this,o,t),!r)return;this.refs[t]=r}return r.validate||this._compileSchemaEnv(r)}removeSchema(t){if(t instanceof RegExp)return this._removeAllSchemas(this.schemas,t),this._removeAllSchemas(this.refs,t),this;switch(typeof t){case"undefined":return this._removeAllSchemas(this.schemas),this._removeAllSchemas(this.refs),this._cache.clear(),this;case"string":{let r=xw.call(this,t);return typeof r=="object"&&this._cache.delete(r.schema),delete this.schemas[t],delete this.refs[t],this}case"object":{let r=t;this._cache.delete(r);let n=t[this.opts.schemaId];return n&&(n=(0,ui.normalizeId)(n),delete this.schemas[n],delete this.refs[n]),this}default:throw new Error("ajv.removeSchema: invalid parameter")}}addVocabulary(t){for(let r of t)this.addKeyword(r);return this}addKeyword(t,r){let n;if(typeof t=="string")n=t,typeof r=="object"&&(this.logger.warn("these parameters are deprecated, see docs for addKeyword"),r.keyword=n);else if(typeof t=="object"&&r===void 0){if(r=t,n=r.keyword,Array.isArray(n)&&!n.length)throw new Error("addKeywords: keyword must be string or non-empty array")}else throw new Error("invalid addKeywords parameters");if(zO.call(this,n,r),!r)return(0,cd.eachItem)(n,i=>ld.call(this,i)),this;VO.call(this,r);let o={...r,type:(0,ru.getJSONTypes)(r.type),schemaType:(0,ru.getJSONTypes)(r.schemaType)};return(0,cd.eachItem)(n,o.type.length===0?i=>ld.call(this,i,o):i=>o.type.forEach(a=>ld.call(this,i,o,a))),this}getKeyword(t){let r=this.RULES.all[t];return typeof r=="object"?r.definition:!!r}removeKeyword(t){let{RULES:r}=this;delete r.keywords[t],delete r.all[t];for(let n of r.rules){let o=n.rules.findIndex(i=>i.keyword===t);o>=0&&n.rules.splice(o,1)}return this}addFormat(t,r){return typeof r=="string"&&(r=new RegExp(r)),this.formats[t]=r,this}errorsText(t=this.errors,{separator:r=", ",dataVar:n="data"}={}){return!t||t.length===0?"No errors":t.map(o=>`${n}${o.instancePath} ${o.message}`).reduce((o,i)=>o+r+i)}$dataMetaSchema(t,r){let n=this.RULES.all;t=JSON.parse(JSON.stringify(t));for(let o of r){let i=o.split("/").slice(1),a=t;for(let l of i)a=a[l];for(let l in n){let c=n[l];if(typeof c!="object")continue;let{$data:d}=c.definition,p=a[l];d&&p&&(a[l]=Bw(p))}}return t}_removeAllSchemas(t,r){for(let n in t){let o=t[n];(!r||r.test(n))&&(typeof o=="string"?delete t[n]:o&&!o.meta&&(this._cache.delete(o.schema),delete t[n]))}}_addSchema(t,r,n,o=this.opts.validateSchema,i=this.opts.addUsedSchema){let a,{schemaId:l}=this.opts;if(typeof t=="object")a=t[l];else{if(this.opts.jtd)throw new Error("schema must be object");if(typeof t!="boolean")throw new Error("schema must be object or boolean")}let c=this._cache.get(t);if(c!==void 0)return c;n=(0,ui.normalizeId)(a||n);let d=ui.getSchemaRefs.call(this,t,n);return c=new ai.SchemaEnv({schema:t,schemaId:l,meta:r,baseId:n,localRefs:d}),this._cache.set(c.schema,c),i&&!n.startsWith("#")&&(n&&this._checkUnique(n),this.refs[n]=c),o&&this.validateSchema(t,!0),c}_checkUnique(t){if(this.schemas[t]||this.refs[t])throw new Error(`schema with key or id "${t}" already exists`)}_compileSchemaEnv(t){if(t.meta?this._compileMetaSchema(t):ai.compileSchema.call(this,t),!t.validate)throw new Error("ajv implementation error");return t.validate}_compileMetaSchema(t){let r=this.opts;this.opts=this._metaOpts;try{ai.compileSchema.call(this,t)}finally{this.opts=r}}};li.ValidationError=TO.default;li.MissingRefError=Pw.default;ve.default=li;function $w(e,t,r,n="error"){for(let o in e){let i=o;i in t&&this.logger[n](`${r}: option ${o}. ${e[i]}`)}}function xw(e){return e=(0,ui.normalizeId)(e),this.schemas[e]||this.refs[e]}function kO(){let e=this.opts.schemas;if(e)if(Array.isArray(e))this.addSchema(e);else for(let t in e)this.addSchema(e[t],t)}function MO(){for(let e in this.opts.formats){let t=this.opts.formats[e];t&&this.addFormat(e,t)}}function NO(e){if(Array.isArray(e)){this.addVocabulary(e);return}this.logger.warn("keywords option as map is deprecated, pass array");for(let t in e){let r=e[t];r.keyword||(r.keyword=t),this.addKeyword(r)}}function jO(){let e={...this.opts};for(let t of xO)delete e[t];return e}var LO={log(){},warn(){},error(){}};function qO(e){if(e===!1)return LO;if(e===void 0)return console;if(e.log&&e.warn&&e.error)return e;throw new Error("logger must implement log, warn and error methods")}var UO=/^[a-z_$][a-z0-9_$:-]*$/i;function zO(e,t){let{RULES:r}=this;if((0,cd.eachItem)(e,n=>{if(r.keywords[n])throw new Error(`Keyword ${n} is already defined`);if(!UO.test(n))throw new Error(`Keyword ${n} has invalid name`)}),!!t&&t.$data&&!("code"in t||"validate"in t))throw new Error('$data keyword must have "code" or "validate" function')}function ld(e,t,r){var n;let o=t?.post;if(r&&o)throw new Error('keyword with "post" flag cannot have "type"');let{RULES:i}=this,a=o?i.post:i.rules.find(({type:c})=>c===r);if(a||(a={type:r,rules:[]},i.rules.push(a)),i.keywords[e]=!0,!t)return;let l={keyword:e,definition:{...t,type:(0,ru.getJSONTypes)(t.type),schemaType:(0,ru.getJSONTypes)(t.schemaType)}};t.before?WO.call(this,a,l,t.before):a.rules.push(l),i.all[e]=l,(n=t.implements)===null||n===void 0||n.forEach(c=>this.addKeyword(c))}function WO(e,t,r){let n=e.rules.findIndex(o=>o.keyword===r);n>=0?e.rules.splice(n,0,t):(e.rules.push(t),this.logger.warn(`rule ${r} is not defined`))}function VO(e){let{metaSchema:t}=e;t!==void 0&&(e.$data&&this.opts.$data&&(t=Bw(t)),e.validateSchema=this.compile(t,!0))}var GO={$ref:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#"};function Bw(e){return{anyOf:[e,GO]}}});var kw=C(fd=>{"use strict";Object.defineProperty(fd,"__esModule",{value:!0});var HO={keyword:"id",code(){throw new Error('NOT SUPPORTED: keyword "id", use "$id" for schema ID')}};fd.default=HO});var Lw=C(qr=>{"use strict";Object.defineProperty(qr,"__esModule",{value:!0});qr.callRef=qr.getValidate=void 0;var YO=ii(),Mw=Ge(),Me=G(),Ln=Wt(),Nw=Xa(),nu=Z(),KO={keyword:"$ref",schemaType:"string",code(e){let{gen:t,schema:r,it:n}=e,{baseId:o,schemaEnv:i,validateName:a,opts:l,self:c}=n,{root:d}=i;if((r==="#"||r==="#/")&&o===d.baseId)return m();let p=Nw.resolveRef.call(c,d,o,r);if(p===void 0)throw new YO.default(n.opts.uriResolver,o,r);if(p instanceof Nw.SchemaEnv)return b(p);return D(p);function m(){if(i===d)return ou(e,a,i,i.$async);let g=t.scopeValue("root",{ref:d});return ou(e,(0,Me._)`${g}.validate`,d,d.$async)}function b(g){let y=jw(e,g);ou(e,y,g,g.$async)}function D(g){let y=t.scopeValue("schema",l.code.source===!0?{ref:g,code:(0,Me.stringify)(g)}:{ref:g}),S=t.name("valid"),w=e.subschema({schema:g,dataTypes:[],schemaPath:Me.nil,topSchemaRef:y,errSchemaPath:r},S);e.mergeEvaluated(w),e.ok(S)}}};function jw(e,t){let{gen:r}=e;return t.validate?r.scopeValue("validate",{ref:t.validate}):(0,Me._)`${r.scopeValue("wrapper",{ref:t})}.validate`}qr.getValidate=jw;function ou(e,t,r,n){let{gen:o,it:i}=e,{allErrors:a,schemaEnv:l,opts:c}=i,d=c.passContext?Ln.default.this:Me.nil;n?p():m();function p(){if(!l.$async)throw new Error("async schema referenced by sync schema");let g=o.let("valid");o.try(()=>{o.code((0,Me._)`await ${(0,Mw.callValidateCode)(e,t,d)}`),D(t),a||o.assign(g,!0)},y=>{o.if((0,Me._)`!(${y} instanceof ${i.ValidationError})`,()=>o.throw(y)),b(y),a||o.assign(g,!1)}),e.ok(g)}function m(){e.result((0,Mw.callValidateCode)(e,t,d),()=>D(t),()=>b(t))}function b(g){let y=(0,Me._)`${g}.errors`;o.assign(Ln.default.vErrors,(0,Me._)`${Ln.default.vErrors} === null ? ${y} : ${Ln.default.vErrors}.concat(${y})`),o.assign(Ln.default.errors,(0,Me._)`${Ln.default.vErrors}.length`)}function D(g){var y;if(!i.opts.unevaluated)return;let S=(y=r?.validate)===null||y===void 0?void 0:y.evaluated;if(i.props!==!0)if(S&&!S.dynamicProps)S.props!==void 0&&(i.props=nu.mergeEvaluated.props(o,S.props,i.props));else{let w=o.var("props",(0,Me._)`${g}.evaluated.props`);i.props=nu.mergeEvaluated.props(o,w,i.props,Me.Name)}if(i.items!==!0)if(S&&!S.dynamicItems)S.items!==void 0&&(i.items=nu.mergeEvaluated.items(o,S.items,i.items));else{let w=o.var("items",(0,Me._)`${g}.evaluated.items`);i.items=nu.mergeEvaluated.items(o,w,i.items,Me.Name)}}}qr.callRef=ou;qr.default=KO});var qw=C(dd=>{"use strict";Object.defineProperty(dd,"__esModule",{value:!0});var JO=kw(),QO=Lw(),XO=["$schema","$id","$defs","$vocabulary",{keyword:"$comment"},"definitions",JO.default,QO.default];dd.default=XO});var Uw=C(pd=>{"use strict";Object.defineProperty(pd,"__esModule",{value:!0});var iu=G(),br=iu.operators,su={maximum:{okStr:"<=",ok:br.LTE,fail:br.GT},minimum:{okStr:">=",ok:br.GTE,fail:br.LT},exclusiveMaximum:{okStr:"<",ok:br.LT,fail:br.GTE},exclusiveMinimum:{okStr:">",ok:br.GT,fail:br.LTE}},ZO={message:({keyword:e,schemaCode:t})=>(0,iu.str)`must be ${su[e].okStr} ${t}`,params:({keyword:e,schemaCode:t})=>(0,iu._)`{comparison: ${su[e].okStr}, limit: ${t}}`},eB={keyword:Object.keys(su),type:"number",schemaType:"number",$data:!0,error:ZO,code(e){let{keyword:t,data:r,schemaCode:n}=e;e.fail$data((0,iu._)`${r} ${su[t].fail} ${n} || isNaN(${r})`)}};pd.default=eB});var zw=C(md=>{"use strict";Object.defineProperty(md,"__esModule",{value:!0});var ci=G(),tB={message:({schemaCode:e})=>(0,ci.str)`must be multiple of ${e}`,params:({schemaCode:e})=>(0,ci._)`{multipleOf: ${e}}`},rB={keyword:"multipleOf",type:"number",schemaType:"number",$data:!0,error:tB,code(e){let{gen:t,data:r,schemaCode:n,it:o}=e,i=o.opts.multipleOfPrecision,a=t.let("res"),l=i?(0,ci._)`Math.abs(Math.round(${a}) - ${a}) > 1e-${i}`:(0,ci._)`${a} !== parseInt(${a})`;e.fail$data((0,ci._)`(${n} === 0 || (${a} = ${r}/${n}, ${l}))`)}};md.default=rB});var Vw=C(hd=>{"use strict";Object.defineProperty(hd,"__esModule",{value:!0});function Ww(e){let t=e.length,r=0,n=0,o;for(;n<t;)r++,o=e.charCodeAt(n++),o>=55296&&o<=56319&&n<t&&(o=e.charCodeAt(n),(o&64512)===56320&&n++);return r}hd.default=Ww;Ww.code='require("ajv/dist/runtime/ucs2length").default'});var Gw=C(gd=>{"use strict";Object.defineProperty(gd,"__esModule",{value:!0});var Ur=G(),nB=Z(),oB=Vw(),iB={message({keyword:e,schemaCode:t}){let r=e==="maxLength"?"more":"fewer";return(0,Ur.str)`must NOT have ${r} than ${t} characters`},params:({schemaCode:e})=>(0,Ur._)`{limit: ${e}}`},sB={keyword:["maxLength","minLength"],type:"string",schemaType:"number",$data:!0,error:iB,code(e){let{keyword:t,data:r,schemaCode:n,it:o}=e,i=t==="maxLength"?Ur.operators.GT:Ur.operators.LT,a=o.opts.unicode===!1?(0,Ur._)`${r}.length`:(0,Ur._)`${(0,nB.useFunc)(e.gen,oB.default)}(${r})`;e.fail$data((0,Ur._)`${a} ${i} ${n}`)}};gd.default=sB});var Hw=C(yd=>{"use strict";Object.defineProperty(yd,"__esModule",{value:!0});var aB=Ge(),au=G(),uB={message:({schemaCode:e})=>(0,au.str)`must match pattern "${e}"`,params:({schemaCode:e})=>(0,au._)`{pattern: ${e}}`},lB={keyword:"pattern",type:"string",schemaType:"string",$data:!0,error:uB,code(e){let{data:t,$data:r,schema:n,schemaCode:o,it:i}=e,a=i.opts.unicodeRegExp?"u":"",l=r?(0,au._)`(new RegExp(${o}, ${a}))`:(0,aB.usePattern)(e,n);e.fail$data((0,au._)`!${l}.test(${t})`)}};yd.default=lB});var Yw=C(Dd=>{"use strict";Object.defineProperty(Dd,"__esModule",{value:!0});var fi=G(),cB={message({keyword:e,schemaCode:t}){let r=e==="maxProperties"?"more":"fewer";return(0,fi.str)`must NOT have ${r} than ${t} properties`},params:({schemaCode:e})=>(0,fi._)`{limit: ${e}}`},fB={keyword:["maxProperties","minProperties"],type:"object",schemaType:"number",$data:!0,error:cB,code(e){let{keyword:t,data:r,schemaCode:n}=e,o=t==="maxProperties"?fi.operators.GT:fi.operators.LT;e.fail$data((0,fi._)`Object.keys(${r}).length ${o} ${n}`)}};Dd.default=fB});var Kw=C(bd=>{"use strict";Object.defineProperty(bd,"__esModule",{value:!0});var di=Ge(),pi=G(),dB=Z(),pB={message:({params:{missingProperty:e}})=>(0,pi.str)`must have required property '${e}'`,params:({params:{missingProperty:e}})=>(0,pi._)`{missingProperty: ${e}}`},mB={keyword:"required",type:"object",schemaType:"array",$data:!0,error:pB,code(e){let{gen:t,schema:r,schemaCode:n,data:o,$data:i,it:a}=e,{opts:l}=a;if(!i&&r.length===0)return;let c=r.length>=l.loopRequired;if(a.allErrors?d():p(),l.strictRequired){let D=e.parentSchema.properties,{definedProperties:g}=e.it;for(let y of r)if(D?.[y]===void 0&&!g.has(y)){let S=a.schemaEnv.baseId+a.errSchemaPath,w=`required property "${y}" is not defined at "${S}" (strictRequired)`;(0,dB.checkStrictMode)(a,w,a.opts.strictRequired)}}function d(){if(c||i)e.block$data(pi.nil,m);else for(let D of r)(0,di.checkReportMissingProp)(e,D)}function p(){let D=t.let("missing");if(c||i){let g=t.let("valid",!0);e.block$data(g,()=>b(D,g)),e.ok(g)}else t.if((0,di.checkMissingProp)(e,r,D)),(0,di.reportMissingProp)(e,D),t.else()}function m(){t.forOf("prop",n,D=>{e.setParams({missingProperty:D}),t.if((0,di.noPropertyInData)(t,o,D,l.ownProperties),()=>e.error())})}function b(D,g){e.setParams({missingProperty:D}),t.forOf(D,n,()=>{t.assign(g,(0,di.propertyInData)(t,o,D,l.ownProperties)),t.if((0,pi.not)(g),()=>{e.error(),t.break()})},pi.nil)}}};bd.default=mB});var Jw=C(_d=>{"use strict";Object.defineProperty(_d,"__esModule",{value:!0});var mi=G(),hB={message({keyword:e,schemaCode:t}){let r=e==="maxItems"?"more":"fewer";return(0,mi.str)`must NOT have ${r} than ${t} items`},params:({schemaCode:e})=>(0,mi._)`{limit: ${e}}`},gB={keyword:["maxItems","minItems"],type:"array",schemaType:"number",$data:!0,error:hB,code(e){let{keyword:t,data:r,schemaCode:n}=e,o=t==="maxItems"?mi.operators.GT:mi.operators.LT;e.fail$data((0,mi._)`${r}.length ${o} ${n}`)}};_d.default=gB});var uu=C(wd=>{"use strict";Object.defineProperty(wd,"__esModule",{value:!0});var Qw=Gf();Qw.code='require("ajv/dist/runtime/equal").default';wd.default=Qw});var Xw=C(Ed=>{"use strict";Object.defineProperty(Ed,"__esModule",{value:!0});var Sd=ei(),Fe=G(),yB=Z(),DB=uu(),bB={message:({params:{i:e,j:t}})=>(0,Fe.str)`must NOT have duplicate items (items ## ${t} and ${e} are identical)`,params:({params:{i:e,j:t}})=>(0,Fe._)`{i: ${e}, j: ${t}}`},_B={keyword:"uniqueItems",type:"array",schemaType:"boolean",$data:!0,error:bB,code(e){let{gen:t,data:r,$data:n,schema:o,parentSchema:i,schemaCode:a,it:l}=e;if(!n&&!o)return;let c=t.let("valid"),d=i.items?(0,Sd.getSchemaTypes)(i.items):[];e.block$data(c,p,(0,Fe._)`${a} === false`),e.ok(c);function p(){let g=t.let("i",(0,Fe._)`${r}.length`),y=t.let("j");e.setParams({i:g,j:y}),t.assign(c,!0),t.if((0,Fe._)`${g} > 1`,()=>(m()?b:D)(g,y))}function m(){return d.length>0&&!d.some(g=>g==="object"||g==="array")}function b(g,y){let S=t.name("item"),w=(0,Sd.checkDataTypes)(d,S,l.opts.strictNumbers,Sd.DataType.Wrong),T=t.const("indices",(0,Fe._)`{}`);t.for((0,Fe._)`;${g}--;`,()=>{t.let(S,(0,Fe._)`${r}[${g}]`),t.if(w,(0,Fe._)`continue`),d.length>1&&t.if((0,Fe._)`typeof ${S} == "string"`,(0,Fe._)`${S} += "_"`),t.if((0,Fe._)`typeof ${T}[${S}] == "number"`,()=>{t.assign(y,(0,Fe._)`${T}[${S}]`),e.error(),t.assign(c,!1).break()}).code((0,Fe._)`${T}[${S}] = ${g}`)})}function D(g,y){let S=(0,yB.useFunc)(t,DB.default),w=t.name("outer");t.label(w).for((0,Fe._)`;${g}--;`,()=>t.for((0,Fe._)`${y} = ${g}; ${y}--;`,()=>t.if((0,Fe._)`${S}(${r}[${g}], ${r}[${y}])`,()=>{e.error(),t.assign(c,!1).break(w)})))}}};Ed.default=_B});var Zw=C(Fd=>{"use strict";Object.defineProperty(Fd,"__esModule",{value:!0});var vd=G(),wB=Z(),SB=uu(),EB={message:"must be equal to constant",params:({schemaCode:e})=>(0,vd._)`{allowedValue: ${e}}`},vB={keyword:"const",$data:!0,error:EB,code(e){let{gen:t,data:r,$data:n,schemaCode:o,schema:i}=e;n||i&&typeof i=="object"?e.fail$data((0,vd._)`!${(0,wB.useFunc)(t,SB.default)}(${r}, ${o})`):e.fail((0,vd._)`${i} !== ${r}`)}};Fd.default=vB});var eS=C(Cd=>{"use strict";Object.defineProperty(Cd,"__esModule",{value:!0});var hi=G(),FB=Z(),CB=uu(),TB={message:"must be equal to one of the allowed values",params:({schemaCode:e})=>(0,hi._)`{allowedValues: ${e}}`},RB={keyword:"enum",schemaType:"array",$data:!0,error:TB,code(e){let{gen:t,data:r,$data:n,schema:o,schemaCode:i,it:a}=e;if(!n&&o.length===0)throw new Error("enum must have non-empty array");let l=o.length>=a.opts.loopEnum,c,d=()=>c??(c=(0,FB.useFunc)(t,CB.default)),p;if(l||n)p=t.let("valid"),e.block$data(p,m);else{if(!Array.isArray(o))throw new Error("ajv implementation error");let D=t.const("vSchema",i);p=(0,hi.or)(...o.map((g,y)=>b(D,y)))}e.pass(p);function m(){t.assign(p,!1),t.forOf("v",i,D=>t.if((0,hi._)`${d()}(${r}, ${D})`,()=>t.assign(p,!0).break()))}function b(D,g){let y=o[g];return typeof y=="object"&&y!==null?(0,hi._)`${d()}(${r}, ${D}[${g}])`:(0,hi._)`${r} === ${y}`}}};Cd.default=RB});var tS=C(Td=>{"use strict";Object.defineProperty(Td,"__esModule",{value:!0});var AB=Uw(),$B=zw(),xB=Gw(),PB=Hw(),OB=Yw(),BB=Kw(),IB=Jw(),kB=Xw(),MB=Zw(),NB=eS(),jB=[AB.default,$B.default,xB.default,PB.default,OB.default,BB.default,IB.default,kB.default,{keyword:"type",schemaType:["string","array"]},{keyword:"nullable",schemaType:"boolean"},MB.default,NB.default];Td.default=jB});var Ad=C(gi=>{"use strict";Object.defineProperty(gi,"__esModule",{value:!0});gi.validateAdditionalItems=void 0;var zr=G(),Rd=Z(),LB={message:({params:{len:e}})=>(0,zr.str)`must NOT have more than ${e} items`,params:({params:{len:e}})=>(0,zr._)`{limit: ${e}}`},qB={keyword:"additionalItems",type:"array",schemaType:["boolean","object"],before:"uniqueItems",error:LB,code(e){let{parentSchema:t,it:r}=e,{items:n}=t;if(!Array.isArray(n)){(0,Rd.checkStrictMode)(r,'"additionalItems" is ignored when "items" is not an array of schemas');return}rS(e,n)}};function rS(e,t){let{gen:r,schema:n,data:o,keyword:i,it:a}=e;a.items=!0;let l=r.const("len",(0,zr._)`${o}.length`);if(n===!1)e.setParams({len:t.length}),e.pass((0,zr._)`${l} <= ${t.length}`);else if(typeof n=="object"&&!(0,Rd.alwaysValidSchema)(a,n)){let d=r.var("valid",(0,zr._)`${l} <= ${t.length}`);r.if((0,zr.not)(d),()=>c(d)),e.ok(d)}function c(d){r.forRange("i",t.length,l,p=>{e.subschema({keyword:i,dataProp:p,dataPropType:Rd.Type.Num},d),a.allErrors||r.if((0,zr.not)(d),()=>r.break())})}}gi.validateAdditionalItems=rS;gi.default=qB});var $d=C(yi=>{"use strict";Object.defineProperty(yi,"__esModule",{value:!0});yi.validateTuple=void 0;var nS=G(),lu=Z(),UB=Ge(),zB={keyword:"items",type:"array",schemaType:["object","array","boolean"],before:"uniqueItems",code(e){let{schema:t,it:r}=e;if(Array.isArray(t))return oS(e,"additionalItems",t);r.items=!0,!(0,lu.alwaysValidSchema)(r,t)&&e.ok((0,UB.validateArray)(e))}};function oS(e,t,r=e.schema){let{gen:n,parentSchema:o,data:i,keyword:a,it:l}=e;p(o),l.opts.unevaluated&&r.length&&l.items!==!0&&(l.items=lu.mergeEvaluated.items(n,r.length,l.items));let c=n.name("valid"),d=n.const("len",(0,nS._)`${i}.length`);r.forEach((m,b)=>{(0,lu.alwaysValidSchema)(l,m)||(n.if((0,nS._)`${d} > ${b}`,()=>e.subschema({keyword:a,schemaProp:b,dataProp:b},c)),e.ok(c))});function p(m){let{opts:b,errSchemaPath:D}=l,g=r.length,y=g===m.minItems&&(g===m.maxItems||m[t]===!1);if(b.strictTuples&&!y){let S=`"${a}" is ${g}-tuple, but minItems or maxItems/${t} are not specified or different at path "${D}"`;(0,lu.checkStrictMode)(l,S,b.strictTuples)}}}yi.validateTuple=oS;yi.default=zB});var iS=C(xd=>{"use strict";Object.defineProperty(xd,"__esModule",{value:!0});var WB=$d(),VB={keyword:"prefixItems",type:"array",schemaType:["array"],before:"uniqueItems",code:e=>(0,WB.validateTuple)(e,"items")};xd.default=VB});var aS=C(Pd=>{"use strict";Object.defineProperty(Pd,"__esModule",{value:!0});var sS=G(),GB=Z(),HB=Ge(),YB=Ad(),KB={message:({params:{len:e}})=>(0,sS.str)`must NOT have more than ${e} items`,params:({params:{len:e}})=>(0,sS._)`{limit: ${e}}`},JB={keyword:"items",type:"array",schemaType:["object","boolean"],before:"uniqueItems",error:KB,code(e){let{schema:t,parentSchema:r,it:n}=e,{prefixItems:o}=r;n.items=!0,!(0,GB.alwaysValidSchema)(n,t)&&(o?(0,YB.validateAdditionalItems)(e,o):e.ok((0,HB.validateArray)(e)))}};Pd.default=JB});var uS=C(Od=>{"use strict";Object.defineProperty(Od,"__esModule",{value:!0});var Ye=G(),cu=Z(),QB={message:({params:{min:e,max:t}})=>t===void 0?(0,Ye.str)`must contain at least ${e} valid item(s)`:(0,Ye.str)`must contain at least ${e} and no more than ${t} valid item(s)`,params:({params:{min:e,max:t}})=>t===void 0?(0,Ye._)`{minContains: ${e}}`:(0,Ye._)`{minContains: ${e}, maxContains: ${t}}`},XB={keyword:"contains",type:"array",schemaType:["object","boolean"],before:"uniqueItems",trackErrors:!0,error:QB,code(e){let{gen:t,schema:r,parentSchema:n,data:o,it:i}=e,a,l,{minContains:c,maxContains:d}=n;i.opts.next?(a=c===void 0?1:c,l=d):a=1;let p=t.const("len",(0,Ye._)`${o}.length`);if(e.setParams({min:a,max:l}),l===void 0&&a===0){(0,cu.checkStrictMode)(i,'"minContains" == 0 without "maxContains": "contains" keyword ignored');return}if(l!==void 0&&a>l){(0,cu.checkStrictMode)(i,'"minContains" > "maxContains" is always invalid'),e.fail();return}if((0,cu.alwaysValidSchema)(i,r)){let y=(0,Ye._)`${p} >= ${a}`;l!==void 0&&(y=(0,Ye._)`${y} && ${p} <= ${l}`),e.pass(y);return}i.items=!0;let m=t.name("valid");l===void 0&&a===1?D(m,()=>t.if(m,()=>t.break())):a===0?(t.let(m,!0),l!==void 0&&t.if((0,Ye._)`${o}.length > 0`,b)):(t.let(m,!1),b()),e.result(m,()=>e.reset());function b(){let y=t.name("_valid"),S=t.let("count",0);D(y,()=>t.if(y,()=>g(S)))}function D(y,S){t.forRange("i",0,p,w=>{e.subschema({keyword:"contains",dataProp:w,dataPropType:cu.Type.Num,compositeRule:!0},y),S()})}function g(y){t.code((0,Ye._)`${y}++`),l===void 0?t.if((0,Ye._)`${y} >= ${a}`,()=>t.assign(m,!0).break()):(t.if((0,Ye._)`${y} > ${l}`,()=>t.assign(m,!1).break()),a===1?t.assign(m,!0):t.if((0,Ye._)`${y} >= ${a}`,()=>t.assign(m,!0)))}}};Od.default=XB});var fS=C($t=>{"use strict";Object.defineProperty($t,"__esModule",{value:!0});$t.validateSchemaDeps=$t.validatePropertyDeps=$t.error=void 0;var Bd=G(),ZB=Z(),Di=Ge();$t.error={message:({params:{property:e,depsCount:t,deps:r}})=>{let n=t===1?"property":"properties";return(0,Bd.str)`must have ${n} ${r} when property ${e} is present`},params:({params:{property:e,depsCount:t,deps:r,missingProperty:n}})=>(0,Bd._)`{property: ${e},
    missingProperty: ${n},
    depsCount: ${t},
    deps: ${r}}`};var eI={keyword:"dependencies",type:"object",schemaType:"object",error:$t.error,code(e){let[t,r]=tI(e);lS(e,t),cS(e,r)}};function tI({schema:e}){let t={},r={};for(let n in e){if(n==="__proto__")continue;let o=Array.isArray(e[n])?t:r;o[n]=e[n]}return[t,r]}function lS(e,t=e.schema){let{gen:r,data:n,it:o}=e;if(Object.keys(t).length===0)return;let i=r.let("missing");for(let a in t){let l=t[a];if(l.length===0)continue;let c=(0,Di.propertyInData)(r,n,a,o.opts.ownProperties);e.setParams({property:a,depsCount:l.length,deps:l.join(", ")}),o.allErrors?r.if(c,()=>{for(let d of l)(0,Di.checkReportMissingProp)(e,d)}):(r.if((0,Bd._)`${c} && (${(0,Di.checkMissingProp)(e,l,i)})`),(0,Di.reportMissingProp)(e,i),r.else())}}$t.validatePropertyDeps=lS;function cS(e,t=e.schema){let{gen:r,data:n,keyword:o,it:i}=e,a=r.name("valid");for(let l in t)(0,ZB.alwaysValidSchema)(i,t[l])||(r.if((0,Di.propertyInData)(r,n,l,i.opts.ownProperties),()=>{let c=e.subschema({keyword:o,schemaProp:l},a);e.mergeValidEvaluated(c,a)},()=>r.var(a,!0)),e.ok(a))}$t.validateSchemaDeps=cS;$t.default=eI});var pS=C(Id=>{"use strict";Object.defineProperty(Id,"__esModule",{value:!0});var dS=G(),rI=Z(),nI={message:"property name must be valid",params:({params:e})=>(0,dS._)`{propertyName: ${e.propertyName}}`},oI={keyword:"propertyNames",type:"object",schemaType:["object","boolean"],error:nI,code(e){let{gen:t,schema:r,data:n,it:o}=e;if((0,rI.alwaysValidSchema)(o,r))return;let i=t.name("valid");t.forIn("key",n,a=>{e.setParams({propertyName:a}),e.subschema({keyword:"propertyNames",data:a,dataTypes:["string"],propertyName:a,compositeRule:!0},i),t.if((0,dS.not)(i),()=>{e.error(!0),o.allErrors||t.break()})}),e.ok(i)}};Id.default=oI});var Md=C(kd=>{"use strict";Object.defineProperty(kd,"__esModule",{value:!0});var fu=Ge(),pt=G(),iI=Wt(),du=Z(),sI={message:"must NOT have additional properties",params:({params:e})=>(0,pt._)`{additionalProperty: ${e.additionalProperty}}`},aI={keyword:"additionalProperties",type:["object"],schemaType:["boolean","object"],allowUndefined:!0,trackErrors:!0,error:sI,code(e){let{gen:t,schema:r,parentSchema:n,data:o,errsCount:i,it:a}=e;if(!i)throw new Error("ajv implementation error");let{allErrors:l,opts:c}=a;if(a.props=!0,c.removeAdditional!=="all"&&(0,du.alwaysValidSchema)(a,r))return;let d=(0,fu.allSchemaProperties)(n.properties),p=(0,fu.allSchemaProperties)(n.patternProperties);m(),e.ok((0,pt._)`${i} === ${iI.default.errors}`);function m(){t.forIn("key",o,S=>{!d.length&&!p.length?g(S):t.if(b(S),()=>g(S))})}function b(S){let w;if(d.length>8){let T=(0,du.schemaRefOrVal)(a,n.properties,"properties");w=(0,fu.isOwnProperty)(t,T,S)}else d.length?w=(0,pt.or)(...d.map(T=>(0,pt._)`${S} === ${T}`)):w=pt.nil;return p.length&&(w=(0,pt.or)(w,...p.map(T=>(0,pt._)`${(0,fu.usePattern)(e,T)}.test(${S})`))),(0,pt.not)(w)}function D(S){t.code((0,pt._)`delete ${o}[${S}]`)}function g(S){if(c.removeAdditional==="all"||c.removeAdditional&&r===!1){D(S);return}if(r===!1){e.setParams({additionalProperty:S}),e.error(),l||t.break();return}if(typeof r=="object"&&!(0,du.alwaysValidSchema)(a,r)){let w=t.name("valid");c.removeAdditional==="failing"?(y(S,w,!1),t.if((0,pt.not)(w),()=>{e.reset(),D(S)})):(y(S,w),l||t.if((0,pt.not)(w),()=>t.break()))}}function y(S,w,T){let R={keyword:"additionalProperties",dataProp:S,dataPropType:du.Type.Str};T===!1&&Object.assign(R,{compositeRule:!0,createErrors:!1,allErrors:!1}),e.subschema(R,w)}}};kd.default=aI});var gS=C(jd=>{"use strict";Object.defineProperty(jd,"__esModule",{value:!0});var uI=oi(),mS=Ge(),Nd=Z(),hS=Md(),lI={keyword:"properties",type:"object",schemaType:"object",code(e){let{gen:t,schema:r,parentSchema:n,data:o,it:i}=e;i.opts.removeAdditional==="all"&&n.additionalProperties===void 0&&hS.default.code(new uI.KeywordCxt(i,hS.default,"additionalProperties"));let a=(0,mS.allSchemaProperties)(r);for(let m of a)i.definedProperties.add(m);i.opts.unevaluated&&a.length&&i.props!==!0&&(i.props=Nd.mergeEvaluated.props(t,(0,Nd.toHash)(a),i.props));let l=a.filter(m=>!(0,Nd.alwaysValidSchema)(i,r[m]));if(l.length===0)return;let c=t.name("valid");for(let m of l)d(m)?p(m):(t.if((0,mS.propertyInData)(t,o,m,i.opts.ownProperties)),p(m),i.allErrors||t.else().var(c,!0),t.endIf()),e.it.definedProperties.add(m),e.ok(c);function d(m){return i.opts.useDefaults&&!i.compositeRule&&r[m].default!==void 0}function p(m){e.subschema({keyword:"properties",schemaProp:m,dataProp:m},c)}}};jd.default=lI});var _S=C(Ld=>{"use strict";Object.defineProperty(Ld,"__esModule",{value:!0});var yS=Ge(),pu=G(),DS=Z(),bS=Z(),cI={keyword:"patternProperties",type:"object",schemaType:"object",code(e){let{gen:t,schema:r,data:n,parentSchema:o,it:i}=e,{opts:a}=i,l=(0,yS.allSchemaProperties)(r),c=l.filter(y=>(0,DS.alwaysValidSchema)(i,r[y]));if(l.length===0||c.length===l.length&&(!i.opts.unevaluated||i.props===!0))return;let d=a.strictSchema&&!a.allowMatchingProperties&&o.properties,p=t.name("valid");i.props!==!0&&!(i.props instanceof pu.Name)&&(i.props=(0,bS.evaluatedPropsToName)(t,i.props));let{props:m}=i;b();function b(){for(let y of l)d&&D(y),i.allErrors?g(y):(t.var(p,!0),g(y),t.if(p))}function D(y){for(let S in d)new RegExp(y).test(S)&&(0,DS.checkStrictMode)(i,`property ${S} matches pattern ${y} (use allowMatchingProperties)`)}function g(y){t.forIn("key",n,S=>{t.if((0,pu._)`${(0,yS.usePattern)(e,y)}.test(${S})`,()=>{let w=c.includes(y);w||e.subschema({keyword:"patternProperties",schemaProp:y,dataProp:S,dataPropType:bS.Type.Str},p),i.opts.unevaluated&&m!==!0?t.assign((0,pu._)`${m}[${S}]`,!0):!w&&!i.allErrors&&t.if((0,pu.not)(p),()=>t.break())})})}}};Ld.default=cI});var wS=C(qd=>{"use strict";Object.defineProperty(qd,"__esModule",{value:!0});var fI=Z(),dI={keyword:"not",schemaType:["object","boolean"],trackErrors:!0,code(e){let{gen:t,schema:r,it:n}=e;if((0,fI.alwaysValidSchema)(n,r)){e.fail();return}let o=t.name("valid");e.subschema({keyword:"not",compositeRule:!0,createErrors:!1,allErrors:!1},o),e.failResult(o,()=>e.reset(),()=>e.error())},error:{message:"must NOT be valid"}};qd.default=dI});var SS=C(Ud=>{"use strict";Object.defineProperty(Ud,"__esModule",{value:!0});var pI=Ge(),mI={keyword:"anyOf",schemaType:"array",trackErrors:!0,code:pI.validateUnion,error:{message:"must match a schema in anyOf"}};Ud.default=mI});var ES=C(zd=>{"use strict";Object.defineProperty(zd,"__esModule",{value:!0});var mu=G(),hI=Z(),gI={message:"must match exactly one schema in oneOf",params:({params:e})=>(0,mu._)`{passingSchemas: ${e.passing}}`},yI={keyword:"oneOf",schemaType:"array",trackErrors:!0,error:gI,code(e){let{gen:t,schema:r,parentSchema:n,it:o}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");if(o.opts.discriminator&&n.discriminator)return;let i=r,a=t.let("valid",!1),l=t.let("passing",null),c=t.name("_valid");e.setParams({passing:l}),t.block(d),e.result(a,()=>e.reset(),()=>e.error(!0));function d(){i.forEach((p,m)=>{let b;(0,hI.alwaysValidSchema)(o,p)?t.var(c,!0):b=e.subschema({keyword:"oneOf",schemaProp:m,compositeRule:!0},c),m>0&&t.if((0,mu._)`${c} && ${a}`).assign(a,!1).assign(l,(0,mu._)`[${l}, ${m}]`).else(),t.if(c,()=>{t.assign(a,!0),t.assign(l,m),b&&e.mergeEvaluated(b,mu.Name)})})}}};zd.default=yI});var vS=C(Wd=>{"use strict";Object.defineProperty(Wd,"__esModule",{value:!0});var DI=Z(),bI={keyword:"allOf",schemaType:"array",code(e){let{gen:t,schema:r,it:n}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");let o=t.name("valid");r.forEach((i,a)=>{if((0,DI.alwaysValidSchema)(n,i))return;let l=e.subschema({keyword:"allOf",schemaProp:a},o);e.ok(o),e.mergeEvaluated(l)})}};Wd.default=bI});var TS=C(Vd=>{"use strict";Object.defineProperty(Vd,"__esModule",{value:!0});var hu=G(),CS=Z(),_I={message:({params:e})=>(0,hu.str)`must match "${e.ifClause}" schema`,params:({params:e})=>(0,hu._)`{failingKeyword: ${e.ifClause}}`},wI={keyword:"if",schemaType:["object","boolean"],trackErrors:!0,error:_I,code(e){let{gen:t,parentSchema:r,it:n}=e;r.then===void 0&&r.else===void 0&&(0,CS.checkStrictMode)(n,'"if" without "then" and "else" is ignored');let o=FS(n,"then"),i=FS(n,"else");if(!o&&!i)return;let a=t.let("valid",!0),l=t.name("_valid");if(c(),e.reset(),o&&i){let p=t.let("ifClause");e.setParams({ifClause:p}),t.if(l,d("then",p),d("else",p))}else o?t.if(l,d("then")):t.if((0,hu.not)(l),d("else"));e.pass(a,()=>e.error(!0));function c(){let p=e.subschema({keyword:"if",compositeRule:!0,createErrors:!1,allErrors:!1},l);e.mergeEvaluated(p)}function d(p,m){return()=>{let b=e.subschema({keyword:p},l);t.assign(a,l),e.mergeValidEvaluated(b,a),m?t.assign(m,(0,hu._)`${p}`):e.setParams({ifClause:p})}}}};function FS(e,t){let r=e.schema[t];return r!==void 0&&!(0,CS.alwaysValidSchema)(e,r)}Vd.default=wI});var RS=C(Gd=>{"use strict";Object.defineProperty(Gd,"__esModule",{value:!0});var SI=Z(),EI={keyword:["then","else"],schemaType:["object","boolean"],code({keyword:e,parentSchema:t,it:r}){t.if===void 0&&(0,SI.checkStrictMode)(r,`"${e}" without "if" is ignored`)}};Gd.default=EI});var AS=C(Hd=>{"use strict";Object.defineProperty(Hd,"__esModule",{value:!0});var vI=Ad(),FI=iS(),CI=$d(),TI=aS(),RI=uS(),AI=fS(),$I=pS(),xI=Md(),PI=gS(),OI=_S(),BI=wS(),II=SS(),kI=ES(),MI=vS(),NI=TS(),jI=RS();function LI(e=!1){let t=[BI.default,II.default,kI.default,MI.default,NI.default,jI.default,$I.default,xI.default,AI.default,PI.default,OI.default];return e?t.push(FI.default,TI.default):t.push(vI.default,CI.default),t.push(RI.default),t}Hd.default=LI});var $S=C(Yd=>{"use strict";Object.defineProperty(Yd,"__esModule",{value:!0});var ye=G(),qI={message:({schemaCode:e})=>(0,ye.str)`must match format "${e}"`,params:({schemaCode:e})=>(0,ye._)`{format: ${e}}`},UI={keyword:"format",type:["number","string"],schemaType:"string",$data:!0,error:qI,code(e,t){let{gen:r,data:n,$data:o,schema:i,schemaCode:a,it:l}=e,{opts:c,errSchemaPath:d,schemaEnv:p,self:m}=l;if(!c.validateFormats)return;o?b():D();function b(){let g=r.scopeValue("formats",{ref:m.formats,code:c.code.formats}),y=r.const("fDef",(0,ye._)`${g}[${a}]`),S=r.let("fType"),w=r.let("format");r.if((0,ye._)`typeof ${y} == "object" && !(${y} instanceof RegExp)`,()=>r.assign(S,(0,ye._)`${y}.type || "string"`).assign(w,(0,ye._)`${y}.validate`),()=>r.assign(S,(0,ye._)`"string"`).assign(w,y)),e.fail$data((0,ye.or)(T(),R()));function T(){return c.strictSchema===!1?ye.nil:(0,ye._)`${a} && !${w}`}function R(){let A=p.$async?(0,ye._)`(${y}.async ? await ${w}(${n}) : ${w}(${n}))`:(0,ye._)`${w}(${n})`,v=(0,ye._)`(typeof ${w} == "function" ? ${A} : ${w}.test(${n}))`;return(0,ye._)`${w} && ${w} !== true && ${S} === ${t} && !${v}`}}function D(){let g=m.formats[i];if(!g){T();return}if(g===!0)return;let[y,S,w]=R(g);y===t&&e.pass(A());function T(){if(c.strictSchema===!1){m.logger.warn(v());return}throw new Error(v());function v(){return`unknown format "${i}" ignored in schema at path "${d}"`}}function R(v){let B=v instanceof RegExp?(0,ye.regexpCode)(v):c.code.formats?(0,ye._)`${c.code.formats}${(0,ye.getProperty)(i)}`:void 0,W=r.scopeValue("formats",{key:i,ref:v,code:B});return typeof v=="object"&&!(v instanceof RegExp)?[v.type||"string",v.validate,(0,ye._)`${W}.validate`]:["string",v,W]}function A(){if(typeof g=="object"&&!(g instanceof RegExp)&&g.async){if(!p.$async)throw new Error("async format in sync schema");return(0,ye._)`await ${w}(${n})`}return typeof S=="function"?(0,ye._)`${w}(${n})`:(0,ye._)`${w}.test(${n})`}}}};Yd.default=UI});var xS=C(Kd=>{"use strict";Object.defineProperty(Kd,"__esModule",{value:!0});var zI=$S(),WI=[zI.default];Kd.default=WI});var PS=C(qn=>{"use strict";Object.defineProperty(qn,"__esModule",{value:!0});qn.contentVocabulary=qn.metadataVocabulary=void 0;qn.metadataVocabulary=["title","description","default","deprecated","readOnly","writeOnly","examples"];qn.contentVocabulary=["contentMediaType","contentEncoding","contentSchema"]});var BS=C(Jd=>{"use strict";Object.defineProperty(Jd,"__esModule",{value:!0});var VI=qw(),GI=tS(),HI=AS(),YI=xS(),OS=PS(),KI=[VI.default,GI.default,(0,HI.default)(),YI.default,OS.metadataVocabulary,OS.contentVocabulary];Jd.default=KI});var kS=C(gu=>{"use strict";Object.defineProperty(gu,"__esModule",{value:!0});gu.DiscrError=void 0;var IS;(function(e){e.Tag="tag",e.Mapping="mapping"})(IS||(gu.DiscrError=IS={}))});var NS=C(Xd=>{"use strict";Object.defineProperty(Xd,"__esModule",{value:!0});var Un=G(),Qd=kS(),MS=Xa(),JI=ii(),QI=Z(),XI={message:({params:{discrError:e,tagName:t}})=>e===Qd.DiscrError.Tag?`tag "${t}" must be string`:`value of tag "${t}" must be in oneOf`,params:({params:{discrError:e,tag:t,tagName:r}})=>(0,Un._)`{error: ${e}, tag: ${r}, tagValue: ${t}}`},ZI={keyword:"discriminator",type:"object",schemaType:"object",error:XI,code(e){let{gen:t,data:r,schema:n,parentSchema:o,it:i}=e,{oneOf:a}=o;if(!i.opts.discriminator)throw new Error("discriminator: requires discriminator option");let l=n.propertyName;if(typeof l!="string")throw new Error("discriminator: requires propertyName");if(n.mapping)throw new Error("discriminator: mapping is not supported");if(!a)throw new Error("discriminator: requires oneOf keyword");let c=t.let("valid",!1),d=t.const("tag",(0,Un._)`${r}${(0,Un.getProperty)(l)}`);t.if((0,Un._)`typeof ${d} == "string"`,()=>p(),()=>e.error(!1,{discrError:Qd.DiscrError.Tag,tag:d,tagName:l})),e.ok(c);function p(){let D=b();t.if(!1);for(let g in D)t.elseIf((0,Un._)`${d} === ${g}`),t.assign(c,m(D[g]));t.else(),e.error(!1,{discrError:Qd.DiscrError.Mapping,tag:d,tagName:l}),t.endIf()}function m(D){let g=t.name("valid"),y=e.subschema({keyword:"oneOf",schemaProp:D},g);return e.mergeEvaluated(y,Un.Name),g}function b(){var D;let g={},y=w(o),S=!0;for(let A=0;A<a.length;A++){let v=a[A];if(v?.$ref&&!(0,QI.schemaHasRulesButRef)(v,i.self.RULES)){let W=v.$ref;if(v=MS.resolveRef.call(i.self,i.schemaEnv.root,i.baseId,W),v instanceof MS.SchemaEnv&&(v=v.schema),v===void 0)throw new JI.default(i.opts.uriResolver,i.baseId,W)}let B=(D=v?.properties)===null||D===void 0?void 0:D[l];if(typeof B!="object")throw new Error(`discriminator: oneOf subschemas (or referenced schemas) must have "properties/${l}"`);S=S&&(y||w(v)),T(B,A)}if(!S)throw new Error(`discriminator: "${l}" must be required`);return g;function w({required:A}){return Array.isArray(A)&&A.includes(l)}function T(A,v){if(A.const)R(A.const,v);else if(A.enum)for(let B of A.enum)R(B,v);else throw new Error(`discriminator: "properties/${l}" must have "const" or "enum"`)}function R(A,v){if(typeof A!="string"||A in g)throw new Error(`discriminator: "${l}" values must be unique strings`);g[A]=v}}}};Xd.default=ZI});var jS=C((Az,ek)=>{ek.exports={$schema:"http://json-schema.org/draft-07/schema#",$id:"http://json-schema.org/draft-07/schema#",title:"Core schema meta-schema",definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},nonNegativeInteger:{type:"integer",minimum:0},nonNegativeIntegerDefault0:{allOf:[{$ref:"#/definitions/nonNegativeInteger"},{default:0}]},simpleTypes:{enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},uniqueItems:!0,default:[]}},type:["object","boolean"],properties:{$id:{type:"string",format:"uri-reference"},$schema:{type:"string",format:"uri"},$ref:{type:"string",format:"uri-reference"},$comment:{type:"string"},title:{type:"string"},description:{type:"string"},default:!0,readOnly:{type:"boolean",default:!1},examples:{type:"array",items:!0},multipleOf:{type:"number",exclusiveMinimum:0},maximum:{type:"number"},exclusiveMaximum:{type:"number"},minimum:{type:"number"},exclusiveMinimum:{type:"number"},maxLength:{$ref:"#/definitions/nonNegativeInteger"},minLength:{$ref:"#/definitions/nonNegativeIntegerDefault0"},pattern:{type:"string",format:"regex"},additionalItems:{$ref:"#"},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:!0},maxItems:{$ref:"#/definitions/nonNegativeInteger"},minItems:{$ref:"#/definitions/nonNegativeIntegerDefault0"},uniqueItems:{type:"boolean",default:!1},contains:{$ref:"#"},maxProperties:{$ref:"#/definitions/nonNegativeInteger"},minProperties:{$ref:"#/definitions/nonNegativeIntegerDefault0"},required:{$ref:"#/definitions/stringArray"},additionalProperties:{$ref:"#"},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},propertyNames:{format:"regex"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},propertyNames:{$ref:"#"},const:!0,enum:{type:"array",items:!0,minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{type:"string"},contentMediaType:{type:"string"},contentEncoding:{type:"string"},if:{$ref:"#"},then:{$ref:"#"},else:{$ref:"#"},allOf:{$ref:"#/definitions/schemaArray"},anyOf:{$ref:"#/definitions/schemaArray"},oneOf:{$ref:"#/definitions/schemaArray"},not:{$ref:"#"}},default:!0}});var qS=C((ce,Zd)=>{"use strict";Object.defineProperty(ce,"__esModule",{value:!0});ce.MissingRefError=ce.ValidationError=ce.CodeGen=ce.Name=ce.nil=ce.stringify=ce.str=ce._=ce.KeywordCxt=ce.Ajv=void 0;var tk=Iw(),rk=BS(),nk=NS(),LS=jS(),ok=["/properties"],yu="http://json-schema.org/draft-07/schema",zn=class extends tk.default{_addVocabularies(){super._addVocabularies(),rk.default.forEach(t=>this.addVocabulary(t)),this.opts.discriminator&&this.addKeyword(nk.default)}_addDefaultMetaSchema(){if(super._addDefaultMetaSchema(),!this.opts.meta)return;let t=this.opts.$data?this.$dataMetaSchema(LS,ok):LS;this.addMetaSchema(t,yu,!1),this.refs["http://json-schema.org/schema"]=yu}defaultMeta(){return this.opts.defaultMeta=super.defaultMeta()||(this.getSchema(yu)?yu:void 0)}};ce.Ajv=zn;Zd.exports=ce=zn;Zd.exports.Ajv=zn;Object.defineProperty(ce,"__esModule",{value:!0});ce.default=zn;var ik=oi();Object.defineProperty(ce,"KeywordCxt",{enumerable:!0,get:function(){return ik.KeywordCxt}});var Wn=G();Object.defineProperty(ce,"_",{enumerable:!0,get:function(){return Wn._}});Object.defineProperty(ce,"str",{enumerable:!0,get:function(){return Wn.str}});Object.defineProperty(ce,"stringify",{enumerable:!0,get:function(){return Wn.stringify}});Object.defineProperty(ce,"nil",{enumerable:!0,get:function(){return Wn.nil}});Object.defineProperty(ce,"Name",{enumerable:!0,get:function(){return Wn.Name}});Object.defineProperty(ce,"CodeGen",{enumerable:!0,get:function(){return Wn.CodeGen}});var sk=Ja();Object.defineProperty(ce,"ValidationError",{enumerable:!0,get:function(){return sk.default}});var ak=ii();Object.defineProperty(ce,"MissingRefError",{enumerable:!0,get:function(){return ak.default}})});var US=C(Du=>{"use strict";Object.defineProperty(Du,"__esModule",{value:!0});Du.titleCase=void 0;function uk(e){let t={and:!0,but:!0,or:!0,nor:!0,for:!0,yet:!0,so:!0,to:!0,as:!0,at:!0,by:!0,from:!0,in:!0,into:!0,of:!0,off:!0,on:!0,onto:!0,out:!0,over:!0,up:!0,with:!0},r={a:!0,an:!0,the:!0},n={npm:!0,"crates.io":!0,dbt:!0,"pub.dev":!0,kubectx:!0,"monday.com":!0,"ray.so":!0,flomo:!0,iterm:!0,xkcd:!0,macos:!0,iphone:!0,github:!0,ide:!0,url:!0,vs:!0,ai:!0},o={npm:"npm","crates.io":"crates.io",dbt:"dbt","pub.dev":"pub.dev",kubectx:"kubectx","monday.com":"monday.com","ray.so":"ray.so",flomo:"flomo",iterm:"iTerm",xkcd:"xkcd",macos:"macOS",iphone:"iPhone",github:"GitHub",ide:"IDE",url:"URL",vs:"VS",ai:"AI"};e=e.replace(/\.\.\./g,"\u2026");let i=e.split(" ");for(let a=0;a<i.length;a++){let l=i[a],c=l.toLowerCase(),d=t[c],p=r[c];n[c]?i[a]=o[c]:!d&&!p||a===0||a===i.length-1||p&&i[a-1].endsWith(":")?i[a]=l.charAt(0).toUpperCase()+l.slice(1).toLowerCase():i[a]=l.toLowerCase()}return i.join(" ")}Du.titleCase=uk});var WS=C((xz,zS)=>{zS.exports.titleCase=US().titleCase});var aE=C((Jz,xk)=>{xk.exports={dots:{interval:80,frames:["\u280B","\u2819","\u2839","\u2838","\u283C","\u2834","\u2826","\u2827","\u2807","\u280F"]},dots2:{interval:80,frames:["\u28FE","\u28FD","\u28FB","\u28BF","\u287F","\u28DF","\u28EF","\u28F7"]},dots3:{interval:80,frames:["\u280B","\u2819","\u281A","\u281E","\u2816","\u2826","\u2834","\u2832","\u2833","\u2813"]},dots4:{interval:80,frames:["\u2804","\u2806","\u2807","\u280B","\u2819","\u2838","\u2830","\u2820","\u2830","\u2838","\u2819","\u280B","\u2807","\u2806"]},dots5:{interval:80,frames:["\u280B","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B"]},dots6:{interval:80,frames:["\u2801","\u2809","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2824","\u2804","\u2804","\u2824","\u2834","\u2832","\u2812","\u2802","\u2802","\u2812","\u281A","\u2819","\u2809","\u2801"]},dots7:{interval:80,frames:["\u2808","\u2809","\u280B","\u2813","\u2812","\u2810","\u2810","\u2812","\u2816","\u2826","\u2824","\u2820","\u2820","\u2824","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B","\u2809","\u2808"]},dots8:{interval:80,frames:["\u2801","\u2801","\u2809","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2824","\u2804","\u2804","\u2824","\u2820","\u2820","\u2824","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B","\u2809","\u2808","\u2808"]},dots9:{interval:80,frames:["\u28B9","\u28BA","\u28BC","\u28F8","\u28C7","\u2867","\u2857","\u284F"]},dots10:{interval:80,frames:["\u2884","\u2882","\u2881","\u2841","\u2848","\u2850","\u2860"]},dots11:{interval:100,frames:["\u2801","\u2802","\u2804","\u2840","\u2880","\u2820","\u2810","\u2808"]},dots12:{interval:80,frames:["\u2880\u2800","\u2840\u2800","\u2804\u2800","\u2882\u2800","\u2842\u2800","\u2805\u2800","\u2883\u2800","\u2843\u2800","\u280D\u2800","\u288B\u2800","\u284B\u2800","\u280D\u2801","\u288B\u2801","\u284B\u2801","\u280D\u2809","\u280B\u2809","\u280B\u2809","\u2809\u2819","\u2809\u2819","\u2809\u2829","\u2808\u2899","\u2808\u2859","\u2888\u2829","\u2840\u2899","\u2804\u2859","\u2882\u2829","\u2842\u2898","\u2805\u2858","\u2883\u2828","\u2843\u2890","\u280D\u2850","\u288B\u2820","\u284B\u2880","\u280D\u2841","\u288B\u2801","\u284B\u2801","\u280D\u2809","\u280B\u2809","\u280B\u2809","\u2809\u2819","\u2809\u2819","\u2809\u2829","\u2808\u2899","\u2808\u2859","\u2808\u2829","\u2800\u2899","\u2800\u2859","\u2800\u2829","\u2800\u2898","\u2800\u2858","\u2800\u2828","\u2800\u2890","\u2800\u2850","\u2800\u2820","\u2800\u2880","\u2800\u2840"]},dots13:{interval:80,frames:["\u28FC","\u28F9","\u28BB","\u283F","\u285F","\u28CF","\u28E7","\u28F6"]},dots8Bit:{interval:80,frames:["\u2800","\u2801","\u2802","\u2803","\u2804","\u2805","\u2806","\u2807","\u2840","\u2841","\u2842","\u2843","\u2844","\u2845","\u2846","\u2847","\u2808","\u2809","\u280A","\u280B","\u280C","\u280D","\u280E","\u280F","\u2848","\u2849","\u284A","\u284B","\u284C","\u284D","\u284E","\u284F","\u2810","\u2811","\u2812","\u2813","\u2814","\u2815","\u2816","\u2817","\u2850","\u2851","\u2852","\u2853","\u2854","\u2855","\u2856","\u2857","\u2818","\u2819","\u281A","\u281B","\u281C","\u281D","\u281E","\u281F","\u2858","\u2859","\u285A","\u285B","\u285C","\u285D","\u285E","\u285F","\u2820","\u2821","\u2822","\u2823","\u2824","\u2825","\u2826","\u2827","\u2860","\u2861","\u2862","\u2863","\u2864","\u2865","\u2866","\u2867","\u2828","\u2829","\u282A","\u282B","\u282C","\u282D","\u282E","\u282F","\u2868","\u2869","\u286A","\u286B","\u286C","\u286D","\u286E","\u286F","\u2830","\u2831","\u2832","\u2833","\u2834","\u2835","\u2836","\u2837","\u2870","\u2871","\u2872","\u2873","\u2874","\u2875","\u2876","\u2877","\u2838","\u2839","\u283A","\u283B","\u283C","\u283D","\u283E","\u283F","\u2878","\u2879","\u287A","\u287B","\u287C","\u287D","\u287E","\u287F","\u2880","\u2881","\u2882","\u2883","\u2884","\u2885","\u2886","\u2887","\u28C0","\u28C1","\u28C2","\u28C3","\u28C4","\u28C5","\u28C6","\u28C7","\u2888","\u2889","\u288A","\u288B","\u288C","\u288D","\u288E","\u288F","\u28C8","\u28C9","\u28CA","\u28CB","\u28CC","\u28CD","\u28CE","\u28CF","\u2890","\u2891","\u2892","\u2893","\u2894","\u2895","\u2896","\u2897","\u28D0","\u28D1","\u28D2","\u28D3","\u28D4","\u28D5","\u28D6","\u28D7","\u2898","\u2899","\u289A","\u289B","\u289C","\u289D","\u289E","\u289F","\u28D8","\u28D9","\u28DA","\u28DB","\u28DC","\u28DD","\u28DE","\u28DF","\u28A0","\u28A1","\u28A2","\u28A3","\u28A4","\u28A5","\u28A6","\u28A7","\u28E0","\u28E1","\u28E2","\u28E3","\u28E4","\u28E5","\u28E6","\u28E7","\u28A8","\u28A9","\u28AA","\u28AB","\u28AC","\u28AD","\u28AE","\u28AF","\u28E8","\u28E9","\u28EA","\u28EB","\u28EC","\u28ED","\u28EE","\u28EF","\u28B0","\u28B1","\u28B2","\u28B3","\u28B4","\u28B5","\u28B6","\u28B7","\u28F0","\u28F1","\u28F2","\u28F3","\u28F4","\u28F5","\u28F6","\u28F7","\u28B8","\u28B9","\u28BA","\u28BB","\u28BC","\u28BD","\u28BE","\u28BF","\u28F8","\u28F9","\u28FA","\u28FB","\u28FC","\u28FD","\u28FE","\u28FF"]},sand:{interval:80,frames:["\u2801","\u2802","\u2804","\u2840","\u2848","\u2850","\u2860","\u28C0","\u28C1","\u28C2","\u28C4","\u28CC","\u28D4","\u28E4","\u28E5","\u28E6","\u28EE","\u28F6","\u28F7","\u28FF","\u287F","\u283F","\u289F","\u281F","\u285B","\u281B","\u282B","\u288B","\u280B","\u280D","\u2849","\u2809","\u2811","\u2821","\u2881"]},line:{interval:130,frames:["-","\\","|","/"]},line2:{interval:100,frames:["\u2802","-","\u2013","\u2014","\u2013","-"]},pipe:{interval:100,frames:["\u2524","\u2518","\u2534","\u2514","\u251C","\u250C","\u252C","\u2510"]},simpleDots:{interval:400,frames:[".  ",".. ","...","   "]},simpleDotsScrolling:{interval:200,frames:[".  ",".. ","..."," ..","  .","   "]},star:{interval:70,frames:["\u2736","\u2738","\u2739","\u273A","\u2739","\u2737"]},star2:{interval:80,frames:["+","x","*"]},flip:{interval:70,frames:["_","_","_","-","`","`","'","\xB4","-","_","_","_"]},hamburger:{interval:100,frames:["\u2631","\u2632","\u2634"]},growVertical:{interval:120,frames:["\u2581","\u2583","\u2584","\u2585","\u2586","\u2587","\u2586","\u2585","\u2584","\u2583"]},growHorizontal:{interval:120,frames:["\u258F","\u258E","\u258D","\u258C","\u258B","\u258A","\u2589","\u258A","\u258B","\u258C","\u258D","\u258E"]},balloon:{interval:140,frames:[" ",".","o","O","@","*"," "]},balloon2:{interval:120,frames:[".","o","O","\xB0","O","o","."]},noise:{interval:100,frames:["\u2593","\u2592","\u2591"]},bounce:{interval:120,frames:["\u2801","\u2802","\u2804","\u2802"]},boxBounce:{interval:120,frames:["\u2596","\u2598","\u259D","\u2597"]},boxBounce2:{interval:100,frames:["\u258C","\u2580","\u2590","\u2584"]},triangle:{interval:50,frames:["\u25E2","\u25E3","\u25E4","\u25E5"]},binary:{interval:80,frames:["010010","001100","100101","111010","111101","010111","101011","111000","110011","110101"]},arc:{interval:100,frames:["\u25DC","\u25E0","\u25DD","\u25DE","\u25E1","\u25DF"]},circle:{interval:120,frames:["\u25E1","\u2299","\u25E0"]},squareCorners:{interval:180,frames:["\u25F0","\u25F3","\u25F2","\u25F1"]},circleQuarters:{interval:120,frames:["\u25F4","\u25F7","\u25F6","\u25F5"]},circleHalves:{interval:50,frames:["\u25D0","\u25D3","\u25D1","\u25D2"]},squish:{interval:100,frames:["\u256B","\u256A"]},toggle:{interval:250,frames:["\u22B6","\u22B7"]},toggle2:{interval:80,frames:["\u25AB","\u25AA"]},toggle3:{interval:120,frames:["\u25A1","\u25A0"]},toggle4:{interval:100,frames:["\u25A0","\u25A1","\u25AA","\u25AB"]},toggle5:{interval:100,frames:["\u25AE","\u25AF"]},toggle6:{interval:300,frames:["\u101D","\u1040"]},toggle7:{interval:80,frames:["\u29BE","\u29BF"]},toggle8:{interval:100,frames:["\u25CD","\u25CC"]},toggle9:{interval:100,frames:["\u25C9","\u25CE"]},toggle10:{interval:100,frames:["\u3282","\u3280","\u3281"]},toggle11:{interval:50,frames:["\u29C7","\u29C6"]},toggle12:{interval:120,frames:["\u2617","\u2616"]},toggle13:{interval:80,frames:["=","*","-"]},arrow:{interval:100,frames:["\u2190","\u2196","\u2191","\u2197","\u2192","\u2198","\u2193","\u2199"]},arrow2:{interval:80,frames:["\u2B06\uFE0F ","\u2197\uFE0F ","\u27A1\uFE0F ","\u2198\uFE0F ","\u2B07\uFE0F ","\u2199\uFE0F ","\u2B05\uFE0F ","\u2196\uFE0F "]},arrow3:{interval:120,frames:["\u25B9\u25B9\u25B9\u25B9\u25B9","\u25B8\u25B9\u25B9\u25B9\u25B9","\u25B9\u25B8\u25B9\u25B9\u25B9","\u25B9\u25B9\u25B8\u25B9\u25B9","\u25B9\u25B9\u25B9\u25B8\u25B9","\u25B9\u25B9\u25B9\u25B9\u25B8"]},bouncingBar:{interval:80,frames:["[    ]","[=   ]","[==  ]","[=== ]","[====]","[ ===]","[  ==]","[   =]","[    ]","[   =]","[  ==]","[ ===]","[====]","[=== ]","[==  ]","[=   ]"]},bouncingBall:{interval:80,frames:["( \u25CF    )","(  \u25CF   )","(   \u25CF  )","(    \u25CF )","(     \u25CF)","(    \u25CF )","(   \u25CF  )","(  \u25CF   )","( \u25CF    )","(\u25CF     )"]},smiley:{interval:200,frames:["\u{1F604} ","\u{1F61D} "]},monkey:{interval:300,frames:["\u{1F648} ","\u{1F648} ","\u{1F649} ","\u{1F64A} "]},hearts:{interval:100,frames:["\u{1F49B} ","\u{1F499} ","\u{1F49C} ","\u{1F49A} ","\u2764\uFE0F "]},clock:{interval:100,frames:["\u{1F55B} ","\u{1F550} ","\u{1F551} ","\u{1F552} ","\u{1F553} ","\u{1F554} ","\u{1F555} ","\u{1F556} ","\u{1F557} ","\u{1F558} ","\u{1F559} ","\u{1F55A} "]},earth:{interval:180,frames:["\u{1F30D} ","\u{1F30E} ","\u{1F30F} "]},material:{interval:17,frames:["\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581"]},moon:{interval:80,frames:["\u{1F311} ","\u{1F312} ","\u{1F313} ","\u{1F314} ","\u{1F315} ","\u{1F316} ","\u{1F317} ","\u{1F318} "]},runner:{interval:140,frames:["\u{1F6B6} ","\u{1F3C3} "]},pong:{interval:80,frames:["\u2590\u2802       \u258C","\u2590\u2808       \u258C","\u2590 \u2802      \u258C","\u2590 \u2820      \u258C","\u2590  \u2840     \u258C","\u2590  \u2820     \u258C","\u2590   \u2802    \u258C","\u2590   \u2808    \u258C","\u2590    \u2802   \u258C","\u2590    \u2820   \u258C","\u2590     \u2840  \u258C","\u2590     \u2820  \u258C","\u2590      \u2802 \u258C","\u2590      \u2808 \u258C","\u2590       \u2802\u258C","\u2590       \u2820\u258C","\u2590       \u2840\u258C","\u2590      \u2820 \u258C","\u2590      \u2802 \u258C","\u2590     \u2808  \u258C","\u2590     \u2802  \u258C","\u2590    \u2820   \u258C","\u2590    \u2840   \u258C","\u2590   \u2820    \u258C","\u2590   \u2802    \u258C","\u2590  \u2808     \u258C","\u2590  \u2802     \u258C","\u2590 \u2820      \u258C","\u2590 \u2840      \u258C","\u2590\u2820       \u258C"]},shark:{interval:120,frames:["\u2590|\\____________\u258C","\u2590_|\\___________\u258C","\u2590__|\\__________\u258C","\u2590___|\\_________\u258C","\u2590____|\\________\u258C","\u2590_____|\\_______\u258C","\u2590______|\\______\u258C","\u2590_______|\\_____\u258C","\u2590________|\\____\u258C","\u2590_________|\\___\u258C","\u2590__________|\\__\u258C","\u2590___________|\\_\u258C","\u2590____________|\\\u258C","\u2590____________/|\u258C","\u2590___________/|_\u258C","\u2590__________/|__\u258C","\u2590_________/|___\u258C","\u2590________/|____\u258C","\u2590_______/|_____\u258C","\u2590______/|______\u258C","\u2590_____/|_______\u258C","\u2590____/|________\u258C","\u2590___/|_________\u258C","\u2590__/|__________\u258C","\u2590_/|___________\u258C","\u2590/|____________\u258C"]},dqpb:{interval:100,frames:["d","q","p","b"]},weather:{interval:100,frames:["\u2600\uFE0F ","\u2600\uFE0F ","\u2600\uFE0F ","\u{1F324} ","\u26C5\uFE0F ","\u{1F325} ","\u2601\uFE0F ","\u{1F327} ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u26C8 ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u2601\uFE0F ","\u{1F325} ","\u26C5\uFE0F ","\u{1F324} ","\u2600\uFE0F ","\u2600\uFE0F "]},christmas:{interval:400,frames:["\u{1F332}","\u{1F384}"]},grenade:{interval:80,frames:["\u060C  ","\u2032  "," \xB4 "," \u203E ","  \u2E0C","  \u2E0A","  |","  \u204E","  \u2055"," \u0DF4 ","  \u2053","   ","   ","   "]},point:{interval:125,frames:["\u2219\u2219\u2219","\u25CF\u2219\u2219","\u2219\u25CF\u2219","\u2219\u2219\u25CF","\u2219\u2219\u2219"]},layer:{interval:150,frames:["-","=","\u2261"]},betaWave:{interval:80,frames:["\u03C1\u03B2\u03B2\u03B2\u03B2\u03B2\u03B2","\u03B2\u03C1\u03B2\u03B2\u03B2\u03B2\u03B2","\u03B2\u03B2\u03C1\u03B2\u03B2\u03B2\u03B2","\u03B2\u03B2\u03B2\u03C1\u03B2\u03B2\u03B2","\u03B2\u03B2\u03B2\u03B2\u03C1\u03B2\u03B2","\u03B2\u03B2\u03B2\u03B2\u03B2\u03C1\u03B2","\u03B2\u03B2\u03B2\u03B2\u03B2\u03B2\u03C1"]},fingerDance:{interval:160,frames:["\u{1F918} ","\u{1F91F} ","\u{1F596} ","\u270B ","\u{1F91A} ","\u{1F446} "]},fistBump:{interval:80,frames:["\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u3000\u{1F91C}\u3000\u3000\u{1F91B}\u3000 ","\u3000\u3000\u{1F91C}\u{1F91B}\u3000\u3000 ","\u3000\u{1F91C}\u2728\u{1F91B}\u3000\u3000 ","\u{1F91C}\u3000\u2728\u3000\u{1F91B}\u3000 "]},soccerHeader:{interval:80,frames:[" \u{1F9D1}\u26BD\uFE0F       \u{1F9D1} ","\u{1F9D1}  \u26BD\uFE0F      \u{1F9D1} ","\u{1F9D1}   \u26BD\uFE0F     \u{1F9D1} ","\u{1F9D1}    \u26BD\uFE0F    \u{1F9D1} ","\u{1F9D1}     \u26BD\uFE0F   \u{1F9D1} ","\u{1F9D1}      \u26BD\uFE0F  \u{1F9D1} ","\u{1F9D1}       \u26BD\uFE0F\u{1F9D1}  ","\u{1F9D1}      \u26BD\uFE0F  \u{1F9D1} ","\u{1F9D1}     \u26BD\uFE0F   \u{1F9D1} ","\u{1F9D1}    \u26BD\uFE0F    \u{1F9D1} ","\u{1F9D1}   \u26BD\uFE0F     \u{1F9D1} ","\u{1F9D1}  \u26BD\uFE0F      \u{1F9D1} "]},mindblown:{interval:160,frames:["\u{1F610} ","\u{1F610} ","\u{1F62E} ","\u{1F62E} ","\u{1F626} ","\u{1F626} ","\u{1F627} ","\u{1F627} ","\u{1F92F} ","\u{1F4A5} ","\u2728 ","\u3000 ","\u3000 ","\u3000 "]},speaker:{interval:160,frames:["\u{1F508} ","\u{1F509} ","\u{1F50A} ","\u{1F509} "]},orangePulse:{interval:100,frames:["\u{1F538} ","\u{1F536} ","\u{1F7E0} ","\u{1F7E0} ","\u{1F536} "]},bluePulse:{interval:100,frames:["\u{1F539} ","\u{1F537} ","\u{1F535} ","\u{1F535} ","\u{1F537} "]},orangeBluePulse:{interval:100,frames:["\u{1F538} ","\u{1F536} ","\u{1F7E0} ","\u{1F7E0} ","\u{1F536} ","\u{1F539} ","\u{1F537} ","\u{1F535} ","\u{1F535} ","\u{1F537} "]},timeTravel:{interval:100,frames:["\u{1F55B} ","\u{1F55A} ","\u{1F559} ","\u{1F558} ","\u{1F557} ","\u{1F556} ","\u{1F555} ","\u{1F554} ","\u{1F553} ","\u{1F552} ","\u{1F551} ","\u{1F550} "]},aesthetic:{interval:80,frames:["\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0","\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1\u25B1"]},dwarfFortress:{interval:80,frames:[" \u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2593\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2593\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2592\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2592\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2591\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2591\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A \u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2593\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2593\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2592\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2592\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2591\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2591\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A \u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2593\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2593\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2592\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2592\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2591\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2591\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A \u2588\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2593\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2593\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2592\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2592\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2591\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2591\u2588\u2588\xA3\xA3\xA3  ","   \u263A \u2588\u2588\xA3\xA3\xA3  ","    \u263A\u2588\u2588\xA3\xA3\xA3  ","    \u263A\u2588\u2588\xA3\xA3\xA3  ","    \u263A\u2593\u2588\xA3\xA3\xA3  ","    \u263A\u2593\u2588\xA3\xA3\xA3  ","    \u263A\u2592\u2588\xA3\xA3\xA3  ","    \u263A\u2592\u2588\xA3\xA3\xA3  ","    \u263A\u2591\u2588\xA3\xA3\xA3  ","    \u263A\u2591\u2588\xA3\xA3\xA3  ","    \u263A \u2588\xA3\xA3\xA3  ","     \u263A\u2588\xA3\xA3\xA3  ","     \u263A\u2588\xA3\xA3\xA3  ","     \u263A\u2593\xA3\xA3\xA3  ","     \u263A\u2593\xA3\xA3\xA3  ","     \u263A\u2592\xA3\xA3\xA3  ","     \u263A\u2592\xA3\xA3\xA3  ","     \u263A\u2591\xA3\xA3\xA3  ","     \u263A\u2591\xA3\xA3\xA3  ","     \u263A \xA3\xA3\xA3  ","      \u263A\xA3\xA3\xA3  ","      \u263A\xA3\xA3\xA3  ","      \u263A\u2593\xA3\xA3  ","      \u263A\u2593\xA3\xA3  ","      \u263A\u2592\xA3\xA3  ","      \u263A\u2592\xA3\xA3  ","      \u263A\u2591\xA3\xA3  ","      \u263A\u2591\xA3\xA3  ","      \u263A \xA3\xA3  ","       \u263A\xA3\xA3  ","       \u263A\xA3\xA3  ","       \u263A\u2593\xA3  ","       \u263A\u2593\xA3  ","       \u263A\u2592\xA3  ","       \u263A\u2592\xA3  ","       \u263A\u2591\xA3  ","       \u263A\u2591\xA3  ","       \u263A \xA3  ","        \u263A\xA3  ","        \u263A\xA3  ","        \u263A\u2593  ","        \u263A\u2593  ","        \u263A\u2592  ","        \u263A\u2592  ","        \u263A\u2591  ","        \u263A\u2591  ","        \u263A   ","        \u263A  &","        \u263A \u263C&","       \u263A \u263C &","       \u263A\u263C  &","      \u263A\u263C  & ","      \u203C   & ","     \u263A   &  ","    \u203C    &  ","   \u263A    &   ","  \u203C     &   "," \u263A     &    ","\u203C      &    ","      &     ","      &     ","     &   \u2591  ","     &   \u2592  ","    &    \u2593  ","    &    \xA3  ","   &    \u2591\xA3  ","   &    \u2592\xA3  ","  &     \u2593\xA3  ","  &     \xA3\xA3  "," &     \u2591\xA3\xA3  "," &     \u2592\xA3\xA3  ","&      \u2593\xA3\xA3  ","&      \xA3\xA3\xA3  ","      \u2591\xA3\xA3\xA3  ","      \u2592\xA3\xA3\xA3  ","      \u2593\xA3\xA3\xA3  ","      \u2588\xA3\xA3\xA3  ","     \u2591\u2588\xA3\xA3\xA3  ","     \u2592\u2588\xA3\xA3\xA3  ","     \u2593\u2588\xA3\xA3\xA3  ","     \u2588\u2588\xA3\xA3\xA3  ","    \u2591\u2588\u2588\xA3\xA3\xA3  ","    \u2592\u2588\u2588\xA3\xA3\xA3  ","    \u2593\u2588\u2588\xA3\xA3\xA3  ","    \u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2591\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2592\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2593\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2591\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2592\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2593\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2591\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2592\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2593\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "]}}});var ap=C((Qz,lE)=>{"use strict";var vu=Object.assign({},aE()),uE=Object.keys(vu);Object.defineProperty(vu,"random",{get(){let e=Math.floor(Math.random()*uE.length),t=uE[e];return vu[t]}});lE.exports=vu});var CE=C((bW,FE)=>{FE.exports=()=>/[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26D3\uFE0F?(?:\u200D\uD83D\uDCA5)?|\u26F9(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF43\uDF45-\uDF4A\uDF4C-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDF44(?:\u200D\uD83D\uDFEB)?|\uDF4B(?:\u200D\uD83D\uDFE9)?|\uDFC3(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4\uDEB5](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE41\uDE43\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC08(?:\u200D\u2B1B)?|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC26(?:\u200D(?:\u2B1B|\uD83D\uDD25))?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?|\uDE42(?:\u200D[\u2194\u2195]\uFE0F?)?|\uDEB6(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE89\uDE8F-\uDEC2\uDEC6\uDECE-\uDEDC\uDEDF-\uDEE9]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDCE(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1|\uDDD1\u200D\uD83E\uDDD2(?:\u200D\uD83E\uDDD2)?|\uDDD2(?:\u200D\uD83E\uDDD2)?))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g});var vp=C((zW,ME)=>{"use strict";var kE=require("fs"),Ep;function i3(){try{return kE.statSync("/.dockerenv"),!0}catch{return!1}}function s3(){try{return kE.readFileSync("/proc/self/cgroup","utf8").includes("docker")}catch{return!1}}ME.exports=()=>(Ep===void 0&&(Ep=i3()||s3()),Ep)});var LE=C((WW,Fp)=>{"use strict";var a3=require("os"),u3=require("fs"),NE=vp(),jE=()=>{if(process.platform!=="linux")return!1;if(a3.release().toLowerCase().includes("microsoft"))return!NE();try{return u3.readFileSync("/proc/version","utf8").toLowerCase().includes("microsoft")?!NE():!1}catch{return!1}};process.env.__IS_WSL_TEST__?Fp.exports=jE:Fp.exports=jE()});var UE=C((VW,qE)=>{"use strict";qE.exports=(e,t,r)=>{let n=o=>Object.defineProperty(e,t,{value:o,enumerable:!0,writable:!0});return Object.defineProperty(e,t,{configurable:!0,enumerable:!0,get(){let o=r();return n(o),o},set(o){n(o)}}),e}});var KE=C((GW,YE)=>{var l3=require("path"),c3=require("child_process"),{promises:$u,constants:HE}=require("fs"),Au=LE(),f3=vp(),Tp=UE(),zE=l3.join(__dirname,"xdg-open"),{platform:Xn,arch:WE}=process,d3=()=>{try{return $u.statSync("/run/.containerenv"),!0}catch{return!1}},Cp;function p3(){return Cp===void 0&&(Cp=d3()||f3()),Cp}var m3=(()=>{let e="/mnt/",t;return async function(){if(t)return t;let r="/etc/wsl.conf",n=!1;try{await $u.access(r,HE.F_OK),n=!0}catch{}if(!n)return e;let o=await $u.readFile(r,{encoding:"utf8"}),i=/(?<!#.*)root\s*=\s*(?<mountPoint>.*)/g.exec(o);return i?(t=i.groups.mountPoint.trim(),t=t.endsWith("/")?t:`${t}/`,t):e}})(),VE=async(e,t)=>{let r;for(let n of e)try{return await t(n)}catch(o){r=o}throw r},xu=async e=>{if(e={wait:!1,background:!1,newInstance:!1,allowNonzeroExitCode:!1,...e},Array.isArray(e.app))return VE(e.app,l=>xu({...e,app:l}));let{name:t,arguments:r=[]}=e.app||{};if(r=[...r],Array.isArray(t))return VE(t,l=>xu({...e,app:{name:l,arguments:r}}));let n,o=[],i={};if(Xn==="darwin")n="open",e.wait&&o.push("--wait-apps"),e.background&&o.push("--background"),e.newInstance&&o.push("--new"),t&&o.push("-a",t);else if(Xn==="win32"||Au&&!p3()&&!t){let l=await m3();n=Au?`${l}c/Windows/System32/WindowsPowerShell/v1.0/powershell.exe`:`${process.env.SYSTEMROOT}\\System32\\WindowsPowerShell\\v1.0\\powershell`,o.push("-NoProfile","-NonInteractive","\u2013ExecutionPolicy","Bypass","-EncodedCommand"),Au||(i.windowsVerbatimArguments=!0);let c=["Start"];e.wait&&c.push("-Wait"),t?(c.push(`"\`"${t}\`""`,"-ArgumentList"),e.target&&r.unshift(e.target)):e.target&&c.push(`"${e.target}"`),r.length>0&&(r=r.map(d=>`"\`"${d}\`""`),c.push(r.join(","))),e.target=Buffer.from(c.join(" "),"utf16le").toString("base64")}else{if(t)n=t;else{let l=!__dirname||__dirname==="/",c=!1;try{await $u.access(zE,HE.X_OK),c=!0}catch{}n=process.versions.electron||Xn==="android"||l||!c?"xdg-open":zE}r.length>0&&o.push(...r),e.wait||(i.stdio="ignore",i.detached=!0)}e.target&&o.push(e.target),Xn==="darwin"&&r.length>0&&o.push("--args",...r);let a=c3.spawn(n,o,i);return e.wait?new Promise((l,c)=>{a.once("error",c),a.once("close",d=>{if(!e.allowNonzeroExitCode&&d>0){c(new Error(`Exited with code ${d}`));return}l(a)})}):(a.unref(),a)},Rp=(e,t)=>{if(typeof e!="string")throw new TypeError("Expected a `target`");return xu({...t,target:e})},h3=(e,t)=>{if(typeof e!="string")throw new TypeError("Expected a `name`");let{arguments:r=[]}=t||{};if(r!=null&&!Array.isArray(r))throw new TypeError("Expected `appArguments` as Array type");return xu({...t,app:{name:e,arguments:r}})};function GE(e){if(typeof e=="string"||Array.isArray(e))return e;let{[WE]:t}=e;if(!t)throw new Error(`${WE} is not supported`);return t}function Ap({[Xn]:e},{wsl:t}){if(t&&Au)return GE(t);if(!e)throw new Error(`${Xn} is not supported`);return GE(e)}var Pu={};Tp(Pu,"chrome",()=>Ap({darwin:"google chrome",win32:"chrome",linux:["google-chrome","google-chrome-stable","chromium"]},{wsl:{ia32:"/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe",x64:["/mnt/c/Program Files/Google/Chrome/Application/chrome.exe","/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe"]}}));Tp(Pu,"firefox",()=>Ap({darwin:"firefox",win32:"C:\\Program Files\\Mozilla Firefox\\firefox.exe",linux:"firefox"},{wsl:"/mnt/c/Program Files/Mozilla Firefox/firefox.exe"}));Tp(Pu,"edge",()=>Ap({darwin:"microsoft edge",win32:"msedge",linux:["microsoft-edge","microsoft-edge-dev"]},{wsl:"/mnt/c/Program Files (x86)/Microsoft/Edge/Application/msedge.exe"}));Rp.apps=Pu;Rp.openApp=h3;YE.exports=Rp});var F3={};$F(F3,{default:()=>Iu,lint:()=>tv});module.exports=xF(F3);var Bu=require("@oclif/core");function ge(e){if(typeof e!="object"||e===null)return!1;let t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}var Vm=require("node:url"),sn=(e,t)=>{let r=pl(PF(e));if(typeof r!="string")throw new TypeError(`${t} must be a string or a file URL: ${r}.`);return r},PF=e=>dl(e)?e.toString():e,dl=e=>typeof e!="string"&&e&&Object.getPrototypeOf(e)===String.prototype,pl=e=>e instanceof URL?(0,Vm.fileURLToPath)(e):e;var ls=(e,t=[],r={})=>{let n=sn(e,"First argument"),[o,i]=ge(t)?[[],t]:[t,r];if(!Array.isArray(o))throw new TypeError(`Second argument must be either an array of arguments or an options object: ${o}`);if(o.some(c=>typeof c=="object"&&c!==null))throw new TypeError(`Second argument must be an array of strings: ${o}`);let a=o.map(String),l=a.find(c=>c.includes("\0"));if(l!==void 0)throw new TypeError(`Arguments cannot contain null bytes ("\\0"): ${l}`);if(!ge(i))throw new TypeError(`Last argument must be an options object: ${i}`);return[n,a,i]};var eh=require("node:child_process");var Gm=require("node:string_decoder"),{toString:Hm}=Object.prototype,Ym=e=>Hm.call(e)==="[object ArrayBuffer]",we=e=>Hm.call(e)==="[object Uint8Array]",kt=e=>new Uint8Array(e.buffer,e.byteOffset,e.byteLength),OF=new TextEncoder,Km=e=>OF.encode(e),BF=new TextDecoder,cs=e=>BF.decode(e),Jm=(e,t)=>IF(e,t).join(""),IF=(e,t)=>{if(t==="utf8"&&e.every(i=>typeof i=="string"))return e;let r=new Gm.StringDecoder(t),n=e.map(i=>typeof i=="string"?Km(i):i).map(i=>r.write(i)),o=r.end();return o===""?n:[...n,o]},yo=e=>e.length===1&&we(e[0])?e[0]:ml(kF(e)),kF=e=>e.map(t=>typeof t=="string"?Km(t):t),ml=e=>{let t=new Uint8Array(MF(e)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t},MF=e=>{let t=0;for(let r of e)t+=r.length;return t};var th=e=>Array.isArray(e)&&Array.isArray(e.raw),rh=(e,t)=>{let r=[];for(let[i,a]of e.entries())r=NF({templates:e,expressions:t,tokens:r,index:i,template:a});if(r.length===0)throw new TypeError("Template script must not be empty");let[n,...o]=r;return[n,o,{}]},NF=({templates:e,expressions:t,tokens:r,index:n,template:o})=>{if(o===void 0)throw new TypeError(`Invalid backslash sequence: ${e.raw[n]}`);let{nextTokens:i,leadingWhitespaces:a,trailingWhitespaces:l}=jF(o,e.raw[n]),c=Xm(r,i,a);if(n===t.length)return c;let d=t[n],p=Array.isArray(d)?d.map(m=>Zm(m)):[Zm(d)];return Xm(c,p,l)},jF=(e,t)=>{if(t.length===0)return{nextTokens:[],leadingWhitespaces:!1,trailingWhitespaces:!1};let r=[],n=0,o=Qm.has(t[0]);for(let a=0,l=0;a<e.length;a+=1,l+=1){let c=t[l];if(Qm.has(c))n!==a&&r.push(e.slice(n,a)),n=a+1;else if(c==="\\"){let d=t[l+1];d===`
`?(a-=1,l+=1):d==="u"&&t[l+2]==="{"?l=t.indexOf("}",l+3):l+=LF[d]??1}}let i=n===e.length;return i||r.push(e.slice(n)),{nextTokens:r,leadingWhitespaces:o,trailingWhitespaces:i}},Qm=new Set([" ","	","\r",`
`]),LF={x:3,u:5},Xm=(e,t,r)=>r||e.length===0||t.length===0?[...e,...t]:[...e.slice(0,-1),`${e.at(-1)}${t[0]}`,...t.slice(1)],Zm=e=>{let t=typeof e;if(t==="string")return e;if(t==="number")return String(e);if(ge(e)&&("stdout"in e||"isMaxBuffer"in e))return qF(e);throw e instanceof eh.ChildProcess||Object.prototype.toString.call(e)==="[object Promise]"?new TypeError("Unexpected subprocess in template expression. Please use ${await subprocess} instead of ${subprocess}."):new TypeError(`Unexpected "${t}" in template expression`)},qF=({stdout:e})=>{if(typeof e=="string")return e;if(we(e))return cs(e);throw e===void 0?new TypeError(`Missing result.stdout in template expression. This is probably due to the previous subprocess' "stdout" option.`):new TypeError(`Unexpected "${typeof e}" stdout in template expression`)};var dD=require("node:child_process");var oh=require("node:util");var fs=N(require("node:process"),1),nt=e=>ds.includes(e),ds=[fs.default.stdin,fs.default.stdout,fs.default.stderr],Ue=["stdin","stdout","stderr"],ps=e=>Ue[e]??`stdio[${e}]`;var ih=e=>{let t={...e};for(let r of yl)t[r]=hl(e,r);return t},hl=(e,t)=>{let r=Array.from({length:UF(e)+1}),n=zF(e[t],r,t);return YF(n,t)},UF=({stdio:e})=>Array.isArray(e)?Math.max(e.length,Ue.length):Ue.length,zF=(e,t,r)=>ge(e)?WF(e,t,r):t.fill(e),WF=(e,t,r)=>{for(let n of Object.keys(e).sort(VF))for(let o of GF(n,r,t))t[o]=e[n];return t},VF=(e,t)=>nh(e)<nh(t)?1:-1,nh=e=>e==="stdout"||e==="stderr"?0:e==="all"?2:1,GF=(e,t,r)=>{if(e==="ipc")return[r.length-1];let n=gl(e);if(n===void 0||n===0)throw new TypeError(`"${t}.${e}" is invalid.
It must be "${t}.stdout", "${t}.stderr", "${t}.all", "${t}.ipc", or "${t}.fd3", "${t}.fd4" (and so on).`);if(n>=r.length)throw new TypeError(`"${t}.${e}" is invalid: that file descriptor does not exist.
Please set the "stdio" option to ensure that file descriptor exists.`);return n==="all"?[1,2]:[n]},gl=e=>{if(e==="all")return e;if(Ue.includes(e))return Ue.indexOf(e);let t=HF.exec(e);if(t!==null)return Number(t[1])},HF=/^fd(\d+)$/,YF=(e,t)=>e.map(r=>r===void 0?JF[t]:r),KF=(0,oh.debuglog)("execa").enabled?"full":"none",JF={lines:!1,buffer:!0,maxBuffer:1e3*1e3*100,verbose:KF,stripFinalNewline:!0},yl=["lines","buffer","maxBuffer","verbose","stripFinalNewline"],Mt=(e,t)=>t==="ipc"?e.at(-1):e[t];var an=({verbose:e},t)=>Dl(e,t)!=="none",un=({verbose:e},t)=>!["none","short"].includes(Dl(e,t)),sh=({verbose:e},t)=>{let r=Dl(e,t);return ms(r)?r:void 0},Dl=(e,t)=>t===void 0?QF(e):Mt(e,t),QF=e=>e.find(t=>ms(t))??hs.findLast(t=>e.includes(t)),ms=e=>typeof e=="function",hs=["none","short","full"];var wh=require("node:util");var ah=require("node:process"),uh=require("node:util"),lh=(e,t)=>{let r=[e,...t],n=r.join(" "),o=r.map(i=>nC(ch(i))).join(" ");return{command:n,escapedCommand:o}},Do=e=>(0,uh.stripVTControlCharacters)(e).split(`
`).map(t=>ch(t)).join(`
`),ch=e=>e.replaceAll(eC,t=>XF(t)),XF=e=>{let t=tC[e];if(t!==void 0)return t;let r=e.codePointAt(0),n=r.toString(16);return r<=rC?`\\u${n.padStart(4,"0")}`:`\\U${n}`},ZF=()=>{try{return new RegExp("\\p{Separator}|\\p{Other}","gu")}catch{return/[\s\u0000-\u001F\u007F-\u009F\u00AD]/g}},eC=ZF(),tC={" ":" ","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t"},rC=65535,nC=e=>oC.test(e)?e:ah.platform==="win32"?`"${e.replaceAll('"','""')}"`:`'${e.replaceAll("'","'\\''")}'`,oC=/^[\w./-]+$/;var bl=N(require("node:process"),1);function bo(){let{env:e}=bl.default,{TERM:t,TERM_PROGRAM:r}=e;return bl.default.platform!=="win32"?t!=="linux":!!e.WT_SESSION||!!e.TERMINUS_SUBLIME||e.ConEmuTask==="{cmd::Cmder}"||r==="Terminus-Sublime"||r==="vscode"||t==="xterm-256color"||t==="alacritty"||t==="rxvt-unicode"||t==="rxvt-unicode-256color"||e.TERMINAL_EMULATOR==="JetBrains-JediTerm"}var fh={circleQuestionMark:"(?)",questionMarkPrefix:"(?)",square:"\u2588",squareDarkShade:"\u2593",squareMediumShade:"\u2592",squareLightShade:"\u2591",squareTop:"\u2580",squareBottom:"\u2584",squareLeft:"\u258C",squareRight:"\u2590",squareCenter:"\u25A0",bullet:"\u25CF",dot:"\u2024",ellipsis:"\u2026",pointerSmall:"\u203A",triangleUp:"\u25B2",triangleUpSmall:"\u25B4",triangleDown:"\u25BC",triangleDownSmall:"\u25BE",triangleLeftSmall:"\u25C2",triangleRightSmall:"\u25B8",home:"\u2302",heart:"\u2665",musicNote:"\u266A",musicNoteBeamed:"\u266B",arrowUp:"\u2191",arrowDown:"\u2193",arrowLeft:"\u2190",arrowRight:"\u2192",arrowLeftRight:"\u2194",arrowUpDown:"\u2195",almostEqual:"\u2248",notEqual:"\u2260",lessOrEqual:"\u2264",greaterOrEqual:"\u2265",identical:"\u2261",infinity:"\u221E",subscriptZero:"\u2080",subscriptOne:"\u2081",subscriptTwo:"\u2082",subscriptThree:"\u2083",subscriptFour:"\u2084",subscriptFive:"\u2085",subscriptSix:"\u2086",subscriptSeven:"\u2087",subscriptEight:"\u2088",subscriptNine:"\u2089",oneHalf:"\xBD",oneThird:"\u2153",oneQuarter:"\xBC",oneFifth:"\u2155",oneSixth:"\u2159",oneEighth:"\u215B",twoThirds:"\u2154",twoFifths:"\u2156",threeQuarters:"\xBE",threeFifths:"\u2157",threeEighths:"\u215C",fourFifths:"\u2158",fiveSixths:"\u215A",fiveEighths:"\u215D",sevenEighths:"\u215E",line:"\u2500",lineBold:"\u2501",lineDouble:"\u2550",lineDashed0:"\u2504",lineDashed1:"\u2505",lineDashed2:"\u2508",lineDashed3:"\u2509",lineDashed4:"\u254C",lineDashed5:"\u254D",lineDashed6:"\u2574",lineDashed7:"\u2576",lineDashed8:"\u2578",lineDashed9:"\u257A",lineDashed10:"\u257C",lineDashed11:"\u257E",lineDashed12:"\u2212",lineDashed13:"\u2013",lineDashed14:"\u2010",lineDashed15:"\u2043",lineVertical:"\u2502",lineVerticalBold:"\u2503",lineVerticalDouble:"\u2551",lineVerticalDashed0:"\u2506",lineVerticalDashed1:"\u2507",lineVerticalDashed2:"\u250A",lineVerticalDashed3:"\u250B",lineVerticalDashed4:"\u254E",lineVerticalDashed5:"\u254F",lineVerticalDashed6:"\u2575",lineVerticalDashed7:"\u2577",lineVerticalDashed8:"\u2579",lineVerticalDashed9:"\u257B",lineVerticalDashed10:"\u257D",lineVerticalDashed11:"\u257F",lineDownLeft:"\u2510",lineDownLeftArc:"\u256E",lineDownBoldLeftBold:"\u2513",lineDownBoldLeft:"\u2512",lineDownLeftBold:"\u2511",lineDownDoubleLeftDouble:"\u2557",lineDownDoubleLeft:"\u2556",lineDownLeftDouble:"\u2555",lineDownRight:"\u250C",lineDownRightArc:"\u256D",lineDownBoldRightBold:"\u250F",lineDownBoldRight:"\u250E",lineDownRightBold:"\u250D",lineDownDoubleRightDouble:"\u2554",lineDownDoubleRight:"\u2553",lineDownRightDouble:"\u2552",lineUpLeft:"\u2518",lineUpLeftArc:"\u256F",lineUpBoldLeftBold:"\u251B",lineUpBoldLeft:"\u251A",lineUpLeftBold:"\u2519",lineUpDoubleLeftDouble:"\u255D",lineUpDoubleLeft:"\u255C",lineUpLeftDouble:"\u255B",lineUpRight:"\u2514",lineUpRightArc:"\u2570",lineUpBoldRightBold:"\u2517",lineUpBoldRight:"\u2516",lineUpRightBold:"\u2515",lineUpDoubleRightDouble:"\u255A",lineUpDoubleRight:"\u2559",lineUpRightDouble:"\u2558",lineUpDownLeft:"\u2524",lineUpBoldDownBoldLeftBold:"\u252B",lineUpBoldDownBoldLeft:"\u2528",lineUpDownLeftBold:"\u2525",lineUpBoldDownLeftBold:"\u2529",lineUpDownBoldLeftBold:"\u252A",lineUpDownBoldLeft:"\u2527",lineUpBoldDownLeft:"\u2526",lineUpDoubleDownDoubleLeftDouble:"\u2563",lineUpDoubleDownDoubleLeft:"\u2562",lineUpDownLeftDouble:"\u2561",lineUpDownRight:"\u251C",lineUpBoldDownBoldRightBold:"\u2523",lineUpBoldDownBoldRight:"\u2520",lineUpDownRightBold:"\u251D",lineUpBoldDownRightBold:"\u2521",lineUpDownBoldRightBold:"\u2522",lineUpDownBoldRight:"\u251F",lineUpBoldDownRight:"\u251E",lineUpDoubleDownDoubleRightDouble:"\u2560",lineUpDoubleDownDoubleRight:"\u255F",lineUpDownRightDouble:"\u255E",lineDownLeftRight:"\u252C",lineDownBoldLeftBoldRightBold:"\u2533",lineDownLeftBoldRightBold:"\u252F",lineDownBoldLeftRight:"\u2530",lineDownBoldLeftBoldRight:"\u2531",lineDownBoldLeftRightBold:"\u2532",lineDownLeftRightBold:"\u252E",lineDownLeftBoldRight:"\u252D",lineDownDoubleLeftDoubleRightDouble:"\u2566",lineDownDoubleLeftRight:"\u2565",lineDownLeftDoubleRightDouble:"\u2564",lineUpLeftRight:"\u2534",lineUpBoldLeftBoldRightBold:"\u253B",lineUpLeftBoldRightBold:"\u2537",lineUpBoldLeftRight:"\u2538",lineUpBoldLeftBoldRight:"\u2539",lineUpBoldLeftRightBold:"\u253A",lineUpLeftRightBold:"\u2536",lineUpLeftBoldRight:"\u2535",lineUpDoubleLeftDoubleRightDouble:"\u2569",lineUpDoubleLeftRight:"\u2568",lineUpLeftDoubleRightDouble:"\u2567",lineUpDownLeftRight:"\u253C",lineUpBoldDownBoldLeftBoldRightBold:"\u254B",lineUpDownBoldLeftBoldRightBold:"\u2548",lineUpBoldDownLeftBoldRightBold:"\u2547",lineUpBoldDownBoldLeftRightBold:"\u254A",lineUpBoldDownBoldLeftBoldRight:"\u2549",lineUpBoldDownLeftRight:"\u2540",lineUpDownBoldLeftRight:"\u2541",lineUpDownLeftBoldRight:"\u253D",lineUpDownLeftRightBold:"\u253E",lineUpBoldDownBoldLeftRight:"\u2542",lineUpDownLeftBoldRightBold:"\u253F",lineUpBoldDownLeftBoldRight:"\u2543",lineUpBoldDownLeftRightBold:"\u2544",lineUpDownBoldLeftBoldRight:"\u2545",lineUpDownBoldLeftRightBold:"\u2546",lineUpDoubleDownDoubleLeftDoubleRightDouble:"\u256C",lineUpDoubleDownDoubleLeftRight:"\u256B",lineUpDownLeftDoubleRightDouble:"\u256A",lineCross:"\u2573",lineBackslash:"\u2572",lineSlash:"\u2571"},dh={tick:"\u2714",info:"\u2139",warning:"\u26A0",cross:"\u2718",squareSmall:"\u25FB",squareSmallFilled:"\u25FC",circle:"\u25EF",circleFilled:"\u25C9",circleDotted:"\u25CC",circleDouble:"\u25CE",circleCircle:"\u24DE",circleCross:"\u24E7",circlePipe:"\u24BE",radioOn:"\u25C9",radioOff:"\u25EF",checkboxOn:"\u2612",checkboxOff:"\u2610",checkboxCircleOn:"\u24E7",checkboxCircleOff:"\u24BE",pointer:"\u276F",triangleUpOutline:"\u25B3",triangleLeft:"\u25C0",triangleRight:"\u25B6",lozenge:"\u25C6",lozengeOutline:"\u25C7",hamburger:"\u2630",smiley:"\u32E1",mustache:"\u0DF4",star:"\u2605",play:"\u25B6",nodejs:"\u2B22",oneSeventh:"\u2150",oneNinth:"\u2151",oneTenth:"\u2152"},iC={tick:"\u221A",info:"i",warning:"\u203C",cross:"\xD7",squareSmall:"\u25A1",squareSmallFilled:"\u25A0",circle:"( )",circleFilled:"(*)",circleDotted:"( )",circleDouble:"( )",circleCircle:"(\u25CB)",circleCross:"(\xD7)",circlePipe:"(\u2502)",radioOn:"(*)",radioOff:"( )",checkboxOn:"[\xD7]",checkboxOff:"[ ]",checkboxCircleOn:"(\xD7)",checkboxCircleOff:"( )",pointer:">",triangleUpOutline:"\u2206",triangleLeft:"\u25C4",triangleRight:"\u25BA",lozenge:"\u2666",lozengeOutline:"\u25CA",hamburger:"\u2261",smiley:"\u263A",mustache:"\u250C\u2500\u2510",star:"\u2736",play:"\u25BA",nodejs:"\u2666",oneSeventh:"1/7",oneNinth:"1/9",oneTenth:"1/10"},sC={...fh,...dh},aC={...fh,...iC},uC=bo(),lC=uC?sC:aC,gs=lC,G3=Object.entries(dh);var ph=N(require("node:tty"),1),cC=ph.default?.WriteStream?.prototype?.hasColors?.()??!1,L=(e,t)=>{if(!cC)return o=>o;let r=`\x1B[${e}m`,n=`\x1B[${t}m`;return o=>{let i=o+"",a=i.indexOf(n);if(a===-1)return r+i+n;let l=r,c=0;for(;a!==-1;)l+=i.slice(c,a)+r,c=a+n.length,a=i.indexOf(n,c);return l+=i.slice(c)+n,l}},Y3=L(0,0),mh=L(1,22),K3=L(2,22),J3=L(3,23),Q3=L(4,24),X3=L(53,55),Z3=L(7,27),eM=L(8,28),tM=L(9,29),rM=L(30,39),nM=L(31,39),oM=L(32,39),iM=L(33,39),sM=L(34,39),aM=L(35,39),uM=L(36,39),lM=L(37,39),ys=L(90,39),cM=L(40,49),fM=L(41,49),dM=L(42,49),pM=L(43,49),mM=L(44,49),hM=L(45,49),gM=L(46,49),yM=L(47,49),DM=L(100,49),hh=L(91,39),bM=L(92,39),gh=L(93,39),_M=L(94,39),wM=L(95,39),SM=L(96,39),EM=L(97,39),vM=L(101,49),FM=L(102,49),CM=L(103,49),TM=L(104,49),RM=L(105,49),AM=L(106,49),$M=L(107,49);var bh=({type:e,message:t,timestamp:r,piped:n,commandId:o,result:{failed:i=!1}={},options:{reject:a=!0}})=>{let l=fC(r),c=dC[e]({failed:i,reject:a,piped:n}),d=pC[e]({reject:a});return`${ys(`[${l}]`)} ${ys(`[${o}]`)} ${d(c)} ${d(t)}`},fC=e=>`${Ds(e.getHours(),2)}:${Ds(e.getMinutes(),2)}:${Ds(e.getSeconds(),2)}.${Ds(e.getMilliseconds(),3)}`,Ds=(e,t)=>String(e).padStart(t,"0"),yh=({failed:e,reject:t})=>e?t?gs.cross:gs.warning:gs.tick,dC={command:({piped:e})=>e?"|":"$",output:()=>" ",ipc:()=>"*",error:yh,duration:yh},Dh=e=>e,pC={command:()=>mh,output:()=>Dh,ipc:()=>Dh,error:({reject:e})=>e?hh:gh,duration:()=>ys};var _h=(e,t,r)=>{let n=sh(t,r);return e.map(({verboseLine:o,verboseObject:i})=>mC(o,i,n)).filter(o=>o!==void 0).map(o=>hC(o)).join("")},mC=(e,t,r)=>{if(r===void 0)return e;let n=r(e,t);if(typeof n=="string")return n},hC=e=>e.endsWith(`
`)?e:`${e}
`;var bt=({type:e,verboseMessage:t,fdNumber:r,verboseInfo:n,result:o})=>{let i=gC({type:e,result:o,verboseInfo:n}),a=yC(t,i),l=_h(a,n,r);l!==""&&console.warn(l.slice(0,-1))},gC=({type:e,result:t,verboseInfo:{escapedCommand:r,commandId:n,rawOptions:{piped:o=!1,...i}}})=>({type:e,escapedCommand:r,commandId:`${n}`,timestamp:new Date,piped:o,result:t,options:i}),yC=(e,t)=>e.split(`
`).map(r=>DC({...t,message:r})),DC=e=>({verboseLine:bh(e),verboseObject:e}),bs=e=>{let t=typeof e=="string"?e:(0,wh.inspect)(e);return Do(t).replaceAll("	"," ".repeat(bC))},bC=2;var Sh=(e,t)=>{an(t)&&bt({type:"command",verboseMessage:e,verboseInfo:t})};var Eh=(e,t,r)=>{SC(e);let n=_C(e);return{verbose:e,escapedCommand:t,commandId:n,rawOptions:r}},_C=e=>an({verbose:e})?wC++:void 0,wC=0n,SC=e=>{for(let t of e){if(t===!1)throw new TypeError(`The "verbose: false" option was renamed to "verbose: 'none'".`);if(t===!0)throw new TypeError(`The "verbose: true" option was renamed to "verbose: 'short'".`);if(!hs.includes(t)&&!ms(t)){let r=hs.map(n=>`'${n}'`).join(", ");throw new TypeError(`The "verbose" option must not be ${t}. Allowed values are: ${r} or a function.`)}}};var _l=require("node:process"),_s=()=>_l.hrtime.bigint(),wl=e=>Number(_l.hrtime.bigint()-e)/1e6;var ws=(e,t,r)=>{let n=_s(),{command:o,escapedCommand:i}=lh(e,t),a=hl(r,"verbose"),l=Eh(a,i,{...r});return Sh(i,l),{command:o,escapedCommand:i,startTime:n,verboseInfo:l}};var My=N(require("node:path"),1),Vl=N(require("node:process"),1),Ny=N(fg(),1);var _o=N(require("node:process"),1),ir=N(require("node:path"),1);function Es(e={}){let{env:t=process.env,platform:r=process.platform}=e;return r!=="win32"?"PATH":Object.keys(t).reverse().find(n=>n.toUpperCase()==="PATH")||"Path"}var dg=require("node:util"),Pl=require("node:child_process"),xl=N(require("node:path"),1),pg=require("node:url"),mN=(0,dg.promisify)(Pl.execFile);function vs(e){return e instanceof URL?(0,pg.fileURLToPath)(e):e}function mg(e){return{*[Symbol.iterator](){let t=xl.default.resolve(vs(e)),r;for(;r!==t;)yield t,r=t,t=xl.default.resolve(t,"..")}}}var hN=10*1024*1024;var KC=({cwd:e=_o.default.cwd(),path:t=_o.default.env[Es()],preferLocal:r=!0,execPath:n=_o.default.execPath,addExecPath:o=!0}={})=>{let i=ir.default.resolve(vs(e)),a=[],l=t.split(ir.default.delimiter);return r&&JC(a,l,i),o&&QC(a,l,n,i),t===""||t===ir.default.delimiter?`${a.join(ir.default.delimiter)}${t}`:[...a,t].join(ir.default.delimiter)},JC=(e,t,r)=>{for(let n of mg(r)){let o=ir.default.join(n,"node_modules/.bin");t.includes(o)||e.push(o)}},QC=(e,t,r,n)=>{let o=ir.default.resolve(n,vs(r),"..");t.includes(o)||e.push(o)},hg=({env:e=_o.default.env,...t}={})=>{e={...e};let r=Es({env:e});return t.path=e[r],e[r]=KC(t),e};var xg=require("node:timers/promises");var gg=(e,t,r)=>{let n=r?So:wo,o=e instanceof ot?{}:{cause:e};return new n(t,o)},ot=class extends Error{},yg=(e,t)=>{Object.defineProperty(e.prototype,"name",{value:t,writable:!0,enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,bg,{value:!0,writable:!1,enumerable:!1,configurable:!1})},Dg=e=>Fs(e)&&bg in e,bg=Symbol("isExecaError"),Fs=e=>Object.prototype.toString.call(e)==="[object Error]",wo=class extends Error{};yg(wo,wo.name);var So=class extends Error{};yg(So,So.name);var fn=require("node:os");var Fg=require("node:os");var _g=()=>{let e=Sg-wg+1;return Array.from({length:e},XC)},XC=(e,t)=>({name:`SIGRT${t+1}`,number:wg+t,action:"terminate",description:"Application-specific signal (realtime)",standard:"posix"}),wg=34,Sg=64;var vg=require("node:os");var Eg=[{name:"SIGHUP",number:1,action:"terminate",description:"Terminal closed",standard:"posix"},{name:"SIGINT",number:2,action:"terminate",description:"User interruption with CTRL-C",standard:"ansi"},{name:"SIGQUIT",number:3,action:"core",description:"User interruption with CTRL-\\",standard:"posix"},{name:"SIGILL",number:4,action:"core",description:"Invalid machine instruction",standard:"ansi"},{name:"SIGTRAP",number:5,action:"core",description:"Debugger breakpoint",standard:"posix"},{name:"SIGABRT",number:6,action:"core",description:"Aborted",standard:"ansi"},{name:"SIGIOT",number:6,action:"core",description:"Aborted",standard:"bsd"},{name:"SIGBUS",number:7,action:"core",description:"Bus error due to misaligned, non-existing address or paging error",standard:"bsd"},{name:"SIGEMT",number:7,action:"terminate",description:"Command should be emulated but is not implemented",standard:"other"},{name:"SIGFPE",number:8,action:"core",description:"Floating point arithmetic error",standard:"ansi"},{name:"SIGKILL",number:9,action:"terminate",description:"Forced termination",standard:"posix",forced:!0},{name:"SIGUSR1",number:10,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGSEGV",number:11,action:"core",description:"Segmentation fault",standard:"ansi"},{name:"SIGUSR2",number:12,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGPIPE",number:13,action:"terminate",description:"Broken pipe or socket",standard:"posix"},{name:"SIGALRM",number:14,action:"terminate",description:"Timeout or timer",standard:"posix"},{name:"SIGTERM",number:15,action:"terminate",description:"Termination",standard:"ansi"},{name:"SIGSTKFLT",number:16,action:"terminate",description:"Stack is empty or overflowed",standard:"other"},{name:"SIGCHLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"posix"},{name:"SIGCLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"other"},{name:"SIGCONT",number:18,action:"unpause",description:"Unpaused",standard:"posix",forced:!0},{name:"SIGSTOP",number:19,action:"pause",description:"Paused",standard:"posix",forced:!0},{name:"SIGTSTP",number:20,action:"pause",description:'Paused using CTRL-Z or "suspend"',standard:"posix"},{name:"SIGTTIN",number:21,action:"pause",description:"Background process cannot read terminal input",standard:"posix"},{name:"SIGBREAK",number:21,action:"terminate",description:"User interruption with CTRL-BREAK",standard:"other"},{name:"SIGTTOU",number:22,action:"pause",description:"Background process cannot write to terminal output",standard:"posix"},{name:"SIGURG",number:23,action:"ignore",description:"Socket received out-of-band data",standard:"bsd"},{name:"SIGXCPU",number:24,action:"core",description:"Process timed out",standard:"bsd"},{name:"SIGXFSZ",number:25,action:"core",description:"File too big",standard:"bsd"},{name:"SIGVTALRM",number:26,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGPROF",number:27,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGWINCH",number:28,action:"ignore",description:"Terminal window size changed",standard:"bsd"},{name:"SIGIO",number:29,action:"terminate",description:"I/O is available",standard:"other"},{name:"SIGPOLL",number:29,action:"terminate",description:"Watched event",standard:"other"},{name:"SIGINFO",number:29,action:"ignore",description:"Request for process information",standard:"other"},{name:"SIGPWR",number:30,action:"terminate",description:"Device running out of power",standard:"systemv"},{name:"SIGSYS",number:31,action:"core",description:"Invalid system call",standard:"other"},{name:"SIGUNUSED",number:31,action:"terminate",description:"Invalid system call",standard:"other"}];var Ol=()=>{let e=_g();return[...Eg,...e].map(ZC)},ZC=({name:e,number:t,description:r,action:n,forced:o=!1,standard:i})=>{let{signals:{[e]:a}}=vg.constants,l=a!==void 0;return{name:e,number:l?a:t,description:r,supported:l,action:n,forced:o,standard:i}};var eT=()=>{let e=Ol();return Object.fromEntries(e.map(tT))},tT=({name:e,number:t,description:r,supported:n,action:o,forced:i,standard:a})=>[e,{name:e,number:t,description:r,supported:n,action:o,forced:i,standard:a}],Cg=eT(),rT=()=>{let e=Ol(),t=65,r=Array.from({length:t},(n,o)=>nT(o,e));return Object.assign({},...r)},nT=(e,t)=>{let r=oT(e,t);if(r===void 0)return{};let{name:n,description:o,supported:i,action:a,forced:l,standard:c}=r;return{[e]:{name:n,number:e,description:o,supported:i,action:a,forced:l,standard:c}}},oT=(e,t)=>{let r=t.find(({name:n})=>Fg.constants.signals[n]===e);return r!==void 0?r:t.find(n=>n.number===e)},RN=rT();var Rg=e=>{let t="option `killSignal`";if(e===0)throw new TypeError(`Invalid ${t}: 0 cannot be used.`);return $g(e,t)},Ag=e=>e===0?e:$g(e,"`subprocess.kill()`'s argument"),$g=(e,t)=>{if(Number.isInteger(e))return iT(e,t);if(typeof e=="string")return aT(e,t);throw new TypeError(`Invalid ${t} ${String(e)}: it must be a string or an integer.
${Bl()}`)},iT=(e,t)=>{if(Tg.has(e))return Tg.get(e);throw new TypeError(`Invalid ${t} ${e}: this signal integer does not exist.
${Bl()}`)},sT=()=>new Map(Object.entries(fn.constants.signals).reverse().map(([e,t])=>[t,e])),Tg=sT(),aT=(e,t)=>{if(e in fn.constants.signals)return e;throw e.toUpperCase()in fn.constants.signals?new TypeError(`Invalid ${t} '${e}': please rename it to '${e.toUpperCase()}'.`):new TypeError(`Invalid ${t} '${e}': this signal name does not exist.
${Bl()}`)},Bl=()=>`Available signal names: ${uT()}.
Available signal numbers: ${lT()}.`,uT=()=>Object.keys(fn.constants.signals).sort().map(e=>`'${e}'`).join(", "),lT=()=>[...new Set(Object.values(fn.constants.signals).sort((e,t)=>e-t))].join(", "),Cs=e=>Cg[e].description;var Pg=e=>{if(e===!1)return e;if(e===!0)return cT;if(!Number.isFinite(e)||e<0)throw new TypeError(`Expected the \`forceKillAfterDelay\` option to be a non-negative integer, got \`${e}\` (${typeof e})`);return e},cT=1e3*5,Og=({kill:e,options:{forceKillAfterDelay:t,killSignal:r},onInternalError:n,context:o,controller:i},a,l)=>{let{signal:c,error:d}=fT(a,l,r);dT(d,n);let p=e(c);return pT({kill:e,signal:c,forceKillAfterDelay:t,killSignal:r,killResult:p,context:o,controller:i}),p},fT=(e,t,r)=>{let[n=r,o]=Fs(e)?[void 0,e]:[e,t];if(typeof n!="string"&&!Number.isInteger(n))throw new TypeError(`The first argument must be an error instance or a signal name string/integer: ${String(n)}`);if(o!==void 0&&!Fs(o))throw new TypeError(`The second argument is optional. If specified, it must be an error instance: ${o}`);return{signal:Ag(n),error:o}},dT=(e,t)=>{e!==void 0&&t.reject(e)},pT=async({kill:e,signal:t,forceKillAfterDelay:r,killSignal:n,killResult:o,context:i,controller:a})=>{t===n&&o&&Il({kill:e,forceKillAfterDelay:r,context:i,controllerSignal:a.signal})},Il=async({kill:e,forceKillAfterDelay:t,context:r,controllerSignal:n})=>{if(t!==!1)try{await(0,xg.setTimeout)(t,void 0,{signal:n}),e("SIGKILL")&&(r.isForcefullyTerminated??=!0)}catch{}};var Bg=require("node:events"),Ts=async(e,t)=>{e.aborted||await(0,Bg.once)(e,"abort",{signal:t})};var Ig=({cancelSignal:e})=>{if(e!==void 0&&Object.prototype.toString.call(e)!=="[object AbortSignal]")throw new Error(`The \`cancelSignal\` option must be an AbortSignal: ${String(e)}`)},kg=({subprocess:e,cancelSignal:t,gracefulCancel:r,context:n,controller:o})=>t===void 0||r?[]:[mT(e,t,n,o)],mT=async(e,t,r,{signal:n})=>{throw await Ts(t,n),r.terminationReason??="cancel",e.kill(),t.reason};var hy=require("node:timers/promises");var py=require("node:util");var dn=({methodName:e,isSubprocess:t,ipc:r,isConnected:n})=>{hT(e,t,r),kl(e,t,n)},hT=(e,t,r)=>{if(!r)throw new Error(`${it(e,t)} can only be used if the \`ipc\` option is \`true\`.`)},kl=(e,t,r)=>{if(!r)throw new Error(`${it(e,t)} cannot be used: the ${sr(t)} has already exited or disconnected.`)},Mg=e=>{throw new Error(`${it("getOneMessage",e)} could not complete: the ${sr(e)} exited or disconnected.`)},Ng=e=>{throw new Error(`${it("sendMessage",e)} failed: the ${sr(e)} is sending a message too, instead of listening to incoming messages.
This can be fixed by both sending a message and listening to incoming messages at the same time:

const [receivedMessage] = await Promise.all([
	${it("getOneMessage",e)},
	${it("sendMessage",e,"message, {strict: true}")},
]);`)},Rs=(e,t)=>new Error(`${it("sendMessage",t)} failed when sending an acknowledgment response to the ${sr(t)}.`,{cause:e}),jg=e=>{throw new Error(`${it("sendMessage",e)} failed: the ${sr(e)} is not listening to incoming messages.`)},Lg=e=>{throw new Error(`${it("sendMessage",e)} failed: the ${sr(e)} exited without listening to incoming messages.`)},qg=()=>new Error(`\`cancelSignal\` aborted: the ${sr(!0)} disconnected.`),Ug=()=>{throw new Error("`getCancelSignal()` cannot be used without setting the `cancelSignal` subprocess option.")},zg=({error:e,methodName:t,isSubprocess:r})=>{if(e.code==="EPIPE")throw new Error(`${it(t,r)} cannot be used: the ${sr(r)} is disconnecting.`,{cause:e})},Wg=({error:e,methodName:t,isSubprocess:r,message:n})=>{if(gT(e))throw new Error(`${it(t,r)}'s argument type is invalid: the message cannot be serialized: ${String(n)}.`,{cause:e})},gT=({code:e,message:t})=>yT.has(e)||DT.some(r=>t.includes(r)),yT=new Set(["ERR_MISSING_ARGS","ERR_INVALID_ARG_TYPE"]),DT=["could not be cloned","circular structure","call stack size exceeded"],it=(e,t,r="")=>e==="cancelSignal"?"`cancelSignal`'s `controller.abort()`":`${bT(t)}${e}(${r})`,bT=e=>e?"":"subprocess.",sr=e=>e?"parent process":"subprocess",pn=e=>{e.connected&&e.disconnect()};var _t=()=>{let e={},t=new Promise((r,n)=>{Object.assign(e,{resolve:r,reject:n})});return Object.assign(t,e)};var $s=(e,t="stdin")=>{let{options:n,fileDescriptors:o}=wt.get(e),i=Vg(o,t,!0),a=e.stdio[i];if(a===null)throw new TypeError(Gg(i,t,n,!0));return a},mn=(e,t="stdout")=>{let{options:n,fileDescriptors:o}=wt.get(e),i=Vg(o,t,!1),a=i==="all"?e.all:e.stdio[i];if(a==null)throw new TypeError(Gg(i,t,n,!1));return a},wt=new WeakMap,Vg=(e,t,r)=>{let n=_T(t,r);return wT(n,t,r,e),n},_T=(e,t)=>{let r=gl(e);if(r!==void 0)return r;let{validOptions:n,defaultValue:o}=t?{validOptions:'"stdin"',defaultValue:"stdin"}:{validOptions:'"stdout", "stderr", "all"',defaultValue:"stdout"};throw new TypeError(`"${Eo(t)}" must not be "${e}".
It must be ${n} or "fd3", "fd4" (and so on).
It is optional and defaults to "${o}".`)},wT=(e,t,r,n)=>{let o=n[Hg(e)];if(o===void 0)throw new TypeError(`"${Eo(r)}" must not be ${t}. That file descriptor does not exist.
Please set the "stdio" option to ensure that file descriptor exists.`);if(o.direction==="input"&&!r)throw new TypeError(`"${Eo(r)}" must not be ${t}. It must be a readable stream, not writable.`);if(o.direction!=="input"&&r)throw new TypeError(`"${Eo(r)}" must not be ${t}. It must be a writable stream, not readable.`)},Gg=(e,t,r,n)=>{if(e==="all"&&!r.all)return`The "all" option must be true to use "from: 'all'".`;let{optionName:o,optionValue:i}=ST(e,r);return`The "${o}: ${As(i)}" option is incompatible with using "${Eo(n)}: ${As(t)}".
Please set this option with "pipe" instead.`},ST=(e,{stdin:t,stdout:r,stderr:n,stdio:o})=>{let i=Hg(e);return i===0&&t!==void 0?{optionName:"stdin",optionValue:t}:i===1&&r!==void 0?{optionName:"stdout",optionValue:r}:i===2&&n!==void 0?{optionName:"stderr",optionValue:n}:{optionName:`stdio[${i}]`,optionValue:o[i]}},Hg=e=>e==="all"?1:e,Eo=e=>e?"to":"from",As=e=>typeof e=="string"?`'${e}'`:typeof e=="number"?`${e}`:"Stream";var sy=require("node:events");var Yg=require("node:events"),Rr=(e,t,r)=>{let n=e.getMaxListeners();n===0||n===Number.POSITIVE_INFINITY||(e.setMaxListeners(n+t),(0,Yg.addAbortListener)(r,()=>{e.setMaxListeners(e.getMaxListeners()-t)}))};var iy=require("node:events");var Qg=require("node:events"),Xg=require("node:timers/promises");var xs=(e,t)=>{t&&Ml(e)},Ml=e=>{e.refCounted()},Ps=(e,t)=>{t&&Nl(e)},Nl=e=>{e.unrefCounted()},Kg=(e,t)=>{t&&(Nl(e),Nl(e))},Jg=(e,t)=>{t&&(Ml(e),Ml(e))};var Zg=async({anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n},o)=>{if(ry(o)||oy(o))return;Os.has(e)||Os.set(e,[]);let i=Os.get(e);if(i.push(o),!(i.length>1))for(;i.length>0;){await ny(e,n,o),await Xg.scheduler.yield();let a=await ty({wrappedMessage:i[0],anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n});i.shift(),n.emit("message",a),n.emit("message:done")}},ey=async({anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n,boundOnMessage:o})=>{jl();let i=Os.get(e);for(;i?.length>0;)await(0,Qg.once)(n,"message:done");e.removeListener("message",o),Jg(t,r),n.connected=!1,n.emit("disconnect")},Os=new WeakMap;var ar=(e,t,r)=>{if(Bs.has(e))return Bs.get(e);let n=new iy.EventEmitter;return n.connected=!0,Bs.set(e,n),ET({ipcEmitter:n,anyProcess:e,channel:t,isSubprocess:r}),n},Bs=new WeakMap,ET=({ipcEmitter:e,anyProcess:t,channel:r,isSubprocess:n})=>{let o=Zg.bind(void 0,{anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:e});t.on("message",o),t.once("disconnect",ey.bind(void 0,{anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:e,boundOnMessage:o})),Kg(r,n)},Is=e=>{let t=Bs.get(e);return t===void 0?e.channel!==null:t.connected};var ay=({anyProcess:e,channel:t,isSubprocess:r,message:n,strict:o})=>{if(!o)return n;let i=ar(e,t,r),a=Ns(e,i);return{id:vT++,type:Ms,message:n,hasListeners:a}},vT=0n,uy=(e,t)=>{if(!(t?.type!==Ms||t.hasListeners))for(let{id:r}of e)r!==void 0&&ks[r].resolve({isDeadlock:!0,hasListeners:!1})},ty=async({wrappedMessage:e,anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:o})=>{if(e?.type!==Ms||!t.connected)return e;let{id:i,message:a}=e,l={id:i,type:cy,message:Ns(t,o)};try{await js({anyProcess:t,channel:r,isSubprocess:n,ipc:!0},l)}catch(c){o.emit("strict:error",c)}return a},ry=e=>{if(e?.type!==cy)return!1;let{id:t,message:r}=e;return ks[t]?.resolve({isDeadlock:!1,hasListeners:r}),!0},ly=async(e,t,r)=>{if(e?.type!==Ms)return;let n=_t();ks[e.id]=n;let o=new AbortController;try{let{isDeadlock:i,hasListeners:a}=await Promise.race([n,FT(t,r,o)]);i&&Ng(r),a||jg(r)}finally{o.abort(),delete ks[e.id]}},ks={},FT=async(e,t,{signal:r})=>{Rr(e,1,r),await(0,sy.once)(e,"disconnect",{signal:r}),Lg(t)},Ms="execa:ipc:request",cy="execa:ipc:response";var fy=(e,t,r)=>{vo.has(e)||vo.set(e,new Set);let n=vo.get(e),o=_t(),i=r?t.id:void 0,a={onMessageSent:o,id:i};return n.add(a),{outgoingMessages:n,outgoingMessage:a}},dy=({outgoingMessages:e,outgoingMessage:t})=>{e.delete(t),t.onMessageSent.resolve()},ny=async(e,t,r)=>{for(;!Ns(e,t)&&vo.get(e)?.size>0;){let n=[...vo.get(e)];uy(n,r),await Promise.all(n.map(({onMessageSent:o})=>o))}},vo=new WeakMap,Ns=(e,t)=>t.listenerCount("message")>CT(e),CT=e=>wt.has(e)&&!Mt(wt.get(e).options.buffer,"ipc")?1:0;var js=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},o,{strict:i=!1}={})=>{let a="sendMessage";return dn({methodName:a,isSubprocess:r,ipc:n,isConnected:e.connected}),TT({anyProcess:e,channel:t,methodName:a,isSubprocess:r,message:o,strict:i})},TT=async({anyProcess:e,channel:t,methodName:r,isSubprocess:n,message:o,strict:i})=>{let a=ay({anyProcess:e,channel:t,isSubprocess:n,message:o,strict:i}),l=fy(e,a,i);try{await ql({anyProcess:e,methodName:r,isSubprocess:n,wrappedMessage:a,message:o})}catch(c){throw pn(e),c}finally{dy(l)}},ql=async({anyProcess:e,methodName:t,isSubprocess:r,wrappedMessage:n,message:o})=>{let i=RT(e);try{await Promise.all([ly(n,e,r),i(n)])}catch(a){throw zg({error:a,methodName:t,isSubprocess:r}),Wg({error:a,methodName:t,isSubprocess:r,message:o}),a}},RT=e=>{if(Ll.has(e))return Ll.get(e);let t=(0,py.promisify)(e.send.bind(e));return Ll.set(e,t),t},Ll=new WeakMap;var gy=(e,t)=>{let r="cancelSignal";return kl(r,!1,e.connected),ql({anyProcess:e,methodName:r,isSubprocess:!1,wrappedMessage:{type:Dy,message:t},message:t})},yy=async({anyProcess:e,channel:t,isSubprocess:r,ipc:n})=>(await AT({anyProcess:e,channel:t,isSubprocess:r,ipc:n}),Ul.signal),AT=async({anyProcess:e,channel:t,isSubprocess:r,ipc:n})=>{if(!my){if(my=!0,!n){Ug();return}if(t===null){jl();return}ar(e,t,r),await hy.scheduler.yield()}},my=!1,oy=e=>e?.type!==Dy?!1:(Ul.abort(e.message),!0),Dy="execa:ipc:cancel",jl=()=>{Ul.abort(qg())},Ul=new AbortController;var by=({gracefulCancel:e,cancelSignal:t,ipc:r,serialization:n})=>{if(e){if(t===void 0)throw new Error("The `cancelSignal` option must be defined when setting the `gracefulCancel` option.");if(!r)throw new Error("The `ipc` option cannot be false when setting the `gracefulCancel` option.");if(n==="json")throw new Error("The `serialization` option cannot be 'json' when setting the `gracefulCancel` option.")}},_y=({subprocess:e,cancelSignal:t,gracefulCancel:r,forceKillAfterDelay:n,context:o,controller:i})=>r?[$T({subprocess:e,cancelSignal:t,forceKillAfterDelay:n,context:o,controller:i})]:[],$T=async({subprocess:e,cancelSignal:t,forceKillAfterDelay:r,context:n,controller:{signal:o}})=>{await Ts(t,o);let i=xT(t);throw await gy(e,i),Il({kill:e.kill,forceKillAfterDelay:r,context:n,controllerSignal:o}),n.terminationReason??="gracefulCancel",t.reason},xT=({reason:e})=>{if(!(e instanceof DOMException))return e;let t=new Error(e.message);return Object.defineProperty(t,"stack",{value:e.stack,enumerable:!1,configurable:!0,writable:!0}),t};var wy=require("node:timers/promises");var Sy=({timeout:e})=>{if(e!==void 0&&(!Number.isFinite(e)||e<0))throw new TypeError(`Expected the \`timeout\` option to be a non-negative integer, got \`${e}\` (${typeof e})`)},Ey=(e,t,r,n)=>t===0||t===void 0?[]:[PT(e,t,r,n)],PT=async(e,t,r,{signal:n})=>{throw await(0,wy.setTimeout)(t,void 0,{signal:n}),r.terminationReason??="timeout",e.kill(),new ot};var Ls=require("node:process"),zl=N(require("node:path"),1);var vy=({options:e})=>{if(e.node===!1)throw new TypeError('The "node" option cannot be false with `execaNode()`.');return{options:{...e,node:!0}}},Fy=(e,t,{node:r=!1,nodePath:n=Ls.execPath,nodeOptions:o=Ls.execArgv.filter(c=>!c.startsWith("--inspect")),cwd:i,execPath:a,...l})=>{if(a!==void 0)throw new TypeError('The "execPath" option has been removed. Please use the "nodePath" option instead.');let c=sn(n,'The "nodePath" option'),d=zl.default.resolve(i,c),p={...l,nodePath:d,node:r,cwd:i};if(!r)return[e,t,p];if(zl.default.basename(e,".exe")==="node")throw new TypeError('When the "node" option is true, the first argument does not need to be "node".');return[d,[...o,e,...t],{ipc:!0,...p,shell:!1}]};var Cy=require("node:v8"),Ty=({ipcInput:e,ipc:t,serialization:r})=>{if(e!==void 0){if(!t)throw new Error("The `ipcInput` option cannot be set unless the `ipc` option is `true`.");IT[r](e)}},OT=e=>{try{(0,Cy.serialize)(e)}catch(t){throw new Error("The `ipcInput` option is not serializable with a structured clone.",{cause:t})}},BT=e=>{try{JSON.stringify(e)}catch(t){throw new Error("The `ipcInput` option is not serializable with JSON.",{cause:t})}},IT={advanced:OT,json:BT},Ry=async(e,t)=>{t!==void 0&&await e.sendMessage(t)};var $y=({encoding:e})=>{if(Wl.has(e))return;let t=MT(e);if(t!==void 0)throw new TypeError(`Invalid option \`encoding: ${qs(e)}\`.
Please rename it to ${qs(t)}.`);let r=[...Wl].map(n=>qs(n)).join(", ");throw new TypeError(`Invalid option \`encoding: ${qs(e)}\`.
Please rename it to one of: ${r}.`)},kT=new Set(["utf8","utf16le"]),je=new Set(["buffer","hex","base64","base64url","latin1","ascii"]),Wl=new Set([...kT,...je]),MT=e=>{if(e===null)return"buffer";if(typeof e!="string")return;let t=e.toLowerCase();if(t in Ay)return Ay[t];if(Wl.has(t))return t},Ay={"utf-8":"utf8","utf-16le":"utf16le","ucs-2":"utf16le",ucs2:"utf16le",binary:"latin1"},qs=e=>typeof e=="string"?`"${e}"`:String(e);var xy=require("node:fs"),Py=N(require("node:path"),1),Oy=N(require("node:process"),1);var By=(e=Iy())=>{let t=sn(e,'The "cwd" option');return Py.default.resolve(t)},Iy=()=>{try{return Oy.default.cwd()}catch(e){throw e.message=`The current directory does not exist.
${e.message}`,e}},ky=(e,t)=>{if(t===Iy())return e;let r;try{r=(0,xy.statSync)(t)}catch(n){return`The "cwd" option is invalid: ${t}.
${n.message}
${e}`}return r.isDirectory()?e:`The "cwd" option is not a directory: ${t}.
${e}`};var Us=(e,t,r)=>{r.cwd=By(r.cwd);let[n,o,i]=Fy(e,t,r),{command:a,args:l,options:c}=Ny.default._parse(n,o,i),d=ih(c),p=NT(d);return Sy(p),$y(p),Ty(p),Ig(p),by(p),p.shell=pl(p.shell),p.env=jT(p),p.killSignal=Rg(p.killSignal),p.forceKillAfterDelay=Pg(p.forceKillAfterDelay),p.lines=p.lines.map((m,b)=>m&&!je.has(p.encoding)&&p.buffer[b]),Vl.default.platform==="win32"&&My.default.basename(a,".exe")==="cmd"&&l.unshift("/q"),{file:a,commandArguments:l,options:p}},NT=({extendEnv:e=!0,preferLocal:t=!1,cwd:r,localDir:n=r,encoding:o="utf8",reject:i=!0,cleanup:a=!0,all:l=!1,windowsHide:c=!0,killSignal:d="SIGTERM",forceKillAfterDelay:p=!0,gracefulCancel:m=!1,ipcInput:b,ipc:D=b!==void 0||m,serialization:g="advanced",...y})=>({...y,extendEnv:e,preferLocal:t,cwd:r,localDirectory:n,encoding:o,reject:i,cleanup:a,all:l,windowsHide:c,killSignal:d,forceKillAfterDelay:p,gracefulCancel:m,ipcInput:b,ipc:D,serialization:g}),jT=({env:e,extendEnv:t,preferLocal:r,node:n,localDirectory:o,nodePath:i})=>{let a=t?{...Vl.default.env,...e}:e;return r||n?hg({env:a,cwd:o,execPath:i,preferLocal:r,addExecPath:n}):a};var zs=(e,t,r)=>r.shell&&t.length>0?[[e,...t].join(" "),[],r]:[e,t,r];var s0=require("node:util");function hn(e){if(typeof e=="string")return LT(e);if(!(ArrayBuffer.isView(e)&&e.BYTES_PER_ELEMENT===1))throw new Error("Input must be a string or a Uint8Array");return qT(e)}var LT=e=>e.at(-1)===jy?e.slice(0,e.at(-2)===Ly?-2:-1):e,qT=e=>e.at(-1)===UT?e.subarray(0,e.at(-2)===zT?-2:-1):e,jy=`
`,UT=jy.codePointAt(0),Ly="\r",zT=Ly.codePointAt(0);var Xy=require("node:events"),Zy=require("node:stream/promises");function st(e,{checkOpen:t=!0}={}){return e!==null&&typeof e=="object"&&(e.writable||e.readable||!t||e.writable===void 0&&e.readable===void 0)&&typeof e.pipe=="function"}function Gl(e,{checkOpen:t=!0}={}){return st(e,{checkOpen:t})&&(e.writable||!t)&&typeof e.write=="function"&&typeof e.end=="function"&&typeof e.writable=="boolean"&&typeof e.writableObjectMode=="boolean"&&typeof e.destroy=="function"&&typeof e.destroyed=="boolean"}function Ar(e,{checkOpen:t=!0}={}){return st(e,{checkOpen:t})&&(e.readable||!t)&&typeof e.read=="function"&&typeof e.readable=="boolean"&&typeof e.readableObjectMode=="boolean"&&typeof e.destroy=="function"&&typeof e.destroyed=="boolean"}function Hl(e,t){return Gl(e,t)&&Ar(e,t)}var WT=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype),Yl=class{#e;#r;#t=!1;#n=void 0;constructor(t,r){this.#e=t,this.#r=r}next(){let t=()=>this.#a();return this.#n=this.#n?this.#n.then(t,t):t(),this.#n}return(t){let r=()=>this.#o(t);return this.#n?this.#n.then(r,r):r()}async#a(){if(this.#t)return{done:!0,value:void 0};let t;try{t=await this.#e.read()}catch(r){throw this.#n=void 0,this.#t=!0,this.#e.releaseLock(),r}return t.done&&(this.#n=void 0,this.#t=!0,this.#e.releaseLock()),t}async#o(t){if(this.#t)return{done:!0,value:t};if(this.#t=!0,!this.#r){let r=this.#e.cancel(t);return this.#e.releaseLock(),await r,{done:!0,value:t}}return this.#e.releaseLock(),{done:!0,value:t}}},Kl=Symbol();function qy(){return this[Kl].next()}Object.defineProperty(qy,"name",{value:"next"});function Uy(e){return this[Kl].return(e)}Object.defineProperty(Uy,"name",{value:"return"});var VT=Object.create(WT,{next:{enumerable:!0,configurable:!0,writable:!0,value:qy},return:{enumerable:!0,configurable:!0,writable:!0,value:Uy}});function Jl({preventCancel:e=!1}={}){let t=this.getReader(),r=new Yl(t,e),n=Object.create(VT);return n[Kl]=r,n}var zy=e=>{if(Ar(e,{checkOpen:!1})&&Fo.on!==void 0)return HT(e);if(typeof e?.[Symbol.asyncIterator]=="function")return e;if(GT.call(e)==="[object ReadableStream]")return Jl.call(e);throw new TypeError("The first argument must be a Readable, a ReadableStream, or an async iterable.")},{toString:GT}=Object.prototype,HT=async function*(e){let t=new AbortController,r={};YT(e,t,r);try{for await(let[n]of Fo.on(e,"data",{signal:t.signal}))yield n}catch(n){if(r.error!==void 0)throw r.error;if(!t.signal.aborted)throw n}finally{e.destroy()}},YT=async(e,t,r)=>{try{await Fo.finished(e,{cleanup:!0,readable:!0,writable:!1,error:!1})}catch(n){r.error=n}finally{t.abort()}},Fo={};var gn=async(e,{init:t,convertChunk:r,getSize:n,truncateChunk:o,addChunk:i,getFinalChunk:a,finalize:l},{maxBuffer:c=Number.POSITIVE_INFINITY}={})=>{let d=zy(e),p=t();p.length=0;try{for await(let m of d){let b=JT(m),D=r[b](m,p);Gy({convertedChunk:D,state:p,getSize:n,truncateChunk:o,addChunk:i,maxBuffer:c})}return KT({state:p,convertChunk:r,getSize:n,truncateChunk:o,addChunk:i,getFinalChunk:a,maxBuffer:c}),l(p)}catch(m){let b=typeof m=="object"&&m!==null?m:new Error(m);throw b.bufferedData=l(p),b}},KT=({state:e,getSize:t,truncateChunk:r,addChunk:n,getFinalChunk:o,maxBuffer:i})=>{let a=o(e);a!==void 0&&Gy({convertedChunk:a,state:e,getSize:t,truncateChunk:r,addChunk:n,maxBuffer:i})},Gy=({convertedChunk:e,state:t,getSize:r,truncateChunk:n,addChunk:o,maxBuffer:i})=>{let a=r(e),l=t.length+a;if(l<=i){Wy(e,t,o,l);return}let c=n(e,i-t.length);throw c!==void 0&&Wy(c,t,o,i),new St},Wy=(e,t,r,n)=>{t.contents=r(e,t,n),t.length=n},JT=e=>{let t=typeof e;if(t==="string")return"string";if(t!=="object"||e===null)return"others";if(globalThis.Buffer?.isBuffer(e))return"buffer";let r=Vy.call(e);return r==="[object ArrayBuffer]"?"arrayBuffer":r==="[object DataView]"?"dataView":Number.isInteger(e.byteLength)&&Number.isInteger(e.byteOffset)&&Vy.call(e.buffer)==="[object ArrayBuffer]"?"typedArray":"others"},{toString:Vy}=Object.prototype,St=class extends Error{name="MaxBufferError";constructor(){super("maxBuffer exceeded")}};var Nt=e=>e,Co=()=>{},Ws=({contents:e})=>e,Vs=e=>{throw new Error(`Streams in object mode are not supported: ${String(e)}`)},Gs=e=>e.length;async function Hs(e,t){return gn(e,eR,t)}var QT=()=>({contents:[]}),XT=()=>1,ZT=(e,{contents:t})=>(t.push(e),t),eR={init:QT,convertChunk:{string:Nt,buffer:Nt,arrayBuffer:Nt,dataView:Nt,typedArray:Nt,others:Nt},getSize:XT,truncateChunk:Co,addChunk:ZT,getFinalChunk:Co,finalize:Ws};async function Ys(e,t){return gn(e,lR,t)}var tR=()=>({contents:new ArrayBuffer(0)}),rR=e=>nR.encode(e),nR=new TextEncoder,Hy=e=>new Uint8Array(e),Yy=e=>new Uint8Array(e.buffer,e.byteOffset,e.byteLength),oR=(e,t)=>e.slice(0,t),iR=(e,{contents:t,length:r},n)=>{let o=Qy()?aR(t,n):sR(t,n);return new Uint8Array(o).set(e,r),o},sR=(e,t)=>{if(t<=e.byteLength)return e;let r=new ArrayBuffer(Jy(t));return new Uint8Array(r).set(new Uint8Array(e),0),r},aR=(e,t)=>{if(t<=e.maxByteLength)return e.resize(t),e;let r=new ArrayBuffer(t,{maxByteLength:Jy(t)});return new Uint8Array(r).set(new Uint8Array(e),0),r},Jy=e=>Ky**Math.ceil(Math.log(e)/Math.log(Ky)),Ky=2,uR=({contents:e,length:t})=>Qy()?e:e.slice(0,t),Qy=()=>"resize"in ArrayBuffer.prototype,lR={init:tR,convertChunk:{string:rR,buffer:Hy,arrayBuffer:Hy,dataView:Yy,typedArray:Yy,others:Vs},getSize:Gs,truncateChunk:oR,addChunk:iR,getFinalChunk:Co,finalize:uR};async function Js(e,t){return gn(e,mR,t)}var cR=()=>({contents:"",textDecoder:new TextDecoder}),Ks=(e,{textDecoder:t})=>t.decode(e,{stream:!0}),fR=(e,{contents:t})=>t+e,dR=(e,t)=>e.slice(0,t),pR=({textDecoder:e})=>{let t=e.decode();return t===""?void 0:t},mR={init:cR,convertChunk:{string:Nt,buffer:Ks,arrayBuffer:Ks,dataView:Ks,typedArray:Ks,others:Vs},getSize:Gs,truncateChunk:dR,addChunk:fR,getFinalChunk:pR,finalize:Ws};Object.assign(Fo,{on:Xy.on,finished:Zy.finished});var e0=({error:e,stream:t,readableObjectMode:r,lines:n,encoding:o,fdNumber:i})=>{if(!(e instanceof St))throw e;if(i==="all")return e;let a=hR(r,n,o);throw e.maxBufferInfo={fdNumber:i,unit:a},t.destroy(),e},hR=(e,t,r)=>e?"objects":t?"lines":r==="buffer"?"bytes":"characters",t0=(e,t,r)=>{if(t.length!==r)return;let n=new St;throw n.maxBufferInfo={fdNumber:"ipc"},n},r0=(e,t)=>{let{streamName:r,threshold:n,unit:o}=gR(e,t);return`Command's ${r} was larger than ${n} ${o}`},gR=(e,t)=>{if(e?.maxBufferInfo===void 0)return{streamName:"output",threshold:t[1],unit:"bytes"};let{maxBufferInfo:{fdNumber:r,unit:n}}=e;delete e.maxBufferInfo;let o=Mt(t,r);return r==="ipc"?{streamName:"IPC output",threshold:o,unit:"messages"}:{streamName:ps(r),threshold:o,unit:n}},n0=(e,t,r)=>e?.code==="ENOBUFS"&&t!==null&&t.some(n=>n!==null&&n.length>Qs(r)),o0=(e,t,r)=>{if(!t)return e;let n=Qs(r);return e.length>n?e.slice(0,n):e},Qs=([,e])=>e;var a0=({stdio:e,all:t,ipcOutput:r,originalError:n,signal:o,signalDescription:i,exitCode:a,escapedCommand:l,timedOut:c,isCanceled:d,isGracefullyCanceled:p,isMaxBuffer:m,isForcefullyTerminated:b,forceKillAfterDelay:D,killSignal:g,maxBuffer:y,timeout:S,cwd:w})=>{let T=n?.code,R=yR({originalError:n,timedOut:c,timeout:S,isMaxBuffer:m,maxBuffer:y,errorCode:T,signal:o,signalDescription:i,exitCode:a,isCanceled:d,isGracefullyCanceled:p,isForcefullyTerminated:b,forceKillAfterDelay:D,killSignal:g}),A=bR(n,w),v=A===void 0?"":`
${A}`,B=`${R}: ${l}${v}`,W=t===void 0?[e[2],e[1]]:[t],ae=[B,...W,...e.slice(3),r.map(P=>_R(P)).join(`
`)].map(P=>Do(hn(wR(P)))).filter(Boolean).join(`

`);return{originalMessage:A,shortMessage:B,message:ae}},yR=({originalError:e,timedOut:t,timeout:r,isMaxBuffer:n,maxBuffer:o,errorCode:i,signal:a,signalDescription:l,exitCode:c,isCanceled:d,isGracefullyCanceled:p,isForcefullyTerminated:m,forceKillAfterDelay:b,killSignal:D})=>{let g=DR(m,b);return t?`Command timed out after ${r} milliseconds${g}`:p?a===void 0?`Command was gracefully canceled with exit code ${c}`:m?`Command was gracefully canceled${g}`:`Command was gracefully canceled with ${a} (${l})`:d?`Command was canceled${g}`:n?`${r0(e,o)}${g}`:i!==void 0?`Command failed with ${i}${g}`:m?`Command was killed with ${D} (${Cs(D)})${g}`:a!==void 0?`Command was killed with ${a} (${l})`:c!==void 0?`Command failed with exit code ${c}`:"Command failed"},DR=(e,t)=>e?` and was forcefully terminated after ${t} milliseconds`:"",bR=(e,t)=>{if(e instanceof ot)return;let r=Dg(e)?e.originalMessage:String(e?.message??e),n=Do(ky(r,t));return n===""?void 0:n},_R=e=>typeof e=="string"?e:(0,s0.inspect)(e),wR=e=>Array.isArray(e)?e.map(t=>hn(i0(t))).filter(Boolean).join(`
`):i0(e),i0=e=>typeof e=="string"?e:we(e)?cs(e):"";var Xs=({command:e,escapedCommand:t,stdio:r,all:n,ipcOutput:o,options:{cwd:i},startTime:a})=>u0({command:e,escapedCommand:t,cwd:i,durationMs:wl(a),failed:!1,timedOut:!1,isCanceled:!1,isGracefullyCanceled:!1,isTerminated:!1,isMaxBuffer:!1,isForcefullyTerminated:!1,exitCode:0,stdout:r[1],stderr:r[2],all:n,stdio:r,ipcOutput:o,pipedFrom:[]}),yn=({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,isSync:a})=>To({error:e,command:t,escapedCommand:r,startTime:i,timedOut:!1,isCanceled:!1,isGracefullyCanceled:!1,isMaxBuffer:!1,isForcefullyTerminated:!1,stdio:Array.from({length:n.length}),ipcOutput:[],options:o,isSync:a}),To=({error:e,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:a,isMaxBuffer:l,isForcefullyTerminated:c,exitCode:d,signal:p,stdio:m,all:b,ipcOutput:D,options:{timeoutDuration:g,timeout:y=g,forceKillAfterDelay:S,killSignal:w,cwd:T,maxBuffer:R},isSync:A})=>{let{exitCode:v,signal:B,signalDescription:W}=ER(d,p),{originalMessage:ae,shortMessage:P,message:x}=a0({stdio:m,all:b,ipcOutput:D,originalError:e,signal:B,signalDescription:W,exitCode:v,escapedCommand:r,timedOut:o,isCanceled:i,isGracefullyCanceled:a,isMaxBuffer:l,isForcefullyTerminated:c,forceKillAfterDelay:S,killSignal:w,maxBuffer:R,timeout:y,cwd:T}),k=gg(e,x,A);return Object.assign(k,SR({error:k,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:a,isMaxBuffer:l,isForcefullyTerminated:c,exitCode:v,signal:B,signalDescription:W,stdio:m,all:b,ipcOutput:D,cwd:T,originalMessage:ae,shortMessage:P})),k},SR=({error:e,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:a,isMaxBuffer:l,isForcefullyTerminated:c,exitCode:d,signal:p,signalDescription:m,stdio:b,all:D,ipcOutput:g,cwd:y,originalMessage:S,shortMessage:w})=>u0({shortMessage:w,originalMessage:S,command:t,escapedCommand:r,cwd:y,durationMs:wl(n),failed:!0,timedOut:o,isCanceled:i,isGracefullyCanceled:a,isTerminated:p!==void 0,isMaxBuffer:l,isForcefullyTerminated:c,exitCode:d,signal:p,signalDescription:m,code:e.cause?.code,stdout:b[1],stderr:b[2],all:D,stdio:b,ipcOutput:g,pipedFrom:[]}),u0=e=>Object.fromEntries(Object.entries(e).filter(([,t])=>t!==void 0)),ER=(e,t)=>{let r=e===null?void 0:e,n=t===null?void 0:t,o=n===void 0?void 0:Cs(t);return{exitCode:r,signal:n,signalDescription:o}};var l0=e=>Number.isFinite(e)?e:0;function vR(e){return{days:Math.trunc(e/864e5),hours:Math.trunc(e/36e5%24),minutes:Math.trunc(e/6e4%60),seconds:Math.trunc(e/1e3%60),milliseconds:Math.trunc(e%1e3),microseconds:Math.trunc(l0(e*1e3)%1e3),nanoseconds:Math.trunc(l0(e*1e6)%1e3)}}function FR(e){return{days:e/86400000n,hours:e/3600000n%24n,minutes:e/60000n%60n,seconds:e/1000n%60n,milliseconds:e%1000n,microseconds:0n,nanoseconds:0n}}function Ql(e){switch(typeof e){case"number":{if(Number.isFinite(e))return vR(e);break}case"bigint":return FR(e)}throw new TypeError("Expected a finite number or bigint")}var CR=e=>e===0||e===0n,TR=(e,t)=>t===1||t===1n?e:`${e}s`,RR=1e-7,AR=24n*60n*60n*1000n;function Xl(e,t){let r=typeof e=="bigint";if(!r&&!Number.isFinite(e))throw new TypeError("Expected a finite number or bigint");t={...t};let n=e<0?"-":"";e=e<0?-e:e,t.colonNotation&&(t.compact=!1,t.formatSubMilliseconds=!1,t.separateMilliseconds=!1,t.verbose=!1),t.compact&&(t.unitCount=1,t.secondsDecimalDigits=0,t.millisecondsDecimalDigits=0);let o=[],i=(p,m)=>{let b=Math.floor(p*10**m+RR);return(Math.round(b)/10**m).toFixed(m)},a=(p,m,b,D)=>{if(!((o.length===0||!t.colonNotation)&&CR(p)&&!(t.colonNotation&&b==="m"))){if(D??=String(p),t.colonNotation){let g=D.includes(".")?D.split(".")[0].length:D.length,y=o.length>0?2:1;D="0".repeat(Math.max(0,y-g))+D}else D+=t.verbose?" "+TR(m,p):b;o.push(D)}},l=Ql(e),c=BigInt(l.days);if(t.hideYearAndDays?a(BigInt(c)*24n+BigInt(l.hours),"hour","h"):(t.hideYear?a(c,"day","d"):(a(c/365n,"year","y"),a(c%365n,"day","d")),a(Number(l.hours),"hour","h")),a(Number(l.minutes),"minute","m"),!t.hideSeconds)if(t.separateMilliseconds||t.formatSubMilliseconds||!t.colonNotation&&e<1e3){let p=Number(l.seconds),m=Number(l.milliseconds),b=Number(l.microseconds),D=Number(l.nanoseconds);if(a(p,"second","s"),t.formatSubMilliseconds)a(m,"millisecond","ms"),a(b,"microsecond","\xB5s"),a(D,"nanosecond","ns");else{let g=m+b/1e3+D/1e6,y=typeof t.millisecondsDecimalDigits=="number"?t.millisecondsDecimalDigits:0,S=g>=1?Math.round(g):Math.ceil(g),w=y?g.toFixed(y):S;a(Number.parseFloat(w),"millisecond","ms",w)}}else{let p=(r?Number(e%AR):e)/1e3%60,m=typeof t.secondsDecimalDigits=="number"?t.secondsDecimalDigits:1,b=i(p,m),D=t.keepDecimalsOnWholeSeconds?b:b.replace(/\.0+$/,"");a(Number.parseFloat(D),"second","s",D)}if(o.length===0)return n+"0"+(t.verbose?" milliseconds":"ms");let d=t.colonNotation?":":" ";return typeof t.unitCount=="number"&&(o=o.slice(0,Math.max(t.unitCount,1))),n+o.join(d)}var c0=(e,t)=>{e.failed&&bt({type:"error",verboseMessage:e.shortMessage,verboseInfo:t,result:e})};var f0=(e,t)=>{an(t)&&(c0(e,t),$R(e,t))},$R=(e,t)=>{let r=`(done in ${Xl(e.durationMs)})`;bt({type:"duration",verboseMessage:r,verboseInfo:t,result:e})};var Dn=(e,t,{reject:r})=>{if(f0(e,t),e.failed&&r)throw e;return e};var sc=require("node:fs");var m0=(e,t)=>$r(e)?"asyncGenerator":y0(e)?"generator":Zs(e)?"fileUrl":IR(e)?"filePath":NR(e)?"webStream":st(e,{checkOpen:!1})?"native":we(e)?"uint8Array":jR(e)?"asyncIterable":LR(e)?"iterable":tc(e)?h0({transform:e},t):BR(e)?xR(e,t):"native",xR=(e,t)=>Hl(e.transform,{checkOpen:!1})?PR(e,t):tc(e.transform)?h0(e,t):OR(e,t),PR=(e,t)=>(g0(e,t,"Duplex stream"),"duplex"),h0=(e,t)=>(g0(e,t,"web TransformStream"),"webTransform"),g0=({final:e,binary:t,objectMode:r},n,o)=>{d0(e,`${n}.final`,o),d0(t,`${n}.binary`,o),Zl(r,`${n}.objectMode`)},d0=(e,t,r)=>{if(e!==void 0)throw new TypeError(`The \`${t}\` option can only be defined when using a generator, not a ${r}.`)},OR=({transform:e,final:t,binary:r,objectMode:n},o)=>{if(e!==void 0&&!p0(e))throw new TypeError(`The \`${o}.transform\` option must be a generator, a Duplex stream or a web TransformStream.`);if(Hl(t,{checkOpen:!1}))throw new TypeError(`The \`${o}.final\` option must not be a Duplex stream.`);if(tc(t))throw new TypeError(`The \`${o}.final\` option must not be a web TransformStream.`);if(t!==void 0&&!p0(t))throw new TypeError(`The \`${o}.final\` option must be a generator.`);return Zl(r,`${o}.binary`),Zl(n,`${o}.objectMode`),$r(e)||$r(t)?"asyncGenerator":"generator"},Zl=(e,t)=>{if(e!==void 0&&typeof e!="boolean")throw new TypeError(`The \`${t}\` option must use a boolean.`)},p0=e=>$r(e)||y0(e),$r=e=>Object.prototype.toString.call(e)==="[object AsyncGeneratorFunction]",y0=e=>Object.prototype.toString.call(e)==="[object GeneratorFunction]",BR=e=>ge(e)&&(e.transform!==void 0||e.final!==void 0),Zs=e=>Object.prototype.toString.call(e)==="[object URL]",D0=e=>Zs(e)&&e.protocol!=="file:",IR=e=>ge(e)&&Object.keys(e).length>0&&Object.keys(e).every(t=>kR.has(t))&&ec(e.file),kR=new Set(["file","append"]),ec=e=>typeof e=="string",b0=(e,t)=>e==="native"&&typeof t=="string"&&!MR.has(t),MR=new Set(["ipc","ignore","inherit","overlapped","pipe"]),_0=e=>Object.prototype.toString.call(e)==="[object ReadableStream]",ea=e=>Object.prototype.toString.call(e)==="[object WritableStream]",NR=e=>_0(e)||ea(e),tc=e=>_0(e?.readable)&&ea(e?.writable),jR=e=>w0(e)&&typeof e[Symbol.asyncIterator]=="function",LR=e=>w0(e)&&typeof e[Symbol.iterator]=="function",w0=e=>typeof e=="object"&&e!==null,ze=new Set(["generator","asyncGenerator","duplex","webTransform"]),ta=new Set(["fileUrl","filePath","fileNumber"]),rc=new Set(["fileUrl","filePath"]),S0=new Set([...rc,"webStream","nodeStream"]),E0=new Set(["webTransform","duplex"]),ur={generator:"a generator",asyncGenerator:"an async generator",fileUrl:"a file URL",filePath:"a file path string",fileNumber:"a file descriptor number",webStream:"a web stream",nodeStream:"a Node.js stream",webTransform:"a web TransformStream",duplex:"a Duplex stream",native:"any value",iterable:"an iterable",asyncIterable:"an async iterable",string:"a string",uint8Array:"a Uint8Array"};var nc=(e,t,r,n)=>n==="output"?qR(e,t,r):UR(e,t,r),qR=(e,t,r)=>{let n=t!==0&&r[t-1].value.readableObjectMode;return{writableObjectMode:n,readableObjectMode:e??n}},UR=(e,t,r)=>{let n=t===0?e===!0:r[t-1].value.readableObjectMode,o=t!==r.length-1&&(e??n);return{writableObjectMode:n,readableObjectMode:o}},v0=(e,t)=>{let r=e.findLast(({type:n})=>ze.has(n));return r===void 0?!1:t==="input"?r.value.writableObjectMode:r.value.readableObjectMode};var F0=(e,t,r,n)=>[...e.filter(({type:o})=>!ze.has(o)),...zR(e,t,r,n)],zR=(e,t,r,{encoding:n})=>{let o=e.filter(({type:a})=>ze.has(a)),i=Array.from({length:o.length});for(let[a,l]of Object.entries(o))i[a]=WR({stdioItem:l,index:Number(a),newTransforms:i,optionName:t,direction:r,encoding:n});return YR(i,r)},WR=({stdioItem:e,stdioItem:{type:t},index:r,newTransforms:n,optionName:o,direction:i,encoding:a})=>t==="duplex"?VR({stdioItem:e,optionName:o}):t==="webTransform"?GR({stdioItem:e,index:r,newTransforms:n,direction:i}):HR({stdioItem:e,index:r,newTransforms:n,direction:i,encoding:a}),VR=({stdioItem:e,stdioItem:{value:{transform:t,transform:{writableObjectMode:r,readableObjectMode:n},objectMode:o=n}},optionName:i})=>{if(o&&!n)throw new TypeError(`The \`${i}.objectMode\` option can only be \`true\` if \`new Duplex({objectMode: true})\` is used.`);if(!o&&n)throw new TypeError(`The \`${i}.objectMode\` option cannot be \`false\` if \`new Duplex({objectMode: true})\` is used.`);return{...e,value:{transform:t,writableObjectMode:r,readableObjectMode:n}}},GR=({stdioItem:e,stdioItem:{value:t},index:r,newTransforms:n,direction:o})=>{let{transform:i,objectMode:a}=ge(t)?t:{transform:t},{writableObjectMode:l,readableObjectMode:c}=nc(a,r,n,o);return{...e,value:{transform:i,writableObjectMode:l,readableObjectMode:c}}},HR=({stdioItem:e,stdioItem:{value:t},index:r,newTransforms:n,direction:o,encoding:i})=>{let{transform:a,final:l,binary:c=!1,preserveNewlines:d=!1,objectMode:p}=ge(t)?t:{transform:t},m=c||je.has(i),{writableObjectMode:b,readableObjectMode:D}=nc(p,r,n,o);return{...e,value:{transform:a,final:l,binary:m,preserveNewlines:d,writableObjectMode:b,readableObjectMode:D}}},YR=(e,t)=>t==="input"?e.reverse():e;var ra=N(require("node:process"),1);var C0=(e,t,r)=>{let n=e.map(o=>KR(o,t));if(n.includes("input")&&n.includes("output"))throw new TypeError(`The \`${r}\` option must not be an array of both readable and writable values.`);return n.find(Boolean)??XR},KR=({type:e,value:t},r)=>JR[r]??T0[e](t),JR=["input","output","output"],bn=()=>{},oc=()=>"input",T0={generator:bn,asyncGenerator:bn,fileUrl:bn,filePath:bn,iterable:oc,asyncIterable:oc,uint8Array:oc,webStream:e=>ea(e)?"output":"input",nodeStream(e){return Ar(e,{checkOpen:!1})?Gl(e,{checkOpen:!1})?void 0:"input":"output"},webTransform:bn,duplex:bn,native(e){let t=QR(e);if(t!==void 0)return t;if(st(e,{checkOpen:!1}))return T0.nodeStream(e)}},QR=e=>{if([0,ra.default.stdin].includes(e))return"input";if([1,2,ra.default.stdout,ra.default.stderr].includes(e))return"output"},XR="output";var R0=(e,t)=>t&&!e.includes("ipc")?[...e,"ipc"]:e;var A0=({stdio:e,ipc:t,buffer:r,...n},o,i)=>{let a=ZR(e,n).map((l,c)=>$0(l,c));return i?tA(a,r,o):R0(a,t)},ZR=(e,t)=>{if(e===void 0)return Ue.map(n=>t[n]);if(eA(t))throw new Error(`It's not possible to provide \`stdio\` in combination with one of ${Ue.map(n=>`\`${n}\``).join(", ")}`);if(typeof e=="string")return[e,e,e];if(!Array.isArray(e))throw new TypeError(`Expected \`stdio\` to be of type \`string\` or \`Array\`, got \`${typeof e}\``);let r=Math.max(e.length,Ue.length);return Array.from({length:r},(n,o)=>e[o])},eA=e=>Ue.some(t=>e[t]!==void 0),$0=(e,t)=>Array.isArray(e)?e.map(r=>$0(r,t)):e??(t>=Ue.length?"ignore":"pipe"),tA=(e,t,r)=>e.map((n,o)=>!t[o]&&o!==0&&!un(r,o)&&rA(n)?"ignore":n),rA=e=>e==="pipe"||Array.isArray(e)&&e.every(t=>t==="pipe");var P0=require("node:fs"),O0=N(require("node:tty"),1);var B0=({stdioItem:e,stdioItem:{type:t},isStdioArray:r,fdNumber:n,direction:o,isSync:i})=>!r||t!=="native"?e:i?nA({stdioItem:e,fdNumber:n,direction:o}):sA({stdioItem:e,fdNumber:n}),nA=({stdioItem:e,stdioItem:{value:t,optionName:r},fdNumber:n,direction:o})=>{let i=oA({value:t,optionName:r,fdNumber:n,direction:o});if(i!==void 0)return i;if(st(t,{checkOpen:!1}))throw new TypeError(`The \`${r}: Stream\` option cannot both be an array and include a stream with synchronous methods.`);return e},oA=({value:e,optionName:t,fdNumber:r,direction:n})=>{let o=iA(e,r);if(o!==void 0){if(n==="output")return{type:"fileNumber",value:o,optionName:t};if(O0.default.isatty(o))throw new TypeError(`The \`${t}: ${As(e)}\` option is invalid: it cannot be a TTY with synchronous methods.`);return{type:"uint8Array",value:kt((0,P0.readFileSync)(o)),optionName:t}}},iA=(e,t)=>{if(e==="inherit")return t;if(typeof e=="number")return e;let r=ds.indexOf(e);if(r!==-1)return r},sA=({stdioItem:e,stdioItem:{value:t,optionName:r},fdNumber:n})=>t==="inherit"?{type:"nodeStream",value:x0(n,t,r),optionName:r}:typeof t=="number"?{type:"nodeStream",value:x0(t,t,r),optionName:r}:st(t,{checkOpen:!1})?{type:"nodeStream",value:t,optionName:r}:e,x0=(e,t,r)=>{let n=ds[e];if(n===void 0)throw new TypeError(`The \`${r}: ${t}\` option is invalid: no such standard stream.`);return n};var I0=({input:e,inputFile:t},r)=>r===0?[...aA(e),...lA(t)]:[],aA=e=>e===void 0?[]:[{type:uA(e),value:e,optionName:"input"}],uA=e=>{if(Ar(e,{checkOpen:!1}))return"nodeStream";if(typeof e=="string")return"string";if(we(e))return"uint8Array";throw new Error("The `input` option must be a string, a Uint8Array or a Node.js Readable stream.")},lA=e=>e===void 0?[]:[{...cA(e),optionName:"inputFile"}],cA=e=>{if(Zs(e))return{type:"fileUrl",value:e};if(ec(e))return{type:"filePath",value:{file:e}};throw new Error("The `inputFile` option must be a file path string or a file URL.")};var k0=e=>e.filter((t,r)=>e.every((n,o)=>t.value!==n.value||r>=o||t.type==="generator"||t.type==="asyncGenerator")),M0=({stdioItem:{type:e,value:t,optionName:r},direction:n,fileDescriptors:o,isSync:i})=>{let a=fA(o,e);if(a.length!==0){if(i){dA({otherStdioItems:a,type:e,value:t,optionName:r,direction:n});return}if(S0.has(e))return N0({otherStdioItems:a,type:e,value:t,optionName:r,direction:n});E0.has(e)&&mA({otherStdioItems:a,type:e,value:t,optionName:r})}},fA=(e,t)=>e.flatMap(({direction:r,stdioItems:n})=>n.filter(o=>o.type===t).map(o=>({...o,direction:r}))),dA=({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})=>{rc.has(t)&&N0({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})},N0=({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})=>{let i=e.filter(l=>pA(l,r));if(i.length===0)return;let a=i.find(l=>l.direction!==o);return j0(a,n,t),o==="output"?i[0].stream:void 0},pA=({type:e,value:t},r)=>e==="filePath"?t.file===r.file:e==="fileUrl"?t.href===r.href:t===r,mA=({otherStdioItems:e,type:t,value:r,optionName:n})=>{let o=e.find(({value:{transform:i}})=>i===r.transform);j0(o,n,t)},j0=(e,t,r)=>{if(e!==void 0)throw new TypeError(`The \`${e.optionName}\` and \`${t}\` options must not target ${ur[r]} that is the same.`)};var na=(e,t,r,n)=>{let i=A0(t,r,n).map((l,c)=>hA({stdioOption:l,fdNumber:c,options:t,isSync:n})),a=EA({initialFileDescriptors:i,addProperties:e,options:t,isSync:n});return t.stdio=a.map(({stdioItems:l})=>CA(l)),a},hA=({stdioOption:e,fdNumber:t,options:r,isSync:n})=>{let o=ps(t),{stdioItems:i,isStdioArray:a}=gA({stdioOption:e,fdNumber:t,options:r,optionName:o}),l=C0(i,t,o),c=i.map(m=>B0({stdioItem:m,isStdioArray:a,fdNumber:t,direction:l,isSync:n})),d=F0(c,o,l,r),p=v0(d,l);return SA(d,p),{direction:l,objectMode:p,stdioItems:d}},gA=({stdioOption:e,fdNumber:t,options:r,optionName:n})=>{let i=[...(Array.isArray(e)?e:[e]).map(c=>yA(c,n)),...I0(r,t)],a=k0(i),l=a.length>1;return DA(a,l,n),_A(a),{stdioItems:a,isStdioArray:l}},yA=(e,t)=>({type:m0(e,t),value:e,optionName:t}),DA=(e,t,r)=>{if(e.length===0)throw new TypeError(`The \`${r}\` option must not be an empty array.`);if(t){for(let{value:n,optionName:o}of e)if(bA.has(n))throw new Error(`The \`${o}\` option must not include \`${n}\`.`)}},bA=new Set(["ignore","ipc"]),_A=e=>{for(let t of e)wA(t)},wA=({type:e,value:t,optionName:r})=>{if(D0(t))throw new TypeError(`The \`${r}: URL\` option must use the \`file:\` scheme.
For example, you can use the \`pathToFileURL()\` method of the \`url\` core module.`);if(b0(e,t))throw new TypeError(`The \`${r}: { file: '...' }\` option must be used instead of \`${r}: '...'\`.`)},SA=(e,t)=>{if(!t)return;let r=e.find(({type:n})=>ta.has(n));if(r!==void 0)throw new TypeError(`The \`${r.optionName}\` option cannot use both files and transforms in objectMode.`)},EA=({initialFileDescriptors:e,addProperties:t,options:r,isSync:n})=>{let o=[];try{for(let i of e)o.push(vA({fileDescriptor:i,fileDescriptors:o,addProperties:t,options:r,isSync:n}));return o}catch(i){throw ic(o),i}},vA=({fileDescriptor:{direction:e,objectMode:t,stdioItems:r},fileDescriptors:n,addProperties:o,options:i,isSync:a})=>{let l=r.map(c=>FA({stdioItem:c,addProperties:o,direction:e,options:i,fileDescriptors:n,isSync:a}));return{direction:e,objectMode:t,stdioItems:l}},FA=({stdioItem:e,addProperties:t,direction:r,options:n,fileDescriptors:o,isSync:i})=>{let a=M0({stdioItem:e,direction:r,fileDescriptors:o,isSync:i});return a!==void 0?{...e,stream:a}:{...e,...t[r][e.type](e,n)}},ic=e=>{for(let{stdioItems:t}of e)for(let{stream:r}of t)r!==void 0&&!nt(r)&&r.destroy()},CA=e=>{if(e.length>1)return e.some(({value:n})=>n==="overlapped")?"overlapped":"pipe";let[{type:t,value:r}]=e;return t==="native"?r:"pipe"};var q0=(e,t)=>na(RA,e,t,!0),Et=({type:e,optionName:t})=>{U0(t,ur[e])},TA=({optionName:e,value:t})=>((t==="ipc"||t==="overlapped")&&U0(e,`"${t}"`),{}),U0=(e,t)=>{throw new TypeError(`The \`${e}\` option cannot be ${t} with synchronous methods.`)},L0={generator(){},asyncGenerator:Et,webStream:Et,nodeStream:Et,webTransform:Et,duplex:Et,asyncIterable:Et,native:TA},RA={input:{...L0,fileUrl:({value:e})=>({contents:[kt((0,sc.readFileSync)(e))]}),filePath:({value:{file:e}})=>({contents:[kt((0,sc.readFileSync)(e))]}),fileNumber:Et,iterable:({value:e})=>({contents:[...e]}),string:({value:e})=>({contents:[e]}),uint8Array:({value:e})=>({contents:[e]})},output:{...L0,fileUrl:({value:e})=>({path:e}),filePath:({value:{file:e,append:t}})=>({path:e,append:t}),fileNumber:({value:e})=>({path:e}),iterable:Et,string:Et,uint8Array:Et}};var jt=(e,{stripFinalNewline:t},r)=>ac(t,r)&&e!==void 0&&!Array.isArray(e)?hn(e):e,ac=(e,t)=>t==="all"?e[1]||e[2]:e[t];var Ao=require("node:stream");var oa=(e,t,r,n)=>e||r?void 0:W0(t,n),lc=(e,t,r)=>r?e.flatMap(n=>z0(n,t)):z0(e,t),z0=(e,t)=>{let{transform:r,final:n}=W0(t,{});return[...r(e),...n()]},W0=(e,t)=>(t.previousChunks="",{transform:AA.bind(void 0,t,e),final:xA.bind(void 0,t)}),AA=function*(e,t,r){if(typeof r!="string"){yield r;return}let{previousChunks:n}=e,o=-1;for(let i=0;i<r.length;i+=1)if(r[i]===`
`){let a=$A(r,i,t,e),l=r.slice(o+1,i+1-a);n.length>0&&(l=uc(n,l),n=""),yield l,o=i}o!==r.length-1&&(n=uc(n,r.slice(o+1))),e.previousChunks=n},$A=(e,t,r,n)=>r?0:(n.isWindowsNewline=t!==0&&e[t-1]==="\r",n.isWindowsNewline?2:1),xA=function*({previousChunks:e}){e.length>0&&(yield e)},V0=({binary:e,preserveNewlines:t,readableObjectMode:r,state:n})=>e||t||r?void 0:{transform:PA.bind(void 0,n)},PA=function*({isWindowsNewline:e=!1},t){let{unixNewline:r,windowsNewline:n,LF:o,concatBytes:i}=typeof t=="string"?OA:IA;if(t.at(-1)===o){yield t;return}yield i(t,e?n:r)},uc=(e,t)=>`${e}${t}`,OA={windowsNewline:`\r
`,unixNewline:`
`,LF:`
`,concatBytes:uc},BA=(e,t)=>{let r=new Uint8Array(e.length+t.length);return r.set(e,0),r.set(t,e.length),r},IA={windowsNewline:new Uint8Array([13,10]),unixNewline:new Uint8Array([10]),LF:10,concatBytes:BA};var G0=require("node:buffer");var H0=(e,t)=>e?void 0:kA.bind(void 0,t),kA=function*(e,t){if(typeof t!="string"&&!we(t)&&!G0.Buffer.isBuffer(t))throw new TypeError(`The \`${e}\` option's transform must use "objectMode: true" to receive as input: ${typeof t}.`);yield t},Y0=(e,t)=>e?MA.bind(void 0,t):NA.bind(void 0,t),MA=function*(e,t){K0(e,t),yield t},NA=function*(e,t){if(K0(e,t),typeof t!="string"&&!we(t))throw new TypeError(`The \`${e}\` option's function must yield a string or an Uint8Array, not ${typeof t}.`);yield t},K0=(e,t)=>{if(t==null)throw new TypeError(`The \`${e}\` option's function must not call \`yield ${t}\`.
Instead, \`yield\` should either be called with a value, or not be called at all. For example:
  if (condition) { yield value; }`)};var J0=require("node:buffer"),Q0=require("node:string_decoder");var ia=(e,t,r)=>{if(r)return;if(e)return{transform:jA.bind(void 0,new TextEncoder)};let n=new Q0.StringDecoder(t);return{transform:LA.bind(void 0,n),final:qA.bind(void 0,n)}},jA=function*(e,t){J0.Buffer.isBuffer(t)?yield kt(t):typeof t=="string"?yield e.encode(t):yield t},LA=function*(e,t){yield we(t)?e.write(t):t},qA=function*(e){let t=e.end();t!==""&&(yield t)};var cc=require("node:util"),fc=(0,cc.callbackify)(async(e,t,r,n)=>{t.currentIterable=e(...r);try{for await(let o of t.currentIterable)n.push(o)}finally{delete t.currentIterable}}),sa=async function*(e,t,r){if(r===t.length){yield e;return}let{transform:n=zA}=t[r];for await(let o of n(e))yield*sa(o,t,r+1)},X0=async function*(e){for(let[t,{final:r}]of Object.entries(e))yield*UA(r,Number(t),e)},UA=async function*(e,t,r){if(e!==void 0)for await(let n of e())yield*sa(n,r,t+1)},Z0=(0,cc.callbackify)(async({currentIterable:e},t)=>{if(e!==void 0){await(t?e.throw(t):e.return());return}if(t)throw t}),zA=function*(e){yield e};var dc=(e,t,r,n)=>{try{for(let o of e(...t))r.push(o);n()}catch(o){n(o)}},eD=(e,t)=>[...t.flatMap(r=>[...xr(r,e,0)]),...Ro(e)],xr=function*(e,t,r){if(r===t.length){yield e;return}let{transform:n=VA}=t[r];for(let o of n(e))yield*xr(o,t,r+1)},Ro=function*(e){for(let[t,{final:r}]of Object.entries(e))yield*WA(r,Number(t),e)},WA=function*(e,t,r){if(e!==void 0)for(let n of e())yield*xr(n,r,t+1)},VA=function*(e){yield e};var pc=({value:e,value:{transform:t,final:r,writableObjectMode:n,readableObjectMode:o},optionName:i},{encoding:a})=>{let l={},c=tD(e,a,i),d=$r(t),p=$r(r),m=d?fc.bind(void 0,sa,l):dc.bind(void 0,xr),b=d||p?fc.bind(void 0,X0,l):dc.bind(void 0,Ro),D=d||p?Z0.bind(void 0,l):void 0;return{stream:new Ao.Transform({writableObjectMode:n,writableHighWaterMark:(0,Ao.getDefaultHighWaterMark)(n),readableObjectMode:o,readableHighWaterMark:(0,Ao.getDefaultHighWaterMark)(o),transform(y,S,w){m([y,c,0],this,w)},flush(y){b([c],this,y)},destroy:D})}},aa=(e,t,r,n)=>{let o=t.filter(({type:a})=>a==="generator"),i=n?o.reverse():o;for(let{value:a,optionName:l}of i){let c=tD(a,r,l);e=eD(c,e)}return e},tD=({transform:e,final:t,binary:r,writableObjectMode:n,readableObjectMode:o,preserveNewlines:i},a,l)=>{let c={};return[{transform:H0(n,l)},ia(r,a,n),oa(r,i,n,c),{transform:e,final:t},{transform:Y0(o,l)},V0({binary:r,preserveNewlines:i,readableObjectMode:o,state:c})].filter(Boolean)};var rD=(e,t)=>{for(let r of GA(e))HA(e,r,t)},GA=e=>new Set(Object.entries(e).filter(([,{direction:t}])=>t==="input").map(([t])=>Number(t))),HA=(e,t,r)=>{let{stdioItems:n}=e[t],o=n.filter(({contents:l})=>l!==void 0);if(o.length===0)return;if(t!==0){let[{type:l,optionName:c}]=o;throw new TypeError(`Only the \`stdin\` option, not \`${c}\`, can be ${ur[l]} with synchronous methods.`)}let a=o.map(({contents:l})=>l).map(l=>YA(l,n));r.input=yo(a)},YA=(e,t)=>{let r=aa(e,t,"utf8",!0);return KA(r),yo(r)},KA=e=>{let t=e.find(r=>typeof r!="string"&&!we(r));if(t!==void 0)throw new TypeError(`The \`stdin\` option is invalid: when passing objects as input, a transform must be used to serialize them to strings or Uint8Arrays: ${t}.`)};var la=require("node:fs");var ua=({stdioItems:e,encoding:t,verboseInfo:r,fdNumber:n})=>n!=="all"&&un(r,n)&&!je.has(t)&&JA(n)&&(e.some(({type:o,value:i})=>o==="native"&&QA.has(i))||e.every(({type:o})=>ze.has(o))),JA=e=>e===1||e===2,QA=new Set(["pipe","overlapped"]),nD=async(e,t,r,n)=>{for await(let o of e)XA(t)||iD(o,r,n)},oD=(e,t,r)=>{for(let n of e)iD(n,t,r)},XA=e=>e._readableState.pipes.length>0,iD=(e,t,r)=>{let n=bs(e);bt({type:"output",verboseMessage:n,fdNumber:t,verboseInfo:r})};var sD=({fileDescriptors:e,syncResult:{output:t},options:r,isMaxBuffer:n,verboseInfo:o})=>{if(t===null)return{output:Array.from({length:3})};let i={},a=new Set([]);return{output:t.map((c,d)=>ZA({result:c,fileDescriptors:e,fdNumber:d,state:i,outputFiles:a,isMaxBuffer:n,verboseInfo:o},r)),...i}},ZA=({result:e,fileDescriptors:t,fdNumber:r,state:n,outputFiles:o,isMaxBuffer:i,verboseInfo:a},{buffer:l,encoding:c,lines:d,stripFinalNewline:p,maxBuffer:m})=>{if(e===null)return;let b=o0(e,i,m),D=kt(b),{stdioItems:g,objectMode:y}=t[r],S=e$([D],g,c,n),{serializedResult:w,finalResult:T=w}=t$({chunks:S,objectMode:y,encoding:c,lines:d,stripFinalNewline:p,fdNumber:r});r$({serializedResult:w,fdNumber:r,state:n,verboseInfo:a,encoding:c,stdioItems:g,objectMode:y});let R=l[r]?T:void 0;try{return n.error===void 0&&n$(w,g,o),R}catch(A){return n.error=A,R}},e$=(e,t,r,n)=>{try{return aa(e,t,r,!1)}catch(o){return n.error=o,e}},t$=({chunks:e,objectMode:t,encoding:r,lines:n,stripFinalNewline:o,fdNumber:i})=>{if(t)return{serializedResult:e};if(r==="buffer")return{serializedResult:yo(e)};let a=Jm(e,r);return n[i]?{serializedResult:a,finalResult:lc(a,!o[i],t)}:{serializedResult:a}},r$=({serializedResult:e,fdNumber:t,state:r,verboseInfo:n,encoding:o,stdioItems:i,objectMode:a})=>{if(!ua({stdioItems:i,encoding:o,verboseInfo:n,fdNumber:t}))return;let l=lc(e,!1,a);try{oD(l,t,n)}catch(c){r.error??=c}},n$=(e,t,r)=>{for(let{path:n,append:o}of t.filter(({type:i})=>ta.has(i))){let i=typeof n=="string"?n:n.toString();o||r.has(i)?(0,la.appendFileSync)(n,e):(r.add(i),(0,la.writeFileSync)(n,e))}};var aD=([,e,t],r)=>{if(r.all)return e===void 0?t:t===void 0?e:Array.isArray(e)?Array.isArray(t)?[...e,...t]:[...e,jt(t,r,"all")]:Array.isArray(t)?[jt(e,r,"all"),...t]:we(e)&&we(t)?ml([e,t]):`${e}${t}`};var ca=require("node:events");var uD=async(e,t)=>{let[r,n]=await o$(e);return t.isForcefullyTerminated??=!1,[r,n]},o$=async e=>{let[t,r]=await Promise.allSettled([(0,ca.once)(e,"spawn"),(0,ca.once)(e,"exit")]);return t.status==="rejected"?[]:r.status==="rejected"?lD(e):r.value},lD=async e=>{try{return await(0,ca.once)(e,"exit")}catch{return lD(e)}},cD=async e=>{let[t,r]=await e;if(!i$(t,r)&&mc(t,r))throw new ot;return[t,r]},i$=(e,t)=>e===void 0&&t===void 0,mc=(e,t)=>e!==0||t!==null;var fD=({error:e,status:t,signal:r,output:n},{maxBuffer:o})=>{let i=s$(e,t,r),a=i?.code==="ETIMEDOUT",l=n0(i,n,o);return{resultError:i,exitCode:t,signal:r,timedOut:a,isMaxBuffer:l}},s$=(e,t,r)=>e!==void 0?e:mc(t,r)?new ot:void 0;var pD=(e,t,r)=>{let{file:n,commandArguments:o,command:i,escapedCommand:a,startTime:l,verboseInfo:c,options:d,fileDescriptors:p}=a$(e,t,r),m=c$({file:n,commandArguments:o,options:d,command:i,escapedCommand:a,verboseInfo:c,fileDescriptors:p,startTime:l});return Dn(m,c,d)},a$=(e,t,r)=>{let{command:n,escapedCommand:o,startTime:i,verboseInfo:a}=ws(e,t,r),l=u$(r),{file:c,commandArguments:d,options:p}=Us(e,t,l);l$(p);let m=q0(p,a);return{file:c,commandArguments:d,command:n,escapedCommand:o,startTime:i,verboseInfo:a,options:p,fileDescriptors:m}},u$=e=>e.node&&!e.ipc?{...e,ipc:!1}:e,l$=({ipc:e,ipcInput:t,detached:r,cancelSignal:n})=>{t&&fa("ipcInput"),e&&fa("ipc: true"),r&&fa("detached: true"),n&&fa("cancelSignal")},fa=e=>{throw new TypeError(`The "${e}" option cannot be used with synchronous methods.`)},c$=({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,verboseInfo:i,fileDescriptors:a,startTime:l})=>{let c=f$({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,fileDescriptors:a,startTime:l});if(c.failed)return c;let{resultError:d,exitCode:p,signal:m,timedOut:b,isMaxBuffer:D}=fD(c,r),{output:g,error:y=d}=sD({fileDescriptors:a,syncResult:c,options:r,isMaxBuffer:D,verboseInfo:i}),S=g.map((T,R)=>jt(T,r,R)),w=jt(aD(g,r),r,"all");return p$({error:y,exitCode:p,signal:m,timedOut:b,isMaxBuffer:D,stdio:S,all:w,options:r,command:n,escapedCommand:o,startTime:l})},f$=({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,fileDescriptors:i,startTime:a})=>{try{rD(i,r);let l=d$(r);return(0,dD.spawnSync)(...zs(e,t,l))}catch(l){return yn({error:l,command:n,escapedCommand:o,fileDescriptors:i,options:r,startTime:a,isSync:!0})}},d$=({encoding:e,maxBuffer:t,...r})=>({...r,encoding:"buffer",maxBuffer:Qs(t)}),p$=({error:e,exitCode:t,signal:r,timedOut:n,isMaxBuffer:o,stdio:i,all:a,options:l,command:c,escapedCommand:d,startTime:p})=>e===void 0?Xs({command:c,escapedCommand:d,stdio:i,all:a,ipcOutput:[],options:l,startTime:p}):To({error:e,command:c,escapedCommand:d,timedOut:n,isCanceled:!1,isGracefullyCanceled:!1,isMaxBuffer:o,isForcefullyTerminated:!1,exitCode:t,signal:r,stdio:i,all:a,ipcOutput:[],options:l,startTime:p,isSync:!0});var _b=require("node:events"),wb=require("node:child_process");var gc=N(require("node:process"),1);var _n=require("node:events");var mD=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},{reference:o=!0,filter:i}={})=>(dn({methodName:"getOneMessage",isSubprocess:r,ipc:n,isConnected:Is(e)}),m$({anyProcess:e,channel:t,isSubprocess:r,filter:i,reference:o})),m$=async({anyProcess:e,channel:t,isSubprocess:r,filter:n,reference:o})=>{xs(t,o);let i=ar(e,t,r),a=new AbortController;try{return await Promise.race([h$(i,n,a),g$(i,r,a),y$(i,r,a)])}catch(l){throw pn(e),l}finally{a.abort(),Ps(t,o)}},h$=async(e,t,{signal:r})=>{if(t===void 0){let[n]=await(0,_n.once)(e,"message",{signal:r});return n}for await(let[n]of(0,_n.on)(e,"message",{signal:r}))if(t(n))return n},g$=async(e,t,{signal:r})=>{await(0,_n.once)(e,"disconnect",{signal:r}),Mg(t)},y$=async(e,t,{signal:r})=>{let[n]=await(0,_n.once)(e,"strict:error",{signal:r});throw Rs(n,t)};var $o=require("node:events");var gD=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},{reference:o=!0}={})=>hc({anyProcess:e,channel:t,isSubprocess:r,ipc:n,shouldAwait:!r,reference:o}),hc=({anyProcess:e,channel:t,isSubprocess:r,ipc:n,shouldAwait:o,reference:i})=>{dn({methodName:"getEachMessage",isSubprocess:r,ipc:n,isConnected:Is(e)}),xs(t,i);let a=ar(e,t,r),l=new AbortController,c={};return D$(e,a,l),b$({ipcEmitter:a,isSubprocess:r,controller:l,state:c}),_$({anyProcess:e,channel:t,ipcEmitter:a,isSubprocess:r,shouldAwait:o,controller:l,state:c,reference:i})},D$=async(e,t,r)=>{try{await(0,$o.once)(t,"disconnect",{signal:r.signal}),r.abort()}catch{}},b$=async({ipcEmitter:e,isSubprocess:t,controller:r,state:n})=>{try{let[o]=await(0,$o.once)(e,"strict:error",{signal:r.signal});n.error=Rs(o,t),r.abort()}catch{}},_$=async function*({anyProcess:e,channel:t,ipcEmitter:r,isSubprocess:n,shouldAwait:o,controller:i,state:a,reference:l}){try{for await(let[c]of(0,$o.on)(r,"message",{signal:i.signal}))hD(a),yield c}catch{hD(a)}finally{i.abort(),Ps(t,l),n||pn(e),o&&await e}},hD=({error:e})=>{if(e)throw e};var yD=(e,{ipc:t})=>{Object.assign(e,bD(e,!1,t))},DD=()=>{let e=gc.default,t=!0,r=gc.default.channel!==void 0;return{...bD(e,t,r),getCancelSignal:yy.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r})}},bD=(e,t,r)=>({sendMessage:js.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r}),getOneMessage:mD.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r}),getEachMessage:gD.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r})});var _D=require("node:child_process"),lr=require("node:stream");var wD=({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,verboseInfo:a})=>{ic(n);let l=new _D.ChildProcess;w$(l,n),Object.assign(l,{readable:S$,writable:E$,duplex:v$});let c=yn({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,isSync:!1}),d=F$(c,a,o);return{subprocess:l,promise:d}},w$=(e,t)=>{let r=xo(),n=xo(),o=xo(),i=Array.from({length:t.length-3},xo),a=xo(),l=[r,n,o,...i];Object.assign(e,{stdin:r,stdout:n,stderr:o,all:a,stdio:l})},xo=()=>{let e=new lr.PassThrough;return e.end(),e},S$=()=>new lr.Readable({read(){}}),E$=()=>new lr.Writable({write(){}}),v$=()=>new lr.Duplex({read(){},write(){}}),F$=async(e,t,r)=>Dn(e,t,r);var wn=require("node:fs"),ED=require("node:buffer"),vt=require("node:stream");var vD=(e,t)=>na(C$,e,t,!1),Po=({type:e,optionName:t})=>{throw new TypeError(`The \`${t}\` option cannot be ${ur[e]}.`)},SD={fileNumber:Po,generator:pc,asyncGenerator:pc,nodeStream:({value:e})=>({stream:e}),webTransform({value:{transform:e,writableObjectMode:t,readableObjectMode:r}}){let n=t||r;return{stream:vt.Duplex.fromWeb(e,{objectMode:n})}},duplex:({value:{transform:e}})=>({stream:e}),native(){}},C$={input:{...SD,fileUrl:({value:e})=>({stream:(0,wn.createReadStream)(e)}),filePath:({value:{file:e}})=>({stream:(0,wn.createReadStream)(e)}),webStream:({value:e})=>({stream:vt.Readable.fromWeb(e)}),iterable:({value:e})=>({stream:vt.Readable.from(e)}),asyncIterable:({value:e})=>({stream:vt.Readable.from(e)}),string:({value:e})=>({stream:vt.Readable.from(e)}),uint8Array:({value:e})=>({stream:vt.Readable.from(ED.Buffer.from(e))})},output:{...SD,fileUrl:({value:e})=>({stream:(0,wn.createWriteStream)(e)}),filePath:({value:{file:e,append:t}})=>({stream:(0,wn.createWriteStream)(e,t?{flags:"a"}:{})}),webStream:({value:e})=>({stream:vt.Writable.fromWeb(e)}),iterable:Po,asyncIterable:Po,string:Po,uint8Array:Po}};var Oo=require("node:events"),pa=require("node:stream"),bc=require("node:stream/promises");function Pr(e){if(!Array.isArray(e))throw new TypeError(`Expected an array, got \`${typeof e}\`.`);for(let o of e)Dc(o);let t=e.some(({readableObjectMode:o})=>o),r=T$(e,t),n=new yc({objectMode:t,writableHighWaterMark:r,readableHighWaterMark:r});for(let o of e)n.add(o);return n}var T$=(e,t)=>{if(e.length===0)return(0,pa.getDefaultHighWaterMark)(t);let r=e.filter(({readableObjectMode:n})=>n===t).map(({readableHighWaterMark:n})=>n);return Math.max(...r)},yc=class extends pa.PassThrough{#e=new Set([]);#r=new Set([]);#t=new Set([]);#n;#a=Symbol("unpipe");#o=new WeakMap;add(t){if(Dc(t),this.#e.has(t))return;this.#e.add(t),this.#n??=R$(this,this.#e,this.#a);let r=x$({passThroughStream:this,stream:t,streams:this.#e,ended:this.#r,aborted:this.#t,onFinished:this.#n,unpipeEvent:this.#a});this.#o.set(t,r),t.pipe(this,{end:!1})}async remove(t){if(Dc(t),!this.#e.has(t))return!1;let r=this.#o.get(t);return r===void 0?!1:(this.#o.delete(t),t.unpipe(this),await r,!0)}},R$=async(e,t,r)=>{da(e,FD);let n=new AbortController;try{await Promise.race([A$(e,n),$$(e,t,r,n)])}finally{n.abort(),da(e,-FD)}},A$=async(e,{signal:t})=>{try{await(0,bc.finished)(e,{signal:t,cleanup:!0})}catch(r){throw TD(e,r),r}},$$=async(e,t,r,{signal:n})=>{for await(let[o]of(0,Oo.on)(e,"unpipe",{signal:n}))t.has(o)&&o.emit(r)},Dc=e=>{if(typeof e?.pipe!="function")throw new TypeError(`Expected a readable stream, got: \`${typeof e}\`.`)},x$=async({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,onFinished:i,unpipeEvent:a})=>{da(e,CD);let l=new AbortController;try{await Promise.race([P$(i,t,l),O$({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,controller:l}),B$({stream:t,streams:r,ended:n,aborted:o,unpipeEvent:a,controller:l})])}finally{l.abort(),da(e,-CD)}r.size>0&&r.size===n.size+o.size&&(n.size===0&&o.size>0?_c(e):I$(e))},P$=async(e,t,{signal:r})=>{try{await e,r.aborted||_c(t)}catch(n){r.aborted||TD(t,n)}},O$=async({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,controller:{signal:i}})=>{try{await(0,bc.finished)(t,{signal:i,cleanup:!0,readable:!0,writable:!1}),r.has(t)&&n.add(t)}catch(a){if(i.aborted||!r.has(t))return;RD(a)?o.add(t):AD(e,a)}},B$=async({stream:e,streams:t,ended:r,aborted:n,unpipeEvent:o,controller:{signal:i}})=>{if(await(0,Oo.once)(e,o,{signal:i}),!e.readable)return(0,Oo.once)(i,"abort",{signal:i});t.delete(e),r.delete(e),n.delete(e)},I$=e=>{e.writable&&e.end()},TD=(e,t)=>{RD(t)?_c(e):AD(e,t)},RD=e=>e?.code==="ERR_STREAM_PREMATURE_CLOSE",_c=e=>{(e.readable||e.writable)&&e.destroy()},AD=(e,t)=>{e.destroyed||(e.once("error",k$),e.destroy(t))},k$=()=>{},da=(e,t)=>{let r=e.getMaxListeners();r!==0&&r!==Number.POSITIVE_INFINITY&&e.setMaxListeners(r+t)},FD=2,CD=1;var wc=require("node:stream/promises");var Sn=(e,t)=>{e.pipe(t),M$(e,t),N$(e,t)},M$=async(e,t)=>{if(!(nt(e)||nt(t))){try{await(0,wc.finished)(e,{cleanup:!0,readable:!0,writable:!1})}catch{}Sc(t)}},Sc=e=>{e.writable&&e.end()},N$=async(e,t)=>{if(!(nt(e)||nt(t))){try{await(0,wc.finished)(t,{cleanup:!0,readable:!1,writable:!0})}catch{}Ec(e)}},Ec=e=>{e.readable&&e.destroy()};var $D=(e,t,r)=>{let n=new Map;for(let[o,{stdioItems:i,direction:a}]of Object.entries(t)){for(let{stream:l}of i.filter(({type:c})=>ze.has(c)))j$(e,l,a,o);for(let{stream:l}of i.filter(({type:c})=>!ze.has(c)))q$({subprocess:e,stream:l,direction:a,fdNumber:o,pipeGroups:n,controller:r})}for(let[o,i]of n.entries()){let a=i.length===1?i[0]:Pr(i);Sn(a,o)}},j$=(e,t,r,n)=>{r==="output"?Sn(e.stdio[n],t):Sn(t,e.stdio[n]);let o=L$[n];o!==void 0&&(e[o]=t),e.stdio[n]=t},L$=["stdin","stdout","stderr"],q$=({subprocess:e,stream:t,direction:r,fdNumber:n,pipeGroups:o,controller:i})=>{if(t===void 0)return;U$(t,i);let[a,l]=r==="output"?[t,e.stdio[n]]:[e.stdio[n],t],c=o.get(a)??[];o.set(a,[...c,l])},U$=(e,{signal:t})=>{nt(e)&&Rr(e,z$,t)},z$=2;var xD=require("node:events");var Or=[];Or.push("SIGHUP","SIGINT","SIGTERM");process.platform!=="win32"&&Or.push("SIGALRM","SIGABRT","SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");process.platform==="linux"&&Or.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT");var ma=e=>!!e&&typeof e=="object"&&typeof e.removeListener=="function"&&typeof e.emit=="function"&&typeof e.reallyExit=="function"&&typeof e.listeners=="function"&&typeof e.kill=="function"&&typeof e.pid=="number"&&typeof e.on=="function",vc=Symbol.for("signal-exit emitter"),Fc=globalThis,W$=Object.defineProperty.bind(Object),Cc=class{emitted={afterExit:!1,exit:!1};listeners={afterExit:[],exit:[]};count=0;id=Math.random();constructor(){if(Fc[vc])return Fc[vc];W$(Fc,vc,{value:this,writable:!1,enumerable:!1,configurable:!1})}on(t,r){this.listeners[t].push(r)}removeListener(t,r){let n=this.listeners[t],o=n.indexOf(r);o!==-1&&(o===0&&n.length===1?n.length=0:n.splice(o,1))}emit(t,r,n){if(this.emitted[t])return!1;this.emitted[t]=!0;let o=!1;for(let i of this.listeners[t])o=i(r,n)===!0||o;return t==="exit"&&(o=this.emit("afterExit",r,n)||o),o}},ha=class{},V$=e=>({onExit(t,r){return e.onExit(t,r)},load(){return e.load()},unload(){return e.unload()}}),Tc=class extends ha{onExit(){return()=>{}}load(){}unload(){}},Rc=class extends ha{#e=Ac.platform==="win32"?"SIGINT":"SIGHUP";#r=new Cc;#t;#n;#a;#o={};#s=!1;constructor(t){super(),this.#t=t,this.#o={};for(let r of Or)this.#o[r]=()=>{let n=this.#t.listeners(r),{count:o}=this.#r,i=t;if(typeof i.__signal_exit_emitter__=="object"&&typeof i.__signal_exit_emitter__.count=="number"&&(o+=i.__signal_exit_emitter__.count),n.length===o){this.unload();let a=this.#r.emit("exit",null,r),l=r==="SIGHUP"?this.#e:r;a||t.kill(t.pid,l)}};this.#a=t.reallyExit,this.#n=t.emit}onExit(t,r){if(!ma(this.#t))return()=>{};this.#s===!1&&this.load();let n=r?.alwaysLast?"afterExit":"exit";return this.#r.on(n,t),()=>{this.#r.removeListener(n,t),this.#r.listeners.exit.length===0&&this.#r.listeners.afterExit.length===0&&this.unload()}}load(){if(!this.#s){this.#s=!0,this.#r.count+=1;for(let t of Or)try{let r=this.#o[t];r&&this.#t.on(t,r)}catch{}this.#t.emit=(t,...r)=>this.#p(t,...r),this.#t.reallyExit=t=>this.#i(t)}}unload(){this.#s&&(this.#s=!1,Or.forEach(t=>{let r=this.#o[t];if(!r)throw new Error("Listener not defined for signal: "+t);try{this.#t.removeListener(t,r)}catch{}}),this.#t.emit=this.#n,this.#t.reallyExit=this.#a,this.#r.count-=1)}#i(t){return ma(this.#t)?(this.#t.exitCode=t||0,this.#r.emit("exit",this.#t.exitCode,null),this.#a.call(this.#t,this.#t.exitCode)):0}#p(t,...r){let n=this.#n;if(t==="exit"&&ma(this.#t)){typeof r[0]=="number"&&(this.#t.exitCode=r[0]);let o=n.call(this.#t,t,...r);return this.#r.emit("exit",this.#t.exitCode,null),o}else return n.call(this.#t,t,...r)}},Ac=globalThis.process,{onExit:ga,load:bq,unload:_q}=V$(ma(Ac)?new Rc(Ac):new Tc);var PD=(e,{cleanup:t,detached:r},{signal:n})=>{if(!t||r)return;let o=ga(()=>{e.kill()});(0,xD.addAbortListener)(n,()=>{o()})};var BD=({source:e,sourcePromise:t,boundOptions:r,createNested:n},...o)=>{let i=_s(),{destination:a,destinationStream:l,destinationError:c,from:d,unpipeSignal:p}=G$(r,n,o),{sourceStream:m,sourceError:b}=Y$(e,d),{options:D,fileDescriptors:g}=wt.get(e);return{sourcePromise:t,sourceStream:m,sourceOptions:D,sourceError:b,destination:a,destinationStream:l,destinationError:c,unpipeSignal:p,fileDescriptors:g,startTime:i}},G$=(e,t,r)=>{try{let{destination:n,pipeOptions:{from:o,to:i,unpipeSignal:a}={}}=H$(e,t,...r),l=$s(n,i);return{destination:n,destinationStream:l,from:o,unpipeSignal:a}}catch(n){return{destinationError:n}}},H$=(e,t,r,...n)=>{if(Array.isArray(r))return{destination:t(OD,e)(r,...n),pipeOptions:e};if(typeof r=="string"||r instanceof URL||dl(r)){if(Object.keys(e).length>0)throw new TypeError('Please use .pipe("file", ..., options) or .pipe(execa("file", ..., options)) instead of .pipe(options)("file", ...).');let[o,i,a]=ls(r,...n);return{destination:t(OD)(o,i,a),pipeOptions:a}}if(wt.has(r)){if(Object.keys(e).length>0)throw new TypeError("Please use .pipe(options)`command` or .pipe($(options)`command`) instead of .pipe(options)($`command`).");return{destination:r,pipeOptions:n[0]}}throw new TypeError(`The first argument must be a template string, an options object, or an Execa subprocess: ${r}`)},OD=({options:e})=>({options:{...e,stdin:"pipe",piped:!0}}),Y$=(e,t)=>{try{return{sourceStream:mn(e,t)}}catch(r){return{sourceError:r}}};var kD=({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n,fileDescriptors:o,sourceOptions:i,startTime:a})=>{let l=K$({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n});if(l!==void 0)throw $c({error:l,fileDescriptors:o,sourceOptions:i,startTime:a})},K$=({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n})=>{if(t!==void 0&&n!==void 0)return n;if(n!==void 0)return Ec(e),n;if(t!==void 0)return Sc(r),t},$c=({error:e,fileDescriptors:t,sourceOptions:r,startTime:n})=>yn({error:e,command:ID,escapedCommand:ID,fileDescriptors:t,options:r,startTime:n,isSync:!1}),ID="source.pipe(destination)";var MD=async e=>{let[{status:t,reason:r,value:n=r},{status:o,reason:i,value:a=i}]=await e;if(a.pipedFrom.includes(n)||a.pipedFrom.push(n),o==="rejected")throw a;if(t==="rejected")throw n;return a};var ND=require("node:stream/promises");var jD=(e,t,r)=>{let n=ya.has(t)?Q$(e,t):J$(e,t);return Rr(e,Z$,r.signal),Rr(t,e1,r.signal),X$(t),n},J$=(e,t)=>{let r=Pr([e]);return Sn(r,t),ya.set(t,r),r},Q$=(e,t)=>{let r=ya.get(t);return r.add(e),r},X$=async e=>{try{await(0,ND.finished)(e,{cleanup:!0,readable:!1,writable:!0})}catch{}ya.delete(e)},ya=new WeakMap,Z$=2,e1=1;var LD=require("node:util");var qD=(e,t)=>e===void 0?[]:[t1(e,t)],t1=async(e,{sourceStream:t,mergedStream:r,fileDescriptors:n,sourceOptions:o,startTime:i})=>{await(0,LD.aborted)(e,t),await r.remove(t);let a=new Error("Pipe canceled by `unpipeSignal` option.");throw $c({error:a,fileDescriptors:n,sourceOptions:o,startTime:i})};var Da=(e,...t)=>{if(ge(t[0]))return Da.bind(void 0,{...e,boundOptions:{...e.boundOptions,...t[0]}});let{destination:r,...n}=BD(e,...t),o=r1({...n,destination:r});return o.pipe=Da.bind(void 0,{...e,source:r,sourcePromise:o,boundOptions:{}}),o},r1=async({sourcePromise:e,sourceStream:t,sourceOptions:r,sourceError:n,destination:o,destinationStream:i,destinationError:a,unpipeSignal:l,fileDescriptors:c,startTime:d})=>{let p=n1(e,o);kD({sourceStream:t,sourceError:n,destinationStream:i,destinationError:a,fileDescriptors:c,sourceOptions:r,startTime:d});let m=new AbortController;try{let b=jD(t,i,m);return await Promise.race([MD(p),...qD(l,{sourceStream:t,mergedStream:b,sourceOptions:r,fileDescriptors:c,startTime:d})])}finally{m.abort()}},n1=(e,t)=>Promise.allSettled([e,t]);var GD=require("node:timers/promises");var zD=require("node:events"),WD=require("node:stream");var ba=({subprocessStdout:e,subprocess:t,binary:r,shouldEncode:n,encoding:o,preserveNewlines:i})=>{let a=new AbortController;return o1(t,a),VD({stream:e,controller:a,binary:r,shouldEncode:!e.readableObjectMode&&n,encoding:o,shouldSplit:!e.readableObjectMode,preserveNewlines:i})},o1=async(e,t)=>{try{await e}catch{}finally{t.abort()}},xc=({stream:e,onStreamEnd:t,lines:r,encoding:n,stripFinalNewline:o,allMixed:i})=>{let a=new AbortController;i1(t,a,e);let l=e.readableObjectMode&&!i;return VD({stream:e,controller:a,binary:n==="buffer",shouldEncode:!l,encoding:n,shouldSplit:!l&&r,preserveNewlines:!o})},i1=async(e,t,r)=>{try{await e}catch{r.destroy()}finally{t.abort()}},VD=({stream:e,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:a})=>{let l=(0,zD.on)(e,"data",{signal:t.signal,highWaterMark:UD,highWatermark:UD});return s1({onStdoutChunk:l,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:a})},Pc=(0,WD.getDefaultHighWaterMark)(!0),UD=Pc,s1=async function*({onStdoutChunk:e,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:a}){let l=a1({binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:a});try{for await(let[c]of e)yield*xr(c,l,0)}catch(c){if(!t.signal.aborted)throw c}finally{yield*Ro(l)}},a1=({binary:e,shouldEncode:t,encoding:r,shouldSplit:n,preserveNewlines:o})=>[ia(e,r,!t),oa(e,o,!n,{})].filter(Boolean);var HD=async({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,buffer:o,maxBuffer:i,lines:a,allMixed:l,stripFinalNewline:c,verboseInfo:d,streamInfo:p})=>{let m=u1({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,allMixed:l,verboseInfo:d,streamInfo:p});if(!o){await Promise.all([l1(e),m]);return}let b=ac(c,r),D=xc({stream:e,onStreamEnd:t,lines:a,encoding:n,stripFinalNewline:b,allMixed:l}),[g]=await Promise.all([c1({stream:e,iterable:D,fdNumber:r,encoding:n,maxBuffer:i,lines:a}),m]);return g},u1=async({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,allMixed:o,verboseInfo:i,streamInfo:{fileDescriptors:a}})=>{if(!ua({stdioItems:a[r]?.stdioItems,encoding:n,verboseInfo:i,fdNumber:r}))return;let l=xc({stream:e,onStreamEnd:t,lines:!0,encoding:n,stripFinalNewline:!0,allMixed:o});await nD(l,e,r,i)},l1=async e=>{await(0,GD.setImmediate)(),e.readableFlowing===null&&e.resume()},c1=async({stream:e,stream:{readableObjectMode:t},iterable:r,fdNumber:n,encoding:o,maxBuffer:i,lines:a})=>{try{return t||a?await Hs(r,{maxBuffer:i}):o==="buffer"?new Uint8Array(await Ys(r,{maxBuffer:i})):await Js(r,{maxBuffer:i})}catch(l){return YD(e0({error:l,stream:e,readableObjectMode:t,lines:a,encoding:o,fdNumber:n}))}},Oc=async e=>{try{return await e}catch(t){return YD(t)}},YD=({bufferedData:e})=>Ym(e)?new Uint8Array(e):e;var JD=require("node:stream/promises"),Bo=async(e,t,r,{isSameDirection:n,stopOnExit:o=!1}={})=>{let i=f1(e,r),a=new AbortController;try{await Promise.race([...o?[r.exitPromise]:[],(0,JD.finished)(e,{cleanup:!0,signal:a.signal})])}catch(l){i.stdinCleanedUp||m1(l,t,r,n)}finally{a.abort()}},f1=(e,{originalStreams:[t],subprocess:r})=>{let n={stdinCleanedUp:!1};return e===t&&d1(e,r,n),n},d1=(e,t,r)=>{let{_destroy:n}=e;e._destroy=(...o)=>{p1(t,r),n.call(e,...o)}},p1=({exitCode:e,signalCode:t},r)=>{(e!==null||t!==null)&&(r.stdinCleanedUp=!0)},m1=(e,t,r,n)=>{if(!h1(e,t,r,n))throw e},h1=(e,t,r,n=!0)=>r.propagating?KD(e)||_a(e):(r.propagating=!0,Bc(r,t)===n?KD(e):_a(e)),Bc=({fileDescriptors:e},t)=>t!=="all"&&e[t].direction==="input",_a=e=>e?.code==="ERR_STREAM_PREMATURE_CLOSE",KD=e=>e?.code==="EPIPE";var QD=({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:i,verboseInfo:a,streamInfo:l})=>e.stdio.map((c,d)=>Ic({stream:c,fdNumber:d,encoding:t,buffer:r[d],maxBuffer:n[d],lines:o[d],allMixed:!1,stripFinalNewline:i,verboseInfo:a,streamInfo:l})),Ic=async({stream:e,fdNumber:t,encoding:r,buffer:n,maxBuffer:o,lines:i,allMixed:a,stripFinalNewline:l,verboseInfo:c,streamInfo:d})=>{if(!e)return;let p=Bo(e,t,d);if(Bc(d,t)){await p;return}let[m]=await Promise.all([HD({stream:e,onStreamEnd:p,fdNumber:t,encoding:r,buffer:n,maxBuffer:o,lines:i,allMixed:a,stripFinalNewline:l,verboseInfo:c,streamInfo:d}),p]);return m};var XD=({stdout:e,stderr:t},{all:r})=>r&&(e||t)?Pr([e,t].filter(Boolean)):void 0,ZD=({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:i,verboseInfo:a,streamInfo:l})=>Ic({...g1(e,r),fdNumber:"all",encoding:t,maxBuffer:n[1]+n[2],lines:o[1]||o[2],allMixed:y1(e),stripFinalNewline:i,verboseInfo:a,streamInfo:l}),g1=({stdout:e,stderr:t,all:r},[,n,o])=>{let i=n||o;return i?n?o?{stream:r,buffer:i}:{stream:e,buffer:i}:{stream:t,buffer:i}:{stream:r,buffer:i}},y1=({all:e,stdout:t,stderr:r})=>e&&t&&r&&t.readableObjectMode!==r.readableObjectMode;var ob=require("node:events");var eb=e=>un(e,"ipc"),tb=(e,t)=>{let r=bs(e);bt({type:"ipc",verboseMessage:r,fdNumber:"ipc",verboseInfo:t})};var rb=async({subprocess:e,buffer:t,maxBuffer:r,ipc:n,ipcOutput:o,verboseInfo:i})=>{if(!n)return o;let a=eb(i),l=Mt(t,"ipc"),c=Mt(r,"ipc");for await(let d of hc({anyProcess:e,channel:e.channel,isSubprocess:!1,ipc:n,shouldAwait:!1,reference:!0}))l&&(t0(e,o,c),o.push(d)),a&&tb(d,i);return o},nb=async(e,t)=>(await Promise.allSettled([e]),t);var ib=async({subprocess:e,options:{encoding:t,buffer:r,maxBuffer:n,lines:o,timeoutDuration:i,cancelSignal:a,gracefulCancel:l,forceKillAfterDelay:c,stripFinalNewline:d,ipc:p,ipcInput:m},context:b,verboseInfo:D,fileDescriptors:g,originalStreams:y,onInternalError:S,controller:w})=>{let T=uD(e,b),R={originalStreams:y,fileDescriptors:g,subprocess:e,exitPromise:T,propagating:!1},A=QD({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:d,verboseInfo:D,streamInfo:R}),v=ZD({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:d,verboseInfo:D,streamInfo:R}),B=[],W=rb({subprocess:e,buffer:r,maxBuffer:n,ipc:p,ipcOutput:B,verboseInfo:D}),ae=D1(y,e,R),P=b1(g,R);try{return await Promise.race([Promise.all([{},cD(T),Promise.all(A),v,W,Ry(e,m),...ae,...P]),S,_1(e,w),...Ey(e,i,b,w),...kg({subprocess:e,cancelSignal:a,gracefulCancel:l,context:b,controller:w}),..._y({subprocess:e,cancelSignal:a,gracefulCancel:l,forceKillAfterDelay:c,context:b,controller:w})])}catch(x){return b.terminationReason??="other",Promise.all([{error:x},T,Promise.all(A.map(k=>Oc(k))),Oc(v),nb(W,B),Promise.allSettled(ae),Promise.allSettled(P)])}},D1=(e,t,r)=>e.map((n,o)=>n===t.stdio[o]?void 0:Bo(n,o,r)),b1=(e,t)=>e.flatMap(({stdioItems:r},n)=>r.filter(({value:o,stream:i=o})=>st(i,{checkOpen:!1})&&!nt(i)).map(({type:o,value:i,stream:a=i})=>Bo(a,n,t,{isSameDirection:ze.has(o),stopOnExit:o==="native"}))),_1=async(e,{signal:t})=>{let[r]=await(0,ob.once)(e,"error",{signal:t});throw r};var sb=()=>({readableDestroy:new WeakMap,writableFinal:new WeakMap,writableDestroy:new WeakMap}),Io=(e,t,r)=>{let n=e[r];n.has(t)||n.set(t,[]);let o=n.get(t),i=_t();return o.push(i),{resolve:i.resolve.bind(i),promises:o}},En=async({resolve:e,promises:t},r)=>{e();let[n]=await Promise.race([Promise.allSettled([!0,r]),Promise.all([!1,...t])]);return!n};var ub=require("node:stream"),lb=require("node:util");var kc=require("node:stream/promises");var Mc=async e=>{if(e!==void 0)try{await Nc(e)}catch{}},ab=async e=>{if(e!==void 0)try{await jc(e)}catch{}},Nc=async e=>{await(0,kc.finished)(e,{cleanup:!0,readable:!1,writable:!0})},jc=async e=>{await(0,kc.finished)(e,{cleanup:!0,readable:!0,writable:!1})},wa=async(e,t)=>{if(await e,t)throw t},Sa=(e,t,r)=>{r&&!_a(r)?e.destroy(r):t&&e.destroy()};var cb=({subprocess:e,concurrentStreams:t,encoding:r},{from:n,binary:o=!0,preserveNewlines:i=!0}={})=>{let a=o||je.has(r),{subprocessStdout:l,waitReadableDestroy:c}=Lc(e,n,t),{readableEncoding:d,readableObjectMode:p,readableHighWaterMark:m}=qc(l,a),{read:b,onStdoutDataDone:D}=Uc({subprocessStdout:l,subprocess:e,binary:a,encoding:r,preserveNewlines:i}),g=new ub.Readable({read:b,destroy:(0,lb.callbackify)(Wc.bind(void 0,{subprocessStdout:l,subprocess:e,waitReadableDestroy:c})),highWaterMark:m,objectMode:p,encoding:d});return zc({subprocessStdout:l,onStdoutDataDone:D,readable:g,subprocess:e}),g},Lc=(e,t,r)=>{let n=mn(e,t),o=Io(r,n,"readableDestroy");return{subprocessStdout:n,waitReadableDestroy:o}},qc=({readableEncoding:e,readableObjectMode:t,readableHighWaterMark:r},n)=>n?{readableEncoding:e,readableObjectMode:t,readableHighWaterMark:r}:{readableEncoding:e,readableObjectMode:!0,readableHighWaterMark:Pc},Uc=({subprocessStdout:e,subprocess:t,binary:r,encoding:n,preserveNewlines:o})=>{let i=_t(),a=ba({subprocessStdout:e,subprocess:t,binary:r,shouldEncode:!r,encoding:n,preserveNewlines:o});return{read(){w1(this,a,i)},onStdoutDataDone:i}},w1=async(e,t,r)=>{try{let{value:n,done:o}=await t.next();o?r.resolve():e.push(n)}catch{}},zc=async({subprocessStdout:e,onStdoutDataDone:t,readable:r,subprocess:n,subprocessStdin:o})=>{try{await jc(e),await n,await Mc(o),await t,r.readable&&r.push(null)}catch(i){await Mc(o),fb(r,i)}},Wc=async({subprocessStdout:e,subprocess:t,waitReadableDestroy:r},n)=>{await En(r,t)&&(fb(e,n),await wa(t,n))},fb=(e,t)=>{Sa(e,e.readable,t)};var db=require("node:stream"),Vc=require("node:util");var pb=({subprocess:e,concurrentStreams:t},{to:r}={})=>{let{subprocessStdin:n,waitWritableFinal:o,waitWritableDestroy:i}=Gc(e,r,t),a=new db.Writable({...Hc(n,e,o),destroy:(0,Vc.callbackify)(Kc.bind(void 0,{subprocessStdin:n,subprocess:e,waitWritableFinal:o,waitWritableDestroy:i})),highWaterMark:n.writableHighWaterMark,objectMode:n.writableObjectMode});return Yc(n,a),a},Gc=(e,t,r)=>{let n=$s(e,t),o=Io(r,n,"writableFinal"),i=Io(r,n,"writableDestroy");return{subprocessStdin:n,waitWritableFinal:o,waitWritableDestroy:i}},Hc=(e,t,r)=>({write:S1.bind(void 0,e),final:(0,Vc.callbackify)(E1.bind(void 0,e,t,r))}),S1=(e,t,r,n)=>{e.write(t,r)?n():e.once("drain",n)},E1=async(e,t,r)=>{await En(r,t)&&(e.writable&&e.end(),await t)},Yc=async(e,t,r)=>{try{await Nc(e),t.writable&&t.end()}catch(n){await ab(r),mb(t,n)}},Kc=async({subprocessStdin:e,subprocess:t,waitWritableFinal:r,waitWritableDestroy:n},o)=>{await En(r,t),await En(n,t)&&(mb(e,o),await wa(t,o))},mb=(e,t)=>{Sa(e,e.writable,t)};var hb=require("node:stream"),gb=require("node:util");var yb=({subprocess:e,concurrentStreams:t,encoding:r},{from:n,to:o,binary:i=!0,preserveNewlines:a=!0}={})=>{let l=i||je.has(r),{subprocessStdout:c,waitReadableDestroy:d}=Lc(e,n,t),{subprocessStdin:p,waitWritableFinal:m,waitWritableDestroy:b}=Gc(e,o,t),{readableEncoding:D,readableObjectMode:g,readableHighWaterMark:y}=qc(c,l),{read:S,onStdoutDataDone:w}=Uc({subprocessStdout:c,subprocess:e,binary:l,encoding:r,preserveNewlines:a}),T=new hb.Duplex({read:S,...Hc(p,e,m),destroy:(0,gb.callbackify)(v1.bind(void 0,{subprocessStdout:c,subprocessStdin:p,subprocess:e,waitReadableDestroy:d,waitWritableFinal:m,waitWritableDestroy:b})),readableHighWaterMark:y,writableHighWaterMark:p.writableHighWaterMark,readableObjectMode:g,writableObjectMode:p.writableObjectMode,encoding:D});return zc({subprocessStdout:c,onStdoutDataDone:w,readable:T,subprocess:e,subprocessStdin:p}),Yc(p,T,c),T},v1=async({subprocessStdout:e,subprocessStdin:t,subprocess:r,waitReadableDestroy:n,waitWritableFinal:o,waitWritableDestroy:i},a)=>{await Promise.all([Wc({subprocessStdout:e,subprocess:r,waitReadableDestroy:n},a),Kc({subprocessStdin:t,subprocess:r,waitWritableFinal:o,waitWritableDestroy:i},a)])};var Jc=(e,t,{from:r,binary:n=!1,preserveNewlines:o=!1}={})=>{let i=n||je.has(t),a=mn(e,r),l=ba({subprocessStdout:a,subprocess:e,binary:i,shouldEncode:!0,encoding:t,preserveNewlines:o});return F1(l,a,e)},F1=async function*(e,t,r){try{yield*e}finally{t.readable&&t.destroy(),await r}};var Db=(e,{encoding:t})=>{let r=sb();e.readable=cb.bind(void 0,{subprocess:e,concurrentStreams:r,encoding:t}),e.writable=pb.bind(void 0,{subprocess:e,concurrentStreams:r}),e.duplex=yb.bind(void 0,{subprocess:e,concurrentStreams:r,encoding:t}),e.iterable=Jc.bind(void 0,e,t),e[Symbol.asyncIterator]=Jc.bind(void 0,e,t,{})};var bb=(e,t)=>{for(let[r,n]of T1){let o=n.value.bind(t);Reflect.defineProperty(e,r,{...n,value:o})}},C1=(async()=>{})().constructor.prototype,T1=["then","catch","finally"].map(e=>[e,Reflect.getOwnPropertyDescriptor(C1,e)]);var Sb=(e,t,r,n)=>{let{file:o,commandArguments:i,command:a,escapedCommand:l,startTime:c,verboseInfo:d,options:p,fileDescriptors:m}=R1(e,t,r),{subprocess:b,promise:D}=$1({file:o,commandArguments:i,options:p,startTime:c,verboseInfo:d,command:a,escapedCommand:l,fileDescriptors:m});return b.pipe=Da.bind(void 0,{source:b,sourcePromise:D,boundOptions:{},createNested:n}),bb(b,D),wt.set(b,{options:p,fileDescriptors:m}),b},R1=(e,t,r)=>{let{command:n,escapedCommand:o,startTime:i,verboseInfo:a}=ws(e,t,r),{file:l,commandArguments:c,options:d}=Us(e,t,r),p=A1(d),m=vD(p,a);return{file:l,commandArguments:c,command:n,escapedCommand:o,startTime:i,verboseInfo:a,options:p,fileDescriptors:m}},A1=({timeout:e,signal:t,...r})=>{if(t!==void 0)throw new TypeError('The "signal" option has been renamed to "cancelSignal" instead.');return{...r,timeoutDuration:e}},$1=({file:e,commandArguments:t,options:r,startTime:n,verboseInfo:o,command:i,escapedCommand:a,fileDescriptors:l})=>{let c;try{c=(0,wb.spawn)(...zs(e,t,r))}catch(g){return wD({error:g,command:i,escapedCommand:a,fileDescriptors:l,options:r,startTime:n,verboseInfo:o})}let d=new AbortController;(0,_b.setMaxListeners)(Number.POSITIVE_INFINITY,d.signal);let p=[...c.stdio];$D(c,l,d),PD(c,r,d);let m={},b=_t();c.kill=Og.bind(void 0,{kill:c.kill.bind(c),options:r,onInternalError:b,context:m,controller:d}),c.all=XD(c,r),Db(c,r),yD(c,r);let D=x1({subprocess:c,options:r,startTime:n,verboseInfo:o,fileDescriptors:l,originalStreams:p,command:i,escapedCommand:a,context:m,onInternalError:b,controller:d});return{subprocess:c,promise:D}},x1=async({subprocess:e,options:t,startTime:r,verboseInfo:n,fileDescriptors:o,originalStreams:i,command:a,escapedCommand:l,context:c,onInternalError:d,controller:p})=>{let[m,[b,D],g,y,S]=await ib({subprocess:e,options:t,context:c,verboseInfo:n,fileDescriptors:o,originalStreams:i,onInternalError:d,controller:p});p.abort(),d.resolve();let w=g.map((A,v)=>jt(A,t,v)),T=jt(y,t,"all"),R=P1({errorInfo:m,exitCode:b,signal:D,stdio:w,all:T,ipcOutput:S,context:c,options:t,command:a,escapedCommand:l,startTime:r});return Dn(R,n,t)},P1=({errorInfo:e,exitCode:t,signal:r,stdio:n,all:o,ipcOutput:i,context:a,options:l,command:c,escapedCommand:d,startTime:p})=>"error"in e?To({error:e.error,command:c,escapedCommand:d,timedOut:a.terminationReason==="timeout",isCanceled:a.terminationReason==="cancel"||a.terminationReason==="gracefulCancel",isGracefullyCanceled:a.terminationReason==="gracefulCancel",isMaxBuffer:e.error instanceof St,isForcefullyTerminated:a.isForcefullyTerminated,exitCode:t,signal:r,stdio:n,all:o,ipcOutput:i,options:l,startTime:p,isSync:!1}):Xs({command:c,escapedCommand:d,stdio:n,all:o,ipcOutput:i,options:l,startTime:p});var Ea=(e,t)=>{let r=Object.fromEntries(Object.entries(t).map(([n,o])=>[n,O1(n,e[n],o)]));return{...e,...r}},O1=(e,t,r)=>B1.has(e)&&ge(t)&&ge(r)?{...t,...r}:r,B1=new Set(["env",...yl]);var cr=(e,t,r,n)=>{let o=(a,l,c)=>cr(a,l,r,c),i=(...a)=>I1({mapArguments:e,deepOptions:r,boundOptions:t,setBoundExeca:n,createNested:o},...a);return n!==void 0&&n(i,o,t),i},I1=({mapArguments:e,deepOptions:t={},boundOptions:r={},setBoundExeca:n,createNested:o},i,...a)=>{if(ge(i))return o(e,Ea(r,i),n);let{file:l,commandArguments:c,options:d,isSync:p}=k1({mapArguments:e,firstArgument:i,nextArguments:a,deepOptions:t,boundOptions:r});return p?pD(l,c,d):Sb(l,c,d,o)},k1=({mapArguments:e,firstArgument:t,nextArguments:r,deepOptions:n,boundOptions:o})=>{let i=th(t)?rh(t,r):[t,...r],[a,l,c]=ls(...i),d=Ea(Ea(n,o),c),{file:p=a,commandArguments:m=l,options:b=d,isSync:D=!1}=e({file:a,commandArguments:l,options:d});return{file:p,commandArguments:m,options:b,isSync:D}};var Eb=({file:e,commandArguments:t})=>Fb(e,t),vb=({file:e,commandArguments:t})=>({...Fb(e,t),isSync:!0}),Fb=(e,t)=>{if(t.length>0)throw new TypeError(`The command and its arguments must be passed as a single string: ${e} ${t}.`);let[r,...n]=M1(e);return{file:r,commandArguments:n}},M1=e=>{if(typeof e!="string")throw new TypeError(`The command must be a string: ${String(e)}.`);let t=e.trim();if(t==="")return[];let r=[];for(let n of t.split(N1)){let o=r.at(-1);o&&o.endsWith("\\")?r[r.length-1]=`${o.slice(0,-1)} ${n}`:r.push(n)}return r},N1=/ +/g;var Cb=(e,t,r)=>{e.sync=t(j1,r),e.s=e.sync},Tb=({options:e})=>Rb(e),j1=({options:e})=>({...Rb(e),isSync:!0}),Rb=e=>({options:{...L1(e),...e}}),L1=({input:e,inputFile:t,stdio:r})=>e===void 0&&t===void 0&&r===void 0?{stdin:"inherit"}:{},Ab={preferLocal:!0};var va=cr(()=>({})),G9=cr(()=>({isSync:!0})),H9=cr(Eb),Y9=cr(vb),K9=cr(vy),J9=cr(Tb,{},Ab,Cb),{sendMessage:Q9,getOneMessage:X9,getEachMessage:Z9,getCancelSignal:e4}=DD();var _e=N(require("node:path")),Xe=N(require("node:fs")),XE=N(xb()),ku=N(Zc()),ZE=N(gf()),ev=N(qS()),Vr=N(WS());var fe=N(Zc());var Ci=N(require("node:process"),1);var VS=(e=0)=>t=>`\x1B[${t+e}m`,GS=(e=0)=>t=>`\x1B[${38+e};5;${t}m`,HS=(e=0)=>(t,r,n)=>`\x1B[${38+e};2;${t};${r};${n}m`,de={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],gray:[90,39],grey:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgGray:[100,49],bgGrey:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}},Pz=Object.keys(de.modifier),lk=Object.keys(de.color),ck=Object.keys(de.bgColor),Oz=[...lk,...ck];function fk(){let e=new Map;for(let[t,r]of Object.entries(de)){for(let[n,o]of Object.entries(r))de[n]={open:`\x1B[${o[0]}m`,close:`\x1B[${o[1]}m`},r[n]=de[n],e.set(o[0],o[1]);Object.defineProperty(de,t,{value:r,enumerable:!1})}return Object.defineProperty(de,"codes",{value:e,enumerable:!1}),de.color.close="\x1B[39m",de.bgColor.close="\x1B[49m",de.color.ansi=VS(),de.color.ansi256=GS(),de.color.ansi16m=HS(),de.bgColor.ansi=VS(10),de.bgColor.ansi256=GS(10),de.bgColor.ansi16m=HS(10),Object.defineProperties(de,{rgbToAnsi256:{value(t,r,n){return t===r&&r===n?t<8?16:t>248?231:Math.round((t-8)/247*24)+232:16+36*Math.round(t/255*5)+6*Math.round(r/255*5)+Math.round(n/255*5)},enumerable:!1},hexToRgb:{value(t){let r=/[a-f\d]{6}|[a-f\d]{3}/i.exec(t.toString(16));if(!r)return[0,0,0];let[n]=r;n.length===3&&(n=[...n].map(i=>i+i).join(""));let o=Number.parseInt(n,16);return[o>>16&255,o>>8&255,o&255]},enumerable:!1},hexToAnsi256:{value:t=>de.rgbToAnsi256(...de.hexToRgb(t)),enumerable:!1},ansi256ToAnsi:{value(t){if(t<8)return 30+t;if(t<16)return 90+(t-8);let r,n,o;if(t>=232)r=((t-232)*10+8)/255,n=r,o=r;else{t-=16;let l=t%36;r=Math.floor(t/36)/5,n=Math.floor(l/6)/5,o=l%6/5}let i=Math.max(r,n,o)*2;if(i===0)return 30;let a=30+(Math.round(o)<<2|Math.round(n)<<1|Math.round(r));return i===2&&(a+=60),a},enumerable:!1},rgbToAnsi:{value:(t,r,n)=>de.ansi256ToAnsi(de.rgbToAnsi256(t,r,n)),enumerable:!1},hexToAnsi:{value:t=>de.ansi256ToAnsi(de.hexToAnsi256(t)),enumerable:!1}}),de}var dk=fk(),mt=dk;var _u=N(require("node:process"),1),KS=N(require("node:os"),1),ep=N(require("node:tty"),1);function Ke(e,t=globalThis.Deno?globalThis.Deno.args:_u.default.argv){let r=e.startsWith("-")?"":e.length===1?"-":"--",n=t.indexOf(r+e),o=t.indexOf("--");return n!==-1&&(o===-1||n<o)}var{env:me}=_u.default,bu;Ke("no-color")||Ke("no-colors")||Ke("color=false")||Ke("color=never")?bu=0:(Ke("color")||Ke("colors")||Ke("color=true")||Ke("color=always"))&&(bu=1);function pk(){if("FORCE_COLOR"in me)return me.FORCE_COLOR==="true"?1:me.FORCE_COLOR==="false"?0:me.FORCE_COLOR.length===0?1:Math.min(Number.parseInt(me.FORCE_COLOR,10),3)}function mk(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function hk(e,{streamIsTTY:t,sniffFlags:r=!0}={}){let n=pk();n!==void 0&&(bu=n);let o=r?bu:n;if(o===0)return 0;if(r){if(Ke("color=16m")||Ke("color=full")||Ke("color=truecolor"))return 3;if(Ke("color=256"))return 2}if("TF_BUILD"in me&&"AGENT_NAME"in me)return 1;if(e&&!t&&o===void 0)return 0;let i=o||0;if(me.TERM==="dumb")return i;if(_u.default.platform==="win32"){let a=KS.default.release().split(".");return Number(a[0])>=10&&Number(a[2])>=10586?Number(a[2])>=14931?3:2:1}if("CI"in me)return"GITHUB_ACTIONS"in me||"GITEA_ACTIONS"in me?3:["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","BUILDKITE","DRONE"].some(a=>a in me)||me.CI_NAME==="codeship"?1:i;if("TEAMCITY_VERSION"in me)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(me.TEAMCITY_VERSION)?1:0;if(me.COLORTERM==="truecolor"||me.TERM==="xterm-kitty")return 3;if("TERM_PROGRAM"in me){let a=Number.parseInt((me.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(me.TERM_PROGRAM){case"iTerm.app":return a>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(me.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(me.TERM)||"COLORTERM"in me?1:i}function YS(e,t={}){let r=hk(e,{streamIsTTY:e&&e.isTTY,...t});return mk(r)}var gk={stdout:YS({isTTY:ep.default.isatty(1)}),stderr:YS({isTTY:ep.default.isatty(2)})},JS=gk;function QS(e,t,r){let n=e.indexOf(t);if(n===-1)return e;let o=t.length,i=0,a="";do a+=e.slice(i,n)+t+r,i=n+o,n=e.indexOf(t,i);while(n!==-1);return a+=e.slice(i),a}function XS(e,t,r,n){let o=0,i="";do{let a=e[n-1]==="\r";i+=e.slice(o,a?n-1:n)+t+(a?`\r
`:`
`)+r,o=n+1,n=e.indexOf(`
`,o)}while(n!==-1);return i+=e.slice(o),i}var{stdout:ZS,stderr:eE}=JS,tp=Symbol("GENERATOR"),Vn=Symbol("STYLER"),bi=Symbol("IS_EMPTY"),tE=["ansi","ansi","ansi256","ansi16m"],Gn=Object.create(null),yk=(e,t={})=>{if(t.level&&!(Number.isInteger(t.level)&&t.level>=0&&t.level<=3))throw new Error("The `level` option should be an integer from 0 to 3");let r=ZS?ZS.level:0;e.level=t.level===void 0?r:t.level};var Dk=e=>{let t=(...r)=>r.join(" ");return yk(t,e),Object.setPrototypeOf(t,_i.prototype),t};function _i(e){return Dk(e)}Object.setPrototypeOf(_i.prototype,Function.prototype);for(let[e,t]of Object.entries(mt))Gn[e]={get(){let r=wu(this,np(t.open,t.close,this[Vn]),this[bi]);return Object.defineProperty(this,e,{value:r}),r}};Gn.visible={get(){let e=wu(this,this[Vn],!0);return Object.defineProperty(this,"visible",{value:e}),e}};var rp=(e,t,r,...n)=>e==="rgb"?t==="ansi16m"?mt[r].ansi16m(...n):t==="ansi256"?mt[r].ansi256(mt.rgbToAnsi256(...n)):mt[r].ansi(mt.rgbToAnsi(...n)):e==="hex"?rp("rgb",t,r,...mt.hexToRgb(...n)):mt[r][e](...n),bk=["rgb","hex","ansi256"];for(let e of bk){Gn[e]={get(){let{level:r}=this;return function(...n){let o=np(rp(e,tE[r],"color",...n),mt.color.close,this[Vn]);return wu(this,o,this[bi])}}};let t="bg"+e[0].toUpperCase()+e.slice(1);Gn[t]={get(){let{level:r}=this;return function(...n){let o=np(rp(e,tE[r],"bgColor",...n),mt.bgColor.close,this[Vn]);return wu(this,o,this[bi])}}}}var _k=Object.defineProperties(()=>{},{...Gn,level:{enumerable:!0,get(){return this[tp].level},set(e){this[tp].level=e}}}),np=(e,t,r)=>{let n,o;return r===void 0?(n=e,o=t):(n=r.openAll+e,o=t+r.closeAll),{open:e,close:t,openAll:n,closeAll:o,parent:r}},wu=(e,t,r)=>{let n=(...o)=>wk(n,o.length===1?""+o[0]:o.join(" "));return Object.setPrototypeOf(n,_k),n[tp]=e,n[Vn]=t,n[bi]=r,n},wk=(e,t)=>{if(e.level<=0||!t)return e[bi]?"":t;let r=e[Vn];if(r===void 0)return t;let{openAll:n,closeAll:o}=r;if(t.includes("\x1B"))for(;r!==void 0;)t=QS(t,r.close,r.open),r=r.parent;let i=t.indexOf(`
`);return i!==-1&&(t=XS(t,o,n,i)),n+t+o};Object.defineProperties(_i.prototype,Gn);var Sk=_i(),Lz=_i({level:eE?eE.level:0});var rE=Sk;var ip=N(require("node:process"),1);var wi=N(require("node:process"),1);var Ek=(e,t,r,n)=>{if(r==="length"||r==="prototype"||r==="arguments"||r==="caller")return;let o=Object.getOwnPropertyDescriptor(e,r),i=Object.getOwnPropertyDescriptor(t,r);!vk(o,i)&&n||Object.defineProperty(e,r,i)},vk=function(e,t){return e===void 0||e.configurable||e.writable===t.writable&&e.enumerable===t.enumerable&&e.configurable===t.configurable&&(e.writable||e.value===t.value)},Fk=(e,t)=>{let r=Object.getPrototypeOf(t);r!==Object.getPrototypeOf(e)&&Object.setPrototypeOf(e,r)},Ck=(e,t)=>`/* Wrapped ${e}*/
${t}`,Tk=Object.getOwnPropertyDescriptor(Function.prototype,"toString"),Rk=Object.getOwnPropertyDescriptor(Function.prototype.toString,"name"),Ak=(e,t,r)=>{let n=r===""?"":`with ${r.trim()}() `,o=Ck.bind(null,n,t.toString());Object.defineProperty(o,"name",Rk);let{writable:i,enumerable:a,configurable:l}=Tk;Object.defineProperty(e,"toString",{value:o,writable:i,enumerable:a,configurable:l})};function op(e,t,{ignoreNonConfigurable:r=!1}={}){let{name:n}=e;for(let o of Reflect.ownKeys(t))Ek(e,t,o,r);return Fk(e,t),Ak(e,t,n),e}var Su=new WeakMap,nE=(e,t={})=>{if(typeof e!="function")throw new TypeError("Expected a function");let r,n=0,o=e.displayName||e.name||"<anonymous>",i=function(...a){if(Su.set(i,++n),n===1)r=e.apply(this,a),e=void 0;else if(t.throw===!0)throw new Error(`Function \`${o}\` can only be called once`);return r};return op(i,e),Su.set(i,n),i};nE.callCount=e=>{if(!Su.has(e))throw new Error(`The given function \`${e.name}\` is not wrapped by the \`onetime\` package`);return Su.get(e)};var oE=nE;var iE=wi.default.stderr.isTTY?wi.default.stderr:wi.default.stdout.isTTY?wi.default.stdout:void 0,$k=iE?oE(()=>{ga(()=>{iE.write("\x1B[?25h")},{alwaysLast:!0})}):()=>{},sE=$k;var Eu=!1,Hn={};Hn.show=(e=ip.default.stderr)=>{e.isTTY&&(Eu=!1,e.write("\x1B[?25h"))};Hn.hide=(e=ip.default.stderr)=>{e.isTTY&&(sE(),Eu=!0,e.write("\x1B[?25l"))};Hn.toggle=(e,t)=>{e!==void 0&&(Eu=e),Eu?Hn.show(t):Hn.hide(t)};var sp=Hn;var Ti=N(ap(),1);var cE=(e=0)=>t=>`\x1B[${t+e}m`,fE=(e=0)=>t=>`\x1B[${38+e};5;${t}m`,dE=(e=0)=>(t,r,n)=>`\x1B[${38+e};2;${t};${r};${n}m`,pe={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],gray:[90,39],grey:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgGray:[100,49],bgGrey:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}},Xz=Object.keys(pe.modifier),Pk=Object.keys(pe.color),Ok=Object.keys(pe.bgColor),Zz=[...Pk,...Ok];function Bk(){let e=new Map;for(let[t,r]of Object.entries(pe)){for(let[n,o]of Object.entries(r))pe[n]={open:`\x1B[${o[0]}m`,close:`\x1B[${o[1]}m`},r[n]=pe[n],e.set(o[0],o[1]);Object.defineProperty(pe,t,{value:r,enumerable:!1})}return Object.defineProperty(pe,"codes",{value:e,enumerable:!1}),pe.color.close="\x1B[39m",pe.bgColor.close="\x1B[49m",pe.color.ansi=cE(),pe.color.ansi256=fE(),pe.color.ansi16m=dE(),pe.bgColor.ansi=cE(10),pe.bgColor.ansi256=fE(10),pe.bgColor.ansi16m=dE(10),Object.defineProperties(pe,{rgbToAnsi256:{value(t,r,n){return t===r&&r===n?t<8?16:t>248?231:Math.round((t-8)/247*24)+232:16+36*Math.round(t/255*5)+6*Math.round(r/255*5)+Math.round(n/255*5)},enumerable:!1},hexToRgb:{value(t){let r=/[a-f\d]{6}|[a-f\d]{3}/i.exec(t.toString(16));if(!r)return[0,0,0];let[n]=r;n.length===3&&(n=[...n].map(i=>i+i).join(""));let o=Number.parseInt(n,16);return[o>>16&255,o>>8&255,o&255]},enumerable:!1},hexToAnsi256:{value:t=>pe.rgbToAnsi256(...pe.hexToRgb(t)),enumerable:!1},ansi256ToAnsi:{value(t){if(t<8)return 30+t;if(t<16)return 90+(t-8);let r,n,o;if(t>=232)r=((t-232)*10+8)/255,n=r,o=r;else{t-=16;let l=t%36;r=Math.floor(t/36)/5,n=Math.floor(l/6)/5,o=l%6/5}let i=Math.max(r,n,o)*2;if(i===0)return 30;let a=30+(Math.round(o)<<2|Math.round(n)<<1|Math.round(r));return i===2&&(a+=60),a},enumerable:!1},rgbToAnsi:{value:(t,r,n)=>pe.ansi256ToAnsi(pe.rgbToAnsi256(t,r,n)),enumerable:!1},hexToAnsi:{value:t=>pe.ansi256ToAnsi(pe.hexToAnsi256(t)),enumerable:!1}}),pe}var Ik=Bk(),ht=Ik;var Cu=N(require("node:process"),1),mE=N(require("node:os"),1),up=N(require("node:tty"),1);function Je(e,t=globalThis.Deno?globalThis.Deno.args:Cu.default.argv){let r=e.startsWith("-")?"":e.length===1?"-":"--",n=t.indexOf(r+e),o=t.indexOf("--");return n!==-1&&(o===-1||n<o)}var{env:he}=Cu.default,Fu;Je("no-color")||Je("no-colors")||Je("color=false")||Je("color=never")?Fu=0:(Je("color")||Je("colors")||Je("color=true")||Je("color=always"))&&(Fu=1);function kk(){if("FORCE_COLOR"in he)return he.FORCE_COLOR==="true"?1:he.FORCE_COLOR==="false"?0:he.FORCE_COLOR.length===0?1:Math.min(Number.parseInt(he.FORCE_COLOR,10),3)}function Mk(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function Nk(e,{streamIsTTY:t,sniffFlags:r=!0}={}){let n=kk();n!==void 0&&(Fu=n);let o=r?Fu:n;if(o===0)return 0;if(r){if(Je("color=16m")||Je("color=full")||Je("color=truecolor"))return 3;if(Je("color=256"))return 2}if("TF_BUILD"in he&&"AGENT_NAME"in he)return 1;if(e&&!t&&o===void 0)return 0;let i=o||0;if(he.TERM==="dumb")return i;if(Cu.default.platform==="win32"){let a=mE.default.release().split(".");return Number(a[0])>=10&&Number(a[2])>=10586?Number(a[2])>=14931?3:2:1}if("CI"in he)return"GITHUB_ACTIONS"in he||"GITEA_ACTIONS"in he?3:["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","BUILDKITE","DRONE"].some(a=>a in he)||he.CI_NAME==="codeship"?1:i;if("TEAMCITY_VERSION"in he)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(he.TEAMCITY_VERSION)?1:0;if(he.COLORTERM==="truecolor"||he.TERM==="xterm-kitty")return 3;if("TERM_PROGRAM"in he){let a=Number.parseInt((he.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(he.TERM_PROGRAM){case"iTerm.app":return a>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(he.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(he.TERM)||"COLORTERM"in he?1:i}function pE(e,t={}){let r=Nk(e,{streamIsTTY:e&&e.isTTY,...t});return Mk(r)}var jk={stdout:pE({isTTY:up.default.isatty(1)}),stderr:pE({isTTY:up.default.isatty(2)})},hE=jk;function gE(e,t,r){let n=e.indexOf(t);if(n===-1)return e;let o=t.length,i=0,a="";do a+=e.slice(i,n)+t+r,i=n+o,n=e.indexOf(t,i);while(n!==-1);return a+=e.slice(i),a}function yE(e,t,r,n){let o=0,i="";do{let a=e[n-1]==="\r";i+=e.slice(o,a?n-1:n)+t+(a?`\r
`:`
`)+r,o=n+1,n=e.indexOf(`
`,o)}while(n!==-1);return i+=e.slice(o),i}var{stdout:DE,stderr:bE}=hE,lp=Symbol("GENERATOR"),Yn=Symbol("STYLER"),Si=Symbol("IS_EMPTY"),_E=["ansi","ansi","ansi256","ansi16m"],Kn=Object.create(null),Lk=(e,t={})=>{if(t.level&&!(Number.isInteger(t.level)&&t.level>=0&&t.level<=3))throw new Error("The `level` option should be an integer from 0 to 3");let r=DE?DE.level:0;e.level=t.level===void 0?r:t.level};var qk=e=>{let t=(...r)=>r.join(" ");return Lk(t,e),Object.setPrototypeOf(t,Ei.prototype),t};function Ei(e){return qk(e)}Object.setPrototypeOf(Ei.prototype,Function.prototype);for(let[e,t]of Object.entries(ht))Kn[e]={get(){let r=Tu(this,fp(t.open,t.close,this[Yn]),this[Si]);return Object.defineProperty(this,e,{value:r}),r}};Kn.visible={get(){let e=Tu(this,this[Yn],!0);return Object.defineProperty(this,"visible",{value:e}),e}};var cp=(e,t,r,...n)=>e==="rgb"?t==="ansi16m"?ht[r].ansi16m(...n):t==="ansi256"?ht[r].ansi256(ht.rgbToAnsi256(...n)):ht[r].ansi(ht.rgbToAnsi(...n)):e==="hex"?cp("rgb",t,r,...ht.hexToRgb(...n)):ht[r][e](...n),Uk=["rgb","hex","ansi256"];for(let e of Uk){Kn[e]={get(){let{level:r}=this;return function(...n){let o=fp(cp(e,_E[r],"color",...n),ht.color.close,this[Yn]);return Tu(this,o,this[Si])}}};let t="bg"+e[0].toUpperCase()+e.slice(1);Kn[t]={get(){let{level:r}=this;return function(...n){let o=fp(cp(e,_E[r],"bgColor",...n),ht.bgColor.close,this[Yn]);return Tu(this,o,this[Si])}}}}var zk=Object.defineProperties(()=>{},{...Kn,level:{enumerable:!0,get(){return this[lp].level},set(e){this[lp].level=e}}}),fp=(e,t,r)=>{let n,o;return r===void 0?(n=e,o=t):(n=r.openAll+e,o=t+r.closeAll),{open:e,close:t,openAll:n,closeAll:o,parent:r}},Tu=(e,t,r)=>{let n=(...o)=>Wk(n,o.length===1?""+o[0]:o.join(" "));return Object.setPrototypeOf(n,zk),n[lp]=e,n[Yn]=t,n[Si]=r,n},Wk=(e,t)=>{if(e.level<=0||!t)return e[Si]?"":t;let r=e[Yn];if(r===void 0)return t;let{openAll:n,closeAll:o}=r;if(t.includes("\x1B"))for(;r!==void 0;)t=gE(t,r.close,r.open),r=r.parent;let i=t.indexOf(`
`);return i!==-1&&(t=yE(t,o,n,i)),n+t+o};Object.defineProperties(Ei.prototype,Kn);var Vk=Ei(),sW=Ei({level:bE?bE.level:0});var Ht=Vk;var Qe=N(require("node:process"),1);function dp(){return Qe.default.platform!=="win32"?Qe.default.env.TERM!=="linux":!!Qe.default.env.CI||!!Qe.default.env.WT_SESSION||!!Qe.default.env.TERMINUS_SUBLIME||Qe.default.env.ConEmuTask==="{cmd::Cmder}"||Qe.default.env.TERM_PROGRAM==="Terminus-Sublime"||Qe.default.env.TERM_PROGRAM==="vscode"||Qe.default.env.TERM==="xterm-256color"||Qe.default.env.TERM==="alacritty"||Qe.default.env.TERMINAL_EMULATOR==="JetBrains-JediTerm"}var Gk={info:Ht.blue("\u2139"),success:Ht.green("\u2714"),warning:Ht.yellow("\u26A0"),error:Ht.red("\u2716")},Hk={info:Ht.blue("i"),success:Ht.green("\u221A"),warning:Ht.yellow("\u203C"),error:Ht.red("\xD7")},Yk=dp()?Gk:Hk,vi=Yk;function pp({onlyFirst:e=!1}={}){let r=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?(?:\\u0007|\\u001B\\u005C|\\u009C))","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-nq-uy=><~]))"].join("|");return new RegExp(r,e?void 0:"g")}var Kk=pp();function Fi(e){if(typeof e!="string")throw new TypeError(`Expected a \`string\`, got \`${typeof e}\``);return e.replace(Kk,"")}function wE(e){return e===161||e===164||e===167||e===168||e===170||e===173||e===174||e>=176&&e<=180||e>=182&&e<=186||e>=188&&e<=191||e===198||e===208||e===215||e===216||e>=222&&e<=225||e===230||e>=232&&e<=234||e===236||e===237||e===240||e===242||e===243||e>=247&&e<=250||e===252||e===254||e===257||e===273||e===275||e===283||e===294||e===295||e===299||e>=305&&e<=307||e===312||e>=319&&e<=322||e===324||e>=328&&e<=331||e===333||e===338||e===339||e===358||e===359||e===363||e===462||e===464||e===466||e===468||e===470||e===472||e===474||e===476||e===593||e===609||e===708||e===711||e>=713&&e<=715||e===717||e===720||e>=728&&e<=731||e===733||e===735||e>=768&&e<=879||e>=913&&e<=929||e>=931&&e<=937||e>=945&&e<=961||e>=963&&e<=969||e===1025||e>=1040&&e<=1103||e===1105||e===8208||e>=8211&&e<=8214||e===8216||e===8217||e===8220||e===8221||e>=8224&&e<=8226||e>=8228&&e<=8231||e===8240||e===8242||e===8243||e===8245||e===8251||e===8254||e===8308||e===8319||e>=8321&&e<=8324||e===8364||e===8451||e===8453||e===8457||e===8467||e===8470||e===8481||e===8482||e===8486||e===8491||e===8531||e===8532||e>=8539&&e<=8542||e>=8544&&e<=8555||e>=8560&&e<=8569||e===8585||e>=8592&&e<=8601||e===8632||e===8633||e===8658||e===8660||e===8679||e===8704||e===8706||e===8707||e===8711||e===8712||e===8715||e===8719||e===8721||e===8725||e===8730||e>=8733&&e<=8736||e===8739||e===8741||e>=8743&&e<=8748||e===8750||e>=8756&&e<=8759||e===8764||e===8765||e===8776||e===8780||e===8786||e===8800||e===8801||e>=8804&&e<=8807||e===8810||e===8811||e===8814||e===8815||e===8834||e===8835||e===8838||e===8839||e===8853||e===8857||e===8869||e===8895||e===8978||e>=9312&&e<=9449||e>=9451&&e<=9547||e>=9552&&e<=9587||e>=9600&&e<=9615||e>=9618&&e<=9621||e===9632||e===9633||e>=9635&&e<=9641||e===9650||e===9651||e===9654||e===9655||e===9660||e===9661||e===9664||e===9665||e>=9670&&e<=9672||e===9675||e>=9678&&e<=9681||e>=9698&&e<=9701||e===9711||e===9733||e===9734||e===9737||e===9742||e===9743||e===9756||e===9758||e===9792||e===9794||e===9824||e===9825||e>=9827&&e<=9829||e>=9831&&e<=9834||e===9836||e===9837||e===9839||e===9886||e===9887||e===9919||e>=9926&&e<=9933||e>=9935&&e<=9939||e>=9941&&e<=9953||e===9955||e===9960||e===9961||e>=9963&&e<=9969||e===9972||e>=9974&&e<=9977||e===9979||e===9980||e===9982||e===9983||e===10045||e>=10102&&e<=10111||e>=11094&&e<=11097||e>=12872&&e<=12879||e>=57344&&e<=63743||e>=65024&&e<=65039||e===65533||e>=127232&&e<=127242||e>=127248&&e<=127277||e>=127280&&e<=127337||e>=127344&&e<=127373||e===127375||e===127376||e>=127387&&e<=127404||e>=917760&&e<=917999||e>=983040&&e<=1048573||e>=1048576&&e<=1114109}function SE(e){return e===12288||e>=65281&&e<=65376||e>=65504&&e<=65510}function EE(e){return e>=4352&&e<=4447||e===8986||e===8987||e===9001||e===9002||e>=9193&&e<=9196||e===9200||e===9203||e===9725||e===9726||e===9748||e===9749||e>=9776&&e<=9783||e>=9800&&e<=9811||e===9855||e>=9866&&e<=9871||e===9875||e===9889||e===9898||e===9899||e===9917||e===9918||e===9924||e===9925||e===9934||e===9940||e===9962||e===9970||e===9971||e===9973||e===9978||e===9981||e===9989||e===9994||e===9995||e===10024||e===10060||e===10062||e>=10067&&e<=10069||e===10071||e>=10133&&e<=10135||e===10160||e===10175||e===11035||e===11036||e===11088||e===11093||e>=11904&&e<=11929||e>=11931&&e<=12019||e>=12032&&e<=12245||e>=12272&&e<=12287||e>=12289&&e<=12350||e>=12353&&e<=12438||e>=12441&&e<=12543||e>=12549&&e<=12591||e>=12593&&e<=12686||e>=12688&&e<=12773||e>=12783&&e<=12830||e>=12832&&e<=12871||e>=12880&&e<=42124||e>=42128&&e<=42182||e>=43360&&e<=43388||e>=44032&&e<=55203||e>=63744&&e<=64255||e>=65040&&e<=65049||e>=65072&&e<=65106||e>=65108&&e<=65126||e>=65128&&e<=65131||e>=94176&&e<=94180||e===94192||e===94193||e>=94208&&e<=100343||e>=100352&&e<=101589||e>=101631&&e<=101640||e>=110576&&e<=110579||e>=110581&&e<=110587||e===110589||e===110590||e>=110592&&e<=110882||e===110898||e>=110928&&e<=110930||e===110933||e>=110948&&e<=110951||e>=110960&&e<=111355||e>=119552&&e<=119638||e>=119648&&e<=119670||e===126980||e===127183||e===127374||e>=127377&&e<=127386||e>=127488&&e<=127490||e>=127504&&e<=127547||e>=127552&&e<=127560||e===127568||e===127569||e>=127584&&e<=127589||e>=127744&&e<=127776||e>=127789&&e<=127797||e>=127799&&e<=127868||e>=127870&&e<=127891||e>=127904&&e<=127946||e>=127951&&e<=127955||e>=127968&&e<=127984||e===127988||e>=127992&&e<=128062||e===128064||e>=128066&&e<=128252||e>=128255&&e<=128317||e>=128331&&e<=128334||e>=128336&&e<=128359||e===128378||e===128405||e===128406||e===128420||e>=128507&&e<=128591||e>=128640&&e<=128709||e===128716||e>=128720&&e<=128722||e>=128725&&e<=128727||e>=128732&&e<=128735||e===128747||e===128748||e>=128756&&e<=128764||e>=128992&&e<=129003||e===129008||e>=129292&&e<=129338||e>=129340&&e<=129349||e>=129351&&e<=129535||e>=129648&&e<=129660||e>=129664&&e<=129673||e>=129679&&e<=129734||e>=129742&&e<=129756||e>=129759&&e<=129769||e>=129776&&e<=129784||e>=131072&&e<=196605||e>=196608&&e<=262141}function Jk(e){if(!Number.isSafeInteger(e))throw new TypeError(`Expected a code point, got \`${typeof e}\`.`)}function vE(e,{ambiguousAsWide:t=!1}={}){return Jk(e),SE(e)||EE(e)||t&&wE(e)?2:1}var TE=N(CE(),1),Qk=new Intl.Segmenter,Xk=/^\p{Default_Ignorable_Code_Point}$/u;function mp(e,t={}){if(typeof e!="string"||e.length===0)return 0;let{ambiguousIsNarrow:r=!0,countAnsiEscapeCodes:n=!1}=t;if(n||(e=Fi(e)),e.length===0)return 0;let o=0,i={ambiguousAsWide:!r};for(let{segment:a}of Qk.segment(e)){let l=a.codePointAt(0);if(!(l<=31||l>=127&&l<=159)&&!(l>=8203&&l<=8207||l===65279)&&!(l>=768&&l<=879||l>=6832&&l<=6911||l>=7616&&l<=7679||l>=8400&&l<=8447||l>=65056&&l<=65071)&&!(l>=55296&&l<=57343)&&!(l>=65024&&l<=65039)&&!Xk.test(a)){if((0,TE.default)().test(a)){o+=2;continue}o+=vE(l,i)}}return o}function hp({stream:e=process.stdout}={}){return!!(e&&e.isTTY&&process.env.TERM!=="dumb"&&!("CI"in process.env))}var gt=N(require("node:process"),1),Zk=3,gp=class{#e=0;start(){this.#e++,this.#e===1&&this.#r()}stop(){if(this.#e<=0)throw new Error("`stop` called more times than `start`");this.#e--,this.#e===0&&this.#t()}#r(){gt.default.platform==="win32"||!gt.default.stdin.isTTY||(gt.default.stdin.setRawMode(!0),gt.default.stdin.on("data",this.#n),gt.default.stdin.resume())}#t(){gt.default.stdin.isTTY&&(gt.default.stdin.off("data",this.#n),gt.default.stdin.pause(),gt.default.stdin.setRawMode(!1))}#n(t){t[0]===Zk&&gt.default.emit("SIGINT")}},e3=new gp,yp=e3;var t3=N(ap(),1),Dp=class{#e=0;#r=!1;#t=0;#n=-1;#a=0;#o;#s;#i;#p;#h;#c;#f;#d;#g;#u;#l;color;constructor(t){typeof t=="string"&&(t={text:t}),this.#o={color:"cyan",stream:Ci.default.stderr,discardStdin:!0,hideCursor:!0,...t},this.color=this.#o.color,this.spinner=this.#o.spinner,this.#h=this.#o.interval,this.#i=this.#o.stream,this.#c=typeof this.#o.isEnabled=="boolean"?this.#o.isEnabled:hp({stream:this.#i}),this.#f=typeof this.#o.isSilent=="boolean"?this.#o.isSilent:!1,this.text=this.#o.text,this.prefixText=this.#o.prefixText,this.suffixText=this.#o.suffixText,this.indent=this.#o.indent,Ci.default.env.NODE_ENV==="test"&&(this._stream=this.#i,this._isEnabled=this.#c,Object.defineProperty(this,"_linesToClear",{get(){return this.#e},set(r){this.#e=r}}),Object.defineProperty(this,"_frameIndex",{get(){return this.#n}}),Object.defineProperty(this,"_lineCount",{get(){return this.#t}}))}get indent(){return this.#d}set indent(t=0){if(!(t>=0&&Number.isInteger(t)))throw new Error("The `indent` option must be an integer from 0 and up");this.#d=t,this.#m()}get interval(){return this.#h??this.#s.interval??100}get spinner(){return this.#s}set spinner(t){if(this.#n=-1,this.#h=void 0,typeof t=="object"){if(t.frames===void 0)throw new Error("The given spinner must have a `frames` property");this.#s=t}else if(!bo())this.#s=Ti.default.line;else if(t===void 0)this.#s=Ti.default.dots;else if(t!=="default"&&Ti.default[t])this.#s=Ti.default[t];else throw new Error(`There is no built-in spinner named '${t}'. See https://github.com/sindresorhus/cli-spinners/blob/main/spinners.json for a full list.`)}get text(){return this.#g}set text(t=""){this.#g=t,this.#m()}get prefixText(){return this.#u}set prefixText(t=""){this.#u=t,this.#m()}get suffixText(){return this.#l}set suffixText(t=""){this.#l=t,this.#m()}get isSpinning(){return this.#p!==void 0}#y(t=this.#u,r=" "){return typeof t=="string"&&t!==""?t+r:typeof t=="function"?t()+r:""}#D(t=this.#l,r=" "){return typeof t=="string"&&t!==""?r+t:typeof t=="function"?r+t():""}#m(){let t=this.#i.columns??80,r=this.#y(this.#u,"-"),n=this.#D(this.#l,"-"),o=" ".repeat(this.#d)+r+"--"+this.#g+"--"+n;this.#t=0;for(let i of Fi(o).split(`
`))this.#t+=Math.max(1,Math.ceil(mp(i,{countAnsiEscapeCodes:!0})/t))}get isEnabled(){return this.#c&&!this.#f}set isEnabled(t){if(typeof t!="boolean")throw new TypeError("The `isEnabled` option must be a boolean");this.#c=t}get isSilent(){return this.#f}set isSilent(t){if(typeof t!="boolean")throw new TypeError("The `isSilent` option must be a boolean");this.#f=t}frame(){let t=Date.now();(this.#n===-1||t-this.#a>=this.interval)&&(this.#n=++this.#n%this.#s.frames.length,this.#a=t);let{frames:r}=this.#s,n=r[this.#n];this.color&&(n=rE[this.color](n));let o=typeof this.#u=="string"&&this.#u!==""?this.#u+" ":"",i=typeof this.text=="string"?" "+this.text:"",a=typeof this.#l=="string"&&this.#l!==""?" "+this.#l:"";return o+n+i+a}clear(){if(!this.#c||!this.#i.isTTY)return this;this.#i.cursorTo(0);for(let t=0;t<this.#e;t++)t>0&&this.#i.moveCursor(0,-1),this.#i.clearLine(1);return(this.#d||this.lastIndent!==this.#d)&&this.#i.cursorTo(this.#d),this.lastIndent=this.#d,this.#e=0,this}render(){return this.#f?this:(this.clear(),this.#i.write(this.frame()),this.#e=this.#t,this)}start(t){return t&&(this.text=t),this.#f?this:this.#c?this.isSpinning?this:(this.#o.hideCursor&&sp.hide(this.#i),this.#o.discardStdin&&Ci.default.stdin.isTTY&&(this.#r=!0,yp.start()),this.render(),this.#p=setInterval(this.render.bind(this),this.interval),this):(this.text&&this.#i.write(`- ${this.text}
`),this)}stop(){return this.#c?(clearInterval(this.#p),this.#p=void 0,this.#n=0,this.clear(),this.#o.hideCursor&&sp.show(this.#i),this.#o.discardStdin&&Ci.default.stdin.isTTY&&this.#r&&(yp.stop(),this.#r=!1),this):this}succeed(t){return this.stopAndPersist({symbol:vi.success,text:t})}fail(t){return this.stopAndPersist({symbol:vi.error,text:t})}warn(t){return this.stopAndPersist({symbol:vi.warning,text:t})}info(t){return this.stopAndPersist({symbol:vi.info,text:t})}stopAndPersist(t={}){if(this.#f)return this;let r=t.prefixText??this.#u,n=this.#y(r," "),o=t.symbol??" ",i=t.text??this.text,l=typeof i=="string"?(o?" ":"")+i:"",c=t.suffixText??this.#l,d=this.#D(c," "),p=n+o+l+d+`
`;return this.stop(),this.#i.write(p),this}};function bp(e){return new Dp(e)}var RE=(0,fe.blue)((0,fe.dim)("internal only"));function Jn(e,t,r){console.log(Yt[e]+t),typeof r?.exit<"u"&&process.exit(r.exit)}async function _r(e,t,r){if(!AE){Jn("wait",e);try{let o=await t();o&&console.log(o),Jn("success",e);return}catch(o){return Jn("error",e),r?.printError!==!1&&console.log((0,fe.red)(o.message)),o}}let n=bp({spinner:"simpleDots",prefixText:Yt.wait+e}).start();try{let o=await t();n.stop(),Jn("success",e),o&&console.log(o)}catch(o){return n.stop(),Jn("error",e),r?.printError!==!1&&console.error(o.message),o}}var Yt={wait:`\u{1F550}${(0,fe.blue)("wait")}  - `,info:`${process.env.GITHUB_ACTIONS==="true"?"\u2139\uFE0F":"\u2139\uFE0F "}${(0,fe.cyan)("info")}  - `,success:`\u2705${(0,fe.green)("ready")}  - `,warn:`${process.env.GITHUB_ACTIONS==="true"?"\u26A0\uFE0F":"\u26A0\uFE0F "}${(0,fe.yellow)("warn")}  - `,error:`\u{1F4A5}${(0,fe.red)("error")}  - `,event:`\u26A1\uFE0F${(0,fe.magenta)("event")}  - `,paymentPrompt:`\u{1F4B0}${(0,fe.yellowBright)("plan")}  - `},AE=!0;function $E(e,t){e||(Yt.wait=`${(0,fe.blue)("wait")}  - `,Yt.info=`${(0,fe.cyan)("info")}  - `,Yt.success=`${(0,fe.green)("ready")}  - `,Yt.warn=`${(0,fe.yellow)("warn")}  - `,Yt.error=`${(0,fe.red)("error")}  - `,Yt.event=`${(0,fe.magenta)("event")}  - `,Yt.paymentPrompt=`${(0,fe.yellowBright)("plan")}  - `),t&&(AE=!1)}var wr=require("@oclif/core");var Ri=N(require("node:fs")),Sp=N(require("node:path")),OE=N(require("node:os"));var xE=N(require("node:path")),PE=N(require("node:fs"));function _p(){let e;try{e=xE.resolve("package.json")}catch(r){throw new Error(`cannot resolve package manifest path: ${r}`)}let t;try{t=JSON.parse(PE.readFileSync(e,"utf8"))}catch(r){throw new Error(`cannot read package manifest: ${r}`)}return t.name=t.name.replace(/^@workaround/g,""),t}var Wr={production:{url:"https://www.raycast.com",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},staging:{url:"https://www.rayca.st",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},development:{url:"http://localhost:3000",clientID:"rOCD_LhnXFObSLu9v7788qOGu25FZDqEzGjet4Q45ZY"}}[process.env.API_ENV||"production"],r3="e69bae0ec90f5e838555",Kt={},BE;function IE(e){BE=e;try{Kt=JSON.parse(Ri.readFileSync(Sp.join(o3(),"config.json"),"utf8"))}catch(t){if(t instanceof Error&&t.code==="ENOENT")return;throw new Error(`Failed to read config file: ${t}`)}}function Qn(e){switch(e){case"raycastApiURL":return process.env.RAY_APIURL||Kt.APIURL||Wr.url;case"raycastAccessToken":return process.env.RAY_TOKEN||Kt.Token||Kt.AccessToken||"";case"raycastClientId":return process.env.RAY_CLIENT_ID||Kt.ClientID||Wr.clientID;case"githubClientId":return process.env.RAY_GithubClientID||Kt.GithubClientID||r3;case"githubAccessToken":return process.env.GITHUB_ACCESS_TOKEN||Kt.GithubAccessToken||"";case"flavorName":return process.env.RAY_Target?process.env.RAY_Target:typeof Kt.Target<"u"?Kt.Target:wp(process.platform==="win32"?"x":"release")}}function wp(e){switch(e){case"debug":return"debug";case"internal":return"internal";case"release":return"";case"x":return"x";case"x-internal":return"x-internal";case"x-development":return"x-development";case void 0:return Qn("flavorName")}}function n3(){let e=wp(BE);return e==""?"raycast":`raycast-${e}`}function o3(){let e=Sp.join(OE.default.homedir(),".config",n3());return Ri.mkdirSync(e,{recursive:!0}),e}var Ru=class extends wr.Command{static baseFlags={"exit-on-error":wr.Flags.boolean({default:!0,helpGroup:"GLOBAL",aliases:["exitOnError"],deprecateAliases:!0,summary:"Always exit with non-zero code on error",allowNo:!0}),emoji:wr.Flags.boolean({default:!1,helpGroup:"GLOBAL",summary:"Prefix output with emojis \u{1F308}"}),help:wr.Flags.boolean({default:!1,helpGroup:"GLOBAL",summary:"Show the help message for the command"}),"non-interactive":wr.Flags.boolean({char:"I",default:!1,helpGroup:"GLOBAL",summary:"Disable interactive outputs, useful for CI"}),target:wr.Flags.option({char:"t",description:"Raycast app target",helpGroup:"GLOBAL",multiple:!1,options:["debug","internal","release","x","x-development","x-internal"],hidden:!0})()};flags;args;async init(){await super.init(),process.on("SIGINT",()=>process.exit(1));let{args:t,flags:r}=await this.parse({flags:this.ctor.flags,baseFlags:super.ctor.baseFlags,enableJsonFlag:this.ctor.enableJsonFlag,args:this.ctor.args,strict:this.ctor.strict});this.flags=r,this.args=t,IE(this.flags.target),$E(this.flags.emoji,this.flags["non-interactive"])}error(t,r){return r?.message&&t instanceof Error&&(t.message=`${r.message} (${t.message})`,delete r.message),super.error(t,r)}async catch(t){return super.catch(t)}async finally(t){return super.finally(t)}};var D3=require("@oclif/core");var g3=require("@oclif/core"),JE=N(gf()),y3=N(KE());async function $p(e,t){let r;try{r=await(0,JE.default)(e,{method:t.method||"GET",headers:{"Content-Type":"application/json",Accept:"application/json",...t.token?{Authorization:`Bearer ${t.token}`}:void 0},body:t.body})}catch(n){throw new Error(`HTTP request: ${n.message}`)}if(!r.ok){switch(r.status){case 401:throw new Jt(r,"not authorized - please log in first using `npx ray login`");case 403:throw new Jt(r,"forbidden - you don't have permissions to perform the request");case 402:throw new Jt(r,"the limit of free commands has been reached")}let n=await r.text(),o;try{o=JSON.parse(n)}catch{throw new Jt(r,`HTTP error: ${r.status} - ${n}`)}throw Array.isArray(o.errors)&&o.errors.length>0?new Jt(r,`error: ${o.errors[0].status} - ${o.errors[0].title}`):new Jt(r,`HTTP error: ${r.status} - ${n}`)}return await r.json()}var Jt=class extends Error{constructor(t,r){let n=t.headers.get("X-Request-Id");n?super(`${r} (${t.url} RequestID: ${n})`):super(r),this.name="HTTPError"}};var e7=`${Wr.url}/sessions/success`,t7=`${Wr.url}/sessions/failure`;function Ou(e){let t=Qn("raycastApiURL"),r=Qn("raycastAccessToken");return $p(`${t}/api/v1/users/${e}`,{token:r})}function QE(e){let t=Qn("raycastApiURL"),r=Qn("raycastAccessToken");return $p(`${t}/api/v1/organizations/${e}`,{token:r})}var Iu=class e extends Ru{static description="Validate the extension manifest and metadata, and lint its source code";static flags={fix:Bu.Flags.boolean({char:"f",default:!1,description:"Attempt to fix linting issues"}),relaxed:Bu.Flags.boolean({char:"r",description:"Use relaxed linting mode to skip validation of: package.json schema, icons and metadata.",default:!1}),schema:Bu.Flags.string({char:"s",description:"Path to JSON schema for package.json validation "+RE,default:`${Wr.url}/schemas/extension.json`,hidden:!0})};async run(){let{flags:t}=await this.parse(e);await tv({skipOwner:!1,fix:t.fix,relaxed:t.relaxed,skipLockFiles:process.env.CI?.toLowerCase()!=="true",schema:t.schema})||this.error("linting issues found (tip: try re-running with '--fix')")}};async function tv(e){let t=!0;try{let r=_p();t=await b3(r,e),t=await _3(r,e)&&t,t=await w3(r,e)&&t,t=await S3(r,e)&&t}catch(r){console.error("error reading manifest:",r),t=!1}return t=await E3(e)&&t,t=await v3(e)&&t,t}async function b3(e,t){return await _r("validate package.json file",async()=>{let n=[],o=[];try{await Ou(e.author)}catch(i){i.message.includes("Couldn't find User")?n.push({pointer:"/author",message:`Invalid author "${e.author}". Use the Raycast username`,level:"error"}):n.push({pointer:"/author",message:`Invalid author "${e.author}". ${i.message}`,level:"error"})}if(await Promise.all((e.contributors||[]).map(async(i,a)=>{try{await Ou(i)}catch(l){l.message.includes("Couldn't find User")?n.push({pointer:"/contributors/"+a,message:`Invalid contributor "${i}". Use the Raycast username`,level:"error"}):n.push({pointer:"/contributors/"+a,message:`Invalid contributor "${i}". ${l.message}`,level:"error"})}i===e.author&&n.push({pointer:"/contributors/"+a,message:`Author "${i}" should not be added as a contributor`,level:"error"})})),await Promise.all((e.pastContributors||[]).map(async(i,a)=>{try{await Ou(i)}catch(l){l.message.includes("Couldn't find User")?n.push({pointer:"/pastContributors/"+a,message:`Invalid contributor "${i}". Use the Raycast username`,level:"error"}):n.push({pointer:"/pastContributors/"+a,message:`Invalid contributor "${i}". ${l.message}`,level:"error"})}})),e.owner&&!t.skipOwner)try{await QE(e.owner)}catch(i){i.message.includes("Couldn't find Organization")?n.push({pointer:"/owner",message:`Invalid owner "${e.owner}". Use the Raycast organization handle`,level:"error"}):n.push({pointer:"/owner",message:`Invalid owner "${e.owner}". ${i.message}`,level:"error"})}if(!t.relaxed){let i=new ev.default({allErrors:!0});try{let a=await(await(0,ZE.default)(t.schema)).json();delete a.$schema;let l=i.compile(a);l(e)||n.push(...(l.errors||[]).map(c=>({pointer:c.instancePath,message:c.message||"",level:"error"})))}catch(a){n.push({message:`Failed compiling JSON schema: ${a.message}`,level:"error"})}e.name.startsWith("@workaround/")&&o.push({pointer:"/name",message:`"@workaround/" is a reserved prefix for internal extensions in companies that have a policy regarding namespaced package.json names. This isn't valid for public extensions`,level:"warning"}),(0,Vr.titleCase)(e.title)!==e.title&&o.push({pointer:"/title",message:`Extension's title has to be Title Cased. Expected "${(0,Vr.titleCase)(e.title)}"`,level:"warning"}),Zn(e.title)||o.push({pointer:"/title",message:'Raycast is spelled "Raycast"',level:"warning"}),Zn(e.description)||o.push({pointer:"/description",message:'Raycast is spelled "Raycast"',level:"warning"}),e.commands.forEach((a,l)=>{(0,Vr.titleCase)(a.title)!==a.title&&o.push({pointer:`/commands/${l}/title`,message:`Command's title has to be Title Cased. Expected "${(0,Vr.titleCase)(a.title)}"`,level:"warning"}),Zn(a.title)||o.push({pointer:`/commands/${l}/title`,message:'Raycast is spelled "Raycast"',level:"warning"}),Zn(a.description)||o.push({pointer:`/commands/${l}/description`,message:'Raycast is spelled "Raycast"',level:"warning"})}),e.tools?.forEach((a,l)=>{(0,Vr.titleCase)(a.title)!==a.title&&o.push({pointer:`/tools/${l}/title`,message:`Tool's title has to be Title Cased. Expected "${(0,Vr.titleCase)(a.title)}"`,level:"warning"}),Zn(a.title)||o.push({pointer:`/tools/${l}/title`,message:'Raycast is spelled "Raycast"',level:"warning"}),Zn(a.description)||o.push({pointer:`/tools/${l}/description`,message:'Raycast is spelled "Raycast"',level:"warning"})})}if(n.length>0)throw new Error(`${_e.resolve("package.json")}
  ${Qt(n.concat(o))}`);o.length>0&&console.log((0,ku.yellow)(`${_e.resolve("package.json")}
  ${Qt(n.concat(o))}`))})===void 0}async function _3(e,t){if(t.relaxed||t.skipLockFiles)return!0;let r=await _r("validate package-lock.json",async()=>{let n=_e.resolve("package-lock.json"),o;try{o=require(n)}catch{throw new Error("missing package-lock.json. Run 'npm install' to generate the file")}if(o.lockfileVersion===1)throw new Error(`${n}
  4:20  error  Wrong 'lockfileVersion'. Version '${o.lockfileVersion}' is unsupported. Update to npm v7 or higher`);let a=/"resolved":\s*"https:\/\/((?!(registry.npmjs.org)|(npm.jsr.io)|(cdn.sheetjs.com))[^/]*)\/.*",/gi.exec(JSON.stringify(o,null,2));if(a)throw new Error(`${n}
        error  Found references to a non-official npm registry (${a[1]}). Remove them`)});return r?!1:(r=await _r("validate other lock files",async()=>{if(Xe.existsSync("yarn.lock"))throw new Error("yarn is not supported. Remove the `yarn.lock` file and run `npm install` instead");if(Xe.existsSync("pnpm-lock.yaml"))throw new Error("pnpm is not supported. Remove the `pnpm-lock.yaml` file and run `npm install` instead");if(Xe.existsSync("shrinkwrap.json"))throw new Error("shrinkwrap is not supported. Remove the `shrinkwrap.json` file and run `npm install` instead");if(Xe.existsSync("npm-shrinkwrap.json"))throw new Error("shrinkwrap is not supported. Remove the `npm-shrinkwrap.json` file and run `npm install` instead")}),r===void 0)}async function w3(e,t){return t.relaxed?!0:await _r("validate extension icons",async()=>{let n=[],o=_e.resolve(_e.join("assets",e.icon)),i=Gr(o,{type:"icon"});if(i&&n.push(`${o}
  ${Qt([i])}`),o=_e.resolve(_e.join("assets",e.icon.replace(/.png$/,"@dark.png"))),i=Gr(o,{type:"icon",isOptional:!0}),i&&n.push(`${o}
  ${Qt([i])}`),e.commands.forEach(a=>{a.icon&&(o=_e.resolve(_e.join("assets",a.icon)),i=Gr(o,{type:"icon"}),i&&n.push(`${o}
  ${Qt([i])}`),o=_e.resolve(_e.join("assets",a.icon.replace(/.png$/,"@dark.png"))),i=Gr(o,{type:"icon",isOptional:!0}),i&&n.push(`${o}
  ${Qt([i])}`))}),e.tools?.forEach(a=>{a.icon&&(o=_e.resolve(_e.join("assets",a.icon)),i=Gr(o,{type:"icon"}),i&&n.push(`${o}
  ${Qt([i])}`),o=_e.resolve(_e.join("assets",a.icon.replace(/.png$/,"@dark.png"))),i=Gr(o,{type:"icon",isOptional:!0}),i&&n.push(`${o}
  ${Qt([i])}`))}),n.length)throw new Error(n.join(`

`))})===void 0}async function S3(e,t){return t.relaxed||!Xe.existsSync("metadata")?!0:await _r("validate extension metadata",async()=>{Xe.readdirSync("metadata").forEach(n=>{let o=_e.resolve(_e.join("metadata",n));if(n.endsWith(".png")){let i=Gr(o,{type:"metadata"});if(i)throw new Error(`${o}
  ${Qt([i])}`)}})})===void 0}function Gr(e,t){if(!Xe.existsSync(e))return t.isOptional?void 0:{message:"Missing file in assets folder",level:"error"};if(!e.endsWith(".png"))return{message:"Use png format for icon",level:"error"};try{let r=Xe.readFileSync(e),n=Buffer.from([137,80,78,71,13,10,26,10]);if(r.compare(n,0,8,0,8)!==0)return{message:"Wrong image format, use a proper png file",level:"error"};let o=r.subarray(8,33);if(o.toString("ascii",4,8)!=="IHDR")return{message:"Could not read png file dimensions",level:"error"};let i=o.readUInt32BE(8),a=o.readUInt32BE(12);if(t.type==="icon"){if(i!==512||a!==512)return{message:`Wrong image size: ${i} x ${a} pixels. Required size is 512 x 512 pixels`,level:"error"}}else if(i!==2e3||a!==1250)return i===2e3/2&&a===1250/2?{message:`Wrong image size: ${i} x ${a} pixels. Required size is 2000 x 1250 pixels. Make sure to use a retina screen when taking the screenshot`,level:"error"}:{message:`Wrong image size: ${i} x ${a} pixels. Required size is 2000 x 1250 pixels`,level:"error"}}catch{return{message:"could not read file",level:"error"}}}async function E3(e){return await _r("run ESLint",async()=>{let r=await va("eslint",["src/**"].concat(e.fix?["--fix"]:[]),{preferLocal:!0,reject:!1,all:!0});if(r.failed)throw r.all?new Error(r.all):new Error(r.message);if(r.all.trim())return(0,ku.yellow)(r.all.trim())})===void 0}async function v3(e){let t=await va("prettier",["-v"],{preferLocal:!0,reject:!1});return await _r(`run Prettier ${t.stdout}`,async()=>{let n=await va("prettier",["src/**","!**/.DS_Store"].concat(e.fix?["--loglevel","warn","--write"]:["--list-different"]),{preferLocal:!0,reject:!1,all:!0});if(n.failed){if(!n.all)throw new Error(n.message);if(e.fix)throw new Error(n.all.trim());let i=n.all.trim().split(`
`).filter(Boolean).map(a=>`${_e.resolve(a)}
  error  Code style issues found. Please run Prettier ${t.stdout} (ray lint --fix).`);throw new Error(i.join(`
`))}})===void 0}function Qt(e){if(e.every(i=>!i.pointer))return e.map(i=>`  ${i.level}  ${i.message}`).join(`
  `);let t=XE.default.parse(Xe.readFileSync("package.json","utf8")).pointers,r=0,n=0;e.forEach(i=>{if(!i.pointer||!t[i.pointer])return;let a=t[i.pointer].value;(a.line+1).toString().length>r&&(r=(a.line+1).toString().length),a.column.toString().length>n&&(n=a.column.toString().length)});let o=[];return e.forEach(i=>{let a="";if(!i.pointer||!t[i.pointer])a=`${"".padStart(r+n+1)}  ${i.level}  ${i.message}`;else{let l=t[i.pointer].value;a=`${(l.line+1).toString().padStart(r)}:${l.column.toString().padStart(n)}  ${i.level}  ${i.message}`}i.level==="warning"?o.push((0,ku.yellow)(a)):o.push(a)}),o.join(`
  `)}function Zn(e){return!e.includes("RayCast")&&!e.includes("raycast")}0&&(module.exports={lint});
/*! Bundled license information:

node-fetch-cjs/dist/index.js:
  (*! fetch-blob. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
  (*! formdata-polyfill. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
  (*! node-domexception. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
*/
