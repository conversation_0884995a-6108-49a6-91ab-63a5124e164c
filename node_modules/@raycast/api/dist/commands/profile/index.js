"use strict";var Rs=Object.create;var cr=Object.defineProperty;var Ts=Object.getOwnPropertyDescriptor;var Es=Object.getOwnPropertyNames;var Cs=Object.getPrototypeOf,As=Object.prototype.hasOwnProperty;var Qe=(r,o)=>()=>(o||r((o={exports:{}}).exports,o),o.exports),Ps=(r,o)=>{for(var a in o)cr(r,a,{get:o[a],enumerable:!0})},Go=(r,o,a,i)=>{if(o&&typeof o=="object"||typeof o=="function")for(let l of Es(o))!As.call(r,l)&&l!==a&&cr(r,l,{get:()=>o[l],enumerable:!(i=Ts(o,l))||i.enumerable});return r};var Je=(r,o,a)=>(a=r!=null?Rs(Cs(r)):{},Go(o||!r||!r.__esModule?cr(a,"default",{value:r,enumerable:!0}):a,r)),ks=r=>Go(cr({},"__esModule",{value:!0}),r);var Ea=Qe(Ta=>{var Bs=Object.create,_r=Object.defineProperty,Os=Object.getOwnPropertyDescriptor,Ds=Object.getOwnPropertyNames,qs=Object.getPrototypeOf,Ws=Object.prototype.hasOwnProperty,ua=r=>_r(r,"__esModule",{value:!0}),Ct=(r,o)=>function(){return r&&(o=(0,r[Object.keys(r)[0]])(r=0)),o},_n=(r,o)=>function(){return o||(0,r[Object.keys(r)[0]])((o={exports:{}}).exports,o),o.exports},ca=(r,o)=>{ua(r);for(var a in o)_r(r,a,{get:o[a],enumerable:!0})},Fs=(r,o,a)=>{if(o&&typeof o=="object"||typeof o=="function")for(let i of Ds(o))!Ws.call(r,i)&&i!=="default"&&_r(r,i,{get:()=>o[i],enumerable:!(a=Os(o,i))||a.enumerable});return r},N=r=>Fs(ua(_r(r!=null?Bs(qs(r)):{},"default",r&&r.__esModule&&"default"in r?{get:()=>r.default,enumerable:!0}:{value:r,enumerable:!0})),r),Is=_n({"node_modules/web-streams-polyfill/dist/ponyfill.es2018.js"(r,o){(function(a,i){typeof r=="object"&&typeof o<"u"?i(r):typeof define=="function"&&define.amd?define(["exports"],i):(a=typeof globalThis<"u"?globalThis:a||self,i(a.WebStreamsPolyfill={}))})(r,function(a){"use strict";let i=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Symbol:e=>`Symbol(${e})`;function l(){}function c(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global}let m=c();function h(e){return typeof e=="object"&&e!==null||typeof e=="function"}let g=l,y=Promise,I=Promise.prototype.then,j=Promise.resolve.bind(y),L=Promise.reject.bind(y);function S(e){return new y(e)}function p(e){return j(e)}function b(e){return L(e)}function P(e,t,n){return I.call(e,t,n)}function v(e,t,n){P(P(e,t,n),void 0,g)}function M(e,t){v(e,t)}function U(e,t){v(e,void 0,t)}function O(e,t,n){return P(e,t,n)}function W(e){P(e,void 0,g)}let x=(()=>{let e=m&&m.queueMicrotask;if(typeof e=="function")return e;let t=p(void 0);return n=>P(t,n)})();function ke(e,t,n){if(typeof e!="function")throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,n)}function ue(e,t,n){try{return p(ke(e,t,n))}catch(s){return b(s)}}let On=16384;class X{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(t){let n=this._back,s=n;n._elements.length===On-1&&(s={_elements:[],_next:void 0}),n._elements.push(t),s!==n&&(this._back=s,n._next=s),++this._size}shift(){let t=this._front,n=t,s=this._cursor,u=s+1,f=t._elements,d=f[s];return u===On&&(n=t._next,u=0),--this._size,this._cursor=u,t!==n&&(this._front=n),f[s]=void 0,d}forEach(t){let n=this._cursor,s=this._front,u=s._elements;for(;(n!==u.length||s._next!==void 0)&&!(n===u.length&&(s=s._next,u=s._elements,n=0,u.length===0));)t(u[n]),++n}peek(){let t=this._front,n=this._cursor;return t._elements[n]}}function Dn(e,t){e._ownerReadableStream=t,t._reader=e,t._state==="readable"?kr(e):t._state==="closed"?Xa(e):qn(e,t._storedError)}function Pr(e,t){let n=e._ownerReadableStream;return te(n,t)}function ce(e){e._ownerReadableStream._state==="readable"?Br(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):Za(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),e._ownerReadableStream._reader=void 0,e._ownerReadableStream=void 0}function Le(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function kr(e){e._closedPromise=S((t,n)=>{e._closedPromise_resolve=t,e._closedPromise_reject=n})}function qn(e,t){kr(e),Br(e,t)}function Xa(e){kr(e),Wn(e)}function Br(e,t){e._closedPromise_reject!==void 0&&(W(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function Za(e,t){qn(e,t)}function Wn(e){e._closedPromise_resolve!==void 0&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}let Fn=i("[[AbortSteps]]"),In=i("[[ErrorSteps]]"),Or=i("[[CancelSteps]]"),Dr=i("[[PullSteps]]"),Ln=Number.isFinite||function(e){return typeof e=="number"&&isFinite(e)},ei=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function ti(e){return typeof e=="object"||typeof e=="function"}function fe(e,t){if(e!==void 0&&!ti(e))throw new TypeError(`${t} is not an object.`)}function Z(e,t){if(typeof e!="function")throw new TypeError(`${t} is not a function.`)}function ri(e){return typeof e=="object"&&e!==null||typeof e=="function"}function xn(e,t){if(!ri(e))throw new TypeError(`${t} is not an object.`)}function de(e,t,n){if(e===void 0)throw new TypeError(`Parameter ${t} is required in '${n}'.`)}function qr(e,t,n){if(e===void 0)throw new TypeError(`${t} is required in '${n}'.`)}function Wr(e){return Number(e)}function $n(e){return e===0?0:e}function ni(e){return $n(ei(e))}function zn(e,t){let s=Number.MAX_SAFE_INTEGER,u=Number(e);if(u=$n(u),!Ln(u))throw new TypeError(`${t} is not a finite number`);if(u=ni(u),u<0||u>s)throw new TypeError(`${t} is outside the accepted range of 0 to ${s}, inclusive`);return!Ln(u)||u===0?0:u}function Fr(e,t){if(!Re(e))throw new TypeError(`${t} is not a ReadableStream.`)}function xe(e){return new at(e)}function jn(e,t){e._reader._readRequests.push(t)}function Ir(e,t,n){let u=e._reader._readRequests.shift();n?u._closeSteps():u._chunkSteps(t)}function Dt(e){return e._reader._readRequests.length}function Mn(e){let t=e._reader;return!(t===void 0||!_e(t))}class at{constructor(t){if(de(t,1,"ReadableStreamDefaultReader"),Fr(t,"First parameter"),Te(t))throw new TypeError("This stream has already been locked for exclusive reading by another reader");Dn(this,t),this._readRequests=new X}get closed(){return _e(this)?this._closedPromise:b(qt("closed"))}cancel(t=void 0){return _e(this)?this._ownerReadableStream===void 0?b(Le("cancel")):Pr(this,t):b(qt("cancel"))}read(){if(!_e(this))return b(qt("read"));if(this._ownerReadableStream===void 0)return b(Le("read from"));let t,n,s=S((f,d)=>{t=f,n=d});return it(this,{_chunkSteps:f=>t({value:f,done:!1}),_closeSteps:()=>t({value:void 0,done:!0}),_errorSteps:f=>n(f)}),s}releaseLock(){if(!_e(this))throw qt("releaseLock");if(this._ownerReadableStream!==void 0){if(this._readRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");ce(this)}}}Object.defineProperties(at.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(at.prototype,i.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});function _e(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_readRequests")?!1:e instanceof at}function it(e,t){let n=e._ownerReadableStream;n._disturbed=!0,n._state==="closed"?t._closeSteps():n._state==="errored"?t._errorSteps(n._storedError):n._readableStreamController[Dr](t)}function qt(e){return new TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}let Un=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype);class Nn{constructor(t,n){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=t,this._preventCancel=n}next(){let t=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?O(this._ongoingPromise,t,t):t(),this._ongoingPromise}return(t){let n=()=>this._returnSteps(t);return this._ongoingPromise?O(this._ongoingPromise,n,n):n()}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});let t=this._reader;if(t._ownerReadableStream===void 0)return b(Le("iterate"));let n,s,u=S((d,_)=>{n=d,s=_});return it(t,{_chunkSteps:d=>{this._ongoingPromise=void 0,x(()=>n({value:d,done:!1}))},_closeSteps:()=>{this._ongoingPromise=void 0,this._isFinished=!0,ce(t),n({value:void 0,done:!0})},_errorSteps:d=>{this._ongoingPromise=void 0,this._isFinished=!0,ce(t),s(d)}}),u}_returnSteps(t){if(this._isFinished)return Promise.resolve({value:t,done:!0});this._isFinished=!0;let n=this._reader;if(n._ownerReadableStream===void 0)return b(Le("finish iterating"));if(!this._preventCancel){let s=Pr(n,t);return ce(n),O(s,()=>({value:t,done:!0}))}return ce(n),p({value:t,done:!0})}}let Hn={next(){return Vn(this)?this._asyncIteratorImpl.next():b(Yn("next"))},return(e){return Vn(this)?this._asyncIteratorImpl.return(e):b(Yn("return"))}};Un!==void 0&&Object.setPrototypeOf(Hn,Un);function oi(e,t){let n=xe(e),s=new Nn(n,t),u=Object.create(Hn);return u._asyncIteratorImpl=s,u}function Vn(e){if(!h(e)||!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof Nn}catch{return!1}}function Yn(e){return new TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}let Gn=Number.isNaN||function(e){return e!==e};function st(e){return e.slice()}function Qn(e,t,n,s,u){new Uint8Array(e).set(new Uint8Array(n,s,u),t)}function Il(e){return e}function Wt(e){return!1}function Jn(e,t,n){if(e.slice)return e.slice(t,n);let s=n-t,u=new ArrayBuffer(s);return Qn(u,0,e,t,s),u}function ai(e){return!(typeof e!="number"||Gn(e)||e<0)}function Kn(e){let t=Jn(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(t)}function Lr(e){let t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function xr(e,t,n){if(!ai(n)||n===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");e._queue.push({value:t,size:n}),e._queueTotalSize+=n}function ii(e){return e._queue.peek().value}function Se(e){e._queue=new X,e._queueTotalSize=0}class lt{constructor(){throw new TypeError("Illegal constructor")}get view(){if(!$r(this))throw Ur("view");return this._view}respond(t){if(!$r(this))throw Ur("respond");if(de(t,1,"respond"),t=zn(t,"First parameter"),this._associatedReadableByteStreamController===void 0)throw new TypeError("This BYOB request has been invalidated");Wt(this._view.buffer),$t(this._associatedReadableByteStreamController,t)}respondWithNewView(t){if(!$r(this))throw Ur("respondWithNewView");if(de(t,1,"respondWithNewView"),!ArrayBuffer.isView(t))throw new TypeError("You can only respond with array buffer views");if(this._associatedReadableByteStreamController===void 0)throw new TypeError("This BYOB request has been invalidated");Wt(t.buffer),zt(this._associatedReadableByteStreamController,t)}}Object.defineProperties(lt.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(lt.prototype,i.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class $e{constructor(){throw new TypeError("Illegal constructor")}get byobRequest(){if(!Be(this))throw ct("byobRequest");return Mr(this)}get desiredSize(){if(!Be(this))throw ct("desiredSize");return ao(this)}close(){if(!Be(this))throw ct("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");let t=this._controlledReadableByteStream._state;if(t!=="readable")throw new TypeError(`The stream (in ${t} state) is not in the readable state and cannot be closed`);ut(this)}enqueue(t){if(!Be(this))throw ct("enqueue");if(de(t,1,"enqueue"),!ArrayBuffer.isView(t))throw new TypeError("chunk must be an array buffer view");if(t.byteLength===0)throw new TypeError("chunk must have non-zero byteLength");if(t.buffer.byteLength===0)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");let n=this._controlledReadableByteStream._state;if(n!=="readable")throw new TypeError(`The stream (in ${n} state) is not in the readable state and cannot be enqueued to`);xt(this,t)}error(t=void 0){if(!Be(this))throw ct("error");ee(this,t)}[Or](t){Xn(this),Se(this);let n=this._cancelAlgorithm(t);return Lt(this),n}[Dr](t){let n=this._controlledReadableByteStream;if(this._queueTotalSize>0){let u=this._queue.shift();this._queueTotalSize-=u.byteLength,ro(this);let f=new Uint8Array(u.buffer,u.byteOffset,u.byteLength);t._chunkSteps(f);return}let s=this._autoAllocateChunkSize;if(s!==void 0){let u;try{u=new ArrayBuffer(s)}catch(d){t._errorSteps(d);return}let f={buffer:u,bufferByteLength:s,byteOffset:0,byteLength:s,bytesFilled:0,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(f)}jn(n,t),Oe(this)}}Object.defineProperties($e.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty($e.prototype,i.toStringTag,{value:"ReadableByteStreamController",configurable:!0});function Be(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")?!1:e instanceof $e}function $r(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")?!1:e instanceof lt}function Oe(e){if(!ci(e))return;if(e._pulling){e._pullAgain=!0;return}e._pulling=!0;let n=e._pullAlgorithm();v(n,()=>{e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,Oe(e))},s=>{ee(e,s)})}function Xn(e){jr(e),e._pendingPullIntos=new X}function zr(e,t){let n=!1;e._state==="closed"&&(n=!0);let s=Zn(t);t.readerType==="default"?Ir(e,s,n):hi(e,s,n)}function Zn(e){let t=e.bytesFilled,n=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/n)}function Ft(e,t,n,s){e._queue.push({buffer:t,byteOffset:n,byteLength:s}),e._queueTotalSize+=s}function eo(e,t){let n=t.elementSize,s=t.bytesFilled-t.bytesFilled%n,u=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),f=t.bytesFilled+u,d=f-f%n,_=u,E=!1;d>s&&(_=d-t.bytesFilled,E=!0);let A=e._queue;for(;_>0;){let k=A.peek(),B=Math.min(_,k.byteLength),$=t.byteOffset+t.bytesFilled;Qn(t.buffer,$,k.buffer,k.byteOffset,B),k.byteLength===B?A.shift():(k.byteOffset+=B,k.byteLength-=B),e._queueTotalSize-=B,to(e,B,t),_-=B}return E}function to(e,t,n){n.bytesFilled+=t}function ro(e){e._queueTotalSize===0&&e._closeRequested?(Lt(e),yt(e._controlledReadableByteStream)):Oe(e)}function jr(e){e._byobRequest!==null&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function no(e){for(;e._pendingPullIntos.length>0;){if(e._queueTotalSize===0)return;let t=e._pendingPullIntos.peek();eo(e,t)&&(It(e),zr(e._controlledReadableByteStream,t))}}function si(e,t,n){let s=e._controlledReadableByteStream,u=1;t.constructor!==DataView&&(u=t.constructor.BYTES_PER_ELEMENT);let f=t.constructor,d=t.buffer,_={buffer:d,bufferByteLength:d.byteLength,byteOffset:t.byteOffset,byteLength:t.byteLength,bytesFilled:0,elementSize:u,viewConstructor:f,readerType:"byob"};if(e._pendingPullIntos.length>0){e._pendingPullIntos.push(_),lo(s,n);return}if(s._state==="closed"){let E=new f(_.buffer,_.byteOffset,0);n._closeSteps(E);return}if(e._queueTotalSize>0){if(eo(e,_)){let E=Zn(_);ro(e),n._chunkSteps(E);return}if(e._closeRequested){let E=new TypeError("Insufficient bytes to fill elements in the given buffer");ee(e,E),n._errorSteps(E);return}}e._pendingPullIntos.push(_),lo(s,n),Oe(e)}function li(e,t){let n=e._controlledReadableByteStream;if(Nr(n))for(;uo(n)>0;){let s=It(e);zr(n,s)}}function ui(e,t,n){if(to(e,t,n),n.bytesFilled<n.elementSize)return;It(e);let s=n.bytesFilled%n.elementSize;if(s>0){let u=n.byteOffset+n.bytesFilled,f=Jn(n.buffer,u-s,u);Ft(e,f,0,f.byteLength)}n.bytesFilled-=s,zr(e._controlledReadableByteStream,n),no(e)}function oo(e,t){let n=e._pendingPullIntos.peek();jr(e),e._controlledReadableByteStream._state==="closed"?li(e):ui(e,t,n),Oe(e)}function It(e){return e._pendingPullIntos.shift()}function ci(e){let t=e._controlledReadableByteStream;return t._state!=="readable"||e._closeRequested||!e._started?!1:!!(Mn(t)&&Dt(t)>0||Nr(t)&&uo(t)>0||ao(e)>0)}function Lt(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function ut(e){let t=e._controlledReadableByteStream;if(!(e._closeRequested||t._state!=="readable")){if(e._queueTotalSize>0){e._closeRequested=!0;return}if(e._pendingPullIntos.length>0&&e._pendingPullIntos.peek().bytesFilled>0){let s=new TypeError("Insufficient bytes to fill elements in the given buffer");throw ee(e,s),s}Lt(e),yt(t)}}function xt(e,t){let n=e._controlledReadableByteStream;if(e._closeRequested||n._state!=="readable")return;let s=t.buffer,u=t.byteOffset,f=t.byteLength,d=s;if(e._pendingPullIntos.length>0){let _=e._pendingPullIntos.peek();Wt(_.buffer),_.buffer=_.buffer}if(jr(e),Mn(n))if(Dt(n)===0)Ft(e,d,u,f);else{e._pendingPullIntos.length>0&&It(e);let _=new Uint8Array(d,u,f);Ir(n,_,!1)}else Nr(n)?(Ft(e,d,u,f),no(e)):Ft(e,d,u,f);Oe(e)}function ee(e,t){let n=e._controlledReadableByteStream;n._state==="readable"&&(Xn(e),Se(e),Lt(e),Wo(n,t))}function Mr(e){if(e._byobRequest===null&&e._pendingPullIntos.length>0){let t=e._pendingPullIntos.peek(),n=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),s=Object.create(lt.prototype);di(s,e,n),e._byobRequest=s}return e._byobRequest}function ao(e){let t=e._controlledReadableByteStream._state;return t==="errored"?null:t==="closed"?0:e._strategyHWM-e._queueTotalSize}function $t(e,t){let n=e._pendingPullIntos.peek();if(e._controlledReadableByteStream._state==="closed"){if(t!==0)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(t===0)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(n.bytesFilled+t>n.byteLength)throw new RangeError("bytesWritten out of range")}n.buffer=n.buffer,oo(e,t)}function zt(e,t){let n=e._pendingPullIntos.peek();if(e._controlledReadableByteStream._state==="closed"){if(t.byteLength!==0)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(t.byteLength===0)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(n.byteOffset+n.bytesFilled!==t.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(n.bufferByteLength!==t.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(n.bytesFilled+t.byteLength>n.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");let u=t.byteLength;n.buffer=t.buffer,oo(e,u)}function io(e,t,n,s,u,f,d){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=null,t._queue=t._queueTotalSize=void 0,Se(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=f,t._pullAlgorithm=s,t._cancelAlgorithm=u,t._autoAllocateChunkSize=d,t._pendingPullIntos=new X,e._readableStreamController=t;let _=n();v(p(_),()=>{t._started=!0,Oe(t)},E=>{ee(t,E)})}function fi(e,t,n){let s=Object.create($e.prototype),u=()=>{},f=()=>p(void 0),d=()=>p(void 0);t.start!==void 0&&(u=()=>t.start(s)),t.pull!==void 0&&(f=()=>t.pull(s)),t.cancel!==void 0&&(d=E=>t.cancel(E));let _=t.autoAllocateChunkSize;if(_===0)throw new TypeError("autoAllocateChunkSize must be greater than 0");io(e,s,u,f,d,n,_)}function di(e,t,n){e._associatedReadableByteStreamController=t,e._view=n}function Ur(e){return new TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}function ct(e){return new TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}function so(e){return new ft(e)}function lo(e,t){e._reader._readIntoRequests.push(t)}function hi(e,t,n){let u=e._reader._readIntoRequests.shift();n?u._closeSteps(t):u._chunkSteps(t)}function uo(e){return e._reader._readIntoRequests.length}function Nr(e){let t=e._reader;return!(t===void 0||!De(t))}class ft{constructor(t){if(de(t,1,"ReadableStreamBYOBReader"),Fr(t,"First parameter"),Te(t))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!Be(t._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");Dn(this,t),this._readIntoRequests=new X}get closed(){return De(this)?this._closedPromise:b(jt("closed"))}cancel(t=void 0){return De(this)?this._ownerReadableStream===void 0?b(Le("cancel")):Pr(this,t):b(jt("cancel"))}read(t){if(!De(this))return b(jt("read"));if(!ArrayBuffer.isView(t))return b(new TypeError("view must be an array buffer view"));if(t.byteLength===0)return b(new TypeError("view must have non-zero byteLength"));if(t.buffer.byteLength===0)return b(new TypeError("view's buffer must have non-zero byteLength"));if(Wt(t.buffer),this._ownerReadableStream===void 0)return b(Le("read from"));let n,s,u=S((d,_)=>{n=d,s=_});return co(this,t,{_chunkSteps:d=>n({value:d,done:!1}),_closeSteps:d=>n({value:d,done:!0}),_errorSteps:d=>s(d)}),u}releaseLock(){if(!De(this))throw jt("releaseLock");if(this._ownerReadableStream!==void 0){if(this._readIntoRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");ce(this)}}}Object.defineProperties(ft.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(ft.prototype,i.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});function De(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")?!1:e instanceof ft}function co(e,t,n){let s=e._ownerReadableStream;s._disturbed=!0,s._state==="errored"?n._errorSteps(s._storedError):si(s._readableStreamController,t,n)}function jt(e){return new TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}function dt(e,t){let{highWaterMark:n}=e;if(n===void 0)return t;if(Gn(n)||n<0)throw new RangeError("Invalid highWaterMark");return n}function Mt(e){let{size:t}=e;return t||(()=>1)}function Ut(e,t){fe(e,t);let n=e?.highWaterMark,s=e?.size;return{highWaterMark:n===void 0?void 0:Wr(n),size:s===void 0?void 0:mi(s,`${t} has member 'size' that`)}}function mi(e,t){return Z(e,t),n=>Wr(e(n))}function pi(e,t){fe(e,t);let n=e?.abort,s=e?.close,u=e?.start,f=e?.type,d=e?.write;return{abort:n===void 0?void 0:bi(n,e,`${t} has member 'abort' that`),close:s===void 0?void 0:gi(s,e,`${t} has member 'close' that`),start:u===void 0?void 0:yi(u,e,`${t} has member 'start' that`),write:d===void 0?void 0:_i(d,e,`${t} has member 'write' that`),type:f}}function bi(e,t,n){return Z(e,n),s=>ue(e,t,[s])}function gi(e,t,n){return Z(e,n),()=>ue(e,t,[])}function yi(e,t,n){return Z(e,n),s=>ke(e,t,[s])}function _i(e,t,n){return Z(e,n),(s,u)=>ue(e,t,[s,u])}function fo(e,t){if(!ze(e))throw new TypeError(`${t} is not a WritableStream.`)}function Si(e){if(typeof e!="object"||e===null)return!1;try{return typeof e.aborted=="boolean"}catch{return!1}}let wi=typeof AbortController=="function";function vi(){if(wi)return new AbortController}class ht{constructor(t={},n={}){t===void 0?t=null:xn(t,"First parameter");let s=Ut(n,"Second parameter"),u=pi(t,"First parameter");if(mo(this),u.type!==void 0)throw new RangeError("Invalid type is specified");let d=Mt(s),_=dt(s,1);Ii(this,u,_,d)}get locked(){if(!ze(this))throw Gt("locked");return je(this)}abort(t=void 0){return ze(this)?je(this)?b(new TypeError("Cannot abort a stream that already has a writer")):Nt(this,t):b(Gt("abort"))}close(){return ze(this)?je(this)?b(new TypeError("Cannot close a stream that already has a writer")):ie(this)?b(new TypeError("Cannot close an already-closing stream")):po(this):b(Gt("close"))}getWriter(){if(!ze(this))throw Gt("getWriter");return ho(this)}}Object.defineProperties(ht.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(ht.prototype,i.toStringTag,{value:"WritableStream",configurable:!0});function ho(e){return new mt(e)}function Ri(e,t,n,s,u=1,f=()=>1){let d=Object.create(ht.prototype);mo(d);let _=Object.create(Me.prototype);return wo(d,_,e,t,n,s,u,f),d}function mo(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new X,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function ze(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")?!1:e instanceof ht}function je(e){return e._writer!==void 0}function Nt(e,t){var n;if(e._state==="closed"||e._state==="errored")return p(void 0);e._writableStreamController._abortReason=t,(n=e._writableStreamController._abortController)===null||n===void 0||n.abort();let s=e._state;if(s==="closed"||s==="errored")return p(void 0);if(e._pendingAbortRequest!==void 0)return e._pendingAbortRequest._promise;let u=!1;s==="erroring"&&(u=!0,t=void 0);let f=S((d,_)=>{e._pendingAbortRequest={_promise:void 0,_resolve:d,_reject:_,_reason:t,_wasAlreadyErroring:u}});return e._pendingAbortRequest._promise=f,u||Vr(e,t),f}function po(e){let t=e._state;if(t==="closed"||t==="errored")return b(new TypeError(`The stream (in ${t} state) is not in the writable state and cannot be closed`));let n=S((u,f)=>{let d={_resolve:u,_reject:f};e._closeRequest=d}),s=e._writer;return s!==void 0&&e._backpressure&&t==="writable"&&tn(s),Li(e._writableStreamController),n}function Ti(e){return S((n,s)=>{let u={_resolve:n,_reject:s};e._writeRequests.push(u)})}function Hr(e,t){if(e._state==="writable"){Vr(e,t);return}Yr(e)}function Vr(e,t){let n=e._writableStreamController;e._state="erroring",e._storedError=t;let s=e._writer;s!==void 0&&go(s,t),!ki(e)&&n._started&&Yr(e)}function Yr(e){e._state="errored",e._writableStreamController[In]();let t=e._storedError;if(e._writeRequests.forEach(u=>{u._reject(t)}),e._writeRequests=new X,e._pendingAbortRequest===void 0){Ht(e);return}let n=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,n._wasAlreadyErroring){n._reject(t),Ht(e);return}let s=e._writableStreamController[Fn](n._reason);v(s,()=>{n._resolve(),Ht(e)},u=>{n._reject(u),Ht(e)})}function Ei(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}function Ci(e,t){e._inFlightWriteRequest._reject(t),e._inFlightWriteRequest=void 0,Hr(e,t)}function Ai(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,e._state==="erroring"&&(e._storedError=void 0,e._pendingAbortRequest!==void 0&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";let n=e._writer;n!==void 0&&Eo(n)}function Pi(e,t){e._inFlightCloseRequest._reject(t),e._inFlightCloseRequest=void 0,e._pendingAbortRequest!==void 0&&(e._pendingAbortRequest._reject(t),e._pendingAbortRequest=void 0),Hr(e,t)}function ie(e){return!(e._closeRequest===void 0&&e._inFlightCloseRequest===void 0)}function ki(e){return!(e._inFlightWriteRequest===void 0&&e._inFlightCloseRequest===void 0)}function Bi(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0}function Oi(e){e._inFlightWriteRequest=e._writeRequests.shift()}function Ht(e){e._closeRequest!==void 0&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);let t=e._writer;t!==void 0&&Zr(t,e._storedError)}function Gr(e,t){let n=e._writer;n!==void 0&&t!==e._backpressure&&(t?Ni(n):tn(n)),e._backpressure=t}class mt{constructor(t){if(de(t,1,"WritableStreamDefaultWriter"),fo(t,"First parameter"),je(t))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=t,t._writer=this;let n=t._state;if(n==="writable")!ie(t)&&t._backpressure?Jt(this):Co(this),Qt(this);else if(n==="erroring")en(this,t._storedError),Qt(this);else if(n==="closed")Co(this),Mi(this);else{let s=t._storedError;en(this,s),To(this,s)}}get closed(){return qe(this)?this._closedPromise:b(We("closed"))}get desiredSize(){if(!qe(this))throw We("desiredSize");if(this._ownerWritableStream===void 0)throw pt("desiredSize");return Fi(this)}get ready(){return qe(this)?this._readyPromise:b(We("ready"))}abort(t=void 0){return qe(this)?this._ownerWritableStream===void 0?b(pt("abort")):Di(this,t):b(We("abort"))}close(){if(!qe(this))return b(We("close"));let t=this._ownerWritableStream;return t===void 0?b(pt("close")):ie(t)?b(new TypeError("Cannot close an already-closing stream")):bo(this)}releaseLock(){if(!qe(this))throw We("releaseLock");this._ownerWritableStream!==void 0&&yo(this)}write(t=void 0){return qe(this)?this._ownerWritableStream===void 0?b(pt("write to")):_o(this,t):b(We("write"))}}Object.defineProperties(mt.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(mt.prototype,i.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});function qe(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")?!1:e instanceof mt}function Di(e,t){let n=e._ownerWritableStream;return Nt(n,t)}function bo(e){let t=e._ownerWritableStream;return po(t)}function qi(e){let t=e._ownerWritableStream,n=t._state;return ie(t)||n==="closed"?p(void 0):n==="errored"?b(t._storedError):bo(e)}function Wi(e,t){e._closedPromiseState==="pending"?Zr(e,t):Ui(e,t)}function go(e,t){e._readyPromiseState==="pending"?Ao(e,t):Hi(e,t)}function Fi(e){let t=e._ownerWritableStream,n=t._state;return n==="errored"||n==="erroring"?null:n==="closed"?0:vo(t._writableStreamController)}function yo(e){let t=e._ownerWritableStream,n=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");go(e,n),Wi(e,n),t._writer=void 0,e._ownerWritableStream=void 0}function _o(e,t){let n=e._ownerWritableStream,s=n._writableStreamController,u=xi(s,t);if(n!==e._ownerWritableStream)return b(pt("write to"));let f=n._state;if(f==="errored")return b(n._storedError);if(ie(n)||f==="closed")return b(new TypeError("The stream is closing or closed and cannot be written to"));if(f==="erroring")return b(n._storedError);let d=Ti(n);return $i(s,t,u),d}let So={};class Me{constructor(){throw new TypeError("Illegal constructor")}get abortReason(){if(!Qr(this))throw Xr("abortReason");return this._abortReason}get signal(){if(!Qr(this))throw Xr("signal");if(this._abortController===void 0)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(t=void 0){if(!Qr(this))throw Xr("error");this._controlledWritableStream._state==="writable"&&Ro(this,t)}[Fn](t){let n=this._abortAlgorithm(t);return Vt(this),n}[In](){Se(this)}}Object.defineProperties(Me.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(Me.prototype,i.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});function Qr(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")?!1:e instanceof Me}function wo(e,t,n,s,u,f,d,_){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,Se(t),t._abortReason=void 0,t._abortController=vi(),t._started=!1,t._strategySizeAlgorithm=_,t._strategyHWM=d,t._writeAlgorithm=s,t._closeAlgorithm=u,t._abortAlgorithm=f;let E=Kr(t);Gr(e,E);let A=n(),k=p(A);v(k,()=>{t._started=!0,Yt(t)},B=>{t._started=!0,Hr(e,B)})}function Ii(e,t,n,s){let u=Object.create(Me.prototype),f=()=>{},d=()=>p(void 0),_=()=>p(void 0),E=()=>p(void 0);t.start!==void 0&&(f=()=>t.start(u)),t.write!==void 0&&(d=A=>t.write(A,u)),t.close!==void 0&&(_=()=>t.close()),t.abort!==void 0&&(E=A=>t.abort(A)),wo(e,u,f,d,_,E,n,s)}function Vt(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function Li(e){xr(e,So,0),Yt(e)}function xi(e,t){try{return e._strategySizeAlgorithm(t)}catch(n){return Jr(e,n),1}}function vo(e){return e._strategyHWM-e._queueTotalSize}function $i(e,t,n){try{xr(e,t,n)}catch(u){Jr(e,u);return}let s=e._controlledWritableStream;if(!ie(s)&&s._state==="writable"){let u=Kr(e);Gr(s,u)}Yt(e)}function Yt(e){let t=e._controlledWritableStream;if(!e._started||t._inFlightWriteRequest!==void 0)return;if(t._state==="erroring"){Yr(t);return}if(e._queue.length===0)return;let s=ii(e);s===So?zi(e):ji(e,s)}function Jr(e,t){e._controlledWritableStream._state==="writable"&&Ro(e,t)}function zi(e){let t=e._controlledWritableStream;Bi(t),Lr(e);let n=e._closeAlgorithm();Vt(e),v(n,()=>{Ai(t)},s=>{Pi(t,s)})}function ji(e,t){let n=e._controlledWritableStream;Oi(n);let s=e._writeAlgorithm(t);v(s,()=>{Ei(n);let u=n._state;if(Lr(e),!ie(n)&&u==="writable"){let f=Kr(e);Gr(n,f)}Yt(e)},u=>{n._state==="writable"&&Vt(e),Ci(n,u)})}function Kr(e){return vo(e)<=0}function Ro(e,t){let n=e._controlledWritableStream;Vt(e),Vr(n,t)}function Gt(e){return new TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}function Xr(e){return new TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}function We(e){return new TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}function pt(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function Qt(e){e._closedPromise=S((t,n)=>{e._closedPromise_resolve=t,e._closedPromise_reject=n,e._closedPromiseState="pending"})}function To(e,t){Qt(e),Zr(e,t)}function Mi(e){Qt(e),Eo(e)}function Zr(e,t){e._closedPromise_reject!==void 0&&(W(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function Ui(e,t){To(e,t)}function Eo(e){e._closedPromise_resolve!==void 0&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function Jt(e){e._readyPromise=S((t,n)=>{e._readyPromise_resolve=t,e._readyPromise_reject=n}),e._readyPromiseState="pending"}function en(e,t){Jt(e),Ao(e,t)}function Co(e){Jt(e),tn(e)}function Ao(e,t){e._readyPromise_reject!==void 0&&(W(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function Ni(e){Jt(e)}function Hi(e,t){en(e,t)}function tn(e){e._readyPromise_resolve!==void 0&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}let Po=typeof DOMException<"u"?DOMException:void 0;function Vi(e){if(!(typeof e=="function"||typeof e=="object"))return!1;try{return new e,!0}catch{return!1}}function Yi(){let e=function(n,s){this.message=n||"",this.name=s||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}let Gi=Vi(Po)?Po:Yi();function ko(e,t,n,s,u,f){let d=xe(e),_=ho(t);e._disturbed=!0;let E=!1,A=p(void 0);return S((k,B)=>{let $;if(f!==void 0){if($=()=>{let w=new Gi("Aborted","AbortError"),C=[];s||C.push(()=>t._state==="writable"?Nt(t,w):p(void 0)),u||C.push(()=>e._state==="readable"?te(e,w):p(void 0)),V(()=>Promise.all(C.map(q=>q())),!0,w)},f.aborted){$();return}f.addEventListener("abort",$)}function re(){return S((w,C)=>{function q(G){G?w():P(He(),q,C)}q(!1)})}function He(){return E?p(!0):P(_._readyPromise,()=>S((w,C)=>{it(d,{_chunkSteps:q=>{A=P(_o(_,q),void 0,l),w(!1)},_closeSteps:()=>w(!0),_errorSteps:C})}))}if(he(e,d._closedPromise,w=>{s?J(!0,w):V(()=>Nt(t,w),!0,w)}),he(t,_._closedPromise,w=>{u?J(!0,w):V(()=>te(e,w),!0,w)}),H(e,d._closedPromise,()=>{n?J():V(()=>qi(_))}),ie(t)||t._state==="closed"){let w=new TypeError("the destination writable stream closed before all data could be piped to it");u?J(!0,w):V(()=>te(e,w),!0,w)}W(re());function Ee(){let w=A;return P(A,()=>w!==A?Ee():void 0)}function he(w,C,q){w._state==="errored"?q(w._storedError):U(C,q)}function H(w,C,q){w._state==="closed"?q():M(C,q)}function V(w,C,q){if(E)return;E=!0,t._state==="writable"&&!ie(t)?M(Ee(),G):G();function G(){v(w(),()=>me(C,q),Ve=>me(!0,Ve))}}function J(w,C){E||(E=!0,t._state==="writable"&&!ie(t)?M(Ee(),()=>me(w,C)):me(w,C))}function me(w,C){yo(_),ce(d),f!==void 0&&f.removeEventListener("abort",$),w?B(C):k(void 0)}})}class Ue{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!Kt(this))throw er("desiredSize");return rn(this)}close(){if(!Kt(this))throw er("close");if(!Ne(this))throw new TypeError("The stream is not in a state that permits close");gt(this)}enqueue(t=void 0){if(!Kt(this))throw er("enqueue");if(!Ne(this))throw new TypeError("The stream is not in a state that permits enqueue");return Zt(this,t)}error(t=void 0){if(!Kt(this))throw er("error");we(this,t)}[Or](t){Se(this);let n=this._cancelAlgorithm(t);return Xt(this),n}[Dr](t){let n=this._controlledReadableStream;if(this._queue.length>0){let s=Lr(this);this._closeRequested&&this._queue.length===0?(Xt(this),yt(n)):bt(this),t._chunkSteps(s)}else jn(n,t),bt(this)}}Object.defineProperties(Ue.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(Ue.prototype,i.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});function Kt(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")?!1:e instanceof Ue}function bt(e){if(!Bo(e))return;if(e._pulling){e._pullAgain=!0;return}e._pulling=!0;let n=e._pullAlgorithm();v(n,()=>{e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,bt(e))},s=>{we(e,s)})}function Bo(e){let t=e._controlledReadableStream;return!Ne(e)||!e._started?!1:!!(Te(t)&&Dt(t)>0||rn(e)>0)}function Xt(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function gt(e){if(!Ne(e))return;let t=e._controlledReadableStream;e._closeRequested=!0,e._queue.length===0&&(Xt(e),yt(t))}function Zt(e,t){if(!Ne(e))return;let n=e._controlledReadableStream;if(Te(n)&&Dt(n)>0)Ir(n,t,!1);else{let s;try{s=e._strategySizeAlgorithm(t)}catch(u){throw we(e,u),u}try{xr(e,t,s)}catch(u){throw we(e,u),u}}bt(e)}function we(e,t){let n=e._controlledReadableStream;n._state==="readable"&&(Se(e),Xt(e),Wo(n,t))}function rn(e){let t=e._controlledReadableStream._state;return t==="errored"?null:t==="closed"?0:e._strategyHWM-e._queueTotalSize}function Qi(e){return!Bo(e)}function Ne(e){let t=e._controlledReadableStream._state;return!e._closeRequested&&t==="readable"}function Oo(e,t,n,s,u,f,d){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,Se(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=d,t._strategyHWM=f,t._pullAlgorithm=s,t._cancelAlgorithm=u,e._readableStreamController=t;let _=n();v(p(_),()=>{t._started=!0,bt(t)},E=>{we(t,E)})}function Ji(e,t,n,s){let u=Object.create(Ue.prototype),f=()=>{},d=()=>p(void 0),_=()=>p(void 0);t.start!==void 0&&(f=()=>t.start(u)),t.pull!==void 0&&(d=()=>t.pull(u)),t.cancel!==void 0&&(_=E=>t.cancel(E)),Oo(e,u,f,d,_,n,s)}function er(e){return new TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}function Ki(e,t){return Be(e._readableStreamController)?Zi(e):Xi(e)}function Xi(e,t){let n=xe(e),s=!1,u=!1,f=!1,d=!1,_,E,A,k,B,$=S(H=>{B=H});function re(){return s?(u=!0,p(void 0)):(s=!0,it(n,{_chunkSteps:V=>{x(()=>{u=!1;let J=V,me=V;f||Zt(A._readableStreamController,J),d||Zt(k._readableStreamController,me),s=!1,u&&re()})},_closeSteps:()=>{s=!1,f||gt(A._readableStreamController),d||gt(k._readableStreamController),(!f||!d)&&B(void 0)},_errorSteps:()=>{s=!1}}),p(void 0))}function He(H){if(f=!0,_=H,d){let V=st([_,E]),J=te(e,V);B(J)}return $}function Ee(H){if(d=!0,E=H,f){let V=st([_,E]),J=te(e,V);B(J)}return $}function he(){}return A=nn(he,re,He),k=nn(he,re,Ee),U(n._closedPromise,H=>{we(A._readableStreamController,H),we(k._readableStreamController,H),(!f||!d)&&B(void 0)}),[A,k]}function Zi(e){let t=xe(e),n=!1,s=!1,u=!1,f=!1,d=!1,_,E,A,k,B,$=S(w=>{B=w});function re(w){U(w._closedPromise,C=>{w===t&&(ee(A._readableStreamController,C),ee(k._readableStreamController,C),(!f||!d)&&B(void 0))})}function He(){De(t)&&(ce(t),t=xe(e),re(t)),it(t,{_chunkSteps:C=>{x(()=>{s=!1,u=!1;let q=C,G=C;if(!f&&!d)try{G=Kn(C)}catch(Ve){ee(A._readableStreamController,Ve),ee(k._readableStreamController,Ve),B(te(e,Ve));return}f||xt(A._readableStreamController,q),d||xt(k._readableStreamController,G),n=!1,s?he():u&&H()})},_closeSteps:()=>{n=!1,f||ut(A._readableStreamController),d||ut(k._readableStreamController),A._readableStreamController._pendingPullIntos.length>0&&$t(A._readableStreamController,0),k._readableStreamController._pendingPullIntos.length>0&&$t(k._readableStreamController,0),(!f||!d)&&B(void 0)},_errorSteps:()=>{n=!1}})}function Ee(w,C){_e(t)&&(ce(t),t=so(e),re(t));let q=C?k:A,G=C?A:k;co(t,w,{_chunkSteps:Ye=>{x(()=>{s=!1,u=!1;let Ge=C?d:f;if(C?f:d)Ge||zt(q._readableStreamController,Ye);else{let Yo;try{Yo=Kn(Ye)}catch(an){ee(q._readableStreamController,an),ee(G._readableStreamController,an),B(te(e,an));return}Ge||zt(q._readableStreamController,Ye),xt(G._readableStreamController,Yo)}n=!1,s?he():u&&H()})},_closeSteps:Ye=>{n=!1;let Ge=C?d:f,ur=C?f:d;Ge||ut(q._readableStreamController),ur||ut(G._readableStreamController),Ye!==void 0&&(Ge||zt(q._readableStreamController,Ye),!ur&&G._readableStreamController._pendingPullIntos.length>0&&$t(G._readableStreamController,0)),(!Ge||!ur)&&B(void 0)},_errorSteps:()=>{n=!1}})}function he(){if(n)return s=!0,p(void 0);n=!0;let w=Mr(A._readableStreamController);return w===null?He():Ee(w._view,!1),p(void 0)}function H(){if(n)return u=!0,p(void 0);n=!0;let w=Mr(k._readableStreamController);return w===null?He():Ee(w._view,!0),p(void 0)}function V(w){if(f=!0,_=w,d){let C=st([_,E]),q=te(e,C);B(q)}return $}function J(w){if(d=!0,E=w,f){let C=st([_,E]),q=te(e,C);B(q)}return $}function me(){}return A=qo(me,he,V),k=qo(me,H,J),re(t),[A,k]}function es(e,t){fe(e,t);let n=e,s=n?.autoAllocateChunkSize,u=n?.cancel,f=n?.pull,d=n?.start,_=n?.type;return{autoAllocateChunkSize:s===void 0?void 0:zn(s,`${t} has member 'autoAllocateChunkSize' that`),cancel:u===void 0?void 0:ts(u,n,`${t} has member 'cancel' that`),pull:f===void 0?void 0:rs(f,n,`${t} has member 'pull' that`),start:d===void 0?void 0:ns(d,n,`${t} has member 'start' that`),type:_===void 0?void 0:os(_,`${t} has member 'type' that`)}}function ts(e,t,n){return Z(e,n),s=>ue(e,t,[s])}function rs(e,t,n){return Z(e,n),s=>ue(e,t,[s])}function ns(e,t,n){return Z(e,n),s=>ke(e,t,[s])}function os(e,t){if(e=`${e}`,e!=="bytes")throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}function as(e,t){fe(e,t);let n=e?.mode;return{mode:n===void 0?void 0:is(n,`${t} has member 'mode' that`)}}function is(e,t){if(e=`${e}`,e!=="byob")throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}function ss(e,t){return fe(e,t),{preventCancel:!!e?.preventCancel}}function Do(e,t){fe(e,t);let n=e?.preventAbort,s=e?.preventCancel,u=e?.preventClose,f=e?.signal;return f!==void 0&&ls(f,`${t} has member 'signal' that`),{preventAbort:!!n,preventCancel:!!s,preventClose:!!u,signal:f}}function ls(e,t){if(!Si(e))throw new TypeError(`${t} is not an AbortSignal.`)}function us(e,t){fe(e,t);let n=e?.readable;qr(n,"readable","ReadableWritablePair"),Fr(n,`${t} has member 'readable' that`);let s=e?.writable;return qr(s,"writable","ReadableWritablePair"),fo(s,`${t} has member 'writable' that`),{readable:n,writable:s}}class ve{constructor(t={},n={}){t===void 0?t=null:xn(t,"First parameter");let s=Ut(n,"Second parameter"),u=es(t,"First parameter");if(on(this),u.type==="bytes"){if(s.size!==void 0)throw new RangeError("The strategy for a byte stream cannot have a size function");let f=dt(s,0);fi(this,u,f)}else{let f=Mt(s),d=dt(s,1);Ji(this,u,d,f)}}get locked(){if(!Re(this))throw Fe("locked");return Te(this)}cancel(t=void 0){return Re(this)?Te(this)?b(new TypeError("Cannot cancel a stream that already has a reader")):te(this,t):b(Fe("cancel"))}getReader(t=void 0){if(!Re(this))throw Fe("getReader");return as(t,"First parameter").mode===void 0?xe(this):so(this)}pipeThrough(t,n={}){if(!Re(this))throw Fe("pipeThrough");de(t,1,"pipeThrough");let s=us(t,"First parameter"),u=Do(n,"Second parameter");if(Te(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(je(s.writable))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");let f=ko(this,s.writable,u.preventClose,u.preventAbort,u.preventCancel,u.signal);return W(f),s.readable}pipeTo(t,n={}){if(!Re(this))return b(Fe("pipeTo"));if(t===void 0)return b("Parameter 1 is required in 'pipeTo'.");if(!ze(t))return b(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));let s;try{s=Do(n,"Second parameter")}catch(u){return b(u)}return Te(this)?b(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):je(t)?b(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):ko(this,t,s.preventClose,s.preventAbort,s.preventCancel,s.signal)}tee(){if(!Re(this))throw Fe("tee");let t=Ki(this);return st(t)}values(t=void 0){if(!Re(this))throw Fe("values");let n=ss(t,"First parameter");return oi(this,n.preventCancel)}}Object.defineProperties(ve.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(ve.prototype,i.toStringTag,{value:"ReadableStream",configurable:!0}),typeof i.asyncIterator=="symbol"&&Object.defineProperty(ve.prototype,i.asyncIterator,{value:ve.prototype.values,writable:!0,configurable:!0});function nn(e,t,n,s=1,u=()=>1){let f=Object.create(ve.prototype);on(f);let d=Object.create(Ue.prototype);return Oo(f,d,e,t,n,s,u),f}function qo(e,t,n){let s=Object.create(ve.prototype);on(s);let u=Object.create($e.prototype);return io(s,u,e,t,n,0,void 0),s}function on(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function Re(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")?!1:e instanceof ve}function Te(e){return e._reader!==void 0}function te(e,t){if(e._disturbed=!0,e._state==="closed")return p(void 0);if(e._state==="errored")return b(e._storedError);yt(e);let n=e._reader;n!==void 0&&De(n)&&(n._readIntoRequests.forEach(u=>{u._closeSteps(void 0)}),n._readIntoRequests=new X);let s=e._readableStreamController[Or](t);return O(s,l)}function yt(e){e._state="closed";let t=e._reader;t!==void 0&&(Wn(t),_e(t)&&(t._readRequests.forEach(n=>{n._closeSteps()}),t._readRequests=new X))}function Wo(e,t){e._state="errored",e._storedError=t;let n=e._reader;n!==void 0&&(Br(n,t),_e(n)?(n._readRequests.forEach(s=>{s._errorSteps(t)}),n._readRequests=new X):(n._readIntoRequests.forEach(s=>{s._errorSteps(t)}),n._readIntoRequests=new X))}function Fe(e){return new TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}function Fo(e,t){fe(e,t);let n=e?.highWaterMark;return qr(n,"highWaterMark","QueuingStrategyInit"),{highWaterMark:Wr(n)}}let Io=e=>e.byteLength;try{Object.defineProperty(Io,"name",{value:"size",configurable:!0})}catch{}class tr{constructor(t){de(t,1,"ByteLengthQueuingStrategy"),t=Fo(t,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=t.highWaterMark}get highWaterMark(){if(!xo(this))throw Lo("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!xo(this))throw Lo("size");return Io}}Object.defineProperties(tr.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(tr.prototype,i.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});function Lo(e){return new TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}function xo(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")?!1:e instanceof tr}let $o=()=>1;try{Object.defineProperty($o,"name",{value:"size",configurable:!0})}catch{}class rr{constructor(t){de(t,1,"CountQueuingStrategy"),t=Fo(t,"First parameter"),this._countQueuingStrategyHighWaterMark=t.highWaterMark}get highWaterMark(){if(!jo(this))throw zo("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!jo(this))throw zo("size");return $o}}Object.defineProperties(rr.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(rr.prototype,i.toStringTag,{value:"CountQueuingStrategy",configurable:!0});function zo(e){return new TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}function jo(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")?!1:e instanceof rr}function cs(e,t){fe(e,t);let n=e?.flush,s=e?.readableType,u=e?.start,f=e?.transform,d=e?.writableType;return{flush:n===void 0?void 0:fs(n,e,`${t} has member 'flush' that`),readableType:s,start:u===void 0?void 0:ds(u,e,`${t} has member 'start' that`),transform:f===void 0?void 0:hs(f,e,`${t} has member 'transform' that`),writableType:d}}function fs(e,t,n){return Z(e,n),s=>ue(e,t,[s])}function ds(e,t,n){return Z(e,n),s=>ke(e,t,[s])}function hs(e,t,n){return Z(e,n),(s,u)=>ue(e,t,[s,u])}class nr{constructor(t={},n={},s={}){t===void 0&&(t=null);let u=Ut(n,"Second parameter"),f=Ut(s,"Third parameter"),d=cs(t,"First parameter");if(d.readableType!==void 0)throw new RangeError("Invalid readableType specified");if(d.writableType!==void 0)throw new RangeError("Invalid writableType specified");let _=dt(f,0),E=Mt(f),A=dt(u,1),k=Mt(u),B,$=S(re=>{B=re});ms(this,$,A,k,_,E),bs(this,d),d.start!==void 0?B(d.start(this._transformStreamController)):B(void 0)}get readable(){if(!Mo(this))throw Vo("readable");return this._readable}get writable(){if(!Mo(this))throw Vo("writable");return this._writable}}Object.defineProperties(nr.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(nr.prototype,i.toStringTag,{value:"TransformStream",configurable:!0});function ms(e,t,n,s,u,f){function d(){return t}function _($){return _s(e,$)}function E($){return Ss(e,$)}function A(){return ws(e)}e._writable=Ri(d,_,A,E,n,s);function k(){return vs(e)}function B($){return ar(e,$),p(void 0)}e._readable=nn(d,k,B,u,f),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,ir(e,!0),e._transformStreamController=void 0}function Mo(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")?!1:e instanceof nr}function or(e,t){we(e._readable._readableStreamController,t),ar(e,t)}function ar(e,t){Uo(e._transformStreamController),Jr(e._writable._writableStreamController,t),e._backpressure&&ir(e,!1)}function ir(e,t){e._backpressureChangePromise!==void 0&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=S(n=>{e._backpressureChangePromise_resolve=n}),e._backpressure=t}class _t{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!sr(this))throw lr("desiredSize");let t=this._controlledTransformStream._readable._readableStreamController;return rn(t)}enqueue(t=void 0){if(!sr(this))throw lr("enqueue");No(this,t)}error(t=void 0){if(!sr(this))throw lr("error");gs(this,t)}terminate(){if(!sr(this))throw lr("terminate");ys(this)}}Object.defineProperties(_t.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(_t.prototype,i.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});function sr(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")?!1:e instanceof _t}function ps(e,t,n,s){t._controlledTransformStream=e,e._transformStreamController=t,t._transformAlgorithm=n,t._flushAlgorithm=s}function bs(e,t){let n=Object.create(_t.prototype),s=f=>{try{return No(n,f),p(void 0)}catch(d){return b(d)}},u=()=>p(void 0);t.transform!==void 0&&(s=f=>t.transform(f,n)),t.flush!==void 0&&(u=()=>t.flush(n)),ps(e,n,s,u)}function Uo(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0}function No(e,t){let n=e._controlledTransformStream,s=n._readable._readableStreamController;if(!Ne(s))throw new TypeError("Readable side is not in a state that permits enqueue");try{Zt(s,t)}catch(f){throw ar(n,f),n._readable._storedError}Qi(s)!==n._backpressure&&ir(n,!0)}function gs(e,t){or(e._controlledTransformStream,t)}function Ho(e,t){let n=e._transformAlgorithm(t);return O(n,void 0,s=>{throw or(e._controlledTransformStream,s),s})}function ys(e){let t=e._controlledTransformStream,n=t._readable._readableStreamController;gt(n);let s=new TypeError("TransformStream terminated");ar(t,s)}function _s(e,t){let n=e._transformStreamController;if(e._backpressure){let s=e._backpressureChangePromise;return O(s,()=>{let u=e._writable;if(u._state==="erroring")throw u._storedError;return Ho(n,t)})}return Ho(n,t)}function Ss(e,t){return or(e,t),p(void 0)}function ws(e){let t=e._readable,n=e._transformStreamController,s=n._flushAlgorithm();return Uo(n),O(s,()=>{if(t._state==="errored")throw t._storedError;gt(t._readableStreamController)},u=>{throw or(e,u),t._storedError})}function vs(e){return ir(e,!1),e._backpressureChangePromise}function lr(e){return new TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}function Vo(e){return new TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}a.ByteLengthQueuingStrategy=tr,a.CountQueuingStrategy=rr,a.ReadableByteStreamController=$e,a.ReadableStream=ve,a.ReadableStreamBYOBReader=ft,a.ReadableStreamBYOBRequest=lt,a.ReadableStreamDefaultController=Ue,a.ReadableStreamDefaultReader=at,a.TransformStream=nr,a.TransformStreamDefaultController=_t,a.WritableStream=ht,a.WritableStreamDefaultController=Me,a.WritableStreamDefaultWriter=mt,Object.defineProperty(a,"__esModule",{value:!0})})}}),Ls=_n({"node_modules/fetch-blob/streams.cjs"(){var r=65536;if(!globalThis.ReadableStream)try{let o=require("process"),{emitWarning:a}=o;try{o.emitWarning=()=>{},Object.assign(globalThis,require("stream/web")),o.emitWarning=a}catch(i){throw o.emitWarning=a,i}}catch{Object.assign(globalThis,Is())}try{let{Blob:o}=require("buffer");o&&!o.prototype.stream&&(o.prototype.stream=function(i){let l=0,c=this;return new ReadableStream({type:"bytes",async pull(m){let g=await c.slice(l,Math.min(c.size,l+r)).arrayBuffer();l+=g.byteLength,m.enqueue(new Uint8Array(g)),l===c.size&&m.close()}})})}catch{}}});async function*sn(r,o=!0){for(let a of r)if("stream"in a)yield*a.stream();else if(ArrayBuffer.isView(a))if(o){let i=a.byteOffset,l=a.byteOffset+a.byteLength;for(;i!==l;){let c=Math.min(l-i,mn),m=a.buffer.slice(i,i+c);i+=m.byteLength,yield new Uint8Array(m)}}else yield a;else{let i=0,l=a;for(;i!==l.size;){let m=await l.slice(i,Math.min(l.size,i+mn)).arrayBuffer();i+=m.byteLength,yield new Uint8Array(m)}}}var xs,mn,ln,pn,tt,At=Ct({"node_modules/fetch-blob/index.js"(){xs=N(Ls()),mn=65536,ln=class bn{#e=[];#t="";#r=0;#n="transparent";constructor(o=[],a={}){if(typeof o!="object"||o===null)throw new TypeError("Failed to construct 'Blob': The provided value cannot be converted to a sequence.");if(typeof o[Symbol.iterator]!="function")throw new TypeError("Failed to construct 'Blob': The object must have a callable @@iterator property.");if(typeof a!="object"&&typeof a!="function")throw new TypeError("Failed to construct 'Blob': parameter 2 cannot convert to dictionary.");a===null&&(a={});let i=new TextEncoder;for(let c of o){let m;ArrayBuffer.isView(c)?m=new Uint8Array(c.buffer.slice(c.byteOffset,c.byteOffset+c.byteLength)):c instanceof ArrayBuffer?m=new Uint8Array(c.slice(0)):c instanceof bn?m=c:m=i.encode(`${c}`),this.#r+=ArrayBuffer.isView(m)?m.byteLength:m.size,this.#e.push(m)}this.#n=`${a.endings===void 0?"transparent":a.endings}`;let l=a.type===void 0?"":String(a.type);this.#t=/^[\x20-\x7E]*$/.test(l)?l:""}get size(){return this.#r}get type(){return this.#t}async text(){let o=new TextDecoder,a="";for await(let i of sn(this.#e,!1))a+=o.decode(i,{stream:!0});return a+=o.decode(),a}async arrayBuffer(){let o=new Uint8Array(this.size),a=0;for await(let i of sn(this.#e,!1))o.set(i,a),a+=i.length;return o.buffer}stream(){let o=sn(this.#e,!0);return new globalThis.ReadableStream({type:"bytes",async pull(a){let i=await o.next();i.done?a.close():a.enqueue(i.value)},async cancel(){await o.return()}})}slice(o=0,a=this.size,i=""){let{size:l}=this,c=o<0?Math.max(l+o,0):Math.min(o,l),m=a<0?Math.max(l+a,0):Math.min(a,l),h=Math.max(m-c,0),g=this.#e,y=[],I=0;for(let L of g){if(I>=h)break;let S=ArrayBuffer.isView(L)?L.byteLength:L.size;if(c&&S<=c)c-=S,m-=S;else{let p;ArrayBuffer.isView(L)?(p=L.subarray(c,Math.min(S,m)),I+=p.byteLength):(p=L.slice(c,Math.min(S,m)),I+=p.size),m-=S,y.push(p),c=0}}let j=new bn([],{type:String(i).toLowerCase()});return j.#r=h,j.#e=y,j}get[Symbol.toStringTag](){return"Blob"}static[Symbol.hasInstance](o){return o&&typeof o=="object"&&typeof o.constructor=="function"&&(typeof o.stream=="function"||typeof o.arrayBuffer=="function")&&/^(Blob|File)$/.test(o[Symbol.toStringTag])}},Object.defineProperties(ln.prototype,{size:{enumerable:!0},type:{enumerable:!0},slice:{enumerable:!0}}),pn=ln,tt=pn}}),Qo,Jo,Pt,fa=Ct({"node_modules/fetch-blob/file.js"(){At(),Qo=class extends tt{#e=0;#t="";constructor(o,a,i={}){if(arguments.length<2)throw new TypeError(`Failed to construct 'File': 2 arguments required, but only ${arguments.length} present.`);super(o,i),i===null&&(i={});let l=i.lastModified===void 0?Date.now():Number(i.lastModified);Number.isNaN(l)||(this.#e=l),this.#t=String(a)}get name(){return this.#t}get lastModified(){return this.#e}get[Symbol.toStringTag](){return"File"}static[Symbol.hasInstance](o){return!!o&&o instanceof tt&&/^(File)$/.test(o[Symbol.toStringTag])}},Jo=Qo,Pt=Jo}});function $s(r,o=tt){var a=`${gn()}${gn()}`.replace(/\./g,"").slice(-28).padStart(32,"-"),i=[],l=`--${a}\r
Content-Disposition: form-data; name="`;return r.forEach((c,m)=>typeof c=="string"?i.push(l+mr(m)+`"\r
\r
${c.replace(/\r(?!\n)|(?<!\r)\n/g,`\r
`)}\r
`):i.push(l+mr(m)+`"; filename="${mr(c.name,1)}"\r
Content-Type: ${c.type||"application/octet-stream"}\r
\r
`,c,`\r
`)),i.push(`--${a}--`),new o(i,{type:"multipart/form-data; boundary="+a})}var Ke,Ko,Xo,gn,Zo,un,mr,Ce,rt,Sr=Ct({"node_modules/formdata-polyfill/esm.min.js"(){At(),fa(),{toStringTag:Ke,iterator:Ko,hasInstance:Xo}=Symbol,gn=Math.random,Zo="append,set,get,getAll,delete,keys,values,entries,forEach,constructor".split(","),un=(r,o,a)=>(r+="",/^(Blob|File)$/.test(o&&o[Ke])?[(a=a!==void 0?a+"":o[Ke]=="File"?o.name:"blob",r),o.name!==a||o[Ke]=="blob"?new Pt([o],a,o):o]:[r,o+""]),mr=(r,o)=>(o?r:r.replace(/\r?\n|\r/g,`\r
`)).replace(/\n/g,"%0A").replace(/\r/g,"%0D").replace(/"/g,"%22"),Ce=(r,o,a)=>{if(o.length<a)throw new TypeError(`Failed to execute '${r}' on 'FormData': ${a} arguments required, but only ${o.length} present.`)},rt=class{#e=[];constructor(...o){if(o.length)throw new TypeError("Failed to construct 'FormData': parameter 1 is not of type 'HTMLFormElement'.")}get[Ke](){return"FormData"}[Ko](){return this.entries()}static[Xo](o){return o&&typeof o=="object"&&o[Ke]==="FormData"&&!Zo.some(a=>typeof o[a]!="function")}append(...o){Ce("append",arguments,2),this.#e.push(un(...o))}delete(o){Ce("delete",arguments,1),o+="",this.#e=this.#e.filter(([a])=>a!==o)}get(o){Ce("get",arguments,1),o+="";for(var a=this.#e,i=a.length,l=0;l<i;l++)if(a[l][0]===o)return a[l][1];return null}getAll(o,a){return Ce("getAll",arguments,1),a=[],o+="",this.#e.forEach(i=>i[0]===o&&a.push(i[1])),a}has(o){return Ce("has",arguments,1),o+="",this.#e.some(a=>a[0]===o)}forEach(o,a){Ce("forEach",arguments,1);for(var[i,l]of this)o.call(a,l,i,this)}set(...o){Ce("set",arguments,2);var a=[],i=!0;o=un(...o),this.#e.forEach(l=>{l[0]===o[0]?i&&(i=!a.push(o)):a.push(l)}),i&&a.push(o),this.#e=a}*entries(){yield*this.#e}*keys(){for(var[o]of this)yield o}*values(){for(var[,o]of this)yield o}}}}),zs=_n({"node_modules/node-domexception/index.js"(r,o){if(!globalThis.DOMException)try{let{MessageChannel:a}=require("worker_threads"),i=new a().port1,l=new ArrayBuffer;i.postMessage(l,[l,l])}catch(a){a.constructor.name==="DOMException"&&(globalThis.DOMException=a.constructor)}o.exports=globalThis.DOMException}}),St,ea,ta,fr,da,ha,ma,pa,cn,fn,dr,ba=Ct({"node_modules/fetch-blob/from.js"(){St=N(require("fs")),ea=N(require("path")),ta=N(zs()),fa(),At(),{stat:fr}=St.promises,da=(r,o)=>cn((0,St.statSync)(r),r,o),ha=(r,o)=>fr(r).then(a=>cn(a,r,o)),ma=(r,o)=>fr(r).then(a=>fn(a,r,o)),pa=(r,o)=>fn((0,St.statSync)(r),r,o),cn=(r,o,a="")=>new tt([new dr({path:o,size:r.size,lastModified:r.mtimeMs,start:0})],{type:a}),fn=(r,o,a="")=>new Pt([new dr({path:o,size:r.size,lastModified:r.mtimeMs,start:0})],(0,ea.basename)(o),{type:a,lastModified:r.mtimeMs}),dr=class{#e;#t;constructor(r){this.#e=r.path,this.#t=r.start,this.size=r.size,this.lastModified=r.lastModified}slice(r,o){return new dr({path:this.#e,lastModified:this.lastModified,size:o-r,start:this.#t+r})}async*stream(){let{mtimeMs:r}=await fr(this.#e);if(r>this.lastModified)throw new ta.default("The requested file could not be read, typically due to permission problems that have occurred after a reference to a file was acquired.","NotReadableError");yield*(0,St.createReadStream)(this.#e,{start:this.#t,end:this.#t+this.size-1})}get[Symbol.toStringTag](){return"Blob"}}}}),ga={};ca(ga,{toFormData:()=>Ms});function js(r){let o=r.match(/\bfilename=("(.*?)"|([^()<>@,;:\\"/[\]?={}\s\t]+))($|;\s)/i);if(!o)return;let a=o[2]||o[3]||"",i=a.slice(a.lastIndexOf("\\")+1);return i=i.replace(/%22/g,'"'),i=i.replace(/&#(\d{4});/g,(l,c)=>String.fromCharCode(c)),i}async function Ms(r,o){if(!/multipart/i.test(o))throw new TypeError("Failed to fetch");let a=o.match(/boundary=(?:"([^"]+)"|([^;]+))/i);if(!a)throw new TypeError("no or bad content-type header, no multipart boundary");let i=new ya(a[1]||a[2]),l,c,m,h,g,y,I=[],j=new rt,L=v=>{m+=P.decode(v,{stream:!0})},S=v=>{I.push(v)},p=()=>{let v=new Pt(I,y,{type:g});j.append(h,v)},b=()=>{j.append(h,m)},P=new TextDecoder("utf-8");P.decode(),i.onPartBegin=function(){i.onPartData=L,i.onPartEnd=b,l="",c="",m="",h="",g="",y=null,I.length=0},i.onHeaderField=function(v){l+=P.decode(v,{stream:!0})},i.onHeaderValue=function(v){c+=P.decode(v,{stream:!0})},i.onHeaderEnd=function(){if(c+=P.decode(),l=l.toLowerCase(),l==="content-disposition"){let v=c.match(/\bname=("([^"]*)"|([^()<>@,;:\\"/[\]?={}\s\t]+))/i);v&&(h=v[2]||v[3]||""),y=js(c),y&&(i.onPartData=S,i.onPartEnd=p)}else l==="content-type"&&(g=c);c="",l=""};for await(let v of r)i.write(v);return i.end(),j}var ne,D,dn,pe,wt,vt,ra,Xe,na,oa,aa,ia,Ae,ya,Us=Ct({"node_modules/node-fetch/src/utils/multipart-parser.js"(){ba(),Sr(),ne=0,D={START_BOUNDARY:ne++,HEADER_FIELD_START:ne++,HEADER_FIELD:ne++,HEADER_VALUE_START:ne++,HEADER_VALUE:ne++,HEADER_VALUE_ALMOST_DONE:ne++,HEADERS_ALMOST_DONE:ne++,PART_DATA_START:ne++,PART_DATA:ne++,END:ne++},dn=1,pe={PART_BOUNDARY:dn,LAST_BOUNDARY:dn*=2},wt=10,vt=13,ra=32,Xe=45,na=58,oa=97,aa=122,ia=r=>r|32,Ae=()=>{},ya=class{constructor(r){this.index=0,this.flags=0,this.onHeaderEnd=Ae,this.onHeaderField=Ae,this.onHeadersEnd=Ae,this.onHeaderValue=Ae,this.onPartBegin=Ae,this.onPartData=Ae,this.onPartEnd=Ae,this.boundaryChars={},r=`\r
--`+r;let o=new Uint8Array(r.length);for(let a=0;a<r.length;a++)o[a]=r.charCodeAt(a),this.boundaryChars[o[a]]=!0;this.boundary=o,this.lookbehind=new Uint8Array(this.boundary.length+8),this.state=D.START_BOUNDARY}write(r){let o=0,a=r.length,i=this.index,{lookbehind:l,boundary:c,boundaryChars:m,index:h,state:g,flags:y}=this,I=this.boundary.length,j=I-1,L=r.length,S,p,b=U=>{this[U+"Mark"]=o},P=U=>{delete this[U+"Mark"]},v=(U,O,W,x)=>{(O===void 0||O!==W)&&this[U](x&&x.subarray(O,W))},M=(U,O)=>{let W=U+"Mark";W in this&&(O?(v(U,this[W],o,r),delete this[W]):(v(U,this[W],r.length,r),this[W]=0))};for(o=0;o<a;o++)switch(S=r[o],g){case D.START_BOUNDARY:if(h===c.length-2){if(S===Xe)y|=pe.LAST_BOUNDARY;else if(S!==vt)return;h++;break}else if(h-1===c.length-2){if(y&pe.LAST_BOUNDARY&&S===Xe)g=D.END,y=0;else if(!(y&pe.LAST_BOUNDARY)&&S===wt)h=0,v("onPartBegin"),g=D.HEADER_FIELD_START;else return;break}S!==c[h+2]&&(h=-2),S===c[h+2]&&h++;break;case D.HEADER_FIELD_START:g=D.HEADER_FIELD,b("onHeaderField"),h=0;case D.HEADER_FIELD:if(S===vt){P("onHeaderField"),g=D.HEADERS_ALMOST_DONE;break}if(h++,S===Xe)break;if(S===na){if(h===1)return;M("onHeaderField",!0),g=D.HEADER_VALUE_START;break}if(p=ia(S),p<oa||p>aa)return;break;case D.HEADER_VALUE_START:if(S===ra)break;b("onHeaderValue"),g=D.HEADER_VALUE;case D.HEADER_VALUE:S===vt&&(M("onHeaderValue",!0),v("onHeaderEnd"),g=D.HEADER_VALUE_ALMOST_DONE);break;case D.HEADER_VALUE_ALMOST_DONE:if(S!==wt)return;g=D.HEADER_FIELD_START;break;case D.HEADERS_ALMOST_DONE:if(S!==wt)return;v("onHeadersEnd"),g=D.PART_DATA_START;break;case D.PART_DATA_START:g=D.PART_DATA,b("onPartData");case D.PART_DATA:if(i=h,h===0){for(o+=j;o<L&&!(r[o]in m);)o+=I;o-=j,S=r[o]}if(h<c.length)c[h]===S?(h===0&&M("onPartData",!0),h++):h=0;else if(h===c.length)h++,S===vt?y|=pe.PART_BOUNDARY:S===Xe?y|=pe.LAST_BOUNDARY:h=0;else if(h-1===c.length)if(y&pe.PART_BOUNDARY){if(h=0,S===wt){y&=~pe.PART_BOUNDARY,v("onPartEnd"),v("onPartBegin"),g=D.HEADER_FIELD_START;break}}else y&pe.LAST_BOUNDARY&&S===Xe?(v("onPartEnd"),g=D.END,y=0):h=0;if(h>0)l[h-1]=S;else if(i>0){let U=new Uint8Array(l.buffer,l.byteOffset,l.byteLength);v("onPartData",0,i,U),i=0,b("onPartData"),o--}break;case D.END:break;default:throw new Error(`Unexpected state entered: ${g}`)}M("onHeaderField"),M("onHeaderValue"),M("onPartData"),this.index=h,this.state=g,this.flags=y}end(){if(this.state===D.HEADER_FIELD_START&&this.index===0||this.state===D.PART_DATA&&this.index===this.boundary.length)this.onPartEnd();else if(this.state!==D.END)throw new Error("MultipartParser.end(): stream ended unexpectedly")}}}});ca(Ta,{AbortError:()=>va,Blob:()=>pn,FetchError:()=>ae,File:()=>Pt,FormData:()=>rt,Headers:()=>be,Request:()=>Et,Response:()=>Q,blobFrom:()=>ha,blobFromSync:()=>da,default:()=>Ra,fileFrom:()=>ma,fileFromSync:()=>pa,isRedirect:()=>wn});var Ns=N(require("http")),Hs=N(require("https")),Ze=N(require("zlib")),se=N(require("stream")),hr=N(require("buffer"));function Vs(r){if(!/^data:/i.test(r))throw new TypeError('`uri` does not appear to be a Data URI (must begin with "data:")');r=r.replace(/\r?\n/g,"");let o=r.indexOf(",");if(o===-1||o<=4)throw new TypeError("malformed data: URI");let a=r.substring(5,o).split(";"),i="",l=!1,c=a[0]||"text/plain",m=c;for(let I=1;I<a.length;I++)a[I]==="base64"?l=!0:a[I]&&(m+=`;${a[I]}`,a[I].indexOf("charset=")===0&&(i=a[I].substring(8)));!a[0]&&!i.length&&(m+=";charset=US-ASCII",i="US-ASCII");let h=l?"base64":"ascii",g=unescape(r.substring(o+1)),y=Buffer.from(g,h);return y.type=c,y.typeFull=m,y.charset=i,y}var Ys=Vs,le=N(require("stream")),nt=N(require("util")),K=N(require("buffer"));At();Sr();var wr=class extends Error{constructor(r,o){super(r),Error.captureStackTrace(this,this.constructor),this.type=o}get name(){return this.constructor.name}get[Symbol.toStringTag](){return this.constructor.name}},ae=class extends wr{constructor(r,o,a){super(r,o),a&&(this.code=this.errno=a.code,this.erroredSysCall=a.syscall)}},br=Symbol.toStringTag,_a=r=>typeof r=="object"&&typeof r.append=="function"&&typeof r.delete=="function"&&typeof r.get=="function"&&typeof r.getAll=="function"&&typeof r.has=="function"&&typeof r.set=="function"&&typeof r.sort=="function"&&r[br]==="URLSearchParams",gr=r=>r&&typeof r=="object"&&typeof r.arrayBuffer=="function"&&typeof r.type=="string"&&typeof r.stream=="function"&&typeof r.constructor=="function"&&/^(Blob|File)$/.test(r[br]),Gs=r=>typeof r=="object"&&(r[br]==="AbortSignal"||r[br]==="EventTarget"),Qs=(r,o)=>{let a=new URL(o).hostname,i=new URL(r).hostname;return a===i||a.endsWith(`.${i}`)},Js=(r,o)=>{let a=new URL(o).protocol,i=new URL(r).protocol;return a===i},Ks=(0,nt.promisify)(le.default.pipeline),Y=Symbol("Body internals"),Tt=class{constructor(r,{size:o=0}={}){let a=null;r===null?r=null:_a(r)?r=K.Buffer.from(r.toString()):gr(r)||K.Buffer.isBuffer(r)||(nt.types.isAnyArrayBuffer(r)?r=K.Buffer.from(r):ArrayBuffer.isView(r)?r=K.Buffer.from(r.buffer,r.byteOffset,r.byteLength):r instanceof le.default||(r instanceof rt?(r=$s(r),a=r.type.split("=")[1]):r=K.Buffer.from(String(r))));let i=r;K.Buffer.isBuffer(r)?i=le.default.Readable.from(r):gr(r)&&(i=le.default.Readable.from(r.stream())),this[Y]={body:r,stream:i,boundary:a,disturbed:!1,error:null},this.size=o,r instanceof le.default&&r.on("error",l=>{let c=l instanceof wr?l:new ae(`Invalid response body while trying to fetch ${this.url}: ${l.message}`,"system",l);this[Y].error=c})}get body(){return this[Y].stream}get bodyUsed(){return this[Y].disturbed}async arrayBuffer(){let{buffer:r,byteOffset:o,byteLength:a}=await hn(this);return r.slice(o,o+a)}async formData(){let r=this.headers.get("content-type");if(r.startsWith("application/x-www-form-urlencoded")){let a=new rt,i=new URLSearchParams(await this.text());for(let[l,c]of i)a.append(l,c);return a}let{toFormData:o}=await Promise.resolve().then(()=>(Us(),ga));return o(this.body,r)}async blob(){let r=this.headers&&this.headers.get("content-type")||this[Y].body&&this[Y].body.type||"",o=await this.arrayBuffer();return new tt([o],{type:r})}async json(){let r=await this.text();return JSON.parse(r)}async text(){let r=await hn(this);return new TextDecoder().decode(r)}buffer(){return hn(this)}};Tt.prototype.buffer=(0,nt.deprecate)(Tt.prototype.buffer,"Please use 'response.arrayBuffer()' instead of 'response.buffer()'","node-fetch#buffer");Object.defineProperties(Tt.prototype,{body:{enumerable:!0},bodyUsed:{enumerable:!0},arrayBuffer:{enumerable:!0},blob:{enumerable:!0},json:{enumerable:!0},text:{enumerable:!0},data:{get:(0,nt.deprecate)(()=>{},"data doesn't exist, use json(), text(), arrayBuffer(), or body instead","https://github.com/node-fetch/node-fetch/issues/1000 (response)")}});async function hn(r){if(r[Y].disturbed)throw new TypeError(`body used already for: ${r.url}`);if(r[Y].disturbed=!0,r[Y].error)throw r[Y].error;let{body:o}=r;if(o===null||!(o instanceof le.default))return K.Buffer.alloc(0);let a=[],i=0;try{for await(let l of o){if(r.size>0&&i+l.length>r.size){let c=new ae(`content size at ${r.url} over limit: ${r.size}`,"max-size");throw o.destroy(c),c}i+=l.length,a.push(l)}}catch(l){throw l instanceof wr?l:new ae(`Invalid response body while trying to fetch ${r.url}: ${l.message}`,"system",l)}if(o.readableEnded===!0||o._readableState.ended===!0)try{return a.every(l=>typeof l=="string")?K.Buffer.from(a.join("")):K.Buffer.concat(a,i)}catch(l){throw new ae(`Could not create Buffer from response body for ${r.url}: ${l.message}`,"system",l)}else throw new ae(`Premature close of server response while trying to fetch ${r.url}`)}var Sn=(r,o)=>{let a,i,{body:l}=r[Y];if(r.bodyUsed)throw new Error("cannot clone body after it is used");return l instanceof le.default&&typeof l.getBoundary!="function"&&(a=new le.PassThrough({highWaterMark:o}),i=new le.PassThrough({highWaterMark:o}),l.pipe(a),l.pipe(i),r[Y].stream=a,l=i),l},Xs=(0,nt.deprecate)(r=>r.getBoundary(),"form-data doesn't follow the spec and requires special treatment. Use alternative package","https://github.com/node-fetch/node-fetch/issues/1167"),Sa=(r,o)=>r===null?null:typeof r=="string"?"text/plain;charset=UTF-8":_a(r)?"application/x-www-form-urlencoded;charset=UTF-8":gr(r)?r.type||null:K.Buffer.isBuffer(r)||nt.types.isAnyArrayBuffer(r)||ArrayBuffer.isView(r)?null:r instanceof rt?`multipart/form-data; boundary=${o[Y].boundary}`:r&&typeof r.getBoundary=="function"?`multipart/form-data;boundary=${Xs(r)}`:r instanceof le.default?null:"text/plain;charset=UTF-8",Zs=r=>{let{body:o}=r[Y];return o===null?0:gr(o)?o.size:K.Buffer.isBuffer(o)?o.length:o&&typeof o.getLengthSync=="function"&&o.hasKnownLength&&o.hasKnownLength()?o.getLengthSync():null},el=async(r,{body:o})=>{o===null?r.end():await Ks(o,r)},sa=N(require("util")),yr=N(require("http")),pr=typeof yr.default.validateHeaderName=="function"?yr.default.validateHeaderName:r=>{if(!/^[\^`\-\w!#$%&'*+.|~]+$/.test(r)){let o=new TypeError(`Header name must be a valid HTTP token [${r}]`);throw Object.defineProperty(o,"code",{value:"ERR_INVALID_HTTP_TOKEN"}),o}},yn=typeof yr.default.validateHeaderValue=="function"?yr.default.validateHeaderValue:(r,o)=>{if(/[^\t\u0020-\u007E\u0080-\u00FF]/.test(o)){let a=new TypeError(`Invalid character in header content ["${r}"]`);throw Object.defineProperty(a,"code",{value:"ERR_INVALID_CHAR"}),a}},be=class extends URLSearchParams{constructor(r){let o=[];if(r instanceof be){let a=r.raw();for(let[i,l]of Object.entries(a))o.push(...l.map(c=>[i,c]))}else if(r!=null)if(typeof r=="object"&&!sa.types.isBoxedPrimitive(r)){let a=r[Symbol.iterator];if(a==null)o.push(...Object.entries(r));else{if(typeof a!="function")throw new TypeError("Header pairs must be iterable");o=[...r].map(i=>{if(typeof i!="object"||sa.types.isBoxedPrimitive(i))throw new TypeError("Each header pair must be an iterable object");return[...i]}).map(i=>{if(i.length!==2)throw new TypeError("Each header pair must be a name/value tuple");return[...i]})}}else throw new TypeError("Failed to construct 'Headers': The provided value is not of type '(sequence<sequence<ByteString>> or record<ByteString, ByteString>)");return o=o.length>0?o.map(([a,i])=>(pr(a),yn(a,String(i)),[String(a).toLowerCase(),String(i)])):void 0,super(o),new Proxy(this,{get(a,i,l){switch(i){case"append":case"set":return(c,m)=>(pr(c),yn(c,String(m)),URLSearchParams.prototype[i].call(a,String(c).toLowerCase(),String(m)));case"delete":case"has":case"getAll":return c=>(pr(c),URLSearchParams.prototype[i].call(a,String(c).toLowerCase()));case"keys":return()=>(a.sort(),new Set(URLSearchParams.prototype.keys.call(a)).keys());default:return Reflect.get(a,i,l)}}})}get[Symbol.toStringTag](){return this.constructor.name}toString(){return Object.prototype.toString.call(this)}get(r){let o=this.getAll(r);if(o.length===0)return null;let a=o.join(", ");return/^content-encoding$/i.test(r)&&(a=a.toLowerCase()),a}forEach(r,o=void 0){for(let a of this.keys())Reflect.apply(r,o,[this.get(a),a,this])}*values(){for(let r of this.keys())yield this.get(r)}*entries(){for(let r of this.keys())yield[r,this.get(r)]}[Symbol.iterator](){return this.entries()}raw(){return[...this.keys()].reduce((r,o)=>(r[o]=this.getAll(o),r),{})}[Symbol.for("nodejs.util.inspect.custom")](){return[...this.keys()].reduce((r,o)=>{let a=this.getAll(o);return o==="host"?r[o]=a[0]:r[o]=a.length>1?a:a[0],r},{})}};Object.defineProperties(be.prototype,["get","entries","forEach","values"].reduce((r,o)=>(r[o]={enumerable:!0},r),{}));function tl(r=[]){return new be(r.reduce((o,a,i,l)=>(i%2===0&&o.push(l.slice(i,i+2)),o),[]).filter(([o,a])=>{try{return pr(o),yn(o,String(a)),!0}catch{return!1}}))}var rl=new Set([301,302,303,307,308]),wn=r=>rl.has(r),oe=Symbol("Response internals"),Q=class extends Tt{constructor(r=null,o={}){super(r,o);let a=o.status!=null?o.status:200,i=new be(o.headers);if(r!==null&&!i.has("Content-Type")){let l=Sa(r,this);l&&i.append("Content-Type",l)}this[oe]={type:"default",url:o.url,status:a,statusText:o.statusText||"",headers:i,counter:o.counter,highWaterMark:o.highWaterMark}}get type(){return this[oe].type}get url(){return this[oe].url||""}get status(){return this[oe].status}get ok(){return this[oe].status>=200&&this[oe].status<300}get redirected(){return this[oe].counter>0}get statusText(){return this[oe].statusText}get headers(){return this[oe].headers}get highWaterMark(){return this[oe].highWaterMark}clone(){return new Q(Sn(this,this.highWaterMark),{type:this.type,url:this.url,status:this.status,statusText:this.statusText,headers:this.headers,ok:this.ok,redirected:this.redirected,size:this.size,highWaterMark:this.highWaterMark})}static redirect(r,o=302){if(!wn(o))throw new RangeError('Failed to execute "redirect" on "response": Invalid status code');return new Q(null,{headers:{location:new URL(r).toString()},status:o})}static error(){let r=new Q(null,{status:0,statusText:""});return r[oe].type="error",r}static json(r=void 0,o={}){let a=JSON.stringify(r);if(a===void 0)throw new TypeError("data is not JSON serializable");let i=new be(o&&o.headers);return i.has("content-type")||i.set("content-type","application/json"),new Q(a,{...o,headers:i})}get[Symbol.toStringTag](){return"Response"}};Object.defineProperties(Q.prototype,{type:{enumerable:!0},url:{enumerable:!0},status:{enumerable:!0},ok:{enumerable:!0},redirected:{enumerable:!0},statusText:{enumerable:!0},headers:{enumerable:!0},clone:{enumerable:!0}});var nl=N(require("url")),ol=N(require("util")),al=r=>{if(r.search)return r.search;let o=r.href.length-1,a=r.hash||(r.href[o]==="#"?"#":"");return r.href[o-a.length]==="?"?"?":""},il=N(require("net"));function la(r,o=!1){return r==null||(r=new URL(r),/^(about|blob|data):$/.test(r.protocol))?"no-referrer":(r.username="",r.password="",r.hash="",o&&(r.pathname="",r.search=""),r)}var wa=new Set(["","no-referrer","no-referrer-when-downgrade","same-origin","origin","strict-origin","origin-when-cross-origin","strict-origin-when-cross-origin","unsafe-url"]),sl="strict-origin-when-cross-origin";function ll(r){if(!wa.has(r))throw new TypeError(`Invalid referrerPolicy: ${r}`);return r}function ul(r){if(/^(http|ws)s:$/.test(r.protocol))return!0;let o=r.host.replace(/(^\[)|(]$)/g,""),a=(0,il.isIP)(o);return a===4&&/^127\./.test(o)||a===6&&/^(((0+:){7})|(::(0+:){0,6}))0*1$/.test(o)?!0:r.host==="localhost"||r.host.endsWith(".localhost")?!1:r.protocol==="file:"}function et(r){return/^about:(blank|srcdoc)$/.test(r)||r.protocol==="data:"||/^(blob|filesystem):$/.test(r.protocol)?!0:ul(r)}function cl(r,{referrerURLCallback:o,referrerOriginCallback:a}={}){if(r.referrer==="no-referrer"||r.referrerPolicy==="")return null;let i=r.referrerPolicy;if(r.referrer==="about:client")return"no-referrer";let l=r.referrer,c=la(l),m=la(l,!0);c.toString().length>4096&&(c=m),o&&(c=o(c)),a&&(m=a(m));let h=new URL(r.url);switch(i){case"no-referrer":return"no-referrer";case"origin":return m;case"unsafe-url":return c;case"strict-origin":return et(c)&&!et(h)?"no-referrer":m.toString();case"strict-origin-when-cross-origin":return c.origin===h.origin?c:et(c)&&!et(h)?"no-referrer":m;case"same-origin":return c.origin===h.origin?c:"no-referrer";case"origin-when-cross-origin":return c.origin===h.origin?c:m;case"no-referrer-when-downgrade":return et(c)&&!et(h)?"no-referrer":c;default:throw new TypeError(`Invalid referrerPolicy: ${i}`)}}function fl(r){let o=(r.get("referrer-policy")||"").split(/[,\s]+/),a="";for(let i of o)i&&wa.has(i)&&(a=i);return a}var z=Symbol("Request internals"),Rt=r=>typeof r=="object"&&typeof r[z]=="object",dl=(0,ol.deprecate)(()=>{},".data is not a valid RequestInit property, use .body instead","https://github.com/node-fetch/node-fetch/issues/1000 (request)"),Et=class extends Tt{constructor(r,o={}){let a;if(Rt(r)?a=new URL(r.url):(a=new URL(r),r={}),a.username!==""||a.password!=="")throw new TypeError(`${a} is an url with embedded credentials.`);let i=o.method||r.method||"GET";if(/^(delete|get|head|options|post|put)$/i.test(i)&&(i=i.toUpperCase()),!Rt(o)&&"data"in o&&dl(),(o.body!=null||Rt(r)&&r.body!==null)&&(i==="GET"||i==="HEAD"))throw new TypeError("Request with GET/HEAD method cannot have body");let l=o.body?o.body:Rt(r)&&r.body!==null?Sn(r):null;super(l,{size:o.size||r.size||0});let c=new be(o.headers||r.headers||{});if(l!==null&&!c.has("Content-Type")){let g=Sa(l,this);g&&c.set("Content-Type",g)}let m=Rt(r)?r.signal:null;if("signal"in o&&(m=o.signal),m!=null&&!Gs(m))throw new TypeError("Expected signal to be an instanceof AbortSignal or EventTarget");let h=o.referrer==null?r.referrer:o.referrer;if(h==="")h="no-referrer";else if(h){let g=new URL(h);h=/^about:(\/\/)?client$/.test(g)?"client":g}else h=void 0;this[z]={method:i,redirect:o.redirect||r.redirect||"follow",headers:c,parsedURL:a,signal:m,referrer:h},this.follow=o.follow===void 0?r.follow===void 0?20:r.follow:o.follow,this.compress=o.compress===void 0?r.compress===void 0?!0:r.compress:o.compress,this.counter=o.counter||r.counter||0,this.agent=o.agent||r.agent,this.highWaterMark=o.highWaterMark||r.highWaterMark||16384,this.insecureHTTPParser=o.insecureHTTPParser||r.insecureHTTPParser||!1,this.referrerPolicy=o.referrerPolicy||r.referrerPolicy||""}get method(){return this[z].method}get url(){return(0,nl.format)(this[z].parsedURL)}get headers(){return this[z].headers}get redirect(){return this[z].redirect}get signal(){return this[z].signal}get referrer(){if(this[z].referrer==="no-referrer")return"";if(this[z].referrer==="client")return"about:client";if(this[z].referrer)return this[z].referrer.toString()}get referrerPolicy(){return this[z].referrerPolicy}set referrerPolicy(r){this[z].referrerPolicy=ll(r)}clone(){return new Et(this)}get[Symbol.toStringTag](){return"Request"}};Object.defineProperties(Et.prototype,{method:{enumerable:!0},url:{enumerable:!0},headers:{enumerable:!0},redirect:{enumerable:!0},clone:{enumerable:!0},signal:{enumerable:!0},referrer:{enumerable:!0},referrerPolicy:{enumerable:!0}});var hl=r=>{let{parsedURL:o}=r[z],a=new be(r[z].headers);a.has("Accept")||a.set("Accept","*/*");let i=null;if(r.body===null&&/^(post|put)$/i.test(r.method)&&(i="0"),r.body!==null){let h=Zs(r);typeof h=="number"&&!Number.isNaN(h)&&(i=String(h))}i&&a.set("Content-Length",i),r.referrerPolicy===""&&(r.referrerPolicy=sl),r.referrer&&r.referrer!=="no-referrer"?r[z].referrer=cl(r):r[z].referrer="no-referrer",r[z].referrer instanceof URL&&a.set("Referer",r.referrer),a.has("User-Agent")||a.set("User-Agent","node-fetch"),r.compress&&!a.has("Accept-Encoding")&&a.set("Accept-Encoding","gzip, deflate, br");let{agent:l}=r;typeof l=="function"&&(l=l(o));let c=al(o),m={path:o.pathname+c,method:r.method,headers:a[Symbol.for("nodejs.util.inspect.custom")](),insecureHTTPParser:r.insecureHTTPParser,agent:l};return{parsedURL:o,options:m}},va=class extends wr{constructor(r,o="aborted"){super(r,o)}};Sr();ba();var ml=new Set(["data:","http:","https:"]);async function Ra(r,o){return new Promise((a,i)=>{let l=new Et(r,o),{parsedURL:c,options:m}=hl(l);if(!ml.has(c.protocol))throw new TypeError(`node-fetch cannot load ${r}. URL scheme "${c.protocol.replace(/:$/,"")}" is not supported.`);if(c.protocol==="data:"){let p=Ys(l.url),b=new Q(p,{headers:{"Content-Type":p.typeFull}});a(b);return}let h=(c.protocol==="https:"?Hs.default:Ns.default).request,{signal:g}=l,y=null,I=()=>{let p=new va("The operation was aborted.");i(p),l.body&&l.body instanceof se.default.Readable&&l.body.destroy(p),!(!y||!y.body)&&y.body.emit("error",p)};if(g&&g.aborted){I();return}let j=()=>{I(),S()},L=h(c.toString(),m);g&&g.addEventListener("abort",j);let S=()=>{L.abort(),g&&g.removeEventListener("abort",j)};L.on("error",p=>{i(new ae(`request to ${l.url} failed, reason: ${p.message}`,"system",p)),S()}),pl(L,p=>{y&&y.body&&y.body.destroy(p)}),process.version<"v14"&&L.on("socket",p=>{let b;p.prependListener("end",()=>{b=p._eventsCount}),p.prependListener("close",P=>{if(y&&b<p._eventsCount&&!P){let v=new Error("Premature close");v.code="ERR_STREAM_PREMATURE_CLOSE",y.body.emit("error",v)}})}),L.on("response",p=>{L.setTimeout(0);let b=tl(p.rawHeaders);if(wn(p.statusCode)){let O=b.get("Location"),W=null;try{W=O===null?null:new URL(O,l.url)}catch{if(l.redirect!=="manual"){i(new ae(`uri requested responds with an invalid redirect URL: ${O}`,"invalid-redirect")),S();return}}switch(l.redirect){case"error":i(new ae(`uri requested responds with a redirect, redirect mode is set to error: ${l.url}`,"no-redirect")),S();return;case"manual":break;case"follow":{if(W===null)break;if(l.counter>=l.follow){i(new ae(`maximum redirect reached at: ${l.url}`,"max-redirect")),S();return}let x={headers:new be(l.headers),follow:l.follow,counter:l.counter+1,agent:l.agent,compress:l.compress,method:l.method,body:Sn(l),signal:l.signal,size:l.size,referrer:l.referrer,referrerPolicy:l.referrerPolicy};if(!Qs(l.url,W)||!Js(l.url,W))for(let ue of["authorization","www-authenticate","cookie","cookie2"])x.headers.delete(ue);if(p.statusCode!==303&&l.body&&o.body instanceof se.default.Readable){i(new ae("Cannot follow redirect with body being a readable stream","unsupported-redirect")),S();return}(p.statusCode===303||(p.statusCode===301||p.statusCode===302)&&l.method==="POST")&&(x.method="GET",x.body=void 0,x.headers.delete("content-length"));let ke=fl(b);ke&&(x.referrerPolicy=ke),a(Ra(new Et(W,x))),S();return}default:return i(new TypeError(`Redirect option '${l.redirect}' is not a valid value of RequestRedirect`))}}g&&p.once("end",()=>{g.removeEventListener("abort",j)});let P=(0,se.pipeline)(p,new se.PassThrough,O=>{O&&i(O)});process.version<"v12.10"&&p.on("aborted",j);let v={url:l.url,status:p.statusCode,statusText:p.statusMessage,headers:b,size:l.size,counter:l.counter,highWaterMark:l.highWaterMark},M=b.get("Content-Encoding");if(!l.compress||l.method==="HEAD"||M===null||p.statusCode===204||p.statusCode===304){y=new Q(P,v),a(y);return}let U={flush:Ze.default.Z_SYNC_FLUSH,finishFlush:Ze.default.Z_SYNC_FLUSH};if(M==="gzip"||M==="x-gzip"){P=(0,se.pipeline)(P,Ze.default.createGunzip(U),O=>{O&&i(O)}),y=new Q(P,v),a(y);return}if(M==="deflate"||M==="x-deflate"){let O=(0,se.pipeline)(p,new se.PassThrough,W=>{W&&i(W)});O.once("data",W=>{(W[0]&15)===8?P=(0,se.pipeline)(P,Ze.default.createInflate(),x=>{x&&i(x)}):P=(0,se.pipeline)(P,Ze.default.createInflateRaw(),x=>{x&&i(x)}),y=new Q(P,v),a(y)}),O.once("end",()=>{y||(y=new Q(P,v),a(y))});return}if(M==="br"){P=(0,se.pipeline)(P,Ze.default.createBrotliDecompress(),O=>{O&&i(O)}),y=new Q(P,v),a(y);return}y=new Q(P,v),a(y)}),el(L,l).catch(i)})}function pl(r,o){let a=hr.Buffer.from(`0\r
\r
`),i=!1,l=!1,c;r.on("response",m=>{let{headers:h}=m;i=h["transfer-encoding"]==="chunked"&&!h["content-length"]}),r.on("socket",m=>{let h=()=>{if(i&&!l){let y=new Error("Premature close");y.code="ERR_STREAM_PREMATURE_CLOSE",o(y)}},g=y=>{l=hr.Buffer.compare(y.slice(-5),a)===0,!l&&c&&(l=hr.Buffer.compare(c.slice(-3),a.slice(0,3))===0&&hr.Buffer.compare(y.slice(-2),a.slice(3))===0),c=y};m.prependListener("close",h),m.on("data",g),r.on("close",()=>{m.removeListener("close",h),m.removeListener("data",g)})})}At();Sr();});var Ba=Qe((eu,ka)=>{var _l=require("node:tty"),Sl=_l?.WriteStream?.prototype?.hasColors?.()??!1,T=(r,o)=>{if(!Sl)return l=>l;let a=`\x1B[${r}m`,i=`\x1B[${o}m`;return l=>{let c=l+"",m=c.indexOf(i);if(m===-1)return a+c+i;let h=a,g=0;for(;m!==-1;)h+=c.slice(g,m)+a,g=m+i.length,m=c.indexOf(i,g);return h+=c.slice(g)+i,h}},R={};R.reset=T(0,0);R.bold=T(1,22);R.dim=T(2,22);R.italic=T(3,23);R.underline=T(4,24);R.overline=T(53,55);R.inverse=T(7,27);R.hidden=T(8,28);R.strikethrough=T(9,29);R.black=T(30,39);R.red=T(31,39);R.green=T(32,39);R.yellow=T(33,39);R.blue=T(34,39);R.magenta=T(35,39);R.cyan=T(36,39);R.white=T(37,39);R.gray=T(90,39);R.bgBlack=T(40,49);R.bgRed=T(41,49);R.bgGreen=T(42,49);R.bgYellow=T(43,49);R.bgBlue=T(44,49);R.bgMagenta=T(45,49);R.bgCyan=T(46,49);R.bgWhite=T(47,49);R.bgGray=T(100,49);R.redBright=T(91,39);R.greenBright=T(92,39);R.yellowBright=T(93,39);R.blueBright=T(94,39);R.magentaBright=T(95,39);R.cyanBright=T(96,39);R.whiteBright=T(97,39);R.bgRedBright=T(101,49);R.bgGreenBright=T(102,49);R.bgYellowBright=T(103,49);R.bgBlueBright=T(104,49);R.bgMagentaBright=T(105,49);R.bgCyanBright=T(106,49);R.bgWhiteBright=T(107,49);ka.exports=R});var En=Qe((nu,Wa)=>{"use strict";var qa=require("fs"),Tn;function vl(){try{return qa.statSync("/.dockerenv"),!0}catch{return!1}}function Rl(){try{return qa.readFileSync("/proc/self/cgroup","utf8").includes("docker")}catch{return!1}}Wa.exports=()=>(Tn===void 0&&(Tn=vl()||Rl()),Tn)});var La=Qe((ou,Cn)=>{"use strict";var Tl=require("os"),El=require("fs"),Fa=En(),Ia=()=>{if(process.platform!=="linux")return!1;if(Tl.release().toLowerCase().includes("microsoft"))return!Fa();try{return El.readFileSync("/proc/version","utf8").toLowerCase().includes("microsoft")?!Fa():!1}catch{return!1}};process.env.__IS_WSL_TEST__?Cn.exports=Ia:Cn.exports=Ia()});var $a=Qe((au,xa)=>{"use strict";xa.exports=(r,o,a)=>{let i=l=>Object.defineProperty(r,o,{value:l,enumerable:!0,writable:!0});return Object.defineProperty(r,o,{configurable:!0,enumerable:!0,get(){let l=a();return i(l),l},set(l){i(l)}}),r}});var Va=Qe((iu,Ha)=>{var Cl=require("path"),Al=require("child_process"),{promises:Rr,constants:Na}=require("fs"),vr=La(),Pl=En(),Pn=$a(),za=Cl.join(__dirname,"xdg-open"),{platform:ot,arch:ja}=process,kl=()=>{try{return Rr.statSync("/run/.containerenv"),!0}catch{return!1}},An;function Bl(){return An===void 0&&(An=kl()||Pl()),An}var Ol=(()=>{let r="/mnt/",o;return async function(){if(o)return o;let a="/etc/wsl.conf",i=!1;try{await Rr.access(a,Na.F_OK),i=!0}catch{}if(!i)return r;let l=await Rr.readFile(a,{encoding:"utf8"}),c=/(?<!#.*)root\s*=\s*(?<mountPoint>.*)/g.exec(l);return c?(o=c.groups.mountPoint.trim(),o=o.endsWith("/")?o:`${o}/`,o):r}})(),Ma=async(r,o)=>{let a;for(let i of r)try{return await o(i)}catch(l){a=l}throw a},Tr=async r=>{if(r={wait:!1,background:!1,newInstance:!1,allowNonzeroExitCode:!1,...r},Array.isArray(r.app))return Ma(r.app,h=>Tr({...r,app:h}));let{name:o,arguments:a=[]}=r.app||{};if(a=[...a],Array.isArray(o))return Ma(o,h=>Tr({...r,app:{name:h,arguments:a}}));let i,l=[],c={};if(ot==="darwin")i="open",r.wait&&l.push("--wait-apps"),r.background&&l.push("--background"),r.newInstance&&l.push("--new"),o&&l.push("-a",o);else if(ot==="win32"||vr&&!Bl()&&!o){let h=await Ol();i=vr?`${h}c/Windows/System32/WindowsPowerShell/v1.0/powershell.exe`:`${process.env.SYSTEMROOT}\\System32\\WindowsPowerShell\\v1.0\\powershell`,l.push("-NoProfile","-NonInteractive","\u2013ExecutionPolicy","Bypass","-EncodedCommand"),vr||(c.windowsVerbatimArguments=!0);let g=["Start"];r.wait&&g.push("-Wait"),o?(g.push(`"\`"${o}\`""`,"-ArgumentList"),r.target&&a.unshift(r.target)):r.target&&g.push(`"${r.target}"`),a.length>0&&(a=a.map(y=>`"\`"${y}\`""`),g.push(a.join(","))),r.target=Buffer.from(g.join(" "),"utf16le").toString("base64")}else{if(o)i=o;else{let h=!__dirname||__dirname==="/",g=!1;try{await Rr.access(za,Na.X_OK),g=!0}catch{}i=process.versions.electron||ot==="android"||h||!g?"xdg-open":za}a.length>0&&l.push(...a),r.wait||(c.stdio="ignore",c.detached=!0)}r.target&&l.push(r.target),ot==="darwin"&&a.length>0&&l.push("--args",...a);let m=Al.spawn(i,l,c);return r.wait?new Promise((h,g)=>{m.once("error",g),m.once("close",y=>{if(!r.allowNonzeroExitCode&&y>0){g(new Error(`Exited with code ${y}`));return}h(m)})}):(m.unref(),m)},kn=(r,o)=>{if(typeof r!="string")throw new TypeError("Expected a `target`");return Tr({...o,target:r})},Dl=(r,o)=>{if(typeof r!="string")throw new TypeError("Expected a `name`");let{arguments:a=[]}=o||{};if(a!=null&&!Array.isArray(a))throw new TypeError("Expected `appArguments` as Array type");return Tr({...o,app:{name:r,arguments:a}})};function Ua(r){if(typeof r=="string"||Array.isArray(r))return r;let{[ja]:o}=r;if(!o)throw new Error(`${ja} is not supported`);return o}function Bn({[ot]:r},{wsl:o}){if(o&&vr)return Ua(o);if(!r)throw new Error(`${ot} is not supported`);return Ua(r)}var Er={};Pn(Er,"chrome",()=>Bn({darwin:"google chrome",win32:"chrome",linux:["google-chrome","google-chrome-stable","chromium"]},{wsl:{ia32:"/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe",x64:["/mnt/c/Program Files/Google/Chrome/Application/chrome.exe","/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe"]}}));Pn(Er,"firefox",()=>Bn({darwin:"firefox",win32:"C:\\Program Files\\Mozilla Firefox\\firefox.exe",linux:"firefox"},{wsl:"/mnt/c/Program Files/Mozilla Firefox/firefox.exe"}));Pn(Er,"edge",()=>Bn({darwin:"microsoft edge",win32:"msedge",linux:["microsoft-edge","microsoft-edge-dev"]},{wsl:"/mnt/c/Program Files (x86)/Microsoft/Edge/Application/msedge.exe"}));kn.apps=Er;kn.openApp=Dl;Ha.exports=kn});var Fl={};Ps(Fl,{default:()=>Ar});module.exports=ks(Fl);var Qa=require("@oclif/core");var Bt=Je(require("node:fs")),Rn=Je(require("node:path")),Ca=Je(require("node:os"));var kt={production:{url:"https://www.raycast.com",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},staging:{url:"https://www.rayca.st",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},development:{url:"http://localhost:3000",clientID:"rOCD_LhnXFObSLu9v7788qOGu25FZDqEzGjet4Q45ZY"}}[process.env.API_ENV||"production"],bl="e69bae0ec90f5e838555",ge={},Aa;function Pa(r){Aa=r;try{ge=JSON.parse(Bt.readFileSync(Rn.join(yl(),"config.json"),"utf8"))}catch(o){if(o instanceof Error&&o.code==="ENOENT")return;throw new Error(`Failed to read config file: ${o}`)}}function Ot(r){switch(r){case"raycastApiURL":return process.env.RAY_APIURL||ge.APIURL||kt.url;case"raycastAccessToken":return process.env.RAY_TOKEN||ge.Token||ge.AccessToken||"";case"raycastClientId":return process.env.RAY_CLIENT_ID||ge.ClientID||kt.clientID;case"githubClientId":return process.env.RAY_GithubClientID||ge.GithubClientID||bl;case"githubAccessToken":return process.env.GITHUB_ACCESS_TOKEN||ge.GithubAccessToken||"";case"flavorName":return process.env.RAY_Target?process.env.RAY_Target:typeof ge.Target<"u"?ge.Target:vn(process.platform==="win32"?"x":"release")}}function vn(r){switch(r){case"debug":return"debug";case"internal":return"internal";case"release":return"";case"x":return"x";case"x-internal":return"x-internal";case"x-development":return"x-development";case void 0:return Ot("flavorName")}}function gl(){let r=vn(Aa);return r==""?"raycast":`raycast-${r}`}function yl(){let r=Rn.join(Ca.default.homedir(),".config",gl());return Bt.mkdirSync(r,{recursive:!0}),r}var F=Je(Ba());var tu=(0,F.blue)((0,F.dim)("internal only"));function Oa(r){Object.entries(r).forEach(([o,a])=>{console.log(`${(0,F.blue)(`- ${o}: `)}${a}`)})}var Ie={wait:`\u{1F550}${(0,F.blue)("wait")}  - `,info:`${process.env.GITHUB_ACTIONS==="true"?"\u2139\uFE0F":"\u2139\uFE0F "}${(0,F.cyan)("info")}  - `,success:`\u2705${(0,F.green)("ready")}  - `,warn:`${process.env.GITHUB_ACTIONS==="true"?"\u26A0\uFE0F":"\u26A0\uFE0F "}${(0,F.yellow)("warn")}  - `,error:`\u{1F4A5}${(0,F.red)("error")}  - `,event:`\u26A1\uFE0F${(0,F.magenta)("event")}  - `,paymentPrompt:`\u{1F4B0}${(0,F.yellowBright)("plan")}  - `},wl=!0;function Da(r,o){r||(Ie.wait=`${(0,F.blue)("wait")}  - `,Ie.info=`${(0,F.cyan)("info")}  - `,Ie.success=`${(0,F.green)("ready")}  - `,Ie.warn=`${(0,F.yellow)("warn")}  - `,Ie.error=`${(0,F.red)("error")}  - `,Ie.event=`${(0,F.magenta)("event")}  - `,Ie.paymentPrompt=`${(0,F.yellowBright)("plan")}  - `),o&&(wl=!1)}var ql=require("@oclif/core"),Ya=Je(Ea()),Wl=Je(Va());async function Ga(r,o){let a;try{a=await(0,Ya.default)(r,{method:o.method||"GET",headers:{"Content-Type":"application/json",Accept:"application/json",...o.token?{Authorization:`Bearer ${o.token}`}:void 0},body:o.body})}catch(i){throw new Error(`HTTP request: ${i.message}`)}if(!a.ok){switch(a.status){case 401:throw new ye(a,"not authorized - please log in first using `npx ray login`");case 403:throw new ye(a,"forbidden - you don't have permissions to perform the request");case 402:throw new ye(a,"the limit of free commands has been reached")}let i=await a.text(),l;try{l=JSON.parse(i)}catch{throw new ye(a,`HTTP error: ${a.status} - ${i}`)}throw Array.isArray(l.errors)&&l.errors.length>0?new ye(a,`error: ${l.errors[0].status} - ${l.errors[0].title}`):new ye(a,`HTTP error: ${a.status} - ${i}`)}return await a.json()}var ye=class extends Error{constructor(o,a){let i=o.headers.get("X-Request-Id");i?super(`${a} (${o.url} RequestID: ${i})`):super(a),this.name="HTTPError"}};var pu=`${kt.url}/sessions/success`,bu=`${kt.url}/sessions/failure`;function Ja(){Ot("raycastAccessToken")===""&&Qa.ux.error("please first log in first using `npx ray login`",{exit:1})}function Ka(){let r=Ot("raycastApiURL"),o=Ot("raycastAccessToken");return Ga(`${r}/api/v1/me`,{token:o})}var Pe=require("@oclif/core");var Cr=class extends Pe.Command{static baseFlags={"exit-on-error":Pe.Flags.boolean({default:!0,helpGroup:"GLOBAL",aliases:["exitOnError"],deprecateAliases:!0,summary:"Always exit with non-zero code on error",allowNo:!0}),emoji:Pe.Flags.boolean({default:!1,helpGroup:"GLOBAL",summary:"Prefix output with emojis \u{1F308}"}),help:Pe.Flags.boolean({default:!1,helpGroup:"GLOBAL",summary:"Show the help message for the command"}),"non-interactive":Pe.Flags.boolean({char:"I",default:!1,helpGroup:"GLOBAL",summary:"Disable interactive outputs, useful for CI"}),target:Pe.Flags.option({char:"t",description:"Raycast app target",helpGroup:"GLOBAL",multiple:!1,options:["debug","internal","release","x","x-development","x-internal"],hidden:!0})()};flags;args;async init(){await super.init(),process.on("SIGINT",()=>process.exit(1));let{args:o,flags:a}=await this.parse({flags:this.ctor.flags,baseFlags:super.ctor.baseFlags,enableJsonFlag:this.ctor.enableJsonFlag,args:this.ctor.args,strict:this.ctor.strict});this.flags=a,this.args=o,Pa(this.flags.target),Da(this.flags.emoji,this.flags["non-interactive"])}error(o,a){return a?.message&&o instanceof Error&&(o.message=`${a.message} (${o.message})`,delete a.message),super.error(o,a)}async catch(o){return super.catch(o)}async finally(o){return super.finally(o)}};var Ar=class r extends Cr{static enableJsonFlag=!0;static description="Show profile of the currently logged in user";async run(){let{flags:o}=await this.parse(r);Ja();try{let a=await Ka(),i={Username:a.username,Name:a.name,Email:a.email};o.json?this.logJson(i):Oa(i)}catch(a){this.error(a,{message:"failed fetching profile"})}}};
/*! Bundled license information:

node-fetch-cjs/dist/index.js:
  (*! fetch-blob. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
  (*! formdata-polyfill. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
  (*! node-domexception. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
*/
