"use strict";var m=Object.create;var n=Object.defineProperty;var f=Object.getOwnPropertyDescriptor;var h=Object.getOwnPropertyNames;var l=Object.getPrototypeOf,u=Object.prototype.hasOwnProperty;var g=(a,e)=>{for(var t in e)n(a,t,{get:e[t],enumerable:!0})},s=(a,e,t,o)=>{if(e&&typeof e=="object"||typeof e=="function")for(let r of h(e))!u.call(a,r)&&r!==t&&n(a,r,{get:()=>e[r],enumerable:!(o=f(e,r))||o.enumerable});return a};var c=(a,e,t)=>(t=a!=null?m(l(a)):{},s(e||!a||!a.__esModule?n(t,"default",{value:a,enumerable:!0}):t,a)),w=a=>s(n({},"__esModule",{value:!0}),a);var d={};g(d,{isPrivateExtension:()=>k,readManifest:()=>y});module.exports=w(d);var p=c(require("node:path")),i=c(require("node:fs"));function y(){let a;try{a=p.resolve("package.json")}catch(t){throw new Error(`cannot resolve package manifest path: ${t}`)}let e;try{e=JSON.parse(i.readFileSync(a,"utf8"))}catch(t){throw new Error(`cannot read package manifest: ${t}`)}return e.name=e.name.replace(/^@workaround/g,""),e}function k(a){return!!a.owner&&a.access!=="public"}0&&(module.exports={isPrivateExtension,readManifest});
