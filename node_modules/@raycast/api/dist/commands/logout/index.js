"use strict";var D=Object.create;var m=Object.defineProperty;var L=Object.getOwnPropertyDescriptor;var O=Object.getOwnPropertyNames;var R=Object.getPrototypeOf,S=Object.prototype.hasOwnProperty;var F=(n,e)=>()=>(e||n((e={exports:{}}).exports,e),e.exports),U=(n,e)=>{for(var s in e)m(n,s,{get:e[s],enumerable:!0})},I=(n,e,s,c)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of O(e))!S.call(n,i)&&i!==s&&m(n,i,{get:()=>e[i],enumerable:!(c=L(e,i))||c.enumerable});return n};var h=(n,e,s)=>(s=n!=null?D(R(n)):{},I(e||!n||!n.__esModule?m(s,"default",{value:n,enumerable:!0}):s,n)),_=n=>I(m({},"__esModule",{value:!0}),n);var $=F((Q,B)=>{var J=require("node:tty"),H=J?.WriteStream?.prototype?.hasColors?.()??!1,t=(n,e)=>{if(!H)return i=>i;let s=`\x1B[${n}m`,c=`\x1B[${e}m`;return i=>{let g=i+"",p=g.indexOf(c);if(p===-1)return s+g+c;let w=s,d=0;for(;p!==-1;)w+=g.slice(d,p)+s,d=p+c.length,p=g.indexOf(c,d);return w+=g.slice(d)+c,w}},r={};r.reset=t(0,0);r.bold=t(1,22);r.dim=t(2,22);r.italic=t(3,23);r.underline=t(4,24);r.overline=t(53,55);r.inverse=t(7,27);r.hidden=t(8,28);r.strikethrough=t(9,29);r.black=t(30,39);r.red=t(31,39);r.green=t(32,39);r.yellow=t(33,39);r.blue=t(34,39);r.magenta=t(35,39);r.cyan=t(36,39);r.white=t(37,39);r.gray=t(90,39);r.bgBlack=t(40,49);r.bgRed=t(41,49);r.bgGreen=t(42,49);r.bgYellow=t(43,49);r.bgBlue=t(44,49);r.bgMagenta=t(45,49);r.bgCyan=t(46,49);r.bgWhite=t(47,49);r.bgGray=t(100,49);r.redBright=t(91,39);r.greenBright=t(92,39);r.yellowBright=t(93,39);r.blueBright=t(94,39);r.magentaBright=t(95,39);r.cyanBright=t(96,39);r.whiteBright=t(97,39);r.bgRedBright=t(101,49);r.bgGreenBright=t(102,49);r.bgYellowBright=t(103,49);r.bgBlueBright=t(104,49);r.bgMagentaBright=t(105,49);r.bgCyanBright=t(106,49);r.bgWhiteBright=t(107,49);B.exports=r});var V={};U(V,{default:()=>x});module.exports=_(V);var f=require("@oclif/core");var u=h(require("node:fs")),y=h(require("node:path")),E=h(require("node:os"));var T={production:{url:"https://www.raycast.com",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},staging:{url:"https://www.rayca.st",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},development:{url:"http://localhost:3000",clientID:"rOCD_LhnXFObSLu9v7788qOGu25FZDqEzGjet4Q45ZY"}}[process.env.API_ENV||"production"],j="e69bae0ec90f5e838555",a={},C;function v(n){C=n;try{a=JSON.parse(u.readFileSync(y.join(N(),"config.json"),"utf8"))}catch(e){if(e instanceof Error&&e.code==="ENOENT")return;throw new Error(`Failed to read config file: ${e}`)}}function Y(n){switch(n){case"raycastApiURL":return process.env.RAY_APIURL||a.APIURL||T.url;case"raycastAccessToken":return process.env.RAY_TOKEN||a.Token||a.AccessToken||"";case"raycastClientId":return process.env.RAY_CLIENT_ID||a.ClientID||T.clientID;case"githubClientId":return process.env.RAY_GithubClientID||a.GithubClientID||j;case"githubAccessToken":return process.env.GITHUB_ACCESS_TOKEN||a.GithubAccessToken||"";case"flavorName":return process.env.RAY_Target?process.env.RAY_Target:typeof a.Target<"u"?a.Target:A(process.platform==="win32"?"x":"release")}}function k(n,e){switch(n){case"raycastApiURL":e===void 0?delete a.APIURL:a.APIURL=e;break;case"raycastAccessToken":e===void 0?delete a.Token:a.Token=e,delete a.AccessToken;break;case"raycastClientId":e===void 0?delete a.ClientID:a.ClientID=e;break;case"githubAccessToken":e===void 0?delete a.GithubAccessToken:a.GithubAccessToken=e;break;case"flavorName":e===void 0?delete a.Target:a.Target=e;break}let s=N();u.writeFileSync(y.join(s,"config.json"),JSON.stringify(a,null,"  "),"utf8")}function A(n){switch(n){case"debug":return"debug";case"internal":return"internal";case"release":return"";case"x":return"x";case"x-internal":return"x-internal";case"x-development":return"x-development";case void 0:return Y("flavorName")}}function M(){let n=A(C);return n==""?"raycast":`raycast-${n}`}function N(){let n=y.join(E.default.homedir(),".config",M());return u.mkdirSync(n,{recursive:!0}),n}var o=h($());var ee=(0,o.blue)((0,o.dim)("internal only"));function P(n,e,s){console.log(l[n]+e),typeof s?.exit<"u"&&process.exit(s.exit)}var l={wait:`\u{1F550}${(0,o.blue)("wait")}  - `,info:`${process.env.GITHUB_ACTIONS==="true"?"\u2139\uFE0F":"\u2139\uFE0F "}${(0,o.cyan)("info")}  - `,success:`\u2705${(0,o.green)("ready")}  - `,warn:`${process.env.GITHUB_ACTIONS==="true"?"\u26A0\uFE0F":"\u26A0\uFE0F "}${(0,o.yellow)("warn")}  - `,error:`\u{1F4A5}${(0,o.red)("error")}  - `,event:`\u26A1\uFE0F${(0,o.magenta)("event")}  - `,paymentPrompt:`\u{1F4B0}${(0,o.yellowBright)("plan")}  - `},K=!0;function G(n,e){n||(l.wait=`${(0,o.blue)("wait")}  - `,l.info=`${(0,o.cyan)("info")}  - `,l.success=`${(0,o.green)("ready")}  - `,l.warn=`${(0,o.yellow)("warn")}  - `,l.error=`${(0,o.red)("error")}  - `,l.event=`${(0,o.magenta)("event")}  - `,l.paymentPrompt=`${(0,o.yellowBright)("plan")}  - `),e&&(K=!1)}var b=class extends f.Command{static baseFlags={"exit-on-error":f.Flags.boolean({default:!0,helpGroup:"GLOBAL",aliases:["exitOnError"],deprecateAliases:!0,summary:"Always exit with non-zero code on error",allowNo:!0}),emoji:f.Flags.boolean({default:!1,helpGroup:"GLOBAL",summary:"Prefix output with emojis \u{1F308}"}),help:f.Flags.boolean({default:!1,helpGroup:"GLOBAL",summary:"Show the help message for the command"}),"non-interactive":f.Flags.boolean({char:"I",default:!1,helpGroup:"GLOBAL",summary:"Disable interactive outputs, useful for CI"}),target:f.Flags.option({char:"t",description:"Raycast app target",helpGroup:"GLOBAL",multiple:!1,options:["debug","internal","release","x","x-development","x-internal"],hidden:!0})()};flags;args;async init(){await super.init(),process.on("SIGINT",()=>process.exit(1));let{args:e,flags:s}=await this.parse({flags:this.ctor.flags,baseFlags:super.ctor.baseFlags,enableJsonFlag:this.ctor.enableJsonFlag,args:this.ctor.args,strict:this.ctor.strict});this.flags=s,this.args=e,v(this.flags.target),G(this.flags.emoji,this.flags["non-interactive"])}error(e,s){return s?.message&&e instanceof Error&&(e.message=`${s.message} (${e.message})`,delete s.message),super.error(e,s)}async catch(e){return super.catch(e)}async finally(e){return super.finally(e)}};var x=class extends b{static description="Log out of your Raycast account";async run(){try{k("raycastAccessToken",void 0)}catch(e){this.error(`failed clearing token (${e.message})`)}P("success","logged out successfully")}};
