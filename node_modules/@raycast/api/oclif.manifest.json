{"commands": {"build": {"aliases": [], "args": {}, "description": "Build the extension to the output directory", "flags": {"exit-on-error": {"aliases": ["exitOnError"], "deprecateAliases": true, "helpGroup": "GLOBAL", "name": "exit-on-error", "summary": "Always exit with non-zero code on error", "allowNo": true, "type": "boolean"}, "emoji": {"helpGroup": "GLOBAL", "name": "emoji", "summary": "Prefix output with emojis 🌈", "allowNo": false, "type": "boolean"}, "help": {"helpGroup": "GLOBAL", "name": "help", "summary": "Show the help message for the command", "allowNo": false, "type": "boolean"}, "non-interactive": {"char": "I", "helpGroup": "GLOBAL", "name": "non-interactive", "summary": "Disable interactive outputs, useful for CI", "allowNo": false, "type": "boolean"}, "target": {"char": "t", "description": "Raycast app target", "helpGroup": "GLOBAL", "hidden": true, "name": "target", "hasDynamicHelp": false, "multiple": false, "options": ["debug", "internal", "release", "x", "x-development", "x-internal"], "type": "option"}, "environment": {"char": "e", "description": "Environment to build for", "name": "environment", "default": "dev", "hasDynamicHelp": false, "multiple": false, "options": ["dev", "dist"], "type": "option"}, "output": {"char": "o", "description": "Output path", "name": "output", "hasDynamicHelp": false, "multiple": false, "type": "option"}, "skip-types": {"description": "Indicates whether to skip types generation \u001b[34m\u001b[2minternal only\u001b[22m\u001b[39m", "hidden": true, "name": "skip-types", "allowNo": false, "type": "boolean"}, "print-tool-schemas": {"description": "Indicates whether to print the schema tools \u001b[34m\u001b[2minternal only\u001b[22m\u001b[39m", "hidden": true, "name": "print-tool-schemas", "allowNo": false, "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "build", "pluginAlias": "ray", "pluginName": "ray", "pluginType": "core", "strict": true, "enableJsonFlag": false, "isESM": false, "relativePath": ["dist", "commands", "build", "index.js"]}, "develop": {"aliases": ["dev"], "args": {}, "description": "Start the extension in development mode and watches for changes", "flags": {"exit-on-error": {"aliases": ["exitOnError"], "deprecateAliases": true, "helpGroup": "GLOBAL", "name": "exit-on-error", "summary": "Always exit with non-zero code on error", "allowNo": true, "type": "boolean"}, "emoji": {"helpGroup": "GLOBAL", "name": "emoji", "summary": "Prefix output with emojis 🌈", "allowNo": false, "type": "boolean"}, "help": {"helpGroup": "GLOBAL", "name": "help", "summary": "Show the help message for the command", "allowNo": false, "type": "boolean"}, "non-interactive": {"char": "I", "helpGroup": "GLOBAL", "name": "non-interactive", "summary": "Disable interactive outputs, useful for CI", "allowNo": false, "type": "boolean"}, "target": {"char": "t", "description": "Raycast app target", "helpGroup": "GLOBAL", "hidden": true, "name": "target", "hasDynamicHelp": false, "multiple": false, "options": ["debug", "internal", "release", "x", "x-development", "x-internal"], "type": "option"}, "print-tool-schemas": {"description": "Indicates whether to print the schema tools \u001b[34m\u001b[2minternal only\u001b[22m\u001b[39m", "hidden": true, "name": "print-tool-schemas", "allowNo": false, "type": "boolean"}, "print-instructions": {"description": "Indicates whether to print the extension's instructions \u001b[34m\u001b[2minternal only\u001b[22m\u001b[39m", "hidden": true, "name": "print-instructions", "allowNo": false, "type": "boolean"}, "print-tool-calls": {"description": "Indicates whether to print the inputs and outputs of tools when they are called from Raycast \u001b[34m\u001b[2minternal only\u001b[22m\u001b[39m", "hidden": true, "name": "print-tool-calls", "allowNo": false, "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "develop", "pluginAlias": "ray", "pluginName": "ray", "pluginType": "core", "strict": true, "enableJsonFlag": false, "isESM": false, "relativePath": ["dist", "commands", "develop", "index.js"]}, "evals": {"aliases": [], "args": {}, "description": "Run AI evals defined in extension package.json", "flags": {"exit-on-error": {"aliases": ["exitOnError"], "deprecateAliases": true, "helpGroup": "GLOBAL", "name": "exit-on-error", "summary": "Always exit with non-zero code on error", "allowNo": true, "type": "boolean"}, "emoji": {"helpGroup": "GLOBAL", "name": "emoji", "summary": "Prefix output with emojis 🌈", "allowNo": false, "type": "boolean"}, "help": {"helpGroup": "GLOBAL", "name": "help", "summary": "Show the help message for the command", "allowNo": false, "type": "boolean"}, "non-interactive": {"char": "I", "helpGroup": "GLOBAL", "name": "non-interactive", "summary": "Disable interactive outputs, useful for CI", "allowNo": false, "type": "boolean"}, "target": {"char": "t", "description": "Raycast app target", "helpGroup": "GLOBAL", "hidden": true, "name": "target", "hasDynamicHelp": false, "multiple": false, "options": ["debug", "internal", "release", "x", "x-development", "x-internal"], "type": "option"}, "skipBuild": {"char": "s", "description": "Skip extension build step", "name": "skipBuild", "allowNo": false, "type": "boolean"}, "extension": {"char": "e", "description": "Path to extension. Current directory is used by default", "name": "extension", "default": "./", "hasDynamicHelp": false, "multiple": false, "type": "option"}, "apiEndpoint": {"char": "a", "description": "AI Evals API endpoint", "hidden": true, "name": "apiEndpoint", "default": "https://ai-evals.raycast.com/run-evals", "hasDynamicHelp": false, "multiple": false, "type": "option"}, "only": {"char": "o", "description": "Only run specified evals (0-2, 1, 1,2,3)", "name": "only", "default": "", "hasDynamicHelp": false, "multiple": false, "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "evals", "pluginAlias": "ray", "pluginName": "ray", "pluginType": "core", "strict": true, "enableJsonFlag": false, "isESM": false, "relativePath": ["dist", "commands", "evals", "index.js"]}, "lint": {"aliases": [], "args": {}, "description": "Validate the extension manifest and metadata, and lint its source code", "flags": {"exit-on-error": {"aliases": ["exitOnError"], "deprecateAliases": true, "helpGroup": "GLOBAL", "name": "exit-on-error", "summary": "Always exit with non-zero code on error", "allowNo": true, "type": "boolean"}, "emoji": {"helpGroup": "GLOBAL", "name": "emoji", "summary": "Prefix output with emojis 🌈", "allowNo": false, "type": "boolean"}, "help": {"helpGroup": "GLOBAL", "name": "help", "summary": "Show the help message for the command", "allowNo": false, "type": "boolean"}, "non-interactive": {"char": "I", "helpGroup": "GLOBAL", "name": "non-interactive", "summary": "Disable interactive outputs, useful for CI", "allowNo": false, "type": "boolean"}, "target": {"char": "t", "description": "Raycast app target", "helpGroup": "GLOBAL", "hidden": true, "name": "target", "hasDynamicHelp": false, "multiple": false, "options": ["debug", "internal", "release", "x", "x-development", "x-internal"], "type": "option"}, "fix": {"char": "f", "description": "Attempt to fix linting issues", "name": "fix", "allowNo": false, "type": "boolean"}, "relaxed": {"char": "r", "description": "Use relaxed linting mode to skip validation of: package.json schema, icons and metadata.", "name": "relaxed", "allowNo": false, "type": "boolean"}, "schema": {"char": "s", "description": "Path to JSON schema for package.json validation \u001b[34m\u001b[2minternal only\u001b[22m\u001b[39m", "hidden": true, "name": "schema", "default": "https://www.raycast.com/schemas/extension.json", "hasDynamicHelp": false, "multiple": false, "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "lint", "pluginAlias": "ray", "pluginName": "ray", "pluginType": "core", "strict": true, "enableJsonFlag": false, "isESM": false, "relativePath": ["dist", "commands", "lint", "index.js"]}, "login": {"aliases": [], "args": {}, "description": "Log into your Raycast account", "flags": {"exit-on-error": {"aliases": ["exitOnError"], "deprecateAliases": true, "helpGroup": "GLOBAL", "name": "exit-on-error", "summary": "Always exit with non-zero code on error", "allowNo": true, "type": "boolean"}, "emoji": {"helpGroup": "GLOBAL", "name": "emoji", "summary": "Prefix output with emojis 🌈", "allowNo": false, "type": "boolean"}, "help": {"helpGroup": "GLOBAL", "name": "help", "summary": "Show the help message for the command", "allowNo": false, "type": "boolean"}, "non-interactive": {"char": "I", "helpGroup": "GLOBAL", "name": "non-interactive", "summary": "Disable interactive outputs, useful for CI", "allowNo": false, "type": "boolean"}, "target": {"char": "t", "description": "Raycast app target", "helpGroup": "GLOBAL", "hidden": true, "name": "target", "hasDynamicHelp": false, "multiple": false, "options": ["debug", "internal", "release", "x", "x-development", "x-internal"], "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "login", "pluginAlias": "ray", "pluginName": "ray", "pluginType": "core", "strict": true, "enableJsonFlag": false, "isESM": false, "relativePath": ["dist", "commands", "login", "index.js"]}, "logout": {"aliases": [], "args": {}, "description": "Log out of your Raycast account", "flags": {"exit-on-error": {"aliases": ["exitOnError"], "deprecateAliases": true, "helpGroup": "GLOBAL", "name": "exit-on-error", "summary": "Always exit with non-zero code on error", "allowNo": true, "type": "boolean"}, "emoji": {"helpGroup": "GLOBAL", "name": "emoji", "summary": "Prefix output with emojis 🌈", "allowNo": false, "type": "boolean"}, "help": {"helpGroup": "GLOBAL", "name": "help", "summary": "Show the help message for the command", "allowNo": false, "type": "boolean"}, "non-interactive": {"char": "I", "helpGroup": "GLOBAL", "name": "non-interactive", "summary": "Disable interactive outputs, useful for CI", "allowNo": false, "type": "boolean"}, "target": {"char": "t", "description": "Raycast app target", "helpGroup": "GLOBAL", "hidden": true, "name": "target", "hasDynamicHelp": false, "multiple": false, "options": ["debug", "internal", "release", "x", "x-development", "x-internal"], "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "logout", "pluginAlias": "ray", "pluginName": "ray", "pluginType": "core", "strict": true, "enableJsonFlag": false, "isESM": false, "relativePath": ["dist", "commands", "logout", "index.js"]}, "migrate": {"aliases": [], "args": {}, "description": "Migrate the extension to a newer version of the Raycast API\nInternally, the command makes use of https://www.npmjs.com/package/@raycast/migration", "flags": {"exit-on-error": {"aliases": ["exitOnError"], "deprecateAliases": true, "helpGroup": "GLOBAL", "name": "exit-on-error", "summary": "Always exit with non-zero code on error", "allowNo": true, "type": "boolean"}, "emoji": {"helpGroup": "GLOBAL", "name": "emoji", "summary": "Prefix output with emojis 🌈", "allowNo": false, "type": "boolean"}, "help": {"helpGroup": "GLOBAL", "name": "help", "summary": "Show the help message for the command", "allowNo": false, "type": "boolean"}, "non-interactive": {"char": "I", "helpGroup": "GLOBAL", "name": "non-interactive", "summary": "Disable interactive outputs, useful for CI", "allowNo": false, "type": "boolean"}, "target": {"char": "t", "description": "Raycast app target", "helpGroup": "GLOBAL", "hidden": true, "name": "target", "hasDynamicHelp": false, "multiple": false, "options": ["debug", "internal", "release", "x", "x-development", "x-internal"], "type": "option"}, "path": {"char": "p", "description": "Path to extension root", "name": "path", "default": ".", "hasDynamicHelp": false, "multiple": false, "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "migrate", "pluginAlias": "ray", "pluginName": "ray", "pluginType": "core", "strict": true, "summary": "Migrate the extension to a newer version of the Raycast API", "enableJsonFlag": false, "isESM": false, "relativePath": ["dist", "commands", "migrate", "index.js"]}, "profile": {"aliases": [], "args": {}, "description": "Show profile of the currently logged in user", "flags": {"json": {"description": "Format output as json.", "helpGroup": "GLOBAL", "name": "json", "allowNo": false, "type": "boolean"}, "exit-on-error": {"aliases": ["exitOnError"], "deprecateAliases": true, "helpGroup": "GLOBAL", "name": "exit-on-error", "summary": "Always exit with non-zero code on error", "allowNo": true, "type": "boolean"}, "emoji": {"helpGroup": "GLOBAL", "name": "emoji", "summary": "Prefix output with emojis 🌈", "allowNo": false, "type": "boolean"}, "help": {"helpGroup": "GLOBAL", "name": "help", "summary": "Show the help message for the command", "allowNo": false, "type": "boolean"}, "non-interactive": {"char": "I", "helpGroup": "GLOBAL", "name": "non-interactive", "summary": "Disable interactive outputs, useful for CI", "allowNo": false, "type": "boolean"}, "target": {"char": "t", "description": "Raycast app target", "helpGroup": "GLOBAL", "hidden": true, "name": "target", "hasDynamicHelp": false, "multiple": false, "options": ["debug", "internal", "release", "x", "x-development", "x-internal"], "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "profile", "pluginAlias": "ray", "pluginName": "ray", "pluginType": "core", "strict": true, "enableJsonFlag": true, "isESM": false, "relativePath": ["dist", "commands", "profile", "index.js"]}, "publish": {"aliases": [], "args": {}, "description": "Publish the extension on the Raycast Store.\nPrivate extension are a team feature. Sign up for it here: https://www.raycast.com/pricing (​https://www.raycast.com/pricing​)", "flags": {"exit-on-error": {"aliases": ["exitOnError"], "deprecateAliases": true, "helpGroup": "GLOBAL", "name": "exit-on-error", "summary": "Always exit with non-zero code on error", "allowNo": true, "type": "boolean"}, "emoji": {"helpGroup": "GLOBAL", "name": "emoji", "summary": "Prefix output with emojis 🌈", "allowNo": false, "type": "boolean"}, "help": {"helpGroup": "GLOBAL", "name": "help", "summary": "Show the help message for the command", "allowNo": false, "type": "boolean"}, "non-interactive": {"char": "I", "helpGroup": "GLOBAL", "name": "non-interactive", "summary": "Disable interactive outputs, useful for CI", "allowNo": false, "type": "boolean"}, "target": {"char": "t", "description": "Raycast app target", "helpGroup": "GLOBAL", "hidden": true, "name": "target", "hasDynamicHelp": false, "multiple": false, "options": ["debug", "internal", "release", "x", "x-development", "x-internal"], "type": "option"}, "clipboard": {"char": "C", "description": "Copy the extension link to the clipboard upon publishing", "name": "clipboard", "allowNo": false, "type": "boolean"}, "skip-validation": {"char": "s", "description": "Skip validation and always publish \u001b[34m\u001b[2minternal only\u001b[22m\u001b[39m", "hidden": true, "name": "skip-validation", "allowNo": false, "type": "boolean"}, "skip-notify-raycast": {"description": "Skip notifying Raycast after publishing \u001b[34m\u001b[2minternal only\u001b[22m\u001b[39m", "hidden": true, "name": "skip-notify-raycast", "allowNo": false, "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "publish", "pluginAlias": "ray", "pluginName": "ray", "pluginType": "core", "strict": true, "summary": "Publish the extension", "enableJsonFlag": false, "isESM": false, "relativePath": ["dist", "commands", "publish", "index.js"]}, "pull-contributions": {"aliases": [], "args": {}, "description": "Pull contributions of the extension", "flags": {"exit-on-error": {"aliases": ["exitOnError"], "deprecateAliases": true, "helpGroup": "GLOBAL", "name": "exit-on-error", "summary": "Always exit with non-zero code on error", "allowNo": true, "type": "boolean"}, "emoji": {"helpGroup": "GLOBAL", "name": "emoji", "summary": "Prefix output with emojis 🌈", "allowNo": false, "type": "boolean"}, "help": {"helpGroup": "GLOBAL", "name": "help", "summary": "Show the help message for the command", "allowNo": false, "type": "boolean"}, "non-interactive": {"char": "I", "helpGroup": "GLOBAL", "name": "non-interactive", "summary": "Disable interactive outputs, useful for CI", "allowNo": false, "type": "boolean"}, "target": {"char": "t", "description": "Raycast app target", "helpGroup": "GLOBAL", "hidden": true, "name": "target", "hasDynamicHelp": false, "multiple": false, "options": ["debug", "internal", "release", "x", "x-development", "x-internal"], "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "pull-contributions", "pluginAlias": "ray", "pluginName": "ray", "pluginType": "core", "strict": true, "enableJsonFlag": false, "isESM": false, "relativePath": ["dist", "commands", "pull-contributions", "index.js"]}, "token": {"aliases": [], "args": {}, "description": "Display the access token", "flags": {"json": {"description": "Format output as json.", "helpGroup": "GLOBAL", "name": "json", "allowNo": false, "type": "boolean"}, "exit-on-error": {"aliases": ["exitOnError"], "deprecateAliases": true, "helpGroup": "GLOBAL", "name": "exit-on-error", "summary": "Always exit with non-zero code on error", "allowNo": true, "type": "boolean"}, "emoji": {"helpGroup": "GLOBAL", "name": "emoji", "summary": "Prefix output with emojis 🌈", "allowNo": false, "type": "boolean"}, "help": {"helpGroup": "GLOBAL", "name": "help", "summary": "Show the help message for the command", "allowNo": false, "type": "boolean"}, "non-interactive": {"char": "I", "helpGroup": "GLOBAL", "name": "non-interactive", "summary": "Disable interactive outputs, useful for CI", "allowNo": false, "type": "boolean"}, "target": {"char": "t", "description": "Raycast app target", "helpGroup": "GLOBAL", "hidden": true, "name": "target", "hasDynamicHelp": false, "multiple": false, "options": ["debug", "internal", "release", "x", "x-development", "x-internal"], "type": "option"}, "clipboard": {"char": "C", "description": "Copy the token to the clipboard", "name": "clipboard", "allowNo": false, "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "token", "pluginAlias": "ray", "pluginName": "ray", "pluginType": "core", "strict": true, "enableJsonFlag": true, "isESM": false, "relativePath": ["dist", "commands", "token", "index.js"]}, "validate": {"aliases": [], "args": {}, "description": "Validate the extension so that it can be published - \u001b[34m\u001b[2minternal only\u001b[22m\u001b[39m", "flags": {"exit-on-error": {"aliases": ["exitOnError"], "deprecateAliases": true, "helpGroup": "GLOBAL", "name": "exit-on-error", "summary": "Always exit with non-zero code on error", "allowNo": true, "type": "boolean"}, "emoji": {"helpGroup": "GLOBAL", "name": "emoji", "summary": "Prefix output with emojis 🌈", "allowNo": false, "type": "boolean"}, "help": {"helpGroup": "GLOBAL", "name": "help", "summary": "Show the help message for the command", "allowNo": false, "type": "boolean"}, "non-interactive": {"char": "I", "helpGroup": "GLOBAL", "name": "non-interactive", "summary": "Disable interactive outputs, useful for CI", "allowNo": false, "type": "boolean"}, "target": {"char": "t", "description": "Raycast app target", "helpGroup": "GLOBAL", "hidden": true, "name": "target", "hasDynamicHelp": false, "multiple": false, "options": ["debug", "internal", "release", "x", "x-development", "x-internal"], "type": "option"}, "schema": {"char": "s", "description": "Path to JSON schema for package.json validation ", "name": "schema", "default": "https://www.raycast.com/schemas/extension.json", "hasDynamicHelp": false, "multiple": false, "type": "option"}, "skip-owner": {"description": "Skip owner checks", "name": "skip-owner", "allowNo": false, "type": "boolean"}}, "hasDynamicHelp": false, "hidden": true, "hiddenAliases": [], "id": "validate", "pluginAlias": "ray", "pluginName": "ray", "pluginType": "core", "strict": true, "summary": "Validate the extension - \u001b[34m\u001b[2minternal only\u001b[22m\u001b[39m", "enableJsonFlag": false, "isESM": false, "relativePath": ["dist", "commands", "validate", "index.js"]}, "version": {"aliases": [], "args": {}, "description": "Print the version number", "flags": {"exit-on-error": {"aliases": ["exitOnError"], "deprecateAliases": true, "helpGroup": "GLOBAL", "name": "exit-on-error", "summary": "Always exit with non-zero code on error", "allowNo": true, "type": "boolean"}, "emoji": {"helpGroup": "GLOBAL", "name": "emoji", "summary": "Prefix output with emojis 🌈", "allowNo": false, "type": "boolean"}, "help": {"helpGroup": "GLOBAL", "name": "help", "summary": "Show the help message for the command", "allowNo": false, "type": "boolean"}, "non-interactive": {"char": "I", "helpGroup": "GLOBAL", "name": "non-interactive", "summary": "Disable interactive outputs, useful for CI", "allowNo": false, "type": "boolean"}, "target": {"char": "t", "description": "Raycast app target", "helpGroup": "GLOBAL", "hidden": true, "name": "target", "hasDynamicHelp": false, "multiple": false, "options": ["debug", "internal", "release", "x", "x-development", "x-internal"], "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "version", "pluginAlias": "ray", "pluginName": "ray", "pluginType": "core", "strict": true, "enableJsonFlag": false, "isESM": false, "relativePath": ["dist", "commands", "version", "index.js"]}}, "version": "1.102.3"}