import { LaunchProps, showHUD, closeMainWindow } from "@raycast/api";
import { StorageService } from "./utils/storage";
import { ValidationUtils } from "./utils/validation";
import { format } from "date-fns";

interface LaunchArguments {
  content?: string;
}

export default async function RecordCommand(props: LaunchProps<{ arguments: LaunchArguments }>) {
  try {
    const inputContent = props.arguments?.content?.trim();

    if (!inputContent) {
      await showHUD("请输入需要记录的内容");
      return;
    }

    const validationError = ValidationUtils.validateContent(inputContent);
    if (validationError) {
      await showHUD(validationError);
      return;
    }

    const sanitizedContent = ValidationUtils.sanitizeContent(inputContent);
    await StorageService.saveRecord(sanitizedContent);

    await closeMainWindow();

    const time = format(new Date(), "HH:mm");
    await showHUD(`已记录: "${sanitizedContent}" (${time})`);
  } catch (error) {
    console.error("Record command error:", error);
    await showHUD("❌ 记录失败，请重试");
  }
}
