"use strict";var Ts=Object.create;var lr=Object.defineProperty;var Es=Object.getOwnPropertyDescriptor;var Cs=Object.getOwnPropertyNames;var As=Object.getPrototypeOf,Ps=Object.prototype.hasOwnProperty;var Je=(r,n)=>()=>(n||r((n={exports:{}}).exports,n),n.exports),ks=(r,n)=>{for(var i in n)lr(r,i,{get:n[i],enumerable:!0})},Vo=(r,n,i,a)=>{if(n&&typeof n=="object"||typeof n=="function")for(let l of Cs(n))!Ps.call(r,l)&&l!==i&&lr(r,l,{get:()=>n[l],enumerable:!(a=Es(n,l))||a.enumerable});return r};var Ae=(r,n,i)=>(i=r!=null?Ts(As(r)):{},Vo(n||!r||!r.__esModule?lr(i,"default",{value:r,enumerable:!0}):i,r)),Os=r=>Vo(lr({},"__esModule",{value:!0}),r);var Xo=Je((Zl,Ko)=>{var qs=require("node:tty"),xs=qs?.WriteStream?.prototype?.hasColors?.()??!1,T=(r,n)=>{if(!xs)return l=>l;let i=`\x1B[${r}m`,a=`\x1B[${n}m`;return l=>{let c=l+"",m=c.indexOf(a);if(m===-1)return i+c+a;let h=i,g=0;for(;m!==-1;)h+=c.slice(g,m)+i,g=m+a.length,m=c.indexOf(a,g);return h+=c.slice(g)+a,h}},R={};R.reset=T(0,0);R.bold=T(1,22);R.dim=T(2,22);R.italic=T(3,23);R.underline=T(4,24);R.overline=T(53,55);R.inverse=T(7,27);R.hidden=T(8,28);R.strikethrough=T(9,29);R.black=T(30,39);R.red=T(31,39);R.green=T(32,39);R.yellow=T(33,39);R.blue=T(34,39);R.magenta=T(35,39);R.cyan=T(36,39);R.white=T(37,39);R.gray=T(90,39);R.bgBlack=T(40,49);R.bgRed=T(41,49);R.bgGreen=T(42,49);R.bgYellow=T(43,49);R.bgBlue=T(44,49);R.bgMagenta=T(45,49);R.bgCyan=T(46,49);R.bgWhite=T(47,49);R.bgGray=T(100,49);R.redBright=T(91,39);R.greenBright=T(92,39);R.yellowBright=T(93,39);R.blueBright=T(94,39);R.magentaBright=T(95,39);R.cyanBright=T(96,39);R.whiteBright=T(97,39);R.bgRedBright=T(101,49);R.bgGreenBright=T(102,49);R.bgYellowBright=T(103,49);R.bgBlueBright=T(104,49);R.bgMagentaBright=T(105,49);R.bgCyanBright=T(106,49);R.bgWhiteBright=T(107,49);Ko.exports=R});var ki=Je(Pi=>{var Is=Object.create,gr=Object.defineProperty,Ls=Object.getOwnPropertyDescriptor,zs=Object.getOwnPropertyNames,js=Object.getPrototypeOf,Ms=Object.prototype.hasOwnProperty,hi=r=>gr(r,"__esModule",{value:!0}),At=(r,n)=>function(){return r&&(n=(0,r[Object.keys(r)[0]])(r=0)),n},yn=(r,n)=>function(){return n||(0,r[Object.keys(r)[0]])((n={exports:{}}).exports,n),n.exports},mi=(r,n)=>{hi(r);for(var i in n)gr(r,i,{get:n[i],enumerable:!0})},Ns=(r,n,i)=>{if(n&&typeof n=="object"||typeof n=="function")for(let a of zs(n))!Ms.call(r,a)&&a!=="default"&&gr(r,a,{get:()=>n[a],enumerable:!(i=Ls(n,a))||i.enumerable});return r},H=r=>Ns(hi(gr(r!=null?Is(js(r)):{},"default",r&&r.__esModule&&"default"in r?{get:()=>r.default,enumerable:!0}:{value:r,enumerable:!0})),r),Us=yn({"node_modules/web-streams-polyfill/dist/ponyfill.es2018.js"(r,n){(function(i,a){typeof r=="object"&&typeof n<"u"?a(r):typeof define=="function"&&define.amd?define(["exports"],a):(i=typeof globalThis<"u"?globalThis:i||self,a(i.WebStreamsPolyfill={}))})(r,function(i){"use strict";let a=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Symbol:e=>`Symbol(${e})`;function l(){}function c(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global}let m=c();function h(e){return typeof e=="object"&&e!==null||typeof e=="function"}let g=l,y=Promise,q=Promise.prototype.then,j=Promise.resolve.bind(y),x=Promise.reject.bind(y);function w(e){return new y(e)}function p(e){return j(e)}function b(e){return x(e)}function P(e,t,o){return q.call(e,t,o)}function v(e,t,o){P(P(e,t,o),void 0,g)}function M(e,t){v(e,t)}function N(e,t){v(e,void 0,t)}function B(e,t,o){return P(e,t,o)}function W(e){P(e,void 0,g)}let F=(()=>{let e=m&&m.queueMicrotask;if(typeof e=="function")return e;let t=p(void 0);return o=>P(t,o)})();function Be(e,t,o){if(typeof e!="function")throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,o)}function de(e,t,o){try{return p(Be(e,t,o))}catch(s){return b(s)}}let On=16384;class ee{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(t){let o=this._back,s=o;o._elements.length===On-1&&(s={_elements:[],_next:void 0}),o._elements.push(t),s!==o&&(this._back=s,o._next=s),++this._size}shift(){let t=this._front,o=t,s=this._cursor,u=s+1,f=t._elements,d=f[s];return u===On&&(o=t._next,u=0),--this._size,this._cursor=u,t!==o&&(this._front=o),f[s]=void 0,d}forEach(t){let o=this._cursor,s=this._front,u=s._elements;for(;(o!==u.length||s._next!==void 0)&&!(o===u.length&&(s=s._next,u=s._elements,o=0,u.length===0));)t(u[o]),++o}peek(){let t=this._front,o=this._cursor;return t._elements[o]}}function Bn(e,t){e._ownerReadableStream=t,t._reader=e,t._state==="readable"?Er(e):t._state==="closed"?Zi(e):Dn(e,t._storedError)}function Tr(e,t){let o=e._ownerReadableStream;return ne(o,t)}function he(e){e._ownerReadableStream._state==="readable"?Cr(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):ea(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),e._ownerReadableStream._reader=void 0,e._ownerReadableStream=void 0}function Ie(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function Er(e){e._closedPromise=w((t,o)=>{e._closedPromise_resolve=t,e._closedPromise_reject=o})}function Dn(e,t){Er(e),Cr(e,t)}function Zi(e){Er(e),$n(e)}function Cr(e,t){e._closedPromise_reject!==void 0&&(W(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function ea(e,t){Dn(e,t)}function $n(e){e._closedPromise_resolve!==void 0&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}let Wn=a("[[AbortSteps]]"),qn=a("[[ErrorSteps]]"),Ar=a("[[CancelSteps]]"),Pr=a("[[PullSteps]]"),xn=Number.isFinite||function(e){return typeof e=="number"&&isFinite(e)},ta=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function ra(e){return typeof e=="object"||typeof e=="function"}function me(e,t){if(e!==void 0&&!ra(e))throw new TypeError(`${t} is not an object.`)}function te(e,t){if(typeof e!="function")throw new TypeError(`${t} is not a function.`)}function na(e){return typeof e=="object"&&e!==null||typeof e=="function"}function Fn(e,t){if(!na(e))throw new TypeError(`${t} is not an object.`)}function pe(e,t,o){if(e===void 0)throw new TypeError(`Parameter ${t} is required in '${o}'.`)}function kr(e,t,o){if(e===void 0)throw new TypeError(`${t} is required in '${o}'.`)}function Or(e){return Number(e)}function In(e){return e===0?0:e}function oa(e){return In(ta(e))}function Ln(e,t){let s=Number.MAX_SAFE_INTEGER,u=Number(e);if(u=In(u),!xn(u))throw new TypeError(`${t} is not a finite number`);if(u=oa(u),u<0||u>s)throw new TypeError(`${t} is outside the accepted range of 0 to ${s}, inclusive`);return!xn(u)||u===0?0:u}function Br(e,t){if(!Te(e))throw new TypeError(`${t} is not a ReadableStream.`)}function Le(e){return new it(e)}function zn(e,t){e._reader._readRequests.push(t)}function Dr(e,t,o){let u=e._reader._readRequests.shift();o?u._closeSteps():u._chunkSteps(t)}function Ot(e){return e._reader._readRequests.length}function jn(e){let t=e._reader;return!(t===void 0||!we(t))}class it{constructor(t){if(pe(t,1,"ReadableStreamDefaultReader"),Br(t,"First parameter"),Ee(t))throw new TypeError("This stream has already been locked for exclusive reading by another reader");Bn(this,t),this._readRequests=new ee}get closed(){return we(this)?this._closedPromise:b(Bt("closed"))}cancel(t=void 0){return we(this)?this._ownerReadableStream===void 0?b(Ie("cancel")):Tr(this,t):b(Bt("cancel"))}read(){if(!we(this))return b(Bt("read"));if(this._ownerReadableStream===void 0)return b(Ie("read from"));let t,o,s=w((f,d)=>{t=f,o=d});return at(this,{_chunkSteps:f=>t({value:f,done:!1}),_closeSteps:()=>t({value:void 0,done:!0}),_errorSteps:f=>o(f)}),s}releaseLock(){if(!we(this))throw Bt("releaseLock");if(this._ownerReadableStream!==void 0){if(this._readRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");he(this)}}}Object.defineProperties(it.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),typeof a.toStringTag=="symbol"&&Object.defineProperty(it.prototype,a.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});function we(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_readRequests")?!1:e instanceof it}function at(e,t){let o=e._ownerReadableStream;o._disturbed=!0,o._state==="closed"?t._closeSteps():o._state==="errored"?t._errorSteps(o._storedError):o._readableStreamController[Pr](t)}function Bt(e){return new TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}let Mn=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype);class Nn{constructor(t,o){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=t,this._preventCancel=o}next(){let t=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?B(this._ongoingPromise,t,t):t(),this._ongoingPromise}return(t){let o=()=>this._returnSteps(t);return this._ongoingPromise?B(this._ongoingPromise,o,o):o()}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});let t=this._reader;if(t._ownerReadableStream===void 0)return b(Ie("iterate"));let o,s,u=w((d,_)=>{o=d,s=_});return at(t,{_chunkSteps:d=>{this._ongoingPromise=void 0,F(()=>o({value:d,done:!1}))},_closeSteps:()=>{this._ongoingPromise=void 0,this._isFinished=!0,he(t),o({value:void 0,done:!0})},_errorSteps:d=>{this._ongoingPromise=void 0,this._isFinished=!0,he(t),s(d)}}),u}_returnSteps(t){if(this._isFinished)return Promise.resolve({value:t,done:!0});this._isFinished=!0;let o=this._reader;if(o._ownerReadableStream===void 0)return b(Ie("finish iterating"));if(!this._preventCancel){let s=Tr(o,t);return he(o),B(s,()=>({value:t,done:!0}))}return he(o),p({value:t,done:!0})}}let Un={next(){return Hn(this)?this._asyncIteratorImpl.next():b(Yn("next"))},return(e){return Hn(this)?this._asyncIteratorImpl.return(e):b(Yn("return"))}};Mn!==void 0&&Object.setPrototypeOf(Un,Mn);function ia(e,t){let o=Le(e),s=new Nn(o,t),u=Object.create(Un);return u._asyncIteratorImpl=s,u}function Hn(e){if(!h(e)||!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof Nn}catch{return!1}}function Yn(e){return new TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}let Vn=Number.isNaN||function(e){return e!==e};function st(e){return e.slice()}function Gn(e,t,o,s,u){new Uint8Array(e).set(new Uint8Array(o,s,u),t)}function Vl(e){return e}function Dt(e){return!1}function Qn(e,t,o){if(e.slice)return e.slice(t,o);let s=o-t,u=new ArrayBuffer(s);return Gn(u,0,e,t,s),u}function aa(e){return!(typeof e!="number"||Vn(e)||e<0)}function Jn(e){let t=Qn(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(t)}function $r(e){let t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function Wr(e,t,o){if(!aa(o)||o===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");e._queue.push({value:t,size:o}),e._queueTotalSize+=o}function sa(e){return e._queue.peek().value}function Se(e){e._queue=new ee,e._queueTotalSize=0}class lt{constructor(){throw new TypeError("Illegal constructor")}get view(){if(!qr(this))throw Lr("view");return this._view}respond(t){if(!qr(this))throw Lr("respond");if(pe(t,1,"respond"),t=Ln(t,"First parameter"),this._associatedReadableByteStreamController===void 0)throw new TypeError("This BYOB request has been invalidated");Dt(this._view.buffer),Ft(this._associatedReadableByteStreamController,t)}respondWithNewView(t){if(!qr(this))throw Lr("respondWithNewView");if(pe(t,1,"respondWithNewView"),!ArrayBuffer.isView(t))throw new TypeError("You can only respond with array buffer views");if(this._associatedReadableByteStreamController===void 0)throw new TypeError("This BYOB request has been invalidated");Dt(t.buffer),It(this._associatedReadableByteStreamController,t)}}Object.defineProperties(lt.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),typeof a.toStringTag=="symbol"&&Object.defineProperty(lt.prototype,a.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class ze{constructor(){throw new TypeError("Illegal constructor")}get byobRequest(){if(!De(this))throw ct("byobRequest");return Ir(this)}get desiredSize(){if(!De(this))throw ct("desiredSize");return oo(this)}close(){if(!De(this))throw ct("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");let t=this._controlledReadableByteStream._state;if(t!=="readable")throw new TypeError(`The stream (in ${t} state) is not in the readable state and cannot be closed`);ut(this)}enqueue(t){if(!De(this))throw ct("enqueue");if(pe(t,1,"enqueue"),!ArrayBuffer.isView(t))throw new TypeError("chunk must be an array buffer view");if(t.byteLength===0)throw new TypeError("chunk must have non-zero byteLength");if(t.buffer.byteLength===0)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");let o=this._controlledReadableByteStream._state;if(o!=="readable")throw new TypeError(`The stream (in ${o} state) is not in the readable state and cannot be enqueued to`);xt(this,t)}error(t=void 0){if(!De(this))throw ct("error");re(this,t)}[Ar](t){Kn(this),Se(this);let o=this._cancelAlgorithm(t);return qt(this),o}[Pr](t){let o=this._controlledReadableByteStream;if(this._queueTotalSize>0){let u=this._queue.shift();this._queueTotalSize-=u.byteLength,to(this);let f=new Uint8Array(u.buffer,u.byteOffset,u.byteLength);t._chunkSteps(f);return}let s=this._autoAllocateChunkSize;if(s!==void 0){let u;try{u=new ArrayBuffer(s)}catch(d){t._errorSteps(d);return}let f={buffer:u,bufferByteLength:s,byteOffset:0,byteLength:s,bytesFilled:0,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(f)}zn(o,t),$e(this)}}Object.defineProperties(ze.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),typeof a.toStringTag=="symbol"&&Object.defineProperty(ze.prototype,a.toStringTag,{value:"ReadableByteStreamController",configurable:!0});function De(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")?!1:e instanceof ze}function qr(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")?!1:e instanceof lt}function $e(e){if(!fa(e))return;if(e._pulling){e._pullAgain=!0;return}e._pulling=!0;let o=e._pullAlgorithm();v(o,()=>{e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,$e(e))},s=>{re(e,s)})}function Kn(e){Fr(e),e._pendingPullIntos=new ee}function xr(e,t){let o=!1;e._state==="closed"&&(o=!0);let s=Xn(t);t.readerType==="default"?Dr(e,s,o):ma(e,s,o)}function Xn(e){let t=e.bytesFilled,o=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/o)}function $t(e,t,o,s){e._queue.push({buffer:t,byteOffset:o,byteLength:s}),e._queueTotalSize+=s}function Zn(e,t){let o=t.elementSize,s=t.bytesFilled-t.bytesFilled%o,u=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),f=t.bytesFilled+u,d=f-f%o,_=u,E=!1;d>s&&(_=d-t.bytesFilled,E=!0);let A=e._queue;for(;_>0;){let k=A.peek(),O=Math.min(_,k.byteLength),I=t.byteOffset+t.bytesFilled;Gn(t.buffer,I,k.buffer,k.byteOffset,O),k.byteLength===O?A.shift():(k.byteOffset+=O,k.byteLength-=O),e._queueTotalSize-=O,eo(e,O,t),_-=O}return E}function eo(e,t,o){o.bytesFilled+=t}function to(e){e._queueTotalSize===0&&e._closeRequested?(qt(e),yt(e._controlledReadableByteStream)):$e(e)}function Fr(e){e._byobRequest!==null&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function ro(e){for(;e._pendingPullIntos.length>0;){if(e._queueTotalSize===0)return;let t=e._pendingPullIntos.peek();Zn(e,t)&&(Wt(e),xr(e._controlledReadableByteStream,t))}}function la(e,t,o){let s=e._controlledReadableByteStream,u=1;t.constructor!==DataView&&(u=t.constructor.BYTES_PER_ELEMENT);let f=t.constructor,d=t.buffer,_={buffer:d,bufferByteLength:d.byteLength,byteOffset:t.byteOffset,byteLength:t.byteLength,bytesFilled:0,elementSize:u,viewConstructor:f,readerType:"byob"};if(e._pendingPullIntos.length>0){e._pendingPullIntos.push(_),so(s,o);return}if(s._state==="closed"){let E=new f(_.buffer,_.byteOffset,0);o._closeSteps(E);return}if(e._queueTotalSize>0){if(Zn(e,_)){let E=Xn(_);to(e),o._chunkSteps(E);return}if(e._closeRequested){let E=new TypeError("Insufficient bytes to fill elements in the given buffer");re(e,E),o._errorSteps(E);return}}e._pendingPullIntos.push(_),so(s,o),$e(e)}function ua(e,t){let o=e._controlledReadableByteStream;if(zr(o))for(;lo(o)>0;){let s=Wt(e);xr(o,s)}}function ca(e,t,o){if(eo(e,t,o),o.bytesFilled<o.elementSize)return;Wt(e);let s=o.bytesFilled%o.elementSize;if(s>0){let u=o.byteOffset+o.bytesFilled,f=Qn(o.buffer,u-s,u);$t(e,f,0,f.byteLength)}o.bytesFilled-=s,xr(e._controlledReadableByteStream,o),ro(e)}function no(e,t){let o=e._pendingPullIntos.peek();Fr(e),e._controlledReadableByteStream._state==="closed"?ua(e):ca(e,t,o),$e(e)}function Wt(e){return e._pendingPullIntos.shift()}function fa(e){let t=e._controlledReadableByteStream;return t._state!=="readable"||e._closeRequested||!e._started?!1:!!(jn(t)&&Ot(t)>0||zr(t)&&lo(t)>0||oo(e)>0)}function qt(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function ut(e){let t=e._controlledReadableByteStream;if(!(e._closeRequested||t._state!=="readable")){if(e._queueTotalSize>0){e._closeRequested=!0;return}if(e._pendingPullIntos.length>0&&e._pendingPullIntos.peek().bytesFilled>0){let s=new TypeError("Insufficient bytes to fill elements in the given buffer");throw re(e,s),s}qt(e),yt(t)}}function xt(e,t){let o=e._controlledReadableByteStream;if(e._closeRequested||o._state!=="readable")return;let s=t.buffer,u=t.byteOffset,f=t.byteLength,d=s;if(e._pendingPullIntos.length>0){let _=e._pendingPullIntos.peek();Dt(_.buffer),_.buffer=_.buffer}if(Fr(e),jn(o))if(Ot(o)===0)$t(e,d,u,f);else{e._pendingPullIntos.length>0&&Wt(e);let _=new Uint8Array(d,u,f);Dr(o,_,!1)}else zr(o)?($t(e,d,u,f),ro(e)):$t(e,d,u,f);$e(e)}function re(e,t){let o=e._controlledReadableByteStream;o._state==="readable"&&(Kn(e),Se(e),qt(e),$o(o,t))}function Ir(e){if(e._byobRequest===null&&e._pendingPullIntos.length>0){let t=e._pendingPullIntos.peek(),o=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),s=Object.create(lt.prototype);ha(s,e,o),e._byobRequest=s}return e._byobRequest}function oo(e){let t=e._controlledReadableByteStream._state;return t==="errored"?null:t==="closed"?0:e._strategyHWM-e._queueTotalSize}function Ft(e,t){let o=e._pendingPullIntos.peek();if(e._controlledReadableByteStream._state==="closed"){if(t!==0)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(t===0)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(o.bytesFilled+t>o.byteLength)throw new RangeError("bytesWritten out of range")}o.buffer=o.buffer,no(e,t)}function It(e,t){let o=e._pendingPullIntos.peek();if(e._controlledReadableByteStream._state==="closed"){if(t.byteLength!==0)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(t.byteLength===0)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(o.byteOffset+o.bytesFilled!==t.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(o.bufferByteLength!==t.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(o.bytesFilled+t.byteLength>o.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");let u=t.byteLength;o.buffer=t.buffer,no(e,u)}function io(e,t,o,s,u,f,d){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=null,t._queue=t._queueTotalSize=void 0,Se(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=f,t._pullAlgorithm=s,t._cancelAlgorithm=u,t._autoAllocateChunkSize=d,t._pendingPullIntos=new ee,e._readableStreamController=t;let _=o();v(p(_),()=>{t._started=!0,$e(t)},E=>{re(t,E)})}function da(e,t,o){let s=Object.create(ze.prototype),u=()=>{},f=()=>p(void 0),d=()=>p(void 0);t.start!==void 0&&(u=()=>t.start(s)),t.pull!==void 0&&(f=()=>t.pull(s)),t.cancel!==void 0&&(d=E=>t.cancel(E));let _=t.autoAllocateChunkSize;if(_===0)throw new TypeError("autoAllocateChunkSize must be greater than 0");io(e,s,u,f,d,o,_)}function ha(e,t,o){e._associatedReadableByteStreamController=t,e._view=o}function Lr(e){return new TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}function ct(e){return new TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}function ao(e){return new ft(e)}function so(e,t){e._reader._readIntoRequests.push(t)}function ma(e,t,o){let u=e._reader._readIntoRequests.shift();o?u._closeSteps(t):u._chunkSteps(t)}function lo(e){return e._reader._readIntoRequests.length}function zr(e){let t=e._reader;return!(t===void 0||!We(t))}class ft{constructor(t){if(pe(t,1,"ReadableStreamBYOBReader"),Br(t,"First parameter"),Ee(t))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!De(t._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");Bn(this,t),this._readIntoRequests=new ee}get closed(){return We(this)?this._closedPromise:b(Lt("closed"))}cancel(t=void 0){return We(this)?this._ownerReadableStream===void 0?b(Ie("cancel")):Tr(this,t):b(Lt("cancel"))}read(t){if(!We(this))return b(Lt("read"));if(!ArrayBuffer.isView(t))return b(new TypeError("view must be an array buffer view"));if(t.byteLength===0)return b(new TypeError("view must have non-zero byteLength"));if(t.buffer.byteLength===0)return b(new TypeError("view's buffer must have non-zero byteLength"));if(Dt(t.buffer),this._ownerReadableStream===void 0)return b(Ie("read from"));let o,s,u=w((d,_)=>{o=d,s=_});return uo(this,t,{_chunkSteps:d=>o({value:d,done:!1}),_closeSteps:d=>o({value:d,done:!0}),_errorSteps:d=>s(d)}),u}releaseLock(){if(!We(this))throw Lt("releaseLock");if(this._ownerReadableStream!==void 0){if(this._readIntoRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");he(this)}}}Object.defineProperties(ft.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),typeof a.toStringTag=="symbol"&&Object.defineProperty(ft.prototype,a.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});function We(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")?!1:e instanceof ft}function uo(e,t,o){let s=e._ownerReadableStream;s._disturbed=!0,s._state==="errored"?o._errorSteps(s._storedError):la(s._readableStreamController,t,o)}function Lt(e){return new TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}function dt(e,t){let{highWaterMark:o}=e;if(o===void 0)return t;if(Vn(o)||o<0)throw new RangeError("Invalid highWaterMark");return o}function zt(e){let{size:t}=e;return t||(()=>1)}function jt(e,t){me(e,t);let o=e?.highWaterMark,s=e?.size;return{highWaterMark:o===void 0?void 0:Or(o),size:s===void 0?void 0:pa(s,`${t} has member 'size' that`)}}function pa(e,t){return te(e,t),o=>Or(e(o))}function ba(e,t){me(e,t);let o=e?.abort,s=e?.close,u=e?.start,f=e?.type,d=e?.write;return{abort:o===void 0?void 0:ga(o,e,`${t} has member 'abort' that`),close:s===void 0?void 0:ya(s,e,`${t} has member 'close' that`),start:u===void 0?void 0:_a(u,e,`${t} has member 'start' that`),write:d===void 0?void 0:wa(d,e,`${t} has member 'write' that`),type:f}}function ga(e,t,o){return te(e,o),s=>de(e,t,[s])}function ya(e,t,o){return te(e,o),()=>de(e,t,[])}function _a(e,t,o){return te(e,o),s=>Be(e,t,[s])}function wa(e,t,o){return te(e,o),(s,u)=>de(e,t,[s,u])}function co(e,t){if(!je(e))throw new TypeError(`${t} is not a WritableStream.`)}function Sa(e){if(typeof e!="object"||e===null)return!1;try{return typeof e.aborted=="boolean"}catch{return!1}}let va=typeof AbortController=="function";function Ra(){if(va)return new AbortController}class ht{constructor(t={},o={}){t===void 0?t=null:Fn(t,"First parameter");let s=jt(o,"Second parameter"),u=ba(t,"First parameter");if(ho(this),u.type!==void 0)throw new RangeError("Invalid type is specified");let d=zt(s),_=dt(s,1);Fa(this,u,_,d)}get locked(){if(!je(this))throw Yt("locked");return Me(this)}abort(t=void 0){return je(this)?Me(this)?b(new TypeError("Cannot abort a stream that already has a writer")):Mt(this,t):b(Yt("abort"))}close(){return je(this)?Me(this)?b(new TypeError("Cannot close a stream that already has a writer")):ue(this)?b(new TypeError("Cannot close an already-closing stream")):mo(this):b(Yt("close"))}getWriter(){if(!je(this))throw Yt("getWriter");return fo(this)}}Object.defineProperties(ht.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),typeof a.toStringTag=="symbol"&&Object.defineProperty(ht.prototype,a.toStringTag,{value:"WritableStream",configurable:!0});function fo(e){return new mt(e)}function Ta(e,t,o,s,u=1,f=()=>1){let d=Object.create(ht.prototype);ho(d);let _=Object.create(Ne.prototype);return wo(d,_,e,t,o,s,u,f),d}function ho(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new ee,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function je(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")?!1:e instanceof ht}function Me(e){return e._writer!==void 0}function Mt(e,t){var o;if(e._state==="closed"||e._state==="errored")return p(void 0);e._writableStreamController._abortReason=t,(o=e._writableStreamController._abortController)===null||o===void 0||o.abort();let s=e._state;if(s==="closed"||s==="errored")return p(void 0);if(e._pendingAbortRequest!==void 0)return e._pendingAbortRequest._promise;let u=!1;s==="erroring"&&(u=!0,t=void 0);let f=w((d,_)=>{e._pendingAbortRequest={_promise:void 0,_resolve:d,_reject:_,_reason:t,_wasAlreadyErroring:u}});return e._pendingAbortRequest._promise=f,u||Mr(e,t),f}function mo(e){let t=e._state;if(t==="closed"||t==="errored")return b(new TypeError(`The stream (in ${t} state) is not in the writable state and cannot be closed`));let o=w((u,f)=>{let d={_resolve:u,_reject:f};e._closeRequest=d}),s=e._writer;return s!==void 0&&e._backpressure&&t==="writable"&&Kr(s),Ia(e._writableStreamController),o}function Ea(e){return w((o,s)=>{let u={_resolve:o,_reject:s};e._writeRequests.push(u)})}function jr(e,t){if(e._state==="writable"){Mr(e,t);return}Nr(e)}function Mr(e,t){let o=e._writableStreamController;e._state="erroring",e._storedError=t;let s=e._writer;s!==void 0&&bo(s,t),!Oa(e)&&o._started&&Nr(e)}function Nr(e){e._state="errored",e._writableStreamController[qn]();let t=e._storedError;if(e._writeRequests.forEach(u=>{u._reject(t)}),e._writeRequests=new ee,e._pendingAbortRequest===void 0){Nt(e);return}let o=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,o._wasAlreadyErroring){o._reject(t),Nt(e);return}let s=e._writableStreamController[Wn](o._reason);v(s,()=>{o._resolve(),Nt(e)},u=>{o._reject(u),Nt(e)})}function Ca(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}function Aa(e,t){e._inFlightWriteRequest._reject(t),e._inFlightWriteRequest=void 0,jr(e,t)}function Pa(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,e._state==="erroring"&&(e._storedError=void 0,e._pendingAbortRequest!==void 0&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";let o=e._writer;o!==void 0&&To(o)}function ka(e,t){e._inFlightCloseRequest._reject(t),e._inFlightCloseRequest=void 0,e._pendingAbortRequest!==void 0&&(e._pendingAbortRequest._reject(t),e._pendingAbortRequest=void 0),jr(e,t)}function ue(e){return!(e._closeRequest===void 0&&e._inFlightCloseRequest===void 0)}function Oa(e){return!(e._inFlightWriteRequest===void 0&&e._inFlightCloseRequest===void 0)}function Ba(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0}function Da(e){e._inFlightWriteRequest=e._writeRequests.shift()}function Nt(e){e._closeRequest!==void 0&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);let t=e._writer;t!==void 0&&Qr(t,e._storedError)}function Ur(e,t){let o=e._writer;o!==void 0&&t!==e._backpressure&&(t?Ha(o):Kr(o)),e._backpressure=t}class mt{constructor(t){if(pe(t,1,"WritableStreamDefaultWriter"),co(t,"First parameter"),Me(t))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=t,t._writer=this;let o=t._state;if(o==="writable")!ue(t)&&t._backpressure?Gt(this):Eo(this),Vt(this);else if(o==="erroring")Jr(this,t._storedError),Vt(this);else if(o==="closed")Eo(this),Na(this);else{let s=t._storedError;Jr(this,s),Ro(this,s)}}get closed(){return qe(this)?this._closedPromise:b(xe("closed"))}get desiredSize(){if(!qe(this))throw xe("desiredSize");if(this._ownerWritableStream===void 0)throw pt("desiredSize");return xa(this)}get ready(){return qe(this)?this._readyPromise:b(xe("ready"))}abort(t=void 0){return qe(this)?this._ownerWritableStream===void 0?b(pt("abort")):$a(this,t):b(xe("abort"))}close(){if(!qe(this))return b(xe("close"));let t=this._ownerWritableStream;return t===void 0?b(pt("close")):ue(t)?b(new TypeError("Cannot close an already-closing stream")):po(this)}releaseLock(){if(!qe(this))throw xe("releaseLock");this._ownerWritableStream!==void 0&&go(this)}write(t=void 0){return qe(this)?this._ownerWritableStream===void 0?b(pt("write to")):yo(this,t):b(xe("write"))}}Object.defineProperties(mt.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),typeof a.toStringTag=="symbol"&&Object.defineProperty(mt.prototype,a.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});function qe(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")?!1:e instanceof mt}function $a(e,t){let o=e._ownerWritableStream;return Mt(o,t)}function po(e){let t=e._ownerWritableStream;return mo(t)}function Wa(e){let t=e._ownerWritableStream,o=t._state;return ue(t)||o==="closed"?p(void 0):o==="errored"?b(t._storedError):po(e)}function qa(e,t){e._closedPromiseState==="pending"?Qr(e,t):Ua(e,t)}function bo(e,t){e._readyPromiseState==="pending"?Co(e,t):Ya(e,t)}function xa(e){let t=e._ownerWritableStream,o=t._state;return o==="errored"||o==="erroring"?null:o==="closed"?0:So(t._writableStreamController)}function go(e){let t=e._ownerWritableStream,o=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");bo(e,o),qa(e,o),t._writer=void 0,e._ownerWritableStream=void 0}function yo(e,t){let o=e._ownerWritableStream,s=o._writableStreamController,u=La(s,t);if(o!==e._ownerWritableStream)return b(pt("write to"));let f=o._state;if(f==="errored")return b(o._storedError);if(ue(o)||f==="closed")return b(new TypeError("The stream is closing or closed and cannot be written to"));if(f==="erroring")return b(o._storedError);let d=Ea(o);return za(s,t,u),d}let _o={};class Ne{constructor(){throw new TypeError("Illegal constructor")}get abortReason(){if(!Hr(this))throw Gr("abortReason");return this._abortReason}get signal(){if(!Hr(this))throw Gr("signal");if(this._abortController===void 0)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(t=void 0){if(!Hr(this))throw Gr("error");this._controlledWritableStream._state==="writable"&&vo(this,t)}[Wn](t){let o=this._abortAlgorithm(t);return Ut(this),o}[qn](){Se(this)}}Object.defineProperties(Ne.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),typeof a.toStringTag=="symbol"&&Object.defineProperty(Ne.prototype,a.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});function Hr(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")?!1:e instanceof Ne}function wo(e,t,o,s,u,f,d,_){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,Se(t),t._abortReason=void 0,t._abortController=Ra(),t._started=!1,t._strategySizeAlgorithm=_,t._strategyHWM=d,t._writeAlgorithm=s,t._closeAlgorithm=u,t._abortAlgorithm=f;let E=Vr(t);Ur(e,E);let A=o(),k=p(A);v(k,()=>{t._started=!0,Ht(t)},O=>{t._started=!0,jr(e,O)})}function Fa(e,t,o,s){let u=Object.create(Ne.prototype),f=()=>{},d=()=>p(void 0),_=()=>p(void 0),E=()=>p(void 0);t.start!==void 0&&(f=()=>t.start(u)),t.write!==void 0&&(d=A=>t.write(A,u)),t.close!==void 0&&(_=()=>t.close()),t.abort!==void 0&&(E=A=>t.abort(A)),wo(e,u,f,d,_,E,o,s)}function Ut(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function Ia(e){Wr(e,_o,0),Ht(e)}function La(e,t){try{return e._strategySizeAlgorithm(t)}catch(o){return Yr(e,o),1}}function So(e){return e._strategyHWM-e._queueTotalSize}function za(e,t,o){try{Wr(e,t,o)}catch(u){Yr(e,u);return}let s=e._controlledWritableStream;if(!ue(s)&&s._state==="writable"){let u=Vr(e);Ur(s,u)}Ht(e)}function Ht(e){let t=e._controlledWritableStream;if(!e._started||t._inFlightWriteRequest!==void 0)return;if(t._state==="erroring"){Nr(t);return}if(e._queue.length===0)return;let s=sa(e);s===_o?ja(e):Ma(e,s)}function Yr(e,t){e._controlledWritableStream._state==="writable"&&vo(e,t)}function ja(e){let t=e._controlledWritableStream;Ba(t),$r(e);let o=e._closeAlgorithm();Ut(e),v(o,()=>{Pa(t)},s=>{ka(t,s)})}function Ma(e,t){let o=e._controlledWritableStream;Da(o);let s=e._writeAlgorithm(t);v(s,()=>{Ca(o);let u=o._state;if($r(e),!ue(o)&&u==="writable"){let f=Vr(e);Ur(o,f)}Ht(e)},u=>{o._state==="writable"&&Ut(e),Aa(o,u)})}function Vr(e){return So(e)<=0}function vo(e,t){let o=e._controlledWritableStream;Ut(e),Mr(o,t)}function Yt(e){return new TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}function Gr(e){return new TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}function xe(e){return new TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}function pt(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function Vt(e){e._closedPromise=w((t,o)=>{e._closedPromise_resolve=t,e._closedPromise_reject=o,e._closedPromiseState="pending"})}function Ro(e,t){Vt(e),Qr(e,t)}function Na(e){Vt(e),To(e)}function Qr(e,t){e._closedPromise_reject!==void 0&&(W(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function Ua(e,t){Ro(e,t)}function To(e){e._closedPromise_resolve!==void 0&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function Gt(e){e._readyPromise=w((t,o)=>{e._readyPromise_resolve=t,e._readyPromise_reject=o}),e._readyPromiseState="pending"}function Jr(e,t){Gt(e),Co(e,t)}function Eo(e){Gt(e),Kr(e)}function Co(e,t){e._readyPromise_reject!==void 0&&(W(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function Ha(e){Gt(e)}function Ya(e,t){Jr(e,t)}function Kr(e){e._readyPromise_resolve!==void 0&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}let Ao=typeof DOMException<"u"?DOMException:void 0;function Va(e){if(!(typeof e=="function"||typeof e=="object"))return!1;try{return new e,!0}catch{return!1}}function Ga(){let e=function(o,s){this.message=o||"",this.name=s||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}let Qa=Va(Ao)?Ao:Ga();function Po(e,t,o,s,u,f){let d=Le(e),_=fo(t);e._disturbed=!0;let E=!1,A=p(void 0);return w((k,O)=>{let I;if(f!==void 0){if(I=()=>{let S=new Qa("Aborted","AbortError"),C=[];s||C.push(()=>t._state==="writable"?Mt(t,S):p(void 0)),u||C.push(()=>e._state==="readable"?ne(e,S):p(void 0)),V(()=>Promise.all(C.map($=>$())),!0,S)},f.aborted){I();return}f.addEventListener("abort",I)}function oe(){return w((S,C)=>{function $(Q){Q?S():P(Ye(),$,C)}$(!1)})}function Ye(){return E?p(!0):P(_._readyPromise,()=>w((S,C)=>{at(d,{_chunkSteps:$=>{A=P(yo(_,$),void 0,l),S(!1)},_closeSteps:()=>S(!0),_errorSteps:C})}))}if(be(e,d._closedPromise,S=>{s?X(!0,S):V(()=>Mt(t,S),!0,S)}),be(t,_._closedPromise,S=>{u?X(!0,S):V(()=>ne(e,S),!0,S)}),Y(e,d._closedPromise,()=>{o?X():V(()=>Wa(_))}),ue(t)||t._state==="closed"){let S=new TypeError("the destination writable stream closed before all data could be piped to it");u?X(!0,S):V(()=>ne(e,S),!0,S)}W(oe());function Ce(){let S=A;return P(A,()=>S!==A?Ce():void 0)}function be(S,C,$){S._state==="errored"?$(S._storedError):N(C,$)}function Y(S,C,$){S._state==="closed"?$():M(C,$)}function V(S,C,$){if(E)return;E=!0,t._state==="writable"&&!ue(t)?M(Ce(),Q):Q();function Q(){v(S(),()=>ge(C,$),Ve=>ge(!0,Ve))}}function X(S,C){E||(E=!0,t._state==="writable"&&!ue(t)?M(Ce(),()=>ge(S,C)):ge(S,C))}function ge(S,C){go(_),he(d),f!==void 0&&f.removeEventListener("abort",I),S?O(C):k(void 0)}})}class Ue{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!Qt(this))throw Xt("desiredSize");return Xr(this)}close(){if(!Qt(this))throw Xt("close");if(!He(this))throw new TypeError("The stream is not in a state that permits close");gt(this)}enqueue(t=void 0){if(!Qt(this))throw Xt("enqueue");if(!He(this))throw new TypeError("The stream is not in a state that permits enqueue");return Kt(this,t)}error(t=void 0){if(!Qt(this))throw Xt("error");ve(this,t)}[Ar](t){Se(this);let o=this._cancelAlgorithm(t);return Jt(this),o}[Pr](t){let o=this._controlledReadableStream;if(this._queue.length>0){let s=$r(this);this._closeRequested&&this._queue.length===0?(Jt(this),yt(o)):bt(this),t._chunkSteps(s)}else zn(o,t),bt(this)}}Object.defineProperties(Ue.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),typeof a.toStringTag=="symbol"&&Object.defineProperty(Ue.prototype,a.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});function Qt(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")?!1:e instanceof Ue}function bt(e){if(!ko(e))return;if(e._pulling){e._pullAgain=!0;return}e._pulling=!0;let o=e._pullAlgorithm();v(o,()=>{e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,bt(e))},s=>{ve(e,s)})}function ko(e){let t=e._controlledReadableStream;return!He(e)||!e._started?!1:!!(Ee(t)&&Ot(t)>0||Xr(e)>0)}function Jt(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function gt(e){if(!He(e))return;let t=e._controlledReadableStream;e._closeRequested=!0,e._queue.length===0&&(Jt(e),yt(t))}function Kt(e,t){if(!He(e))return;let o=e._controlledReadableStream;if(Ee(o)&&Ot(o)>0)Dr(o,t,!1);else{let s;try{s=e._strategySizeAlgorithm(t)}catch(u){throw ve(e,u),u}try{Wr(e,t,s)}catch(u){throw ve(e,u),u}}bt(e)}function ve(e,t){let o=e._controlledReadableStream;o._state==="readable"&&(Se(e),Jt(e),$o(o,t))}function Xr(e){let t=e._controlledReadableStream._state;return t==="errored"?null:t==="closed"?0:e._strategyHWM-e._queueTotalSize}function Ja(e){return!ko(e)}function He(e){let t=e._controlledReadableStream._state;return!e._closeRequested&&t==="readable"}function Oo(e,t,o,s,u,f,d){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,Se(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=d,t._strategyHWM=f,t._pullAlgorithm=s,t._cancelAlgorithm=u,e._readableStreamController=t;let _=o();v(p(_),()=>{t._started=!0,bt(t)},E=>{ve(t,E)})}function Ka(e,t,o,s){let u=Object.create(Ue.prototype),f=()=>{},d=()=>p(void 0),_=()=>p(void 0);t.start!==void 0&&(f=()=>t.start(u)),t.pull!==void 0&&(d=()=>t.pull(u)),t.cancel!==void 0&&(_=E=>t.cancel(E)),Oo(e,u,f,d,_,o,s)}function Xt(e){return new TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}function Xa(e,t){return De(e._readableStreamController)?es(e):Za(e)}function Za(e,t){let o=Le(e),s=!1,u=!1,f=!1,d=!1,_,E,A,k,O,I=w(Y=>{O=Y});function oe(){return s?(u=!0,p(void 0)):(s=!0,at(o,{_chunkSteps:V=>{F(()=>{u=!1;let X=V,ge=V;f||Kt(A._readableStreamController,X),d||Kt(k._readableStreamController,ge),s=!1,u&&oe()})},_closeSteps:()=>{s=!1,f||gt(A._readableStreamController),d||gt(k._readableStreamController),(!f||!d)&&O(void 0)},_errorSteps:()=>{s=!1}}),p(void 0))}function Ye(Y){if(f=!0,_=Y,d){let V=st([_,E]),X=ne(e,V);O(X)}return I}function Ce(Y){if(d=!0,E=Y,f){let V=st([_,E]),X=ne(e,V);O(X)}return I}function be(){}return A=Zr(be,oe,Ye),k=Zr(be,oe,Ce),N(o._closedPromise,Y=>{ve(A._readableStreamController,Y),ve(k._readableStreamController,Y),(!f||!d)&&O(void 0)}),[A,k]}function es(e){let t=Le(e),o=!1,s=!1,u=!1,f=!1,d=!1,_,E,A,k,O,I=w(S=>{O=S});function oe(S){N(S._closedPromise,C=>{S===t&&(re(A._readableStreamController,C),re(k._readableStreamController,C),(!f||!d)&&O(void 0))})}function Ye(){We(t)&&(he(t),t=Le(e),oe(t)),at(t,{_chunkSteps:C=>{F(()=>{s=!1,u=!1;let $=C,Q=C;if(!f&&!d)try{Q=Jn(C)}catch(Ve){re(A._readableStreamController,Ve),re(k._readableStreamController,Ve),O(ne(e,Ve));return}f||xt(A._readableStreamController,$),d||xt(k._readableStreamController,Q),o=!1,s?be():u&&Y()})},_closeSteps:()=>{o=!1,f||ut(A._readableStreamController),d||ut(k._readableStreamController),A._readableStreamController._pendingPullIntos.length>0&&Ft(A._readableStreamController,0),k._readableStreamController._pendingPullIntos.length>0&&Ft(k._readableStreamController,0),(!f||!d)&&O(void 0)},_errorSteps:()=>{o=!1}})}function Ce(S,C){we(t)&&(he(t),t=ao(e),oe(t));let $=C?k:A,Q=C?A:k;uo(t,S,{_chunkSteps:Ge=>{F(()=>{s=!1,u=!1;let Qe=C?d:f;if(C?f:d)Qe||It($._readableStreamController,Ge);else{let Yo;try{Yo=Jn(Ge)}catch(tn){re($._readableStreamController,tn),re(Q._readableStreamController,tn),O(ne(e,tn));return}Qe||It($._readableStreamController,Ge),xt(Q._readableStreamController,Yo)}o=!1,s?be():u&&Y()})},_closeSteps:Ge=>{o=!1;let Qe=C?d:f,sr=C?f:d;Qe||ut($._readableStreamController),sr||ut(Q._readableStreamController),Ge!==void 0&&(Qe||It($._readableStreamController,Ge),!sr&&Q._readableStreamController._pendingPullIntos.length>0&&Ft(Q._readableStreamController,0)),(!Qe||!sr)&&O(void 0)},_errorSteps:()=>{o=!1}})}function be(){if(o)return s=!0,p(void 0);o=!0;let S=Ir(A._readableStreamController);return S===null?Ye():Ce(S._view,!1),p(void 0)}function Y(){if(o)return u=!0,p(void 0);o=!0;let S=Ir(k._readableStreamController);return S===null?Ye():Ce(S._view,!0),p(void 0)}function V(S){if(f=!0,_=S,d){let C=st([_,E]),$=ne(e,C);O($)}return I}function X(S){if(d=!0,E=S,f){let C=st([_,E]),$=ne(e,C);O($)}return I}function ge(){}return A=Do(ge,be,V),k=Do(ge,Y,X),oe(t),[A,k]}function ts(e,t){me(e,t);let o=e,s=o?.autoAllocateChunkSize,u=o?.cancel,f=o?.pull,d=o?.start,_=o?.type;return{autoAllocateChunkSize:s===void 0?void 0:Ln(s,`${t} has member 'autoAllocateChunkSize' that`),cancel:u===void 0?void 0:rs(u,o,`${t} has member 'cancel' that`),pull:f===void 0?void 0:ns(f,o,`${t} has member 'pull' that`),start:d===void 0?void 0:os(d,o,`${t} has member 'start' that`),type:_===void 0?void 0:is(_,`${t} has member 'type' that`)}}function rs(e,t,o){return te(e,o),s=>de(e,t,[s])}function ns(e,t,o){return te(e,o),s=>de(e,t,[s])}function os(e,t,o){return te(e,o),s=>Be(e,t,[s])}function is(e,t){if(e=`${e}`,e!=="bytes")throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}function as(e,t){me(e,t);let o=e?.mode;return{mode:o===void 0?void 0:ss(o,`${t} has member 'mode' that`)}}function ss(e,t){if(e=`${e}`,e!=="byob")throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}function ls(e,t){return me(e,t),{preventCancel:!!e?.preventCancel}}function Bo(e,t){me(e,t);let o=e?.preventAbort,s=e?.preventCancel,u=e?.preventClose,f=e?.signal;return f!==void 0&&us(f,`${t} has member 'signal' that`),{preventAbort:!!o,preventCancel:!!s,preventClose:!!u,signal:f}}function us(e,t){if(!Sa(e))throw new TypeError(`${t} is not an AbortSignal.`)}function cs(e,t){me(e,t);let o=e?.readable;kr(o,"readable","ReadableWritablePair"),Br(o,`${t} has member 'readable' that`);let s=e?.writable;return kr(s,"writable","ReadableWritablePair"),co(s,`${t} has member 'writable' that`),{readable:o,writable:s}}class Re{constructor(t={},o={}){t===void 0?t=null:Fn(t,"First parameter");let s=jt(o,"Second parameter"),u=ts(t,"First parameter");if(en(this),u.type==="bytes"){if(s.size!==void 0)throw new RangeError("The strategy for a byte stream cannot have a size function");let f=dt(s,0);da(this,u,f)}else{let f=zt(s),d=dt(s,1);Ka(this,u,d,f)}}get locked(){if(!Te(this))throw Fe("locked");return Ee(this)}cancel(t=void 0){return Te(this)?Ee(this)?b(new TypeError("Cannot cancel a stream that already has a reader")):ne(this,t):b(Fe("cancel"))}getReader(t=void 0){if(!Te(this))throw Fe("getReader");return as(t,"First parameter").mode===void 0?Le(this):ao(this)}pipeThrough(t,o={}){if(!Te(this))throw Fe("pipeThrough");pe(t,1,"pipeThrough");let s=cs(t,"First parameter"),u=Bo(o,"Second parameter");if(Ee(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(Me(s.writable))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");let f=Po(this,s.writable,u.preventClose,u.preventAbort,u.preventCancel,u.signal);return W(f),s.readable}pipeTo(t,o={}){if(!Te(this))return b(Fe("pipeTo"));if(t===void 0)return b("Parameter 1 is required in 'pipeTo'.");if(!je(t))return b(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));let s;try{s=Bo(o,"Second parameter")}catch(u){return b(u)}return Ee(this)?b(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):Me(t)?b(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):Po(this,t,s.preventClose,s.preventAbort,s.preventCancel,s.signal)}tee(){if(!Te(this))throw Fe("tee");let t=Xa(this);return st(t)}values(t=void 0){if(!Te(this))throw Fe("values");let o=ls(t,"First parameter");return ia(this,o.preventCancel)}}Object.defineProperties(Re.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),typeof a.toStringTag=="symbol"&&Object.defineProperty(Re.prototype,a.toStringTag,{value:"ReadableStream",configurable:!0}),typeof a.asyncIterator=="symbol"&&Object.defineProperty(Re.prototype,a.asyncIterator,{value:Re.prototype.values,writable:!0,configurable:!0});function Zr(e,t,o,s=1,u=()=>1){let f=Object.create(Re.prototype);en(f);let d=Object.create(Ue.prototype);return Oo(f,d,e,t,o,s,u),f}function Do(e,t,o){let s=Object.create(Re.prototype);en(s);let u=Object.create(ze.prototype);return io(s,u,e,t,o,0,void 0),s}function en(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function Te(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")?!1:e instanceof Re}function Ee(e){return e._reader!==void 0}function ne(e,t){if(e._disturbed=!0,e._state==="closed")return p(void 0);if(e._state==="errored")return b(e._storedError);yt(e);let o=e._reader;o!==void 0&&We(o)&&(o._readIntoRequests.forEach(u=>{u._closeSteps(void 0)}),o._readIntoRequests=new ee);let s=e._readableStreamController[Ar](t);return B(s,l)}function yt(e){e._state="closed";let t=e._reader;t!==void 0&&($n(t),we(t)&&(t._readRequests.forEach(o=>{o._closeSteps()}),t._readRequests=new ee))}function $o(e,t){e._state="errored",e._storedError=t;let o=e._reader;o!==void 0&&(Cr(o,t),we(o)?(o._readRequests.forEach(s=>{s._errorSteps(t)}),o._readRequests=new ee):(o._readIntoRequests.forEach(s=>{s._errorSteps(t)}),o._readIntoRequests=new ee))}function Fe(e){return new TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}function Wo(e,t){me(e,t);let o=e?.highWaterMark;return kr(o,"highWaterMark","QueuingStrategyInit"),{highWaterMark:Or(o)}}let qo=e=>e.byteLength;try{Object.defineProperty(qo,"name",{value:"size",configurable:!0})}catch{}class Zt{constructor(t){pe(t,1,"ByteLengthQueuingStrategy"),t=Wo(t,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=t.highWaterMark}get highWaterMark(){if(!Fo(this))throw xo("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!Fo(this))throw xo("size");return qo}}Object.defineProperties(Zt.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),typeof a.toStringTag=="symbol"&&Object.defineProperty(Zt.prototype,a.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});function xo(e){return new TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}function Fo(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")?!1:e instanceof Zt}let Io=()=>1;try{Object.defineProperty(Io,"name",{value:"size",configurable:!0})}catch{}class er{constructor(t){pe(t,1,"CountQueuingStrategy"),t=Wo(t,"First parameter"),this._countQueuingStrategyHighWaterMark=t.highWaterMark}get highWaterMark(){if(!zo(this))throw Lo("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!zo(this))throw Lo("size");return Io}}Object.defineProperties(er.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),typeof a.toStringTag=="symbol"&&Object.defineProperty(er.prototype,a.toStringTag,{value:"CountQueuingStrategy",configurable:!0});function Lo(e){return new TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}function zo(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")?!1:e instanceof er}function fs(e,t){me(e,t);let o=e?.flush,s=e?.readableType,u=e?.start,f=e?.transform,d=e?.writableType;return{flush:o===void 0?void 0:ds(o,e,`${t} has member 'flush' that`),readableType:s,start:u===void 0?void 0:hs(u,e,`${t} has member 'start' that`),transform:f===void 0?void 0:ms(f,e,`${t} has member 'transform' that`),writableType:d}}function ds(e,t,o){return te(e,o),s=>de(e,t,[s])}function hs(e,t,o){return te(e,o),s=>Be(e,t,[s])}function ms(e,t,o){return te(e,o),(s,u)=>de(e,t,[s,u])}class tr{constructor(t={},o={},s={}){t===void 0&&(t=null);let u=jt(o,"Second parameter"),f=jt(s,"Third parameter"),d=fs(t,"First parameter");if(d.readableType!==void 0)throw new RangeError("Invalid readableType specified");if(d.writableType!==void 0)throw new RangeError("Invalid writableType specified");let _=dt(f,0),E=zt(f),A=dt(u,1),k=zt(u),O,I=w(oe=>{O=oe});ps(this,I,A,k,_,E),gs(this,d),d.start!==void 0?O(d.start(this._transformStreamController)):O(void 0)}get readable(){if(!jo(this))throw Ho("readable");return this._readable}get writable(){if(!jo(this))throw Ho("writable");return this._writable}}Object.defineProperties(tr.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),typeof a.toStringTag=="symbol"&&Object.defineProperty(tr.prototype,a.toStringTag,{value:"TransformStream",configurable:!0});function ps(e,t,o,s,u,f){function d(){return t}function _(I){return ws(e,I)}function E(I){return Ss(e,I)}function A(){return vs(e)}e._writable=Ta(d,_,A,E,o,s);function k(){return Rs(e)}function O(I){return nr(e,I),p(void 0)}e._readable=Zr(d,k,O,u,f),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,or(e,!0),e._transformStreamController=void 0}function jo(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")?!1:e instanceof tr}function rr(e,t){ve(e._readable._readableStreamController,t),nr(e,t)}function nr(e,t){Mo(e._transformStreamController),Yr(e._writable._writableStreamController,t),e._backpressure&&or(e,!1)}function or(e,t){e._backpressureChangePromise!==void 0&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=w(o=>{e._backpressureChangePromise_resolve=o}),e._backpressure=t}class _t{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!ir(this))throw ar("desiredSize");let t=this._controlledTransformStream._readable._readableStreamController;return Xr(t)}enqueue(t=void 0){if(!ir(this))throw ar("enqueue");No(this,t)}error(t=void 0){if(!ir(this))throw ar("error");ys(this,t)}terminate(){if(!ir(this))throw ar("terminate");_s(this)}}Object.defineProperties(_t.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),typeof a.toStringTag=="symbol"&&Object.defineProperty(_t.prototype,a.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});function ir(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")?!1:e instanceof _t}function bs(e,t,o,s){t._controlledTransformStream=e,e._transformStreamController=t,t._transformAlgorithm=o,t._flushAlgorithm=s}function gs(e,t){let o=Object.create(_t.prototype),s=f=>{try{return No(o,f),p(void 0)}catch(d){return b(d)}},u=()=>p(void 0);t.transform!==void 0&&(s=f=>t.transform(f,o)),t.flush!==void 0&&(u=()=>t.flush(o)),bs(e,o,s,u)}function Mo(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0}function No(e,t){let o=e._controlledTransformStream,s=o._readable._readableStreamController;if(!He(s))throw new TypeError("Readable side is not in a state that permits enqueue");try{Kt(s,t)}catch(f){throw nr(o,f),o._readable._storedError}Ja(s)!==o._backpressure&&or(o,!0)}function ys(e,t){rr(e._controlledTransformStream,t)}function Uo(e,t){let o=e._transformAlgorithm(t);return B(o,void 0,s=>{throw rr(e._controlledTransformStream,s),s})}function _s(e){let t=e._controlledTransformStream,o=t._readable._readableStreamController;gt(o);let s=new TypeError("TransformStream terminated");nr(t,s)}function ws(e,t){let o=e._transformStreamController;if(e._backpressure){let s=e._backpressureChangePromise;return B(s,()=>{let u=e._writable;if(u._state==="erroring")throw u._storedError;return Uo(o,t)})}return Uo(o,t)}function Ss(e,t){return rr(e,t),p(void 0)}function vs(e){let t=e._readable,o=e._transformStreamController,s=o._flushAlgorithm();return Mo(o),B(s,()=>{if(t._state==="errored")throw t._storedError;gt(t._readableStreamController)},u=>{throw rr(e,u),t._storedError})}function Rs(e){return or(e,!1),e._backpressureChangePromise}function ar(e){return new TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}function Ho(e){return new TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}i.ByteLengthQueuingStrategy=Zt,i.CountQueuingStrategy=er,i.ReadableByteStreamController=ze,i.ReadableStream=Re,i.ReadableStreamBYOBReader=ft,i.ReadableStreamBYOBRequest=lt,i.ReadableStreamDefaultController=Ue,i.ReadableStreamDefaultReader=it,i.TransformStream=tr,i.TransformStreamDefaultController=_t,i.WritableStream=ht,i.WritableStreamDefaultController=Ne,i.WritableStreamDefaultWriter=mt,Object.defineProperty(i,"__esModule",{value:!0})})}}),Hs=yn({"node_modules/fetch-blob/streams.cjs"(){var r=65536;if(!globalThis.ReadableStream)try{let n=require("process"),{emitWarning:i}=n;try{n.emitWarning=()=>{},Object.assign(globalThis,require("stream/web")),n.emitWarning=i}catch(a){throw n.emitWarning=i,a}}catch{Object.assign(globalThis,Us())}try{let{Blob:n}=require("buffer");n&&!n.prototype.stream&&(n.prototype.stream=function(a){let l=0,c=this;return new ReadableStream({type:"bytes",async pull(m){let g=await c.slice(l,Math.min(c.size,l+r)).arrayBuffer();l+=g.byteLength,m.enqueue(new Uint8Array(g)),l===c.size&&m.close()}})})}catch{}}});async function*an(r,n=!0){for(let i of r)if("stream"in i)yield*i.stream();else if(ArrayBuffer.isView(i))if(n){let a=i.byteOffset,l=i.byteOffset+i.byteLength;for(;a!==l;){let c=Math.min(l-a,hn),m=i.buffer.slice(a,a+c);a+=m.byteLength,yield new Uint8Array(m)}}else yield i;else{let a=0,l=i;for(;a!==l.size;){let m=await l.slice(a,Math.min(l.size,a+hn)).arrayBuffer();a+=m.byteLength,yield new Uint8Array(m)}}}var Ys,hn,sn,mn,tt,Pt=At({"node_modules/fetch-blob/index.js"(){Ys=H(Hs()),hn=65536,sn=class pn{#e=[];#t="";#r=0;#n="transparent";constructor(n=[],i={}){if(typeof n!="object"||n===null)throw new TypeError("Failed to construct 'Blob': The provided value cannot be converted to a sequence.");if(typeof n[Symbol.iterator]!="function")throw new TypeError("Failed to construct 'Blob': The object must have a callable @@iterator property.");if(typeof i!="object"&&typeof i!="function")throw new TypeError("Failed to construct 'Blob': parameter 2 cannot convert to dictionary.");i===null&&(i={});let a=new TextEncoder;for(let c of n){let m;ArrayBuffer.isView(c)?m=new Uint8Array(c.buffer.slice(c.byteOffset,c.byteOffset+c.byteLength)):c instanceof ArrayBuffer?m=new Uint8Array(c.slice(0)):c instanceof pn?m=c:m=a.encode(`${c}`),this.#r+=ArrayBuffer.isView(m)?m.byteLength:m.size,this.#e.push(m)}this.#n=`${i.endings===void 0?"transparent":i.endings}`;let l=i.type===void 0?"":String(i.type);this.#t=/^[\x20-\x7E]*$/.test(l)?l:""}get size(){return this.#r}get type(){return this.#t}async text(){let n=new TextDecoder,i="";for await(let a of an(this.#e,!1))i+=n.decode(a,{stream:!0});return i+=n.decode(),i}async arrayBuffer(){let n=new Uint8Array(this.size),i=0;for await(let a of an(this.#e,!1))n.set(a,i),i+=a.length;return n.buffer}stream(){let n=an(this.#e,!0);return new globalThis.ReadableStream({type:"bytes",async pull(i){let a=await n.next();a.done?i.close():i.enqueue(a.value)},async cancel(){await n.return()}})}slice(n=0,i=this.size,a=""){let{size:l}=this,c=n<0?Math.max(l+n,0):Math.min(n,l),m=i<0?Math.max(l+i,0):Math.min(i,l),h=Math.max(m-c,0),g=this.#e,y=[],q=0;for(let x of g){if(q>=h)break;let w=ArrayBuffer.isView(x)?x.byteLength:x.size;if(c&&w<=c)c-=w,m-=w;else{let p;ArrayBuffer.isView(x)?(p=x.subarray(c,Math.min(w,m)),q+=p.byteLength):(p=x.slice(c,Math.min(w,m)),q+=p.size),m-=w,y.push(p),c=0}}let j=new pn([],{type:String(a).toLowerCase()});return j.#r=h,j.#e=y,j}get[Symbol.toStringTag](){return"Blob"}static[Symbol.hasInstance](n){return n&&typeof n=="object"&&typeof n.constructor=="function"&&(typeof n.stream=="function"||typeof n.arrayBuffer=="function")&&/^(Blob|File)$/.test(n[Symbol.toStringTag])}},Object.defineProperties(sn.prototype,{size:{enumerable:!0},type:{enumerable:!0},slice:{enumerable:!0}}),mn=sn,tt=mn}}),Zo,ei,kt,pi=At({"node_modules/fetch-blob/file.js"(){Pt(),Zo=class extends tt{#e=0;#t="";constructor(n,i,a={}){if(arguments.length<2)throw new TypeError(`Failed to construct 'File': 2 arguments required, but only ${arguments.length} present.`);super(n,a),a===null&&(a={});let l=a.lastModified===void 0?Date.now():Number(a.lastModified);Number.isNaN(l)||(this.#e=l),this.#t=String(i)}get name(){return this.#t}get lastModified(){return this.#e}get[Symbol.toStringTag](){return"File"}static[Symbol.hasInstance](n){return!!n&&n instanceof tt&&/^(File)$/.test(n[Symbol.toStringTag])}},ei=Zo,kt=ei}});function Vs(r,n=tt){var i=`${bn()}${bn()}`.replace(/\./g,"").slice(-28).padStart(32,"-"),a=[],l=`--${i}\r
Content-Disposition: form-data; name="`;return r.forEach((c,m)=>typeof c=="string"?a.push(l+dr(m)+`"\r
\r
${c.replace(/\r(?!\n)|(?<!\r)\n/g,`\r
`)}\r
`):a.push(l+dr(m)+`"; filename="${dr(c.name,1)}"\r
Content-Type: ${c.type||"application/octet-stream"}\r
\r
`,c,`\r
`)),a.push(`--${i}--`),new n(a,{type:"multipart/form-data; boundary="+i})}var Ke,ti,ri,bn,ni,ln,dr,Pe,rt,yr=At({"node_modules/formdata-polyfill/esm.min.js"(){Pt(),pi(),{toStringTag:Ke,iterator:ti,hasInstance:ri}=Symbol,bn=Math.random,ni="append,set,get,getAll,delete,keys,values,entries,forEach,constructor".split(","),ln=(r,n,i)=>(r+="",/^(Blob|File)$/.test(n&&n[Ke])?[(i=i!==void 0?i+"":n[Ke]=="File"?n.name:"blob",r),n.name!==i||n[Ke]=="blob"?new kt([n],i,n):n]:[r,n+""]),dr=(r,n)=>(n?r:r.replace(/\r?\n|\r/g,`\r
`)).replace(/\n/g,"%0A").replace(/\r/g,"%0D").replace(/"/g,"%22"),Pe=(r,n,i)=>{if(n.length<i)throw new TypeError(`Failed to execute '${r}' on 'FormData': ${i} arguments required, but only ${n.length} present.`)},rt=class{#e=[];constructor(...n){if(n.length)throw new TypeError("Failed to construct 'FormData': parameter 1 is not of type 'HTMLFormElement'.")}get[Ke](){return"FormData"}[ti](){return this.entries()}static[ri](n){return n&&typeof n=="object"&&n[Ke]==="FormData"&&!ni.some(i=>typeof n[i]!="function")}append(...n){Pe("append",arguments,2),this.#e.push(ln(...n))}delete(n){Pe("delete",arguments,1),n+="",this.#e=this.#e.filter(([i])=>i!==n)}get(n){Pe("get",arguments,1),n+="";for(var i=this.#e,a=i.length,l=0;l<a;l++)if(i[l][0]===n)return i[l][1];return null}getAll(n,i){return Pe("getAll",arguments,1),i=[],n+="",this.#e.forEach(a=>a[0]===n&&i.push(a[1])),i}has(n){return Pe("has",arguments,1),n+="",this.#e.some(i=>i[0]===n)}forEach(n,i){Pe("forEach",arguments,1);for(var[a,l]of this)n.call(i,l,a,this)}set(...n){Pe("set",arguments,2);var i=[],a=!0;n=ln(...n),this.#e.forEach(l=>{l[0]===n[0]?a&&(a=!i.push(n)):i.push(l)}),a&&i.push(n),this.#e=i}*entries(){yield*this.#e}*keys(){for(var[n]of this)yield n}*values(){for(var[,n]of this)yield n}}}}),Gs=yn({"node_modules/node-domexception/index.js"(r,n){if(!globalThis.DOMException)try{let{MessageChannel:i}=require("worker_threads"),a=new i().port1,l=new ArrayBuffer;a.postMessage(l,[l,l])}catch(i){i.constructor.name==="DOMException"&&(globalThis.DOMException=i.constructor)}n.exports=globalThis.DOMException}}),St,oi,ii,ur,bi,gi,yi,_i,un,cn,cr,wi=At({"node_modules/fetch-blob/from.js"(){St=H(require("fs")),oi=H(require("path")),ii=H(Gs()),pi(),Pt(),{stat:ur}=St.promises,bi=(r,n)=>un((0,St.statSync)(r),r,n),gi=(r,n)=>ur(r).then(i=>un(i,r,n)),yi=(r,n)=>ur(r).then(i=>cn(i,r,n)),_i=(r,n)=>cn((0,St.statSync)(r),r,n),un=(r,n,i="")=>new tt([new cr({path:n,size:r.size,lastModified:r.mtimeMs,start:0})],{type:i}),cn=(r,n,i="")=>new kt([new cr({path:n,size:r.size,lastModified:r.mtimeMs,start:0})],(0,oi.basename)(n),{type:i,lastModified:r.mtimeMs}),cr=class{#e;#t;constructor(r){this.#e=r.path,this.#t=r.start,this.size=r.size,this.lastModified=r.lastModified}slice(r,n){return new cr({path:this.#e,lastModified:this.lastModified,size:n-r,start:this.#t+r})}async*stream(){let{mtimeMs:r}=await ur(this.#e);if(r>this.lastModified)throw new ii.default("The requested file could not be read, typically due to permission problems that have occurred after a reference to a file was acquired.","NotReadableError");yield*(0,St.createReadStream)(this.#e,{start:this.#t,end:this.#t+this.size-1})}get[Symbol.toStringTag](){return"Blob"}}}}),Si={};mi(Si,{toFormData:()=>Js});function Qs(r){let n=r.match(/\bfilename=("(.*?)"|([^()<>@,;:\\"/[\]?={}\s\t]+))($|;\s)/i);if(!n)return;let i=n[2]||n[3]||"",a=i.slice(i.lastIndexOf("\\")+1);return a=a.replace(/%22/g,'"'),a=a.replace(/&#(\d{4});/g,(l,c)=>String.fromCharCode(c)),a}async function Js(r,n){if(!/multipart/i.test(n))throw new TypeError("Failed to fetch");let i=n.match(/boundary=(?:"([^"]+)"|([^;]+))/i);if(!i)throw new TypeError("no or bad content-type header, no multipart boundary");let a=new vi(i[1]||i[2]),l,c,m,h,g,y,q=[],j=new rt,x=v=>{m+=P.decode(v,{stream:!0})},w=v=>{q.push(v)},p=()=>{let v=new kt(q,y,{type:g});j.append(h,v)},b=()=>{j.append(h,m)},P=new TextDecoder("utf-8");P.decode(),a.onPartBegin=function(){a.onPartData=x,a.onPartEnd=b,l="",c="",m="",h="",g="",y=null,q.length=0},a.onHeaderField=function(v){l+=P.decode(v,{stream:!0})},a.onHeaderValue=function(v){c+=P.decode(v,{stream:!0})},a.onHeaderEnd=function(){if(c+=P.decode(),l=l.toLowerCase(),l==="content-disposition"){let v=c.match(/\bname=("([^"]*)"|([^()<>@,;:\\"/[\]?={}\s\t]+))/i);v&&(h=v[2]||v[3]||""),y=Qs(c),y&&(a.onPartData=w,a.onPartEnd=p)}else l==="content-type"&&(g=c);c="",l=""};for await(let v of r)a.write(v);return a.end(),j}var ie,D,fn,ye,vt,Rt,ai,Xe,si,li,ui,ci,ke,vi,Ks=At({"node_modules/node-fetch/src/utils/multipart-parser.js"(){wi(),yr(),ie=0,D={START_BOUNDARY:ie++,HEADER_FIELD_START:ie++,HEADER_FIELD:ie++,HEADER_VALUE_START:ie++,HEADER_VALUE:ie++,HEADER_VALUE_ALMOST_DONE:ie++,HEADERS_ALMOST_DONE:ie++,PART_DATA_START:ie++,PART_DATA:ie++,END:ie++},fn=1,ye={PART_BOUNDARY:fn,LAST_BOUNDARY:fn*=2},vt=10,Rt=13,ai=32,Xe=45,si=58,li=97,ui=122,ci=r=>r|32,ke=()=>{},vi=class{constructor(r){this.index=0,this.flags=0,this.onHeaderEnd=ke,this.onHeaderField=ke,this.onHeadersEnd=ke,this.onHeaderValue=ke,this.onPartBegin=ke,this.onPartData=ke,this.onPartEnd=ke,this.boundaryChars={},r=`\r
--`+r;let n=new Uint8Array(r.length);for(let i=0;i<r.length;i++)n[i]=r.charCodeAt(i),this.boundaryChars[n[i]]=!0;this.boundary=n,this.lookbehind=new Uint8Array(this.boundary.length+8),this.state=D.START_BOUNDARY}write(r){let n=0,i=r.length,a=this.index,{lookbehind:l,boundary:c,boundaryChars:m,index:h,state:g,flags:y}=this,q=this.boundary.length,j=q-1,x=r.length,w,p,b=N=>{this[N+"Mark"]=n},P=N=>{delete this[N+"Mark"]},v=(N,B,W,F)=>{(B===void 0||B!==W)&&this[N](F&&F.subarray(B,W))},M=(N,B)=>{let W=N+"Mark";W in this&&(B?(v(N,this[W],n,r),delete this[W]):(v(N,this[W],r.length,r),this[W]=0))};for(n=0;n<i;n++)switch(w=r[n],g){case D.START_BOUNDARY:if(h===c.length-2){if(w===Xe)y|=ye.LAST_BOUNDARY;else if(w!==Rt)return;h++;break}else if(h-1===c.length-2){if(y&ye.LAST_BOUNDARY&&w===Xe)g=D.END,y=0;else if(!(y&ye.LAST_BOUNDARY)&&w===vt)h=0,v("onPartBegin"),g=D.HEADER_FIELD_START;else return;break}w!==c[h+2]&&(h=-2),w===c[h+2]&&h++;break;case D.HEADER_FIELD_START:g=D.HEADER_FIELD,b("onHeaderField"),h=0;case D.HEADER_FIELD:if(w===Rt){P("onHeaderField"),g=D.HEADERS_ALMOST_DONE;break}if(h++,w===Xe)break;if(w===si){if(h===1)return;M("onHeaderField",!0),g=D.HEADER_VALUE_START;break}if(p=ci(w),p<li||p>ui)return;break;case D.HEADER_VALUE_START:if(w===ai)break;b("onHeaderValue"),g=D.HEADER_VALUE;case D.HEADER_VALUE:w===Rt&&(M("onHeaderValue",!0),v("onHeaderEnd"),g=D.HEADER_VALUE_ALMOST_DONE);break;case D.HEADER_VALUE_ALMOST_DONE:if(w!==vt)return;g=D.HEADER_FIELD_START;break;case D.HEADERS_ALMOST_DONE:if(w!==vt)return;v("onHeadersEnd"),g=D.PART_DATA_START;break;case D.PART_DATA_START:g=D.PART_DATA,b("onPartData");case D.PART_DATA:if(a=h,h===0){for(n+=j;n<x&&!(r[n]in m);)n+=q;n-=j,w=r[n]}if(h<c.length)c[h]===w?(h===0&&M("onPartData",!0),h++):h=0;else if(h===c.length)h++,w===Rt?y|=ye.PART_BOUNDARY:w===Xe?y|=ye.LAST_BOUNDARY:h=0;else if(h-1===c.length)if(y&ye.PART_BOUNDARY){if(h=0,w===vt){y&=~ye.PART_BOUNDARY,v("onPartEnd"),v("onPartBegin"),g=D.HEADER_FIELD_START;break}}else y&ye.LAST_BOUNDARY&&w===Xe?(v("onPartEnd"),g=D.END,y=0):h=0;if(h>0)l[h-1]=w;else if(a>0){let N=new Uint8Array(l.buffer,l.byteOffset,l.byteLength);v("onPartData",0,a,N),a=0,b("onPartData"),n--}break;case D.END:break;default:throw new Error(`Unexpected state entered: ${g}`)}M("onHeaderField"),M("onHeaderValue"),M("onPartData"),this.index=h,this.state=g,this.flags=y}end(){if(this.state===D.HEADER_FIELD_START&&this.index===0||this.state===D.PART_DATA&&this.index===this.boundary.length)this.onPartEnd();else if(this.state!==D.END)throw new Error("MultipartParser.end(): stream ended unexpectedly")}}}});mi(Pi,{AbortError:()=>Ci,Blob:()=>mn,FetchError:()=>se,File:()=>kt,FormData:()=>rt,Headers:()=>_e,Request:()=>Ct,Response:()=>K,blobFrom:()=>gi,blobFromSync:()=>bi,default:()=>Ai,fileFrom:()=>yi,fileFromSync:()=>_i,isRedirect:()=>wn});var Xs=H(require("http")),Zs=H(require("https")),Ze=H(require("zlib")),ce=H(require("stream")),fr=H(require("buffer"));function el(r){if(!/^data:/i.test(r))throw new TypeError('`uri` does not appear to be a Data URI (must begin with "data:")');r=r.replace(/\r?\n/g,"");let n=r.indexOf(",");if(n===-1||n<=4)throw new TypeError("malformed data: URI");let i=r.substring(5,n).split(";"),a="",l=!1,c=i[0]||"text/plain",m=c;for(let q=1;q<i.length;q++)i[q]==="base64"?l=!0:i[q]&&(m+=`;${i[q]}`,i[q].indexOf("charset=")===0&&(a=i[q].substring(8)));!i[0]&&!a.length&&(m+=";charset=US-ASCII",a="US-ASCII");let h=l?"base64":"ascii",g=unescape(r.substring(n+1)),y=Buffer.from(g,h);return y.type=c,y.typeFull=m,y.charset=a,y}var tl=el,fe=H(require("stream")),nt=H(require("util")),Z=H(require("buffer"));Pt();yr();var _r=class extends Error{constructor(r,n){super(r),Error.captureStackTrace(this,this.constructor),this.type=n}get name(){return this.constructor.name}get[Symbol.toStringTag](){return this.constructor.name}},se=class extends _r{constructor(r,n,i){super(r,n),i&&(this.code=this.errno=i.code,this.erroredSysCall=i.syscall)}},mr=Symbol.toStringTag,Ri=r=>typeof r=="object"&&typeof r.append=="function"&&typeof r.delete=="function"&&typeof r.get=="function"&&typeof r.getAll=="function"&&typeof r.has=="function"&&typeof r.set=="function"&&typeof r.sort=="function"&&r[mr]==="URLSearchParams",pr=r=>r&&typeof r=="object"&&typeof r.arrayBuffer=="function"&&typeof r.type=="string"&&typeof r.stream=="function"&&typeof r.constructor=="function"&&/^(Blob|File)$/.test(r[mr]),rl=r=>typeof r=="object"&&(r[mr]==="AbortSignal"||r[mr]==="EventTarget"),nl=(r,n)=>{let i=new URL(n).hostname,a=new URL(r).hostname;return i===a||i.endsWith(`.${a}`)},ol=(r,n)=>{let i=new URL(n).protocol,a=new URL(r).protocol;return i===a},il=(0,nt.promisify)(fe.default.pipeline),G=Symbol("Body internals"),Et=class{constructor(r,{size:n=0}={}){let i=null;r===null?r=null:Ri(r)?r=Z.Buffer.from(r.toString()):pr(r)||Z.Buffer.isBuffer(r)||(nt.types.isAnyArrayBuffer(r)?r=Z.Buffer.from(r):ArrayBuffer.isView(r)?r=Z.Buffer.from(r.buffer,r.byteOffset,r.byteLength):r instanceof fe.default||(r instanceof rt?(r=Vs(r),i=r.type.split("=")[1]):r=Z.Buffer.from(String(r))));let a=r;Z.Buffer.isBuffer(r)?a=fe.default.Readable.from(r):pr(r)&&(a=fe.default.Readable.from(r.stream())),this[G]={body:r,stream:a,boundary:i,disturbed:!1,error:null},this.size=n,r instanceof fe.default&&r.on("error",l=>{let c=l instanceof _r?l:new se(`Invalid response body while trying to fetch ${this.url}: ${l.message}`,"system",l);this[G].error=c})}get body(){return this[G].stream}get bodyUsed(){return this[G].disturbed}async arrayBuffer(){let{buffer:r,byteOffset:n,byteLength:i}=await dn(this);return r.slice(n,n+i)}async formData(){let r=this.headers.get("content-type");if(r.startsWith("application/x-www-form-urlencoded")){let i=new rt,a=new URLSearchParams(await this.text());for(let[l,c]of a)i.append(l,c);return i}let{toFormData:n}=await Promise.resolve().then(()=>(Ks(),Si));return n(this.body,r)}async blob(){let r=this.headers&&this.headers.get("content-type")||this[G].body&&this[G].body.type||"",n=await this.arrayBuffer();return new tt([n],{type:r})}async json(){let r=await this.text();return JSON.parse(r)}async text(){let r=await dn(this);return new TextDecoder().decode(r)}buffer(){return dn(this)}};Et.prototype.buffer=(0,nt.deprecate)(Et.prototype.buffer,"Please use 'response.arrayBuffer()' instead of 'response.buffer()'","node-fetch#buffer");Object.defineProperties(Et.prototype,{body:{enumerable:!0},bodyUsed:{enumerable:!0},arrayBuffer:{enumerable:!0},blob:{enumerable:!0},json:{enumerable:!0},text:{enumerable:!0},data:{get:(0,nt.deprecate)(()=>{},"data doesn't exist, use json(), text(), arrayBuffer(), or body instead","https://github.com/node-fetch/node-fetch/issues/1000 (response)")}});async function dn(r){if(r[G].disturbed)throw new TypeError(`body used already for: ${r.url}`);if(r[G].disturbed=!0,r[G].error)throw r[G].error;let{body:n}=r;if(n===null||!(n instanceof fe.default))return Z.Buffer.alloc(0);let i=[],a=0;try{for await(let l of n){if(r.size>0&&a+l.length>r.size){let c=new se(`content size at ${r.url} over limit: ${r.size}`,"max-size");throw n.destroy(c),c}a+=l.length,i.push(l)}}catch(l){throw l instanceof _r?l:new se(`Invalid response body while trying to fetch ${r.url}: ${l.message}`,"system",l)}if(n.readableEnded===!0||n._readableState.ended===!0)try{return i.every(l=>typeof l=="string")?Z.Buffer.from(i.join("")):Z.Buffer.concat(i,a)}catch(l){throw new se(`Could not create Buffer from response body for ${r.url}: ${l.message}`,"system",l)}else throw new se(`Premature close of server response while trying to fetch ${r.url}`)}var _n=(r,n)=>{let i,a,{body:l}=r[G];if(r.bodyUsed)throw new Error("cannot clone body after it is used");return l instanceof fe.default&&typeof l.getBoundary!="function"&&(i=new fe.PassThrough({highWaterMark:n}),a=new fe.PassThrough({highWaterMark:n}),l.pipe(i),l.pipe(a),r[G].stream=i,l=a),l},al=(0,nt.deprecate)(r=>r.getBoundary(),"form-data doesn't follow the spec and requires special treatment. Use alternative package","https://github.com/node-fetch/node-fetch/issues/1167"),Ti=(r,n)=>r===null?null:typeof r=="string"?"text/plain;charset=UTF-8":Ri(r)?"application/x-www-form-urlencoded;charset=UTF-8":pr(r)?r.type||null:Z.Buffer.isBuffer(r)||nt.types.isAnyArrayBuffer(r)||ArrayBuffer.isView(r)?null:r instanceof rt?`multipart/form-data; boundary=${n[G].boundary}`:r&&typeof r.getBoundary=="function"?`multipart/form-data;boundary=${al(r)}`:r instanceof fe.default?null:"text/plain;charset=UTF-8",sl=r=>{let{body:n}=r[G];return n===null?0:pr(n)?n.size:Z.Buffer.isBuffer(n)?n.length:n&&typeof n.getLengthSync=="function"&&n.hasKnownLength&&n.hasKnownLength()?n.getLengthSync():null},ll=async(r,{body:n})=>{n===null?r.end():await il(n,r)},fi=H(require("util")),br=H(require("http")),hr=typeof br.default.validateHeaderName=="function"?br.default.validateHeaderName:r=>{if(!/^[\^`\-\w!#$%&'*+.|~]+$/.test(r)){let n=new TypeError(`Header name must be a valid HTTP token [${r}]`);throw Object.defineProperty(n,"code",{value:"ERR_INVALID_HTTP_TOKEN"}),n}},gn=typeof br.default.validateHeaderValue=="function"?br.default.validateHeaderValue:(r,n)=>{if(/[^\t\u0020-\u007E\u0080-\u00FF]/.test(n)){let i=new TypeError(`Invalid character in header content ["${r}"]`);throw Object.defineProperty(i,"code",{value:"ERR_INVALID_CHAR"}),i}},_e=class extends URLSearchParams{constructor(r){let n=[];if(r instanceof _e){let i=r.raw();for(let[a,l]of Object.entries(i))n.push(...l.map(c=>[a,c]))}else if(r!=null)if(typeof r=="object"&&!fi.types.isBoxedPrimitive(r)){let i=r[Symbol.iterator];if(i==null)n.push(...Object.entries(r));else{if(typeof i!="function")throw new TypeError("Header pairs must be iterable");n=[...r].map(a=>{if(typeof a!="object"||fi.types.isBoxedPrimitive(a))throw new TypeError("Each header pair must be an iterable object");return[...a]}).map(a=>{if(a.length!==2)throw new TypeError("Each header pair must be a name/value tuple");return[...a]})}}else throw new TypeError("Failed to construct 'Headers': The provided value is not of type '(sequence<sequence<ByteString>> or record<ByteString, ByteString>)");return n=n.length>0?n.map(([i,a])=>(hr(i),gn(i,String(a)),[String(i).toLowerCase(),String(a)])):void 0,super(n),new Proxy(this,{get(i,a,l){switch(a){case"append":case"set":return(c,m)=>(hr(c),gn(c,String(m)),URLSearchParams.prototype[a].call(i,String(c).toLowerCase(),String(m)));case"delete":case"has":case"getAll":return c=>(hr(c),URLSearchParams.prototype[a].call(i,String(c).toLowerCase()));case"keys":return()=>(i.sort(),new Set(URLSearchParams.prototype.keys.call(i)).keys());default:return Reflect.get(i,a,l)}}})}get[Symbol.toStringTag](){return this.constructor.name}toString(){return Object.prototype.toString.call(this)}get(r){let n=this.getAll(r);if(n.length===0)return null;let i=n.join(", ");return/^content-encoding$/i.test(r)&&(i=i.toLowerCase()),i}forEach(r,n=void 0){for(let i of this.keys())Reflect.apply(r,n,[this.get(i),i,this])}*values(){for(let r of this.keys())yield this.get(r)}*entries(){for(let r of this.keys())yield[r,this.get(r)]}[Symbol.iterator](){return this.entries()}raw(){return[...this.keys()].reduce((r,n)=>(r[n]=this.getAll(n),r),{})}[Symbol.for("nodejs.util.inspect.custom")](){return[...this.keys()].reduce((r,n)=>{let i=this.getAll(n);return n==="host"?r[n]=i[0]:r[n]=i.length>1?i:i[0],r},{})}};Object.defineProperties(_e.prototype,["get","entries","forEach","values"].reduce((r,n)=>(r[n]={enumerable:!0},r),{}));function ul(r=[]){return new _e(r.reduce((n,i,a,l)=>(a%2===0&&n.push(l.slice(a,a+2)),n),[]).filter(([n,i])=>{try{return hr(n),gn(n,String(i)),!0}catch{return!1}}))}var cl=new Set([301,302,303,307,308]),wn=r=>cl.has(r),ae=Symbol("Response internals"),K=class extends Et{constructor(r=null,n={}){super(r,n);let i=n.status!=null?n.status:200,a=new _e(n.headers);if(r!==null&&!a.has("Content-Type")){let l=Ti(r,this);l&&a.append("Content-Type",l)}this[ae]={type:"default",url:n.url,status:i,statusText:n.statusText||"",headers:a,counter:n.counter,highWaterMark:n.highWaterMark}}get type(){return this[ae].type}get url(){return this[ae].url||""}get status(){return this[ae].status}get ok(){return this[ae].status>=200&&this[ae].status<300}get redirected(){return this[ae].counter>0}get statusText(){return this[ae].statusText}get headers(){return this[ae].headers}get highWaterMark(){return this[ae].highWaterMark}clone(){return new K(_n(this,this.highWaterMark),{type:this.type,url:this.url,status:this.status,statusText:this.statusText,headers:this.headers,ok:this.ok,redirected:this.redirected,size:this.size,highWaterMark:this.highWaterMark})}static redirect(r,n=302){if(!wn(n))throw new RangeError('Failed to execute "redirect" on "response": Invalid status code');return new K(null,{headers:{location:new URL(r).toString()},status:n})}static error(){let r=new K(null,{status:0,statusText:""});return r[ae].type="error",r}static json(r=void 0,n={}){let i=JSON.stringify(r);if(i===void 0)throw new TypeError("data is not JSON serializable");let a=new _e(n&&n.headers);return a.has("content-type")||a.set("content-type","application/json"),new K(i,{...n,headers:a})}get[Symbol.toStringTag](){return"Response"}};Object.defineProperties(K.prototype,{type:{enumerable:!0},url:{enumerable:!0},status:{enumerable:!0},ok:{enumerable:!0},redirected:{enumerable:!0},statusText:{enumerable:!0},headers:{enumerable:!0},clone:{enumerable:!0}});var fl=H(require("url")),dl=H(require("util")),hl=r=>{if(r.search)return r.search;let n=r.href.length-1,i=r.hash||(r.href[n]==="#"?"#":"");return r.href[n-i.length]==="?"?"?":""},ml=H(require("net"));function di(r,n=!1){return r==null||(r=new URL(r),/^(about|blob|data):$/.test(r.protocol))?"no-referrer":(r.username="",r.password="",r.hash="",n&&(r.pathname="",r.search=""),r)}var Ei=new Set(["","no-referrer","no-referrer-when-downgrade","same-origin","origin","strict-origin","origin-when-cross-origin","strict-origin-when-cross-origin","unsafe-url"]),pl="strict-origin-when-cross-origin";function bl(r){if(!Ei.has(r))throw new TypeError(`Invalid referrerPolicy: ${r}`);return r}function gl(r){if(/^(http|ws)s:$/.test(r.protocol))return!0;let n=r.host.replace(/(^\[)|(]$)/g,""),i=(0,ml.isIP)(n);return i===4&&/^127\./.test(n)||i===6&&/^(((0+:){7})|(::(0+:){0,6}))0*1$/.test(n)?!0:r.host==="localhost"||r.host.endsWith(".localhost")?!1:r.protocol==="file:"}function et(r){return/^about:(blank|srcdoc)$/.test(r)||r.protocol==="data:"||/^(blob|filesystem):$/.test(r.protocol)?!0:gl(r)}function yl(r,{referrerURLCallback:n,referrerOriginCallback:i}={}){if(r.referrer==="no-referrer"||r.referrerPolicy==="")return null;let a=r.referrerPolicy;if(r.referrer==="about:client")return"no-referrer";let l=r.referrer,c=di(l),m=di(l,!0);c.toString().length>4096&&(c=m),n&&(c=n(c)),i&&(m=i(m));let h=new URL(r.url);switch(a){case"no-referrer":return"no-referrer";case"origin":return m;case"unsafe-url":return c;case"strict-origin":return et(c)&&!et(h)?"no-referrer":m.toString();case"strict-origin-when-cross-origin":return c.origin===h.origin?c:et(c)&&!et(h)?"no-referrer":m;case"same-origin":return c.origin===h.origin?c:"no-referrer";case"origin-when-cross-origin":return c.origin===h.origin?c:m;case"no-referrer-when-downgrade":return et(c)&&!et(h)?"no-referrer":c;default:throw new TypeError(`Invalid referrerPolicy: ${a}`)}}function _l(r){let n=(r.get("referrer-policy")||"").split(/[,\s]+/),i="";for(let a of n)a&&Ei.has(a)&&(i=a);return i}var z=Symbol("Request internals"),Tt=r=>typeof r=="object"&&typeof r[z]=="object",wl=(0,dl.deprecate)(()=>{},".data is not a valid RequestInit property, use .body instead","https://github.com/node-fetch/node-fetch/issues/1000 (request)"),Ct=class extends Et{constructor(r,n={}){let i;if(Tt(r)?i=new URL(r.url):(i=new URL(r),r={}),i.username!==""||i.password!=="")throw new TypeError(`${i} is an url with embedded credentials.`);let a=n.method||r.method||"GET";if(/^(delete|get|head|options|post|put)$/i.test(a)&&(a=a.toUpperCase()),!Tt(n)&&"data"in n&&wl(),(n.body!=null||Tt(r)&&r.body!==null)&&(a==="GET"||a==="HEAD"))throw new TypeError("Request with GET/HEAD method cannot have body");let l=n.body?n.body:Tt(r)&&r.body!==null?_n(r):null;super(l,{size:n.size||r.size||0});let c=new _e(n.headers||r.headers||{});if(l!==null&&!c.has("Content-Type")){let g=Ti(l,this);g&&c.set("Content-Type",g)}let m=Tt(r)?r.signal:null;if("signal"in n&&(m=n.signal),m!=null&&!rl(m))throw new TypeError("Expected signal to be an instanceof AbortSignal or EventTarget");let h=n.referrer==null?r.referrer:n.referrer;if(h==="")h="no-referrer";else if(h){let g=new URL(h);h=/^about:(\/\/)?client$/.test(g)?"client":g}else h=void 0;this[z]={method:a,redirect:n.redirect||r.redirect||"follow",headers:c,parsedURL:i,signal:m,referrer:h},this.follow=n.follow===void 0?r.follow===void 0?20:r.follow:n.follow,this.compress=n.compress===void 0?r.compress===void 0?!0:r.compress:n.compress,this.counter=n.counter||r.counter||0,this.agent=n.agent||r.agent,this.highWaterMark=n.highWaterMark||r.highWaterMark||16384,this.insecureHTTPParser=n.insecureHTTPParser||r.insecureHTTPParser||!1,this.referrerPolicy=n.referrerPolicy||r.referrerPolicy||""}get method(){return this[z].method}get url(){return(0,fl.format)(this[z].parsedURL)}get headers(){return this[z].headers}get redirect(){return this[z].redirect}get signal(){return this[z].signal}get referrer(){if(this[z].referrer==="no-referrer")return"";if(this[z].referrer==="client")return"about:client";if(this[z].referrer)return this[z].referrer.toString()}get referrerPolicy(){return this[z].referrerPolicy}set referrerPolicy(r){this[z].referrerPolicy=bl(r)}clone(){return new Ct(this)}get[Symbol.toStringTag](){return"Request"}};Object.defineProperties(Ct.prototype,{method:{enumerable:!0},url:{enumerable:!0},headers:{enumerable:!0},redirect:{enumerable:!0},clone:{enumerable:!0},signal:{enumerable:!0},referrer:{enumerable:!0},referrerPolicy:{enumerable:!0}});var Sl=r=>{let{parsedURL:n}=r[z],i=new _e(r[z].headers);i.has("Accept")||i.set("Accept","*/*");let a=null;if(r.body===null&&/^(post|put)$/i.test(r.method)&&(a="0"),r.body!==null){let h=sl(r);typeof h=="number"&&!Number.isNaN(h)&&(a=String(h))}a&&i.set("Content-Length",a),r.referrerPolicy===""&&(r.referrerPolicy=pl),r.referrer&&r.referrer!=="no-referrer"?r[z].referrer=yl(r):r[z].referrer="no-referrer",r[z].referrer instanceof URL&&i.set("Referer",r.referrer),i.has("User-Agent")||i.set("User-Agent","node-fetch"),r.compress&&!i.has("Accept-Encoding")&&i.set("Accept-Encoding","gzip, deflate, br");let{agent:l}=r;typeof l=="function"&&(l=l(n));let c=hl(n),m={path:n.pathname+c,method:r.method,headers:i[Symbol.for("nodejs.util.inspect.custom")](),insecureHTTPParser:r.insecureHTTPParser,agent:l};return{parsedURL:n,options:m}},Ci=class extends _r{constructor(r,n="aborted"){super(r,n)}};yr();wi();var vl=new Set(["data:","http:","https:"]);async function Ai(r,n){return new Promise((i,a)=>{let l=new Ct(r,n),{parsedURL:c,options:m}=Sl(l);if(!vl.has(c.protocol))throw new TypeError(`node-fetch cannot load ${r}. URL scheme "${c.protocol.replace(/:$/,"")}" is not supported.`);if(c.protocol==="data:"){let p=tl(l.url),b=new K(p,{headers:{"Content-Type":p.typeFull}});i(b);return}let h=(c.protocol==="https:"?Zs.default:Xs.default).request,{signal:g}=l,y=null,q=()=>{let p=new Ci("The operation was aborted.");a(p),l.body&&l.body instanceof ce.default.Readable&&l.body.destroy(p),!(!y||!y.body)&&y.body.emit("error",p)};if(g&&g.aborted){q();return}let j=()=>{q(),w()},x=h(c.toString(),m);g&&g.addEventListener("abort",j);let w=()=>{x.abort(),g&&g.removeEventListener("abort",j)};x.on("error",p=>{a(new se(`request to ${l.url} failed, reason: ${p.message}`,"system",p)),w()}),Rl(x,p=>{y&&y.body&&y.body.destroy(p)}),process.version<"v14"&&x.on("socket",p=>{let b;p.prependListener("end",()=>{b=p._eventsCount}),p.prependListener("close",P=>{if(y&&b<p._eventsCount&&!P){let v=new Error("Premature close");v.code="ERR_STREAM_PREMATURE_CLOSE",y.body.emit("error",v)}})}),x.on("response",p=>{x.setTimeout(0);let b=ul(p.rawHeaders);if(wn(p.statusCode)){let B=b.get("Location"),W=null;try{W=B===null?null:new URL(B,l.url)}catch{if(l.redirect!=="manual"){a(new se(`uri requested responds with an invalid redirect URL: ${B}`,"invalid-redirect")),w();return}}switch(l.redirect){case"error":a(new se(`uri requested responds with a redirect, redirect mode is set to error: ${l.url}`,"no-redirect")),w();return;case"manual":break;case"follow":{if(W===null)break;if(l.counter>=l.follow){a(new se(`maximum redirect reached at: ${l.url}`,"max-redirect")),w();return}let F={headers:new _e(l.headers),follow:l.follow,counter:l.counter+1,agent:l.agent,compress:l.compress,method:l.method,body:_n(l),signal:l.signal,size:l.size,referrer:l.referrer,referrerPolicy:l.referrerPolicy};if(!nl(l.url,W)||!ol(l.url,W))for(let de of["authorization","www-authenticate","cookie","cookie2"])F.headers.delete(de);if(p.statusCode!==303&&l.body&&n.body instanceof ce.default.Readable){a(new se("Cannot follow redirect with body being a readable stream","unsupported-redirect")),w();return}(p.statusCode===303||(p.statusCode===301||p.statusCode===302)&&l.method==="POST")&&(F.method="GET",F.body=void 0,F.headers.delete("content-length"));let Be=_l(b);Be&&(F.referrerPolicy=Be),i(Ai(new Ct(W,F))),w();return}default:return a(new TypeError(`Redirect option '${l.redirect}' is not a valid value of RequestRedirect`))}}g&&p.once("end",()=>{g.removeEventListener("abort",j)});let P=(0,ce.pipeline)(p,new ce.PassThrough,B=>{B&&a(B)});process.version<"v12.10"&&p.on("aborted",j);let v={url:l.url,status:p.statusCode,statusText:p.statusMessage,headers:b,size:l.size,counter:l.counter,highWaterMark:l.highWaterMark},M=b.get("Content-Encoding");if(!l.compress||l.method==="HEAD"||M===null||p.statusCode===204||p.statusCode===304){y=new K(P,v),i(y);return}let N={flush:Ze.default.Z_SYNC_FLUSH,finishFlush:Ze.default.Z_SYNC_FLUSH};if(M==="gzip"||M==="x-gzip"){P=(0,ce.pipeline)(P,Ze.default.createGunzip(N),B=>{B&&a(B)}),y=new K(P,v),i(y);return}if(M==="deflate"||M==="x-deflate"){let B=(0,ce.pipeline)(p,new ce.PassThrough,W=>{W&&a(W)});B.once("data",W=>{(W[0]&15)===8?P=(0,ce.pipeline)(P,Ze.default.createInflate(),F=>{F&&a(F)}):P=(0,ce.pipeline)(P,Ze.default.createInflateRaw(),F=>{F&&a(F)}),y=new K(P,v),i(y)}),B.once("end",()=>{y||(y=new K(P,v),i(y))});return}if(M==="br"){P=(0,ce.pipeline)(P,Ze.default.createBrotliDecompress(),B=>{B&&a(B)}),y=new K(P,v),i(y);return}y=new K(P,v),i(y)}),ll(x,l).catch(a)})}function Rl(r,n){let i=fr.Buffer.from(`0\r
\r
`),a=!1,l=!1,c;r.on("response",m=>{let{headers:h}=m;a=h["transfer-encoding"]==="chunked"&&!h["content-length"]}),r.on("socket",m=>{let h=()=>{if(a&&!l){let y=new Error("Premature close");y.code="ERR_STREAM_PREMATURE_CLOSE",n(y)}},g=y=>{l=fr.Buffer.compare(y.slice(-5),i)===0,!l&&c&&(l=fr.Buffer.compare(c.slice(-3),i.slice(0,3))===0&&fr.Buffer.compare(y.slice(-2),i.slice(3))===0),c=y};m.prependListener("close",h),m.on("data",g),r.on("close",()=>{m.removeListener("close",h),m.removeListener("data",g)})})}Pt();yr();});var vn=Je((mu,Bi)=>{"use strict";var Oi=require("fs"),Sn;function Tl(){try{return Oi.statSync("/.dockerenv"),!0}catch{return!1}}function El(){try{return Oi.readFileSync("/proc/self/cgroup","utf8").includes("docker")}catch{return!1}}Bi.exports=()=>(Sn===void 0&&(Sn=Tl()||El()),Sn)});var Wi=Je((pu,Rn)=>{"use strict";var Cl=require("os"),Al=require("fs"),Di=vn(),$i=()=>{if(process.platform!=="linux")return!1;if(Cl.release().toLowerCase().includes("microsoft"))return!Di();try{return Al.readFileSync("/proc/version","utf8").toLowerCase().includes("microsoft")?!Di():!1}catch{return!1}};process.env.__IS_WSL_TEST__?Rn.exports=$i:Rn.exports=$i()});var xi=Je((bu,qi)=>{"use strict";qi.exports=(r,n,i)=>{let a=l=>Object.defineProperty(r,n,{value:l,enumerable:!0,writable:!0});return Object.defineProperty(r,n,{configurable:!0,enumerable:!0,get(){let l=i();return a(l),l},set(l){a(l)}}),r}});var Ni=Je((gu,Mi)=>{var Pl=require("path"),kl=require("child_process"),{promises:Sr,constants:ji}=require("fs"),wr=Wi(),Ol=vn(),En=xi(),Fi=Pl.join(__dirname,"xdg-open"),{platform:ot,arch:Ii}=process,Bl=()=>{try{return Sr.statSync("/run/.containerenv"),!0}catch{return!1}},Tn;function Dl(){return Tn===void 0&&(Tn=Bl()||Ol()),Tn}var $l=(()=>{let r="/mnt/",n;return async function(){if(n)return n;let i="/etc/wsl.conf",a=!1;try{await Sr.access(i,ji.F_OK),a=!0}catch{}if(!a)return r;let l=await Sr.readFile(i,{encoding:"utf8"}),c=/(?<!#.*)root\s*=\s*(?<mountPoint>.*)/g.exec(l);return c?(n=c.groups.mountPoint.trim(),n=n.endsWith("/")?n:`${n}/`,n):r}})(),Li=async(r,n)=>{let i;for(let a of r)try{return await n(a)}catch(l){i=l}throw i},vr=async r=>{if(r={wait:!1,background:!1,newInstance:!1,allowNonzeroExitCode:!1,...r},Array.isArray(r.app))return Li(r.app,h=>vr({...r,app:h}));let{name:n,arguments:i=[]}=r.app||{};if(i=[...i],Array.isArray(n))return Li(n,h=>vr({...r,app:{name:h,arguments:i}}));let a,l=[],c={};if(ot==="darwin")a="open",r.wait&&l.push("--wait-apps"),r.background&&l.push("--background"),r.newInstance&&l.push("--new"),n&&l.push("-a",n);else if(ot==="win32"||wr&&!Dl()&&!n){let h=await $l();a=wr?`${h}c/Windows/System32/WindowsPowerShell/v1.0/powershell.exe`:`${process.env.SYSTEMROOT}\\System32\\WindowsPowerShell\\v1.0\\powershell`,l.push("-NoProfile","-NonInteractive","\u2013ExecutionPolicy","Bypass","-EncodedCommand"),wr||(c.windowsVerbatimArguments=!0);let g=["Start"];r.wait&&g.push("-Wait"),n?(g.push(`"\`"${n}\`""`,"-ArgumentList"),r.target&&i.unshift(r.target)):r.target&&g.push(`"${r.target}"`),i.length>0&&(i=i.map(y=>`"\`"${y}\`""`),g.push(i.join(","))),r.target=Buffer.from(g.join(" "),"utf16le").toString("base64")}else{if(n)a=n;else{let h=!__dirname||__dirname==="/",g=!1;try{await Sr.access(Fi,ji.X_OK),g=!0}catch{}a=process.versions.electron||ot==="android"||h||!g?"xdg-open":Fi}i.length>0&&l.push(...i),r.wait||(c.stdio="ignore",c.detached=!0)}r.target&&l.push(r.target),ot==="darwin"&&i.length>0&&l.push("--args",...i);let m=kl.spawn(a,l,c);return r.wait?new Promise((h,g)=>{m.once("error",g),m.once("close",y=>{if(!r.allowNonzeroExitCode&&y>0){g(new Error(`Exited with code ${y}`));return}h(m)})}):(m.unref(),m)},Cn=(r,n)=>{if(typeof r!="string")throw new TypeError("Expected a `target`");return vr({...n,target:r})},Wl=(r,n)=>{if(typeof r!="string")throw new TypeError("Expected a `name`");let{arguments:i=[]}=n||{};if(i!=null&&!Array.isArray(i))throw new TypeError("Expected `appArguments` as Array type");return vr({...n,app:{name:r,arguments:i}})};function zi(r){if(typeof r=="string"||Array.isArray(r))return r;let{[Ii]:n}=r;if(!n)throw new Error(`${Ii} is not supported`);return n}function An({[ot]:r},{wsl:n}){if(n&&wr)return zi(n);if(!r)throw new Error(`${ot} is not supported`);return zi(r)}var Rr={};En(Rr,"chrome",()=>An({darwin:"google chrome",win32:"chrome",linux:["google-chrome","google-chrome-stable","chromium"]},{wsl:{ia32:"/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe",x64:["/mnt/c/Program Files/Google/Chrome/Application/chrome.exe","/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe"]}}));En(Rr,"firefox",()=>An({darwin:"firefox",win32:"C:\\Program Files\\Mozilla Firefox\\firefox.exe",linux:"firefox"},{wsl:"/mnt/c/Program Files/Mozilla Firefox/firefox.exe"}));En(Rr,"edge",()=>An({darwin:"microsoft edge",win32:"msedge",linux:["microsoft-edge","microsoft-edge-dev"]},{wsl:"/mnt/c/Program Files (x86)/Microsoft/Edge/Application/msedge.exe"}));Cn.apps=Rr;Cn.openApp=Wl;Mi.exports=Cn});var Yl={};ks(Yl,{authorize:()=>xl,clone:()=>zl,commitWasAlreadyMerged:()=>Nl,enableDeleteBranchOnMerge:()=>Ll,findExtensionPath:()=>Ml,forkAndGetRepo:()=>Fl,getExistingPR:()=>Ul,openPRAsDraft:()=>Hl,setGitRemote:()=>jl,syncWithUpstream:()=>Il});module.exports=Os(Yl);var wt=Ae(require("node:fs")),nn=Ae(require("node:path")),Qo=Ae(require("node:os"));var Go={production:{url:"https://www.raycast.com",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},staging:{url:"https://www.rayca.st",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},development:{url:"http://localhost:3000",clientID:"rOCD_LhnXFObSLu9v7788qOGu25FZDqEzGjet4Q45ZY"}}[process.env.API_ENV||"production"],Bs="e69bae0ec90f5e838555",L={},Ds;function J(r){switch(r){case"raycastApiURL":return process.env.RAY_APIURL||L.APIURL||Go.url;case"raycastAccessToken":return process.env.RAY_TOKEN||L.Token||L.AccessToken||"";case"raycastClientId":return process.env.RAY_CLIENT_ID||L.ClientID||Go.clientID;case"githubClientId":return process.env.RAY_GithubClientID||L.GithubClientID||Bs;case"githubAccessToken":return process.env.GITHUB_ACCESS_TOKEN||L.GithubAccessToken||"";case"flavorName":return process.env.RAY_Target?process.env.RAY_Target:typeof L.Target<"u"?L.Target:rn(process.platform==="win32"?"x":"release")}}function Jo(r,n){switch(r){case"raycastApiURL":n===void 0?delete L.APIURL:L.APIURL=n;break;case"raycastAccessToken":n===void 0?delete L.Token:L.Token=n,delete L.AccessToken;break;case"raycastClientId":n===void 0?delete L.ClientID:L.ClientID=n;break;case"githubAccessToken":n===void 0?delete L.GithubAccessToken:L.GithubAccessToken=n;break;case"flavorName":n===void 0?delete L.Target:L.Target=n;break}let i=Ws();wt.writeFileSync(nn.join(i,"config.json"),JSON.stringify(L,null,"  "),"utf8")}function rn(r){switch(r){case"debug":return"debug";case"internal":return"internal";case"release":return"";case"x":return"x";case"x-internal":return"x-internal";case"x-development":return"x-development";case void 0:return J("flavorName")}}function $s(){let r=rn(Ds);return r==""?"raycast":`raycast-${r}`}function Ws(){let r=nn.join(Qo.default.homedir(),".config",$s());return wt.mkdirSync(r,{recursive:!0}),r}var U=Ae(Xo());var eu=(0,U.blue)((0,U.dim)("internal only"));function on(r,n,i){console.log(Fs[r]+n),typeof i?.exit<"u"&&process.exit(i.exit)}var Fs={wait:`\u{1F550}${(0,U.blue)("wait")}  - `,info:`${process.env.GITHUB_ACTIONS==="true"?"\u2139\uFE0F":"\u2139\uFE0F "}${(0,U.cyan)("info")}  - `,success:`\u2705${(0,U.green)("ready")}  - `,warn:`${process.env.GITHUB_ACTIONS==="true"?"\u26A0\uFE0F":"\u26A0\uFE0F "}${(0,U.yellow)("warn")}  - `,error:`\u{1F4A5}${(0,U.red)("error")}  - `,event:`\u26A1\uFE0F${(0,U.magenta)("event")}  - `,paymentPrompt:`\u{1F4B0}${(0,U.yellowBright)("plan")}  - `};var Ui=require("@oclif/core"),Hi=Ae(ki()),Yi=Ae(Ni());async function le(r,n){let i;try{i=await(0,Hi.default)(r,{method:n.method||"GET",headers:{"Content-Type":"application/json",Accept:"application/json",...n.token?{Authorization:`Bearer ${n.token}`}:void 0},body:n.body})}catch(a){throw new Error(`HTTP request: ${a.message}`)}if(!i.ok){switch(i.status){case 401:throw new Oe(i,"not authorized - please log in first using `npx ray login`");case 403:throw new Oe(i,"forbidden - you don't have permissions to perform the request");case 402:throw new Oe(i,"the limit of free commands has been reached")}let a=await i.text(),l;try{l=JSON.parse(a)}catch{throw new Oe(i,`HTTP error: ${i.status} - ${a}`)}throw Array.isArray(l.errors)&&l.errors.length>0?new Oe(i,`error: ${l.errors[0].status} - ${l.errors[0].title}`):new Oe(i,`HTTP error: ${i.status} - ${a}`)}return await i.json()}var Oe=class extends Error{constructor(n,i){let a=n.headers.get("X-Request-Id");a?super(`${i} (${n.url} RequestID: ${a})`):super(i),this.name="HTTPError"}};function Vi(r){(0,Yi.default)(r).catch(n=>{Ui.ux.error(new Error(`failed opening browser to URL ${r}: ${n.message}`),{exit:1})})}var Gi=require("child_process");var Qi=Ae(require("path"));var Ji;function Pn(r,n){return new Promise((i,a)=>{(0,Gi.exec)(r,{cwd:Ji},(l,c,m)=>{if(n.throwOnError&&l){a(new Error(`failed running git ${l.message.replace(/oauth2:gho_[a-zA-Z0-9]+@/g,"oauth2:gho_xxxxx")}
${c.trim()}
${m.trim()}`));return}i(c.trim())})})}async function Ki(r,n){Ji=Qi.default.dirname(n),await Pn(`git clone --filter=blob:none --no-checkout ${r} "${n}"`,{throwOnError:!0})}async function Xi(r,n){try{await Pn(`git remote set-url ${r} ${n}`,{throwOnError:!0})}catch{await Pn(`git remote add ${r} ${n}`,{throwOnError:!0})}}var kn=Ae(require("path"));async function xl(){if(J("githubAccessToken"))return;let r=J("githubClientId"),n=await le("https://github.com/login/device/code",{method:"POST",body:JSON.stringify({client_id:r,scope:"repo"})});on("info",`

\u{1F510} Raycast extensions are published on GitHub.
To automate this process, you have to authenticate with GitHub.

First copy your one-time code: ${n.user_code}
Press Enter to open github.com in your browser...`),process.stdin.setRawMode(!0),process.stdin.resume(),await new Promise(a=>process.stdin.once("data",l=>{let c=[...l];c.length>0&&c[0]===3&&(console.log("^C"),process.exit(1)),process.stdin.setRawMode(!1),a(void 0)})),Vi(n.verification_uri);let i=n.interval*1e3;for(;;){await new Promise(a=>setTimeout(a,i));try{let a=await le("https://github.com/login/oauth/access_token",{method:"POST",body:JSON.stringify({client_id:r,device_code:n.device_code,grant_type:"urn:ietf:params:oauth:grant-type:device_code"})});if(!a.error){Jo("githubAccessToken",a.access_token);return}if(a.error!=="authorization_pending")throw new Error(a.error_description)}catch(a){throw new Error(`failed to get the access token (${a.message})`)}}}async function Fl(r,n){let i=J("githubAccessToken"),a=await le(`https://api.github.com/repos/${r.owner.login}/${r.name}/forks`,{method:"POST",token:i,body:JSON.stringify({name:n,default_branch_only:!0})});for(let l=0;l<=30;l++){try{await le(`https://api.github.com/repos/${a.owner.login}/${a.name}/commits?per_page=1`,{token:i});break}catch(c){if(l===30)throw new Error(`fork not ready after 1min (${c.message})`)}await new Promise(c=>setTimeout(c,2e3))}return a}async function Il(r){let n=J("githubAccessToken");try{await le(`https://api.github.com/repos/${r.owner.login}/${r.name}/merge-upstream`,{method:"POST",token:n,body:JSON.stringify({branch:"main"})})}catch(i){throw new Error(`could not get the latest changes. Head to https://github.com/${r.owner.login}/${r.name}, select the Sync fork dropdown menu above the list of files, and then click Update branch. Once you've done that, try running this command again

Error: ${i.message}`)}}async function Ll(r){let n=J("githubAccessToken");await le(`https://api.github.com/repos/${r.owner.login}/${r.name}`,{method:"POST",token:n,body:JSON.stringify({delete_branch_on_merge:"true"})})}function zl(r,n){return Ki(`https://oauth2:${J("githubAccessToken")}@github.com/${r.owner.login}/${r.name}`,n)}function jl(r,n){return Xi(r,`https://oauth2:${J("githubAccessToken")}@github.com/${n.owner.login}/${n.name}`)}async function Ml(r,n,i){let a=`"\\"name\\": \\"${n}\\"" "\\"author\\": \\"${i}\\"" repo:raycast/extensions in:file path:extensions extension:json`,l=J("githubAccessToken"),m=(await le(`https://api.github.com/search/code?q=${encodeURIComponent(a)}&per_page=3`,{token:l})).items.filter(h=>h.name==="package.json");if(m.length===0)return kn.default.join("extensions",n);if(m.length>1)throw new Error(`found more than one extension with name ${n}`);return kn.default.dirname(m[0].path)}async function Nl(r){let n=`type:pr repo:raycast/extensions ${r} is:merged`,i=J("githubAccessToken");return(await le(`https://api.github.com/search/issues?q=${encodeURIComponent(n)}&per_page=1`,{token:i})).items.length>0}async function Ul(r,n,i){return(await le(`https://api.github.com/repos/${r.owner.login}/${r.name}/pulls?base=main&head=${n.owner.login}:${encodeURIComponent(i)}`,{token:J("githubAccessToken")})).find(l=>l.head.ref===i&&l.head.user.login===n.owner.login)}function Hl(r,n,i,a,l){return le(`https://api.github.com/repos/${r.owner.login}/${r.name}/pulls`,{method:"POST",token:J("githubAccessToken"),body:JSON.stringify({title:a,head:`${n.owner.login}:${i}`,base:"main",body:l,maintainer_can_modify:!0,draft:!0})})}0&&(module.exports={authorize,clone,commitWasAlreadyMerged,enableDeleteBranchOnMerge,findExtensionPath,forkAndGetRepo,getExistingPR,openPRAsDraft,setGitRemote,syncWithUpstream});
/*! Bundled license information:

node-fetch-cjs/dist/index.js:
  (*! fetch-blob. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
  (*! formdata-polyfill. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
  (*! node-domexception. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
*/
