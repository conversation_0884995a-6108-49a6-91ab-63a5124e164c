import { LocalStorage } from "@raycast/api";
import { RecordItem, RecordsData } from "../types";
import { format } from "date-fns";

const STORAGE_KEY = "flash-memo-records";

export class StorageService {
  static async getAllRecords(): Promise<RecordsData> {
    try {
      const stored = await LocalStorage.getItem(STORAGE_KEY);
      if (typeof stored === "string") {
        const parsed = JSON.parse(stored);
        // Convert string dates back to Date objects
        for (const date in parsed) {
          parsed[date] = parsed[date].map((item: unknown) => {
            const recordItem = item as RecordItem;
            return {
              ...recordItem,
              timestamp: new Date(recordItem.timestamp),
            };
          });
        }
        return parsed;
      }
      return {};
    } catch (error) {
      console.error("Failed to load records:", error);
      return {};
    }
  }

  static async saveRecord(content: string): Promise<void> {
    try {
      const records = await this.getAllRecords();
      const today = format(new Date(), "yyyy-MM-dd");

      if (!records[today]) {
        records[today] = [];
      }

      const newRecord: RecordItem = {
        id: `${today}-${Date.now()}`,
        content: content.trim(),
        timestamp: new Date(),
        date: today,
      };

      records[today].unshift(newRecord);

      await LocalStorage.setItem(STORAGE_KEY, JSON.stringify(records));
    } catch (error) {
      console.error("Failed to save record:", error);
      throw error;
    }
  }

  static async getTodayRecords(): Promise<RecordItem[]> {
    try {
      const allRecords = await this.getAllRecords();
      const today = format(new Date(), "yyyy-MM-dd");
      return allRecords[today] || [];
    } catch (error) {
      console.error("Failed to get today's records:", error);
      return [];
    }
  }

  static async clearTodayRecords(): Promise<void> {
    try {
      const records = await this.getAllRecords();
      const today = format(new Date(), "yyyy-MM-dd");
      delete records[today];
      await LocalStorage.setItem(STORAGE_KEY, JSON.stringify(records));
    } catch (error) {
      console.error("Failed to clear today's records:", error);
      throw error;
    }
  }
}
