"use strict";var ef=Object.create;var Xe=Object.defineProperty;var tf=Object.getOwnPropertyDescriptor;var rf=Object.getOwnPropertyNames;var nf=Object.getPrototypeOf,of=Object.prototype.hasOwnProperty;var D=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),sf=(e,t)=>{for(var r in t)Xe(e,r,{get:t[r],enumerable:!0})},no=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of rf(t))!of.call(e,o)&&o!==r&&Xe(e,o,{get:()=>t[o],enumerable:!(n=tf(t,o))||n.enumerable});return e};var T=(e,t,r)=>(r=e!=null?ef(nf(e)):{},no(t||!e||!e.__esModule?Xe(r,"default",{value:e,enumerable:!0}):r,e)),af=e=>no(Xe({},"__esModule",{value:!0}),e);var lo=D((Mg,co)=>{var pf=require("node:tty"),uf=pf?.WriteStream?.prototype?.hasColors?.()??!1,g=(e,t)=>{if(!uf)return o=>o;let r=`\x1B[${e}m`,n=`\x1B[${t}m`;return o=>{let i=o+"",s=i.indexOf(n);if(s===-1)return r+i+n;let a=r,c=0;for(;s!==-1;)a+=i.slice(c,s)+r,c=s+n.length,s=i.indexOf(n,c);return a+=i.slice(c)+n,a}},h={};h.reset=g(0,0);h.bold=g(1,22);h.dim=g(2,22);h.italic=g(3,23);h.underline=g(4,24);h.overline=g(53,55);h.inverse=g(7,27);h.hidden=g(8,28);h.strikethrough=g(9,29);h.black=g(30,39);h.red=g(31,39);h.green=g(32,39);h.yellow=g(33,39);h.blue=g(34,39);h.magenta=g(35,39);h.cyan=g(36,39);h.white=g(37,39);h.gray=g(90,39);h.bgBlack=g(40,49);h.bgRed=g(41,49);h.bgGreen=g(42,49);h.bgYellow=g(43,49);h.bgBlue=g(44,49);h.bgMagenta=g(45,49);h.bgCyan=g(46,49);h.bgWhite=g(47,49);h.bgGray=g(100,49);h.redBright=g(91,39);h.greenBright=g(92,39);h.yellowBright=g(93,39);h.blueBright=g(94,39);h.magentaBright=g(95,39);h.cyanBright=g(96,39);h.whiteBright=g(97,39);h.bgRedBright=g(101,49);h.bgGreenBright=g(102,49);h.bgYellowBright=g(103,49);h.bgBlueBright=g(104,49);h.bgMagentaBright=g(105,49);h.bgCyanBright=g(106,49);h.bgWhiteBright=g(107,49);co.exports=h});var Xo=D((uS,Ko)=>{Ko.exports=Ho;Ho.sync=ld;var Yo=require("fs");function cd(e,t){var r=t.pathExt!==void 0?t.pathExt:process.env.PATHEXT;if(!r||(r=r.split(";"),r.indexOf("")!==-1))return!0;for(var n=0;n<r.length;n++){var o=r[n].toLowerCase();if(o&&e.substr(-o.length).toLowerCase()===o)return!0}return!1}function qo(e,t,r){return!e.isSymbolicLink()&&!e.isFile()?!1:cd(t,r)}function Ho(e,t,r){Yo.stat(e,function(n,o){r(n,n?!1:qo(o,e,t))})}function ld(e,t){return qo(Yo.statSync(e),e,t)}});var ti=D((mS,ei)=>{ei.exports=Zo;Zo.sync=fd;var Jo=require("fs");function Zo(e,t,r){Jo.stat(e,function(n,o){r(n,n?!1:Qo(o,t))})}function fd(e,t){return Qo(Jo.statSync(e),t)}function Qo(e,t){return e.isFile()&&dd(e,t)}function dd(e,t){var r=e.mode,n=e.uid,o=e.gid,i=t.uid!==void 0?t.uid:process.getuid&&process.getuid(),s=t.gid!==void 0?t.gid:process.getgid&&process.getgid(),a=parseInt("100",8),c=parseInt("010",8),f=parseInt("001",8),l=a|c,d=r&f||r&c&&o===s||r&a&&n===i||r&l&&i===0;return d}});var ni=D((gS,ri)=>{var hS=require("fs"),ut;process.platform==="win32"||global.TESTING_WINDOWS?ut=Xo():ut=ti();ri.exports=Lr;Lr.sync=pd;function Lr(e,t,r){if(typeof t=="function"&&(r=t,t={}),!r){if(typeof Promise!="function")throw new TypeError("callback not provided");return new Promise(function(n,o){Lr(e,t||{},function(i,s){i?o(i):n(s)})})}ut(e,t||{},function(n,o){n&&(n.code==="EACCES"||t&&t.ignoreErrors)&&(n=null,o=!1),r(n,o)})}function pd(e,t){try{return ut.sync(e,t||{})}catch(r){if(t&&t.ignoreErrors||r.code==="EACCES")return!1;throw r}}});var fi=D((yS,li)=>{var he=process.platform==="win32"||process.env.OSTYPE==="cygwin"||process.env.OSTYPE==="msys",oi=require("path"),ud=he?";":":",ii=ni(),si=e=>Object.assign(new Error(`not found: ${e}`),{code:"ENOENT"}),ai=(e,t)=>{let r=t.colon||ud,n=e.match(/\//)||he&&e.match(/\\/)?[""]:[...he?[process.cwd()]:[],...(t.path||process.env.PATH||"").split(r)],o=he?t.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM":"",i=he?o.split(r):[""];return he&&e.indexOf(".")!==-1&&i[0]!==""&&i.unshift(""),{pathEnv:n,pathExt:i,pathExtExe:o}},ci=(e,t,r)=>{typeof t=="function"&&(r=t,t={}),t||(t={});let{pathEnv:n,pathExt:o,pathExtExe:i}=ai(e,t),s=[],a=f=>new Promise((l,d)=>{if(f===n.length)return t.all&&s.length?l(s):d(si(e));let p=n[f],u=/^".*"$/.test(p)?p.slice(1,-1):p,m=oi.join(u,e),S=!u&&/^\.[\\\/]/.test(e)?e.slice(0,2)+m:m;l(c(S,f,0))}),c=(f,l,d)=>new Promise((p,u)=>{if(d===o.length)return p(a(l+1));let m=o[d];ii(f+m,{pathExt:i},(S,E)=>{if(!S&&E)if(t.all)s.push(f+m);else return p(f+m);return p(c(f,l,d+1))})});return r?a(0).then(f=>r(null,f),r):a(0)},md=(e,t)=>{t=t||{};let{pathEnv:r,pathExt:n,pathExtExe:o}=ai(e,t),i=[];for(let s=0;s<r.length;s++){let a=r[s],c=/^".*"$/.test(a)?a.slice(1,-1):a,f=oi.join(c,e),l=!c&&/^\.[\\\/]/.test(e)?e.slice(0,2)+f:f;for(let d=0;d<n.length;d++){let p=l+n[d];try{if(ii.sync(p,{pathExt:o}))if(t.all)i.push(p);else return p}catch{}}}if(t.all&&i.length)return i;if(t.nothrow)return null;throw si(e)};li.exports=ci;ci.sync=md});var pi=D((SS,Fr)=>{"use strict";var di=(e={})=>{let t=e.env||process.env;return(e.platform||process.platform)!=="win32"?"PATH":Object.keys(t).reverse().find(n=>n.toUpperCase()==="PATH")||"Path"};Fr.exports=di;Fr.exports.default=di});var gi=D((bS,hi)=>{"use strict";var ui=require("path"),hd=fi(),gd=pi();function mi(e,t){let r=e.options.env||process.env,n=process.cwd(),o=e.options.cwd!=null,i=o&&process.chdir!==void 0&&!process.chdir.disabled;if(i)try{process.chdir(e.options.cwd)}catch{}let s;try{s=hd.sync(e.command,{path:r[gd({env:r})],pathExt:t?ui.delimiter:void 0})}catch{}finally{i&&process.chdir(n)}return s&&(s=ui.resolve(o?e.options.cwd:"",s)),s}function yd(e){return mi(e)||mi(e,!0)}hi.exports=yd});var yi=D((wS,vr)=>{"use strict";var $r=/([()\][%!^"`<>&|;, *?])/g;function Sd(e){return e=e.replace($r,"^$1"),e}function bd(e,t){return e=`${e}`,e=e.replace(/(?=(\\+?)?)\1"/g,'$1$1\\"'),e=e.replace(/(?=(\\+?)?)\1$/,"$1$1"),e=`"${e}"`,e=e.replace($r,"^$1"),t&&(e=e.replace($r,"^$1")),e}vr.exports.command=Sd;vr.exports.argument=bd});var bi=D((xS,Si)=>{"use strict";Si.exports=/^#!(.*)/});var xi=D((ES,wi)=>{"use strict";var wd=bi();wi.exports=(e="")=>{let t=e.match(wd);if(!t)return null;let[r,n]=t[0].replace(/#! ?/,"").split(" "),o=r.split("/").pop();return o==="env"?n:n?`${o} ${n}`:o}});var Ti=D((TS,Ei)=>{"use strict";var Ur=require("fs"),xd=xi();function Ed(e){let r=Buffer.alloc(150),n;try{n=Ur.openSync(e,"r"),Ur.readSync(n,r,0,150,0),Ur.closeSync(n)}catch{}return xd(r.toString())}Ei.exports=Ed});var Di=D((AS,Ii)=>{"use strict";var Td=require("path"),Ai=gi(),Oi=yi(),Ad=Ti(),Od=process.platform==="win32",Id=/\.(?:com|exe)$/i,Dd=/node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;function Rd(e){e.file=Ai(e);let t=e.file&&Ad(e.file);return t?(e.args.unshift(e.file),e.command=t,Ai(e)):e.file}function Cd(e){if(!Od)return e;let t=Rd(e),r=!Id.test(t);if(e.options.forceShell||r){let n=Dd.test(t);e.command=Td.normalize(e.command),e.command=Oi.command(e.command),e.args=e.args.map(i=>Oi.argument(i,n));let o=[e.command].concat(e.args).join(" ");e.args=["/d","/s","/c",`"${o}"`],e.command=process.env.comspec||"cmd.exe",e.options.windowsVerbatimArguments=!0}return e}function Md(e,t,r){t&&!Array.isArray(t)&&(r=t,t=null),t=t?t.slice(0):[],r=Object.assign({},r);let n={command:e,args:t,options:r,file:void 0,original:{command:e,args:t}};return r.shell?n:Cd(n)}Ii.exports=Md});var Mi=D((OS,Ci)=>{"use strict";var Nr=process.platform==="win32";function _r(e,t){return Object.assign(new Error(`${t} ${e.command} ENOENT`),{code:"ENOENT",errno:"ENOENT",syscall:`${t} ${e.command}`,path:e.command,spawnargs:e.args})}function Pd(e,t){if(!Nr)return;let r=e.emit;e.emit=function(n,o){if(n==="exit"){let i=Ri(o,t);if(i)return r.call(e,"error",i)}return r.apply(e,arguments)}}function Ri(e,t){return Nr&&e===1&&!t.file?_r(t.original,"spawn"):null}function Bd(e,t){return Nr&&e===1&&!t.file?_r(t.original,"spawnSync"):null}Ci.exports={hookChildProcess:Pd,verifyENOENT:Ri,verifyENOENTSync:Bd,notFoundError:_r}});var Li=D((IS,ge)=>{"use strict";var Pi=require("child_process"),jr=Di(),kr=Mi();function Bi(e,t,r){let n=jr(e,t,r),o=Pi.spawn(n.command,n.args,n.options);return kr.hookChildProcess(o,n),o}function Ld(e,t,r){let n=jr(e,t,r),o=Pi.spawnSync(n.command,n.args,n.options);return o.error=o.error||kr.verifyENOENTSync(o.status,n),o}ge.exports=Bi;ge.exports.spawn=Bi;ge.exports.sync=Ld;ge.exports._parse=jr;ge.exports._enoent=kr});var Ag={};sf(Ag,{default:()=>pr});module.exports=af(Ag);var Ql=require("@oclif/core");var X=require("@oclif/core");var Pe=T(require("node:fs")),hr=T(require("node:path")),io=T(require("node:os"));var oo={production:{url:"https://www.raycast.com",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},staging:{url:"https://www.rayca.st",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},development:{url:"http://localhost:3000",clientID:"rOCD_LhnXFObSLu9v7788qOGu25FZDqEzGjet4Q45ZY"}}[process.env.API_ENV||"production"],cf="e69bae0ec90f5e838555",z={},so;function ao(e){so=e;try{z=JSON.parse(Pe.readFileSync(hr.join(df(),"config.json"),"utf8"))}catch(t){if(t instanceof Error&&t.code==="ENOENT")return;throw new Error(`Failed to read config file: ${t}`)}}function lf(e){switch(e){case"raycastApiURL":return process.env.RAY_APIURL||z.APIURL||oo.url;case"raycastAccessToken":return process.env.RAY_TOKEN||z.Token||z.AccessToken||"";case"raycastClientId":return process.env.RAY_CLIENT_ID||z.ClientID||oo.clientID;case"githubClientId":return process.env.RAY_GithubClientID||z.GithubClientID||cf;case"githubAccessToken":return process.env.GITHUB_ACCESS_TOKEN||z.GithubAccessToken||"";case"flavorName":return process.env.RAY_Target?process.env.RAY_Target:typeof z.Target<"u"?z.Target:mr(process.platform==="win32"?"x":"release")}}function mr(e){switch(e){case"debug":return"debug";case"internal":return"internal";case"release":return"";case"x":return"x";case"x-internal":return"x-internal";case"x-development":return"x-development";case void 0:return lf("flavorName")}}function ff(){let e=mr(so);return e==""?"raycast":`raycast-${e}`}function df(){let e=hr.join(io.default.homedir(),".config",ff());return Pe.mkdirSync(e,{recursive:!0}),e}var w=T(lo());var oe=[];oe.push("SIGHUP","SIGINT","SIGTERM");process.platform!=="win32"&&oe.push("SIGALRM","SIGABRT","SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");process.platform==="linux"&&oe.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT");var Je=e=>!!e&&typeof e=="object"&&typeof e.removeListener=="function"&&typeof e.emit=="function"&&typeof e.reallyExit=="function"&&typeof e.listeners=="function"&&typeof e.kill=="function"&&typeof e.pid=="number"&&typeof e.on=="function",gr=Symbol.for("signal-exit emitter"),yr=globalThis,mf=Object.defineProperty.bind(Object),Sr=class{emitted={afterExit:!1,exit:!1};listeners={afterExit:[],exit:[]};count=0;id=Math.random();constructor(){if(yr[gr])return yr[gr];mf(yr,gr,{value:this,writable:!1,enumerable:!1,configurable:!1})}on(t,r){this.listeners[t].push(r)}removeListener(t,r){let n=this.listeners[t],o=n.indexOf(r);o!==-1&&(o===0&&n.length===1?n.length=0:n.splice(o,1))}emit(t,r,n){if(this.emitted[t])return!1;this.emitted[t]=!0;let o=!1;for(let i of this.listeners[t])o=i(r,n)===!0||o;return t==="exit"&&(o=this.emit("afterExit",r,n)||o),o}},Ze=class{},hf=e=>({onExit(t,r){return e.onExit(t,r)},load(){return e.load()},unload(){return e.unload()}}),br=class extends Ze{onExit(){return()=>{}}load(){}unload(){}},wr=class extends Ze{#t=xr.platform==="win32"?"SIGINT":"SIGHUP";#r=new Sr;#e;#n;#i;#o={};#s=!1;constructor(t){super(),this.#e=t,this.#o={};for(let r of oe)this.#o[r]=()=>{let n=this.#e.listeners(r),{count:o}=this.#r,i=t;if(typeof i.__signal_exit_emitter__=="object"&&typeof i.__signal_exit_emitter__.count=="number"&&(o+=i.__signal_exit_emitter__.count),n.length===o){this.unload();let s=this.#r.emit("exit",null,r),a=r==="SIGHUP"?this.#t:r;s||t.kill(t.pid,a)}};this.#i=t.reallyExit,this.#n=t.emit}onExit(t,r){if(!Je(this.#e))return()=>{};this.#s===!1&&this.load();let n=r?.alwaysLast?"afterExit":"exit";return this.#r.on(n,t),()=>{this.#r.removeListener(n,t),this.#r.listeners.exit.length===0&&this.#r.listeners.afterExit.length===0&&this.unload()}}load(){if(!this.#s){this.#s=!0,this.#r.count+=1;for(let t of oe)try{let r=this.#o[t];r&&this.#e.on(t,r)}catch{}this.#e.emit=(t,...r)=>this.#c(t,...r),this.#e.reallyExit=t=>this.#a(t)}}unload(){this.#s&&(this.#s=!1,oe.forEach(t=>{let r=this.#o[t];if(!r)throw new Error("Listener not defined for signal: "+t);try{this.#e.removeListener(t,r)}catch{}}),this.#e.emit=this.#n,this.#e.reallyExit=this.#i,this.#r.count-=1)}#a(t){return Je(this.#e)?(this.#e.exitCode=t||0,this.#r.emit("exit",this.#e.exitCode,null),this.#i.call(this.#e,this.#e.exitCode)):0}#c(t,...r){let n=this.#n;if(t==="exit"&&Je(this.#e)){typeof r[0]=="number"&&(this.#e.exitCode=r[0]);let o=n.call(this.#e,t,...r);return this.#r.emit("exit",this.#e.exitCode,null),o}else return n.call(this.#e,t,...r)}},xr=globalThis.process,{onExit:fo,load:Lg,unload:Fg}=hf(Je(xr)?new wr(xr):new br);var Er=T(require("node:process"),1);function Tr(){let{env:e}=Er.default,{TERM:t,TERM_PROGRAM:r}=e;return Er.default.platform!=="win32"?t!=="linux":!!e.WT_SESSION||!!e.TERMINUS_SUBLIME||e.ConEmuTask==="{cmd::Cmder}"||r==="Terminus-Sublime"||r==="vscode"||t==="xterm-256color"||t==="alacritty"||t==="rxvt-unicode"||t==="rxvt-unicode-256color"||e.TERMINAL_EMULATOR==="JetBrains-JediTerm"}var Ug=(0,w.blue)((0,w.dim)("internal only"));var ie={wait:`\u{1F550}${(0,w.blue)("wait")}  - `,info:`${process.env.GITHUB_ACTIONS==="true"?"\u2139\uFE0F":"\u2139\uFE0F "}${(0,w.cyan)("info")}  - `,success:`\u2705${(0,w.green)("ready")}  - `,warn:`${process.env.GITHUB_ACTIONS==="true"?"\u26A0\uFE0F":"\u26A0\uFE0F "}${(0,w.yellow)("warn")}  - `,error:`\u{1F4A5}${(0,w.red)("error")}  - `,event:`\u26A1\uFE0F${(0,w.magenta)("event")}  - `,paymentPrompt:`\u{1F4B0}${(0,w.yellowBright)("plan")}  - `},gf=!0;function po(e,t){e||(ie.wait=`${(0,w.blue)("wait")}  - `,ie.info=`${(0,w.cyan)("info")}  - `,ie.success=`${(0,w.green)("ready")}  - `,ie.warn=`${(0,w.yellow)("warn")}  - `,ie.error=`${(0,w.red)("error")}  - `,ie.event=`${(0,w.magenta)("event")}  - `,ie.paymentPrompt=`${(0,w.yellowBright)("plan")}  - `),t&&(gf=!1)}var Qe=class extends X.Command{static baseFlags={"exit-on-error":X.Flags.boolean({default:!0,helpGroup:"GLOBAL",aliases:["exitOnError"],deprecateAliases:!0,summary:"Always exit with non-zero code on error",allowNo:!0}),emoji:X.Flags.boolean({default:!1,helpGroup:"GLOBAL",summary:"Prefix output with emojis \u{1F308}"}),help:X.Flags.boolean({default:!1,helpGroup:"GLOBAL",summary:"Show the help message for the command"}),"non-interactive":X.Flags.boolean({char:"I",default:!1,helpGroup:"GLOBAL",summary:"Disable interactive outputs, useful for CI"}),target:X.Flags.option({char:"t",description:"Raycast app target",helpGroup:"GLOBAL",multiple:!1,options:["debug","internal","release","x","x-development","x-internal"],hidden:!0})()};flags;args;async init(){await super.init(),process.on("SIGINT",()=>process.exit(1));let{args:t,flags:r}=await this.parse({flags:this.ctor.flags,baseFlags:super.ctor.baseFlags,enableJsonFlag:this.ctor.enableJsonFlag,args:this.ctor.args,strict:this.ctor.strict});this.flags=r,this.args=t,ao(this.flags.target),po(this.flags.emoji,this.flags["non-interactive"])}error(t,r){return r?.message&&t instanceof Error&&(t.message=`${r.message} (${t.message})`,delete r.message),super.error(t,r)}async catch(t){return super.catch(t)}async finally(t){return super.finally(t)}};function x(e){if(typeof e!="object"||e===null)return!1;let t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}var uo=require("node:url"),pe=(e,t)=>{let r=Or(yf(e));if(typeof r!="string")throw new TypeError(`${t} must be a string or a file URL: ${r}.`);return r},yf=e=>Ar(e)?e.toString():e,Ar=e=>typeof e!="string"&&e&&Object.getPrototypeOf(e)===String.prototype,Or=e=>e instanceof URL?(0,uo.fileURLToPath)(e):e;var et=(e,t=[],r={})=>{let n=pe(e,"First argument"),[o,i]=x(t)?[[],t]:[t,r];if(!Array.isArray(o))throw new TypeError(`Second argument must be either an array of arguments or an options object: ${o}`);if(o.some(c=>typeof c=="object"&&c!==null))throw new TypeError(`Second argument must be an array of strings: ${o}`);let s=o.map(String),a=s.find(c=>c.includes("\0"));if(a!==void 0)throw new TypeError(`Arguments cannot contain null bytes ("\\0"): ${a}`);if(!x(i))throw new TypeError(`Last argument must be an options object: ${i}`);return[n,s,i]};var Eo=require("node:child_process");var mo=require("node:string_decoder"),{toString:ho}=Object.prototype,go=e=>ho.call(e)==="[object ArrayBuffer]",A=e=>ho.call(e)==="[object Uint8Array]",W=e=>new Uint8Array(e.buffer,e.byteOffset,e.byteLength),Sf=new TextEncoder,yo=e=>Sf.encode(e),bf=new TextDecoder,tt=e=>bf.decode(e),So=(e,t)=>wf(e,t).join(""),wf=(e,t)=>{if(t==="utf8"&&e.every(i=>typeof i=="string"))return e;let r=new mo.StringDecoder(t),n=e.map(i=>typeof i=="string"?yo(i):i).map(i=>r.write(i)),o=r.end();return o===""?n:[...n,o]},Be=e=>e.length===1&&A(e[0])?e[0]:Ir(xf(e)),xf=e=>e.map(t=>typeof t=="string"?yo(t):t),Ir=e=>{let t=new Uint8Array(Ef(e)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t},Ef=e=>{let t=0;for(let r of e)t+=r.length;return t};var To=e=>Array.isArray(e)&&Array.isArray(e.raw),Ao=(e,t)=>{let r=[];for(let[i,s]of e.entries())r=Tf({templates:e,expressions:t,tokens:r,index:i,template:s});if(r.length===0)throw new TypeError("Template script must not be empty");let[n,...o]=r;return[n,o,{}]},Tf=({templates:e,expressions:t,tokens:r,index:n,template:o})=>{if(o===void 0)throw new TypeError(`Invalid backslash sequence: ${e.raw[n]}`);let{nextTokens:i,leadingWhitespaces:s,trailingWhitespaces:a}=Af(o,e.raw[n]),c=wo(r,i,s);if(n===t.length)return c;let f=t[n],l=Array.isArray(f)?f.map(d=>xo(d)):[xo(f)];return wo(c,l,a)},Af=(e,t)=>{if(t.length===0)return{nextTokens:[],leadingWhitespaces:!1,trailingWhitespaces:!1};let r=[],n=0,o=bo.has(t[0]);for(let s=0,a=0;s<e.length;s+=1,a+=1){let c=t[a];if(bo.has(c))n!==s&&r.push(e.slice(n,s)),n=s+1;else if(c==="\\"){let f=t[a+1];f===`
`?(s-=1,a+=1):f==="u"&&t[a+2]==="{"?a=t.indexOf("}",a+3):a+=Of[f]??1}}let i=n===e.length;return i||r.push(e.slice(n)),{nextTokens:r,leadingWhitespaces:o,trailingWhitespaces:i}},bo=new Set([" ","	","\r",`
`]),Of={x:3,u:5},wo=(e,t,r)=>r||e.length===0||t.length===0?[...e,...t]:[...e.slice(0,-1),`${e.at(-1)}${t[0]}`,...t.slice(1)],xo=e=>{let t=typeof e;if(t==="string")return e;if(t==="number")return String(e);if(x(e)&&("stdout"in e||"isMaxBuffer"in e))return If(e);throw e instanceof Eo.ChildProcess||Object.prototype.toString.call(e)==="[object Promise]"?new TypeError("Unexpected subprocess in template expression. Please use ${await subprocess} instead of ${subprocess}."):new TypeError(`Unexpected "${t}" in template expression`)},If=({stdout:e})=>{if(typeof e=="string")return e;if(A(e))return tt(e);throw e===void 0?new TypeError(`Missing result.stdout in template expression. This is probably due to the previous subprocess' "stdout" option.`):new TypeError(`Unexpected "${typeof e}" stdout in template expression`)};var Fc=require("node:child_process");var Io=require("node:util");var rt=T(require("node:process"),1),B=e=>nt.includes(e),nt=[rt.default.stdin,rt.default.stdout,rt.default.stderr],M=["stdin","stdout","stderr"],ot=e=>M[e]??`stdio[${e}]`;var Do=e=>{let t={...e};for(let r of Cr)t[r]=Dr(e,r);return t},Dr=(e,t)=>{let r=Array.from({length:Df(e)+1}),n=Rf(e[t],r,t);return Lf(n,t)},Df=({stdio:e})=>Array.isArray(e)?Math.max(e.length,M.length):M.length,Rf=(e,t,r)=>x(e)?Cf(e,t,r):t.fill(e),Cf=(e,t,r)=>{for(let n of Object.keys(e).sort(Mf))for(let o of Pf(n,r,t))t[o]=e[n];return t},Mf=(e,t)=>Oo(e)<Oo(t)?1:-1,Oo=e=>e==="stdout"||e==="stderr"?0:e==="all"?2:1,Pf=(e,t,r)=>{if(e==="ipc")return[r.length-1];let n=Rr(e);if(n===void 0||n===0)throw new TypeError(`"${t}.${e}" is invalid.
It must be "${t}.stdout", "${t}.stderr", "${t}.all", "${t}.ipc", or "${t}.fd3", "${t}.fd4" (and so on).`);if(n>=r.length)throw new TypeError(`"${t}.${e}" is invalid: that file descriptor does not exist.
Please set the "stdio" option to ensure that file descriptor exists.`);return n==="all"?[1,2]:[n]},Rr=e=>{if(e==="all")return e;if(M.includes(e))return M.indexOf(e);let t=Bf.exec(e);if(t!==null)return Number(t[1])},Bf=/^fd(\d+)$/,Lf=(e,t)=>e.map(r=>r===void 0?$f[t]:r),Ff=(0,Io.debuglog)("execa").enabled?"full":"none",$f={lines:!1,buffer:!0,maxBuffer:1e3*1e3*100,verbose:Ff,stripFinalNewline:!0},Cr=["lines","buffer","maxBuffer","verbose","stripFinalNewline"],V=(e,t)=>t==="ipc"?e.at(-1):e[t];var ue=({verbose:e},t)=>Mr(e,t)!=="none",me=({verbose:e},t)=>!["none","short"].includes(Mr(e,t)),Ro=({verbose:e},t)=>{let r=Mr(e,t);return it(r)?r:void 0},Mr=(e,t)=>t===void 0?vf(e):V(e,t),vf=e=>e.find(t=>it(t))??st.findLast(t=>e.includes(t)),it=e=>typeof e=="function",st=["none","short","full"];var zo=require("node:util");var Co=require("node:process"),Mo=require("node:util"),Po=(e,t)=>{let r=[e,...t],n=r.join(" "),o=r.map(i=>Gf(Bo(i))).join(" ");return{command:n,escapedCommand:o}},Le=e=>(0,Mo.stripVTControlCharacters)(e).split(`
`).map(t=>Bo(t)).join(`
`),Bo=e=>e.replaceAll(_f,t=>Uf(t)),Uf=e=>{let t=jf[e];if(t!==void 0)return t;let r=e.codePointAt(0),n=r.toString(16);return r<=kf?`\\u${n.padStart(4,"0")}`:`\\U${n}`},Nf=()=>{try{return new RegExp("\\p{Separator}|\\p{Other}","gu")}catch{return/[\s\u0000-\u001F\u007F-\u009F\u00AD]/g}},_f=Nf(),jf={" ":" ","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t"},kf=65535,Gf=e=>zf.test(e)?e:Co.platform==="win32"?`"${e.replaceAll('"','""')}"`:`'${e.replaceAll("'","'\\''")}'`,zf=/^[\w./-]+$/;var Lo={circleQuestionMark:"(?)",questionMarkPrefix:"(?)",square:"\u2588",squareDarkShade:"\u2593",squareMediumShade:"\u2592",squareLightShade:"\u2591",squareTop:"\u2580",squareBottom:"\u2584",squareLeft:"\u258C",squareRight:"\u2590",squareCenter:"\u25A0",bullet:"\u25CF",dot:"\u2024",ellipsis:"\u2026",pointerSmall:"\u203A",triangleUp:"\u25B2",triangleUpSmall:"\u25B4",triangleDown:"\u25BC",triangleDownSmall:"\u25BE",triangleLeftSmall:"\u25C2",triangleRightSmall:"\u25B8",home:"\u2302",heart:"\u2665",musicNote:"\u266A",musicNoteBeamed:"\u266B",arrowUp:"\u2191",arrowDown:"\u2193",arrowLeft:"\u2190",arrowRight:"\u2192",arrowLeftRight:"\u2194",arrowUpDown:"\u2195",almostEqual:"\u2248",notEqual:"\u2260",lessOrEqual:"\u2264",greaterOrEqual:"\u2265",identical:"\u2261",infinity:"\u221E",subscriptZero:"\u2080",subscriptOne:"\u2081",subscriptTwo:"\u2082",subscriptThree:"\u2083",subscriptFour:"\u2084",subscriptFive:"\u2085",subscriptSix:"\u2086",subscriptSeven:"\u2087",subscriptEight:"\u2088",subscriptNine:"\u2089",oneHalf:"\xBD",oneThird:"\u2153",oneQuarter:"\xBC",oneFifth:"\u2155",oneSixth:"\u2159",oneEighth:"\u215B",twoThirds:"\u2154",twoFifths:"\u2156",threeQuarters:"\xBE",threeFifths:"\u2157",threeEighths:"\u215C",fourFifths:"\u2158",fiveSixths:"\u215A",fiveEighths:"\u215D",sevenEighths:"\u215E",line:"\u2500",lineBold:"\u2501",lineDouble:"\u2550",lineDashed0:"\u2504",lineDashed1:"\u2505",lineDashed2:"\u2508",lineDashed3:"\u2509",lineDashed4:"\u254C",lineDashed5:"\u254D",lineDashed6:"\u2574",lineDashed7:"\u2576",lineDashed8:"\u2578",lineDashed9:"\u257A",lineDashed10:"\u257C",lineDashed11:"\u257E",lineDashed12:"\u2212",lineDashed13:"\u2013",lineDashed14:"\u2010",lineDashed15:"\u2043",lineVertical:"\u2502",lineVerticalBold:"\u2503",lineVerticalDouble:"\u2551",lineVerticalDashed0:"\u2506",lineVerticalDashed1:"\u2507",lineVerticalDashed2:"\u250A",lineVerticalDashed3:"\u250B",lineVerticalDashed4:"\u254E",lineVerticalDashed5:"\u254F",lineVerticalDashed6:"\u2575",lineVerticalDashed7:"\u2577",lineVerticalDashed8:"\u2579",lineVerticalDashed9:"\u257B",lineVerticalDashed10:"\u257D",lineVerticalDashed11:"\u257F",lineDownLeft:"\u2510",lineDownLeftArc:"\u256E",lineDownBoldLeftBold:"\u2513",lineDownBoldLeft:"\u2512",lineDownLeftBold:"\u2511",lineDownDoubleLeftDouble:"\u2557",lineDownDoubleLeft:"\u2556",lineDownLeftDouble:"\u2555",lineDownRight:"\u250C",lineDownRightArc:"\u256D",lineDownBoldRightBold:"\u250F",lineDownBoldRight:"\u250E",lineDownRightBold:"\u250D",lineDownDoubleRightDouble:"\u2554",lineDownDoubleRight:"\u2553",lineDownRightDouble:"\u2552",lineUpLeft:"\u2518",lineUpLeftArc:"\u256F",lineUpBoldLeftBold:"\u251B",lineUpBoldLeft:"\u251A",lineUpLeftBold:"\u2519",lineUpDoubleLeftDouble:"\u255D",lineUpDoubleLeft:"\u255C",lineUpLeftDouble:"\u255B",lineUpRight:"\u2514",lineUpRightArc:"\u2570",lineUpBoldRightBold:"\u2517",lineUpBoldRight:"\u2516",lineUpRightBold:"\u2515",lineUpDoubleRightDouble:"\u255A",lineUpDoubleRight:"\u2559",lineUpRightDouble:"\u2558",lineUpDownLeft:"\u2524",lineUpBoldDownBoldLeftBold:"\u252B",lineUpBoldDownBoldLeft:"\u2528",lineUpDownLeftBold:"\u2525",lineUpBoldDownLeftBold:"\u2529",lineUpDownBoldLeftBold:"\u252A",lineUpDownBoldLeft:"\u2527",lineUpBoldDownLeft:"\u2526",lineUpDoubleDownDoubleLeftDouble:"\u2563",lineUpDoubleDownDoubleLeft:"\u2562",lineUpDownLeftDouble:"\u2561",lineUpDownRight:"\u251C",lineUpBoldDownBoldRightBold:"\u2523",lineUpBoldDownBoldRight:"\u2520",lineUpDownRightBold:"\u251D",lineUpBoldDownRightBold:"\u2521",lineUpDownBoldRightBold:"\u2522",lineUpDownBoldRight:"\u251F",lineUpBoldDownRight:"\u251E",lineUpDoubleDownDoubleRightDouble:"\u2560",lineUpDoubleDownDoubleRight:"\u255F",lineUpDownRightDouble:"\u255E",lineDownLeftRight:"\u252C",lineDownBoldLeftBoldRightBold:"\u2533",lineDownLeftBoldRightBold:"\u252F",lineDownBoldLeftRight:"\u2530",lineDownBoldLeftBoldRight:"\u2531",lineDownBoldLeftRightBold:"\u2532",lineDownLeftRightBold:"\u252E",lineDownLeftBoldRight:"\u252D",lineDownDoubleLeftDoubleRightDouble:"\u2566",lineDownDoubleLeftRight:"\u2565",lineDownLeftDoubleRightDouble:"\u2564",lineUpLeftRight:"\u2534",lineUpBoldLeftBoldRightBold:"\u253B",lineUpLeftBoldRightBold:"\u2537",lineUpBoldLeftRight:"\u2538",lineUpBoldLeftBoldRight:"\u2539",lineUpBoldLeftRightBold:"\u253A",lineUpLeftRightBold:"\u2536",lineUpLeftBoldRight:"\u2535",lineUpDoubleLeftDoubleRightDouble:"\u2569",lineUpDoubleLeftRight:"\u2568",lineUpLeftDoubleRightDouble:"\u2567",lineUpDownLeftRight:"\u253C",lineUpBoldDownBoldLeftBoldRightBold:"\u254B",lineUpDownBoldLeftBoldRightBold:"\u2548",lineUpBoldDownLeftBoldRightBold:"\u2547",lineUpBoldDownBoldLeftRightBold:"\u254A",lineUpBoldDownBoldLeftBoldRight:"\u2549",lineUpBoldDownLeftRight:"\u2540",lineUpDownBoldLeftRight:"\u2541",lineUpDownLeftBoldRight:"\u253D",lineUpDownLeftRightBold:"\u253E",lineUpBoldDownBoldLeftRight:"\u2542",lineUpDownLeftBoldRightBold:"\u253F",lineUpBoldDownLeftBoldRight:"\u2543",lineUpBoldDownLeftRightBold:"\u2544",lineUpDownBoldLeftBoldRight:"\u2545",lineUpDownBoldLeftRightBold:"\u2546",lineUpDoubleDownDoubleLeftDoubleRightDouble:"\u256C",lineUpDoubleDownDoubleLeftRight:"\u256B",lineUpDownLeftDoubleRightDouble:"\u256A",lineCross:"\u2573",lineBackslash:"\u2572",lineSlash:"\u2571"},Fo={tick:"\u2714",info:"\u2139",warning:"\u26A0",cross:"\u2718",squareSmall:"\u25FB",squareSmallFilled:"\u25FC",circle:"\u25EF",circleFilled:"\u25C9",circleDotted:"\u25CC",circleDouble:"\u25CE",circleCircle:"\u24DE",circleCross:"\u24E7",circlePipe:"\u24BE",radioOn:"\u25C9",radioOff:"\u25EF",checkboxOn:"\u2612",checkboxOff:"\u2610",checkboxCircleOn:"\u24E7",checkboxCircleOff:"\u24BE",pointer:"\u276F",triangleUpOutline:"\u25B3",triangleLeft:"\u25C0",triangleRight:"\u25B6",lozenge:"\u25C6",lozengeOutline:"\u25C7",hamburger:"\u2630",smiley:"\u32E1",mustache:"\u0DF4",star:"\u2605",play:"\u25B6",nodejs:"\u2B22",oneSeventh:"\u2150",oneNinth:"\u2151",oneTenth:"\u2152"},Wf={tick:"\u221A",info:"i",warning:"\u203C",cross:"\xD7",squareSmall:"\u25A1",squareSmallFilled:"\u25A0",circle:"( )",circleFilled:"(*)",circleDotted:"( )",circleDouble:"( )",circleCircle:"(\u25CB)",circleCross:"(\xD7)",circlePipe:"(\u2502)",radioOn:"(*)",radioOff:"( )",checkboxOn:"[\xD7]",checkboxOff:"[ ]",checkboxCircleOn:"(\xD7)",checkboxCircleOff:"( )",pointer:">",triangleUpOutline:"\u2206",triangleLeft:"\u25C4",triangleRight:"\u25BA",lozenge:"\u2666",lozengeOutline:"\u25CA",hamburger:"\u2261",smiley:"\u263A",mustache:"\u250C\u2500\u2510",star:"\u2736",play:"\u25BA",nodejs:"\u2666",oneSeventh:"1/7",oneNinth:"1/9",oneTenth:"1/10"},Vf={...Lo,...Fo},Yf={...Lo,...Wf},qf=Tr(),Hf=qf?Vf:Yf,at=Hf,sy=Object.entries(Fo);var $o=T(require("node:tty"),1),Kf=$o.default?.WriteStream?.prototype?.hasColors?.()??!1,y=(e,t)=>{if(!Kf)return o=>o;let r=`\x1B[${e}m`,n=`\x1B[${t}m`;return o=>{let i=o+"",s=i.indexOf(n);if(s===-1)return r+i+n;let a=r,c=0;for(;s!==-1;)a+=i.slice(c,s)+r,c=s+n.length,s=i.indexOf(n,c);return a+=i.slice(c)+n,a}},cy=y(0,0),vo=y(1,22),ly=y(2,22),fy=y(3,23),dy=y(4,24),py=y(53,55),uy=y(7,27),my=y(8,28),hy=y(9,29),gy=y(30,39),yy=y(31,39),Sy=y(32,39),by=y(33,39),wy=y(34,39),xy=y(35,39),Ey=y(36,39),Ty=y(37,39),ct=y(90,39),Ay=y(40,49),Oy=y(41,49),Iy=y(42,49),Dy=y(43,49),Ry=y(44,49),Cy=y(45,49),My=y(46,49),Py=y(47,49),By=y(100,49),Uo=y(91,39),Ly=y(92,39),No=y(93,39),Fy=y(94,39),$y=y(95,39),vy=y(96,39),Uy=y(97,39),Ny=y(101,49),_y=y(102,49),jy=y(103,49),ky=y(104,49),Gy=y(105,49),zy=y(106,49),Wy=y(107,49);var ko=({type:e,message:t,timestamp:r,piped:n,commandId:o,result:{failed:i=!1}={},options:{reject:s=!0}})=>{let a=Xf(r),c=Jf[e]({failed:i,reject:s,piped:n}),f=Zf[e]({reject:s});return`${ct(`[${a}]`)} ${ct(`[${o}]`)} ${f(c)} ${f(t)}`},Xf=e=>`${lt(e.getHours(),2)}:${lt(e.getMinutes(),2)}:${lt(e.getSeconds(),2)}.${lt(e.getMilliseconds(),3)}`,lt=(e,t)=>String(e).padStart(t,"0"),_o=({failed:e,reject:t})=>e?t?at.cross:at.warning:at.tick,Jf={command:({piped:e})=>e?"|":"$",output:()=>" ",ipc:()=>"*",error:_o,duration:_o},jo=e=>e,Zf={command:()=>vo,output:()=>jo,ipc:()=>jo,error:({reject:e})=>e?Uo:No,duration:()=>ct};var Go=(e,t,r)=>{let n=Ro(t,r);return e.map(({verboseLine:o,verboseObject:i})=>Qf(o,i,n)).filter(o=>o!==void 0).map(o=>ed(o)).join("")},Qf=(e,t,r)=>{if(r===void 0)return e;let n=r(e,t);if(typeof n=="string")return n},ed=e=>e.endsWith(`
`)?e:`${e}
`;var v=({type:e,verboseMessage:t,fdNumber:r,verboseInfo:n,result:o})=>{let i=td({type:e,result:o,verboseInfo:n}),s=rd(t,i),a=Go(s,n,r);a!==""&&console.warn(a.slice(0,-1))},td=({type:e,result:t,verboseInfo:{escapedCommand:r,commandId:n,rawOptions:{piped:o=!1,...i}}})=>({type:e,escapedCommand:r,commandId:`${n}`,timestamp:new Date,piped:o,result:t,options:i}),rd=(e,t)=>e.split(`
`).map(r=>nd({...t,message:r})),nd=e=>({verboseLine:ko(e),verboseObject:e}),ft=e=>{let t=typeof e=="string"?e:(0,zo.inspect)(e);return Le(t).replaceAll("	"," ".repeat(od))},od=2;var Wo=(e,t)=>{ue(t)&&v({type:"command",verboseMessage:e,verboseInfo:t})};var Vo=(e,t,r)=>{ad(e);let n=id(e);return{verbose:e,escapedCommand:t,commandId:n,rawOptions:r}},id=e=>ue({verbose:e})?sd++:void 0,sd=0n,ad=e=>{for(let t of e){if(t===!1)throw new TypeError(`The "verbose: false" option was renamed to "verbose: 'none'".`);if(t===!0)throw new TypeError(`The "verbose: true" option was renamed to "verbose: 'short'".`);if(!st.includes(t)&&!it(t)){let r=st.map(n=>`'${n}'`).join(", ");throw new TypeError(`The "verbose" option must not be ${t}. Allowed values are: ${r} or a function.`)}}};var Pr=require("node:process"),dt=()=>Pr.hrtime.bigint(),Br=e=>Number(Pr.hrtime.bigint()-e)/1e6;var pt=(e,t,r)=>{let n=dt(),{command:o,escapedCommand:i}=Po(e,t),s=Dr(r,"verbose"),a=Vo(s,i,{...r});return Wo(i,a),{command:o,escapedCommand:i,startTime:n,verboseInfo:a}};var ia=T(require("node:path"),1),rn=T(require("node:process"),1),sa=T(Li(),1);var Fe=T(require("node:process"),1),J=T(require("node:path"),1);function mt(e={}){let{env:t=process.env,platform:r=process.platform}=e;return r!=="win32"?"PATH":Object.keys(t).reverse().find(n=>n.toUpperCase()==="PATH")||"Path"}var Fi=require("node:util"),zr=require("node:child_process"),Gr=T(require("node:path"),1),$i=require("node:url"),RS=(0,Fi.promisify)(zr.execFile);function ht(e){return e instanceof URL?(0,$i.fileURLToPath)(e):e}function vi(e){return{*[Symbol.iterator](){let t=Gr.default.resolve(ht(e)),r;for(;r!==t;)yield t,r=t,t=Gr.default.resolve(t,"..")}}}var CS=10*1024*1024;var Fd=({cwd:e=Fe.default.cwd(),path:t=Fe.default.env[mt()],preferLocal:r=!0,execPath:n=Fe.default.execPath,addExecPath:o=!0}={})=>{let i=J.default.resolve(ht(e)),s=[],a=t.split(J.default.delimiter);return r&&$d(s,a,i),o&&vd(s,a,n,i),t===""||t===J.default.delimiter?`${s.join(J.default.delimiter)}${t}`:[...s,t].join(J.default.delimiter)},$d=(e,t,r)=>{for(let n of vi(r)){let o=J.default.join(n,"node_modules/.bin");t.includes(o)||e.push(o)}},vd=(e,t,r,n)=>{let o=J.default.resolve(n,ht(r),"..");t.includes(o)||e.push(o)},Ui=({env:e=Fe.default.env,...t}={})=>{e={...e};let r=mt({env:e});return t.path=e[r],e[r]=Fd(t),e};var Qi=require("node:timers/promises");var Ni=(e,t,r)=>{let n=r?ve:$e,o=e instanceof L?{}:{cause:e};return new n(t,o)},L=class extends Error{},_i=(e,t)=>{Object.defineProperty(e.prototype,"name",{value:t,writable:!0,enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,ki,{value:!0,writable:!1,enumerable:!1,configurable:!1})},ji=e=>gt(e)&&ki in e,ki=Symbol("isExecaError"),gt=e=>Object.prototype.toString.call(e)==="[object Error]",$e=class extends Error{};_i($e,$e.name);var ve=class extends Error{};_i(ve,ve.name);var ye=require("node:os");var qi=require("node:os");var Gi=()=>{let e=Wi-zi+1;return Array.from({length:e},Ud)},Ud=(e,t)=>({name:`SIGRT${t+1}`,number:zi+t,action:"terminate",description:"Application-specific signal (realtime)",standard:"posix"}),zi=34,Wi=64;var Yi=require("node:os");var Vi=[{name:"SIGHUP",number:1,action:"terminate",description:"Terminal closed",standard:"posix"},{name:"SIGINT",number:2,action:"terminate",description:"User interruption with CTRL-C",standard:"ansi"},{name:"SIGQUIT",number:3,action:"core",description:"User interruption with CTRL-\\",standard:"posix"},{name:"SIGILL",number:4,action:"core",description:"Invalid machine instruction",standard:"ansi"},{name:"SIGTRAP",number:5,action:"core",description:"Debugger breakpoint",standard:"posix"},{name:"SIGABRT",number:6,action:"core",description:"Aborted",standard:"ansi"},{name:"SIGIOT",number:6,action:"core",description:"Aborted",standard:"bsd"},{name:"SIGBUS",number:7,action:"core",description:"Bus error due to misaligned, non-existing address or paging error",standard:"bsd"},{name:"SIGEMT",number:7,action:"terminate",description:"Command should be emulated but is not implemented",standard:"other"},{name:"SIGFPE",number:8,action:"core",description:"Floating point arithmetic error",standard:"ansi"},{name:"SIGKILL",number:9,action:"terminate",description:"Forced termination",standard:"posix",forced:!0},{name:"SIGUSR1",number:10,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGSEGV",number:11,action:"core",description:"Segmentation fault",standard:"ansi"},{name:"SIGUSR2",number:12,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGPIPE",number:13,action:"terminate",description:"Broken pipe or socket",standard:"posix"},{name:"SIGALRM",number:14,action:"terminate",description:"Timeout or timer",standard:"posix"},{name:"SIGTERM",number:15,action:"terminate",description:"Termination",standard:"ansi"},{name:"SIGSTKFLT",number:16,action:"terminate",description:"Stack is empty or overflowed",standard:"other"},{name:"SIGCHLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"posix"},{name:"SIGCLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"other"},{name:"SIGCONT",number:18,action:"unpause",description:"Unpaused",standard:"posix",forced:!0},{name:"SIGSTOP",number:19,action:"pause",description:"Paused",standard:"posix",forced:!0},{name:"SIGTSTP",number:20,action:"pause",description:'Paused using CTRL-Z or "suspend"',standard:"posix"},{name:"SIGTTIN",number:21,action:"pause",description:"Background process cannot read terminal input",standard:"posix"},{name:"SIGBREAK",number:21,action:"terminate",description:"User interruption with CTRL-BREAK",standard:"other"},{name:"SIGTTOU",number:22,action:"pause",description:"Background process cannot write to terminal output",standard:"posix"},{name:"SIGURG",number:23,action:"ignore",description:"Socket received out-of-band data",standard:"bsd"},{name:"SIGXCPU",number:24,action:"core",description:"Process timed out",standard:"bsd"},{name:"SIGXFSZ",number:25,action:"core",description:"File too big",standard:"bsd"},{name:"SIGVTALRM",number:26,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGPROF",number:27,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGWINCH",number:28,action:"ignore",description:"Terminal window size changed",standard:"bsd"},{name:"SIGIO",number:29,action:"terminate",description:"I/O is available",standard:"other"},{name:"SIGPOLL",number:29,action:"terminate",description:"Watched event",standard:"other"},{name:"SIGINFO",number:29,action:"ignore",description:"Request for process information",standard:"other"},{name:"SIGPWR",number:30,action:"terminate",description:"Device running out of power",standard:"systemv"},{name:"SIGSYS",number:31,action:"core",description:"Invalid system call",standard:"other"},{name:"SIGUNUSED",number:31,action:"terminate",description:"Invalid system call",standard:"other"}];var Wr=()=>{let e=Gi();return[...Vi,...e].map(Nd)},Nd=({name:e,number:t,description:r,action:n,forced:o=!1,standard:i})=>{let{signals:{[e]:s}}=Yi.constants,a=s!==void 0;return{name:e,number:a?s:t,description:r,supported:a,action:n,forced:o,standard:i}};var _d=()=>{let e=Wr();return Object.fromEntries(e.map(jd))},jd=({name:e,number:t,description:r,supported:n,action:o,forced:i,standard:s})=>[e,{name:e,number:t,description:r,supported:n,action:o,forced:i,standard:s}],Hi=_d(),kd=()=>{let e=Wr(),t=65,r=Array.from({length:t},(n,o)=>Gd(o,e));return Object.assign({},...r)},Gd=(e,t)=>{let r=zd(e,t);if(r===void 0)return{};let{name:n,description:o,supported:i,action:s,forced:a,standard:c}=r;return{[e]:{name:n,number:e,description:o,supported:i,action:s,forced:a,standard:c}}},zd=(e,t)=>{let r=t.find(({name:n})=>qi.constants.signals[n]===e);return r!==void 0?r:t.find(n=>n.number===e)},GS=kd();var Xi=e=>{let t="option `killSignal`";if(e===0)throw new TypeError(`Invalid ${t}: 0 cannot be used.`);return Zi(e,t)},Ji=e=>e===0?e:Zi(e,"`subprocess.kill()`'s argument"),Zi=(e,t)=>{if(Number.isInteger(e))return Wd(e,t);if(typeof e=="string")return Yd(e,t);throw new TypeError(`Invalid ${t} ${String(e)}: it must be a string or an integer.
${Vr()}`)},Wd=(e,t)=>{if(Ki.has(e))return Ki.get(e);throw new TypeError(`Invalid ${t} ${e}: this signal integer does not exist.
${Vr()}`)},Vd=()=>new Map(Object.entries(ye.constants.signals).reverse().map(([e,t])=>[t,e])),Ki=Vd(),Yd=(e,t)=>{if(e in ye.constants.signals)return e;throw e.toUpperCase()in ye.constants.signals?new TypeError(`Invalid ${t} '${e}': please rename it to '${e.toUpperCase()}'.`):new TypeError(`Invalid ${t} '${e}': this signal name does not exist.
${Vr()}`)},Vr=()=>`Available signal names: ${qd()}.
Available signal numbers: ${Hd()}.`,qd=()=>Object.keys(ye.constants.signals).sort().map(e=>`'${e}'`).join(", "),Hd=()=>[...new Set(Object.values(ye.constants.signals).sort((e,t)=>e-t))].join(", "),yt=e=>Hi[e].description;var es=e=>{if(e===!1)return e;if(e===!0)return Kd;if(!Number.isFinite(e)||e<0)throw new TypeError(`Expected the \`forceKillAfterDelay\` option to be a non-negative integer, got \`${e}\` (${typeof e})`);return e},Kd=1e3*5,ts=({kill:e,options:{forceKillAfterDelay:t,killSignal:r},onInternalError:n,context:o,controller:i},s,a)=>{let{signal:c,error:f}=Xd(s,a,r);Jd(f,n);let l=e(c);return Zd({kill:e,signal:c,forceKillAfterDelay:t,killSignal:r,killResult:l,context:o,controller:i}),l},Xd=(e,t,r)=>{let[n=r,o]=gt(e)?[void 0,e]:[e,t];if(typeof n!="string"&&!Number.isInteger(n))throw new TypeError(`The first argument must be an error instance or a signal name string/integer: ${String(n)}`);if(o!==void 0&&!gt(o))throw new TypeError(`The second argument is optional. If specified, it must be an error instance: ${o}`);return{signal:Ji(n),error:o}},Jd=(e,t)=>{e!==void 0&&t.reject(e)},Zd=async({kill:e,signal:t,forceKillAfterDelay:r,killSignal:n,killResult:o,context:i,controller:s})=>{t===n&&o&&Yr({kill:e,forceKillAfterDelay:r,context:i,controllerSignal:s.signal})},Yr=async({kill:e,forceKillAfterDelay:t,context:r,controllerSignal:n})=>{if(t!==!1)try{await(0,Qi.setTimeout)(t,void 0,{signal:n}),e("SIGKILL")&&(r.isForcefullyTerminated??=!0)}catch{}};var rs=require("node:events"),St=async(e,t)=>{e.aborted||await(0,rs.once)(e,"abort",{signal:t})};var ns=({cancelSignal:e})=>{if(e!==void 0&&Object.prototype.toString.call(e)!=="[object AbortSignal]")throw new Error(`The \`cancelSignal\` option must be an AbortSignal: ${String(e)}`)},os=({subprocess:e,cancelSignal:t,gracefulCancel:r,context:n,controller:o})=>t===void 0||r?[]:[Qd(e,t,n,o)],Qd=async(e,t,r,{signal:n})=>{throw await St(t,n),r.terminationReason??="cancel",e.kill(),t.reason};var Us=require("node:timers/promises");var $s=require("node:util");var Se=({methodName:e,isSubprocess:t,ipc:r,isConnected:n})=>{ep(e,t,r),qr(e,t,n)},ep=(e,t,r)=>{if(!r)throw new Error(`${F(e,t)} can only be used if the \`ipc\` option is \`true\`.`)},qr=(e,t,r)=>{if(!r)throw new Error(`${F(e,t)} cannot be used: the ${Z(t)} has already exited or disconnected.`)},is=e=>{throw new Error(`${F("getOneMessage",e)} could not complete: the ${Z(e)} exited or disconnected.`)},ss=e=>{throw new Error(`${F("sendMessage",e)} failed: the ${Z(e)} is sending a message too, instead of listening to incoming messages.
This can be fixed by both sending a message and listening to incoming messages at the same time:

const [receivedMessage] = await Promise.all([
	${F("getOneMessage",e)},
	${F("sendMessage",e,"message, {strict: true}")},
]);`)},bt=(e,t)=>new Error(`${F("sendMessage",t)} failed when sending an acknowledgment response to the ${Z(t)}.`,{cause:e}),as=e=>{throw new Error(`${F("sendMessage",e)} failed: the ${Z(e)} is not listening to incoming messages.`)},cs=e=>{throw new Error(`${F("sendMessage",e)} failed: the ${Z(e)} exited without listening to incoming messages.`)},ls=()=>new Error(`\`cancelSignal\` aborted: the ${Z(!0)} disconnected.`),fs=()=>{throw new Error("`getCancelSignal()` cannot be used without setting the `cancelSignal` subprocess option.")},ds=({error:e,methodName:t,isSubprocess:r})=>{if(e.code==="EPIPE")throw new Error(`${F(t,r)} cannot be used: the ${Z(r)} is disconnecting.`,{cause:e})},ps=({error:e,methodName:t,isSubprocess:r,message:n})=>{if(tp(e))throw new Error(`${F(t,r)}'s argument type is invalid: the message cannot be serialized: ${String(n)}.`,{cause:e})},tp=({code:e,message:t})=>rp.has(e)||np.some(r=>t.includes(r)),rp=new Set(["ERR_MISSING_ARGS","ERR_INVALID_ARG_TYPE"]),np=["could not be cloned","circular structure","call stack size exceeded"],F=(e,t,r="")=>e==="cancelSignal"?"`cancelSignal`'s `controller.abort()`":`${op(t)}${e}(${r})`,op=e=>e?"":"subprocess.",Z=e=>e?"parent process":"subprocess",be=e=>{e.connected&&e.disconnect()};var U=()=>{let e={},t=new Promise((r,n)=>{Object.assign(e,{resolve:r,reject:n})});return Object.assign(t,e)};var xt=(e,t="stdin")=>{let{options:n,fileDescriptors:o}=N.get(e),i=us(o,t,!0),s=e.stdio[i];if(s===null)throw new TypeError(ms(i,t,n,!0));return s},we=(e,t="stdout")=>{let{options:n,fileDescriptors:o}=N.get(e),i=us(o,t,!1),s=i==="all"?e.all:e.stdio[i];if(s==null)throw new TypeError(ms(i,t,n,!1));return s},N=new WeakMap,us=(e,t,r)=>{let n=ip(t,r);return sp(n,t,r,e),n},ip=(e,t)=>{let r=Rr(e);if(r!==void 0)return r;let{validOptions:n,defaultValue:o}=t?{validOptions:'"stdin"',defaultValue:"stdin"}:{validOptions:'"stdout", "stderr", "all"',defaultValue:"stdout"};throw new TypeError(`"${Ue(t)}" must not be "${e}".
It must be ${n} or "fd3", "fd4" (and so on).
It is optional and defaults to "${o}".`)},sp=(e,t,r,n)=>{let o=n[hs(e)];if(o===void 0)throw new TypeError(`"${Ue(r)}" must not be ${t}. That file descriptor does not exist.
Please set the "stdio" option to ensure that file descriptor exists.`);if(o.direction==="input"&&!r)throw new TypeError(`"${Ue(r)}" must not be ${t}. It must be a readable stream, not writable.`);if(o.direction!=="input"&&r)throw new TypeError(`"${Ue(r)}" must not be ${t}. It must be a writable stream, not readable.`)},ms=(e,t,r,n)=>{if(e==="all"&&!r.all)return`The "all" option must be true to use "from: 'all'".`;let{optionName:o,optionValue:i}=ap(e,r);return`The "${o}: ${wt(i)}" option is incompatible with using "${Ue(n)}: ${wt(t)}".
Please set this option with "pipe" instead.`},ap=(e,{stdin:t,stdout:r,stderr:n,stdio:o})=>{let i=hs(e);return i===0&&t!==void 0?{optionName:"stdin",optionValue:t}:i===1&&r!==void 0?{optionName:"stdout",optionValue:r}:i===2&&n!==void 0?{optionName:"stderr",optionValue:n}:{optionName:`stdio[${i}]`,optionValue:o[i]}},hs=e=>e==="all"?1:e,Ue=e=>e?"to":"from",wt=e=>typeof e=="string"?`'${e}'`:typeof e=="number"?`${e}`:"Stream";var Rs=require("node:events");var gs=require("node:events"),se=(e,t,r)=>{let n=e.getMaxListeners();n===0||n===Number.POSITIVE_INFINITY||(e.setMaxListeners(n+t),(0,gs.addAbortListener)(r,()=>{e.setMaxListeners(e.getMaxListeners()-t)}))};var Ds=require("node:events");var bs=require("node:events"),ws=require("node:timers/promises");var Et=(e,t)=>{t&&Hr(e)},Hr=e=>{e.refCounted()},Tt=(e,t)=>{t&&Kr(e)},Kr=e=>{e.unrefCounted()},ys=(e,t)=>{t&&(Kr(e),Kr(e))},Ss=(e,t)=>{t&&(Hr(e),Hr(e))};var xs=async({anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n},o)=>{if(As(o)||Is(o))return;At.has(e)||At.set(e,[]);let i=At.get(e);if(i.push(o),!(i.length>1))for(;i.length>0;){await Os(e,n,o),await ws.scheduler.yield();let s=await Ts({wrappedMessage:i[0],anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n});i.shift(),n.emit("message",s),n.emit("message:done")}},Es=async({anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n,boundOnMessage:o})=>{Xr();let i=At.get(e);for(;i?.length>0;)await(0,bs.once)(n,"message:done");e.removeListener("message",o),Ss(t,r),n.connected=!1,n.emit("disconnect")},At=new WeakMap;var Q=(e,t,r)=>{if(Ot.has(e))return Ot.get(e);let n=new Ds.EventEmitter;return n.connected=!0,Ot.set(e,n),cp({ipcEmitter:n,anyProcess:e,channel:t,isSubprocess:r}),n},Ot=new WeakMap,cp=({ipcEmitter:e,anyProcess:t,channel:r,isSubprocess:n})=>{let o=xs.bind(void 0,{anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:e});t.on("message",o),t.once("disconnect",Es.bind(void 0,{anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:e,boundOnMessage:o})),ys(r,n)},It=e=>{let t=Ot.get(e);return t===void 0?e.channel!==null:t.connected};var Cs=({anyProcess:e,channel:t,isSubprocess:r,message:n,strict:o})=>{if(!o)return n;let i=Q(e,t,r),s=Ct(e,i);return{id:lp++,type:Rt,message:n,hasListeners:s}},lp=0n,Ms=(e,t)=>{if(!(t?.type!==Rt||t.hasListeners))for(let{id:r}of e)r!==void 0&&Dt[r].resolve({isDeadlock:!0,hasListeners:!1})},Ts=async({wrappedMessage:e,anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:o})=>{if(e?.type!==Rt||!t.connected)return e;let{id:i,message:s}=e,a={id:i,type:Bs,message:Ct(t,o)};try{await Mt({anyProcess:t,channel:r,isSubprocess:n,ipc:!0},a)}catch(c){o.emit("strict:error",c)}return s},As=e=>{if(e?.type!==Bs)return!1;let{id:t,message:r}=e;return Dt[t]?.resolve({isDeadlock:!1,hasListeners:r}),!0},Ps=async(e,t,r)=>{if(e?.type!==Rt)return;let n=U();Dt[e.id]=n;let o=new AbortController;try{let{isDeadlock:i,hasListeners:s}=await Promise.race([n,fp(t,r,o)]);i&&ss(r),s||as(r)}finally{o.abort(),delete Dt[e.id]}},Dt={},fp=async(e,t,{signal:r})=>{se(e,1,r),await(0,Rs.once)(e,"disconnect",{signal:r}),cs(t)},Rt="execa:ipc:request",Bs="execa:ipc:response";var Ls=(e,t,r)=>{Ne.has(e)||Ne.set(e,new Set);let n=Ne.get(e),o=U(),i=r?t.id:void 0,s={onMessageSent:o,id:i};return n.add(s),{outgoingMessages:n,outgoingMessage:s}},Fs=({outgoingMessages:e,outgoingMessage:t})=>{e.delete(t),t.onMessageSent.resolve()},Os=async(e,t,r)=>{for(;!Ct(e,t)&&Ne.get(e)?.size>0;){let n=[...Ne.get(e)];Ms(n,r),await Promise.all(n.map(({onMessageSent:o})=>o))}},Ne=new WeakMap,Ct=(e,t)=>t.listenerCount("message")>dp(e),dp=e=>N.has(e)&&!V(N.get(e).options.buffer,"ipc")?1:0;var Mt=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},o,{strict:i=!1}={})=>{let s="sendMessage";return Se({methodName:s,isSubprocess:r,ipc:n,isConnected:e.connected}),pp({anyProcess:e,channel:t,methodName:s,isSubprocess:r,message:o,strict:i})},pp=async({anyProcess:e,channel:t,methodName:r,isSubprocess:n,message:o,strict:i})=>{let s=Cs({anyProcess:e,channel:t,isSubprocess:n,message:o,strict:i}),a=Ls(e,s,i);try{await Zr({anyProcess:e,methodName:r,isSubprocess:n,wrappedMessage:s,message:o})}catch(c){throw be(e),c}finally{Fs(a)}},Zr=async({anyProcess:e,methodName:t,isSubprocess:r,wrappedMessage:n,message:o})=>{let i=up(e);try{await Promise.all([Ps(n,e,r),i(n)])}catch(s){throw ds({error:s,methodName:t,isSubprocess:r}),ps({error:s,methodName:t,isSubprocess:r,message:o}),s}},up=e=>{if(Jr.has(e))return Jr.get(e);let t=(0,$s.promisify)(e.send.bind(e));return Jr.set(e,t),t},Jr=new WeakMap;var Ns=(e,t)=>{let r="cancelSignal";return qr(r,!1,e.connected),Zr({anyProcess:e,methodName:r,isSubprocess:!1,wrappedMessage:{type:js,message:t},message:t})},_s=async({anyProcess:e,channel:t,isSubprocess:r,ipc:n})=>(await mp({anyProcess:e,channel:t,isSubprocess:r,ipc:n}),Qr.signal),mp=async({anyProcess:e,channel:t,isSubprocess:r,ipc:n})=>{if(!vs){if(vs=!0,!n){fs();return}if(t===null){Xr();return}Q(e,t,r),await Us.scheduler.yield()}},vs=!1,Is=e=>e?.type!==js?!1:(Qr.abort(e.message),!0),js="execa:ipc:cancel",Xr=()=>{Qr.abort(ls())},Qr=new AbortController;var ks=({gracefulCancel:e,cancelSignal:t,ipc:r,serialization:n})=>{if(e){if(t===void 0)throw new Error("The `cancelSignal` option must be defined when setting the `gracefulCancel` option.");if(!r)throw new Error("The `ipc` option cannot be false when setting the `gracefulCancel` option.");if(n==="json")throw new Error("The `serialization` option cannot be 'json' when setting the `gracefulCancel` option.")}},Gs=({subprocess:e,cancelSignal:t,gracefulCancel:r,forceKillAfterDelay:n,context:o,controller:i})=>r?[hp({subprocess:e,cancelSignal:t,forceKillAfterDelay:n,context:o,controller:i})]:[],hp=async({subprocess:e,cancelSignal:t,forceKillAfterDelay:r,context:n,controller:{signal:o}})=>{await St(t,o);let i=gp(t);throw await Ns(e,i),Yr({kill:e.kill,forceKillAfterDelay:r,context:n,controllerSignal:o}),n.terminationReason??="gracefulCancel",t.reason},gp=({reason:e})=>{if(!(e instanceof DOMException))return e;let t=new Error(e.message);return Object.defineProperty(t,"stack",{value:e.stack,enumerable:!1,configurable:!0,writable:!0}),t};var zs=require("node:timers/promises");var Ws=({timeout:e})=>{if(e!==void 0&&(!Number.isFinite(e)||e<0))throw new TypeError(`Expected the \`timeout\` option to be a non-negative integer, got \`${e}\` (${typeof e})`)},Vs=(e,t,r,n)=>t===0||t===void 0?[]:[yp(e,t,r,n)],yp=async(e,t,r,{signal:n})=>{throw await(0,zs.setTimeout)(t,void 0,{signal:n}),r.terminationReason??="timeout",e.kill(),new L};var Pt=require("node:process"),en=T(require("node:path"),1);var Ys=({options:e})=>{if(e.node===!1)throw new TypeError('The "node" option cannot be false with `execaNode()`.');return{options:{...e,node:!0}}},qs=(e,t,{node:r=!1,nodePath:n=Pt.execPath,nodeOptions:o=Pt.execArgv.filter(c=>!c.startsWith("--inspect")),cwd:i,execPath:s,...a})=>{if(s!==void 0)throw new TypeError('The "execPath" option has been removed. Please use the "nodePath" option instead.');let c=pe(n,'The "nodePath" option'),f=en.default.resolve(i,c),l={...a,nodePath:f,node:r,cwd:i};if(!r)return[e,t,l];if(en.default.basename(e,".exe")==="node")throw new TypeError('When the "node" option is true, the first argument does not need to be "node".');return[f,[...o,e,...t],{ipc:!0,...l,shell:!1}]};var Hs=require("node:v8"),Ks=({ipcInput:e,ipc:t,serialization:r})=>{if(e!==void 0){if(!t)throw new Error("The `ipcInput` option cannot be set unless the `ipc` option is `true`.");wp[r](e)}},Sp=e=>{try{(0,Hs.serialize)(e)}catch(t){throw new Error("The `ipcInput` option is not serializable with a structured clone.",{cause:t})}},bp=e=>{try{JSON.stringify(e)}catch(t){throw new Error("The `ipcInput` option is not serializable with JSON.",{cause:t})}},wp={advanced:Sp,json:bp},Xs=async(e,t)=>{t!==void 0&&await e.sendMessage(t)};var Zs=({encoding:e})=>{if(tn.has(e))return;let t=Ep(e);if(t!==void 0)throw new TypeError(`Invalid option \`encoding: ${Bt(e)}\`.
Please rename it to ${Bt(t)}.`);let r=[...tn].map(n=>Bt(n)).join(", ");throw new TypeError(`Invalid option \`encoding: ${Bt(e)}\`.
Please rename it to one of: ${r}.`)},xp=new Set(["utf8","utf16le"]),R=new Set(["buffer","hex","base64","base64url","latin1","ascii"]),tn=new Set([...xp,...R]),Ep=e=>{if(e===null)return"buffer";if(typeof e!="string")return;let t=e.toLowerCase();if(t in Js)return Js[t];if(tn.has(t))return t},Js={"utf-8":"utf8","utf-16le":"utf16le","ucs-2":"utf16le",ucs2:"utf16le",binary:"latin1"},Bt=e=>typeof e=="string"?`"${e}"`:String(e);var Qs=require("node:fs"),ea=T(require("node:path"),1),ta=T(require("node:process"),1);var ra=(e=na())=>{let t=pe(e,'The "cwd" option');return ea.default.resolve(t)},na=()=>{try{return ta.default.cwd()}catch(e){throw e.message=`The current directory does not exist.
${e.message}`,e}},oa=(e,t)=>{if(t===na())return e;let r;try{r=(0,Qs.statSync)(t)}catch(n){return`The "cwd" option is invalid: ${t}.
${n.message}
${e}`}return r.isDirectory()?e:`The "cwd" option is not a directory: ${t}.
${e}`};var Lt=(e,t,r)=>{r.cwd=ra(r.cwd);let[n,o,i]=qs(e,t,r),{command:s,args:a,options:c}=sa.default._parse(n,o,i),f=Do(c),l=Tp(f);return Ws(l),Zs(l),Ks(l),ns(l),ks(l),l.shell=Or(l.shell),l.env=Ap(l),l.killSignal=Xi(l.killSignal),l.forceKillAfterDelay=es(l.forceKillAfterDelay),l.lines=l.lines.map((d,p)=>d&&!R.has(l.encoding)&&l.buffer[p]),rn.default.platform==="win32"&&ia.default.basename(s,".exe")==="cmd"&&a.unshift("/q"),{file:s,commandArguments:a,options:l}},Tp=({extendEnv:e=!0,preferLocal:t=!1,cwd:r,localDir:n=r,encoding:o="utf8",reject:i=!0,cleanup:s=!0,all:a=!1,windowsHide:c=!0,killSignal:f="SIGTERM",forceKillAfterDelay:l=!0,gracefulCancel:d=!1,ipcInput:p,ipc:u=p!==void 0||d,serialization:m="advanced",...S})=>({...S,extendEnv:e,preferLocal:t,cwd:r,localDirectory:n,encoding:o,reject:i,cleanup:s,all:a,windowsHide:c,killSignal:f,forceKillAfterDelay:l,gracefulCancel:d,ipcInput:p,ipc:u,serialization:m}),Ap=({env:e,extendEnv:t,preferLocal:r,node:n,localDirectory:o,nodePath:i})=>{let s=t?{...rn.default.env,...e}:e;return r||n?Ui({env:s,cwd:o,execPath:i,preferLocal:r,addExecPath:n}):s};var Ft=(e,t,r)=>r.shell&&t.length>0?[[e,...t].join(" "),[],r]:[e,t,r];var Ra=require("node:util");function xe(e){if(typeof e=="string")return Op(e);if(!(ArrayBuffer.isView(e)&&e.BYTES_PER_ELEMENT===1))throw new Error("Input must be a string or a Uint8Array");return Ip(e)}var Op=e=>e.at(-1)===aa?e.slice(0,e.at(-2)===ca?-2:-1):e,Ip=e=>e.at(-1)===Dp?e.subarray(0,e.at(-2)===Rp?-2:-1):e,aa=`
`,Dp=aa.codePointAt(0),ca="\r",Rp=ca.codePointAt(0);var wa=require("node:events"),xa=require("node:stream/promises");function $(e,{checkOpen:t=!0}={}){return e!==null&&typeof e=="object"&&(e.writable||e.readable||!t||e.writable===void 0&&e.readable===void 0)&&typeof e.pipe=="function"}function nn(e,{checkOpen:t=!0}={}){return $(e,{checkOpen:t})&&(e.writable||!t)&&typeof e.write=="function"&&typeof e.end=="function"&&typeof e.writable=="boolean"&&typeof e.writableObjectMode=="boolean"&&typeof e.destroy=="function"&&typeof e.destroyed=="boolean"}function ae(e,{checkOpen:t=!0}={}){return $(e,{checkOpen:t})&&(e.readable||!t)&&typeof e.read=="function"&&typeof e.readable=="boolean"&&typeof e.readableObjectMode=="boolean"&&typeof e.destroy=="function"&&typeof e.destroyed=="boolean"}function on(e,t){return nn(e,t)&&ae(e,t)}var Cp=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype),sn=class{#t;#r;#e=!1;#n=void 0;constructor(t,r){this.#t=t,this.#r=r}next(){let t=()=>this.#i();return this.#n=this.#n?this.#n.then(t,t):t(),this.#n}return(t){let r=()=>this.#o(t);return this.#n?this.#n.then(r,r):r()}async#i(){if(this.#e)return{done:!0,value:void 0};let t;try{t=await this.#t.read()}catch(r){throw this.#n=void 0,this.#e=!0,this.#t.releaseLock(),r}return t.done&&(this.#n=void 0,this.#e=!0,this.#t.releaseLock()),t}async#o(t){if(this.#e)return{done:!0,value:t};if(this.#e=!0,!this.#r){let r=this.#t.cancel(t);return this.#t.releaseLock(),await r,{done:!0,value:t}}return this.#t.releaseLock(),{done:!0,value:t}}},an=Symbol();function la(){return this[an].next()}Object.defineProperty(la,"name",{value:"next"});function fa(e){return this[an].return(e)}Object.defineProperty(fa,"name",{value:"return"});var Mp=Object.create(Cp,{next:{enumerable:!0,configurable:!0,writable:!0,value:la},return:{enumerable:!0,configurable:!0,writable:!0,value:fa}});function cn({preventCancel:e=!1}={}){let t=this.getReader(),r=new sn(t,e),n=Object.create(Mp);return n[an]=r,n}var da=e=>{if(ae(e,{checkOpen:!1})&&_e.on!==void 0)return Bp(e);if(typeof e?.[Symbol.asyncIterator]=="function")return e;if(Pp.call(e)==="[object ReadableStream]")return cn.call(e);throw new TypeError("The first argument must be a Readable, a ReadableStream, or an async iterable.")},{toString:Pp}=Object.prototype,Bp=async function*(e){let t=new AbortController,r={};Lp(e,t,r);try{for await(let[n]of _e.on(e,"data",{signal:t.signal}))yield n}catch(n){if(r.error!==void 0)throw r.error;if(!t.signal.aborted)throw n}finally{e.destroy()}},Lp=async(e,t,r)=>{try{await _e.finished(e,{cleanup:!0,readable:!0,writable:!1,error:!1})}catch(n){r.error=n}finally{t.abort()}},_e={};var Ee=async(e,{init:t,convertChunk:r,getSize:n,truncateChunk:o,addChunk:i,getFinalChunk:s,finalize:a},{maxBuffer:c=Number.POSITIVE_INFINITY}={})=>{let f=da(e),l=t();l.length=0;try{for await(let d of f){let p=$p(d),u=r[p](d,l);ma({convertedChunk:u,state:l,getSize:n,truncateChunk:o,addChunk:i,maxBuffer:c})}return Fp({state:l,convertChunk:r,getSize:n,truncateChunk:o,addChunk:i,getFinalChunk:s,maxBuffer:c}),a(l)}catch(d){let p=typeof d=="object"&&d!==null?d:new Error(d);throw p.bufferedData=a(l),p}},Fp=({state:e,getSize:t,truncateChunk:r,addChunk:n,getFinalChunk:o,maxBuffer:i})=>{let s=o(e);s!==void 0&&ma({convertedChunk:s,state:e,getSize:t,truncateChunk:r,addChunk:n,maxBuffer:i})},ma=({convertedChunk:e,state:t,getSize:r,truncateChunk:n,addChunk:o,maxBuffer:i})=>{let s=r(e),a=t.length+s;if(a<=i){pa(e,t,o,a);return}let c=n(e,i-t.length);throw c!==void 0&&pa(c,t,o,i),new _},pa=(e,t,r,n)=>{t.contents=r(e,t,n),t.length=n},$p=e=>{let t=typeof e;if(t==="string")return"string";if(t!=="object"||e===null)return"others";if(globalThis.Buffer?.isBuffer(e))return"buffer";let r=ua.call(e);return r==="[object ArrayBuffer]"?"arrayBuffer":r==="[object DataView]"?"dataView":Number.isInteger(e.byteLength)&&Number.isInteger(e.byteOffset)&&ua.call(e.buffer)==="[object ArrayBuffer]"?"typedArray":"others"},{toString:ua}=Object.prototype,_=class extends Error{name="MaxBufferError";constructor(){super("maxBuffer exceeded")}};var Y=e=>e,je=()=>{},$t=({contents:e})=>e,vt=e=>{throw new Error(`Streams in object mode are not supported: ${String(e)}`)},Ut=e=>e.length;async function Nt(e,t){return Ee(e,_p,t)}var vp=()=>({contents:[]}),Up=()=>1,Np=(e,{contents:t})=>(t.push(e),t),_p={init:vp,convertChunk:{string:Y,buffer:Y,arrayBuffer:Y,dataView:Y,typedArray:Y,others:Y},getSize:Up,truncateChunk:je,addChunk:Np,getFinalChunk:je,finalize:$t};async function _t(e,t){return Ee(e,Hp,t)}var jp=()=>({contents:new ArrayBuffer(0)}),kp=e=>Gp.encode(e),Gp=new TextEncoder,ha=e=>new Uint8Array(e),ga=e=>new Uint8Array(e.buffer,e.byteOffset,e.byteLength),zp=(e,t)=>e.slice(0,t),Wp=(e,{contents:t,length:r},n)=>{let o=ba()?Yp(t,n):Vp(t,n);return new Uint8Array(o).set(e,r),o},Vp=(e,t)=>{if(t<=e.byteLength)return e;let r=new ArrayBuffer(Sa(t));return new Uint8Array(r).set(new Uint8Array(e),0),r},Yp=(e,t)=>{if(t<=e.maxByteLength)return e.resize(t),e;let r=new ArrayBuffer(t,{maxByteLength:Sa(t)});return new Uint8Array(r).set(new Uint8Array(e),0),r},Sa=e=>ya**Math.ceil(Math.log(e)/Math.log(ya)),ya=2,qp=({contents:e,length:t})=>ba()?e:e.slice(0,t),ba=()=>"resize"in ArrayBuffer.prototype,Hp={init:jp,convertChunk:{string:kp,buffer:ha,arrayBuffer:ha,dataView:ga,typedArray:ga,others:vt},getSize:Ut,truncateChunk:zp,addChunk:Wp,getFinalChunk:je,finalize:qp};async function kt(e,t){return Ee(e,Qp,t)}var Kp=()=>({contents:"",textDecoder:new TextDecoder}),jt=(e,{textDecoder:t})=>t.decode(e,{stream:!0}),Xp=(e,{contents:t})=>t+e,Jp=(e,t)=>e.slice(0,t),Zp=({textDecoder:e})=>{let t=e.decode();return t===""?void 0:t},Qp={init:Kp,convertChunk:{string:Y,buffer:jt,arrayBuffer:jt,dataView:jt,typedArray:jt,others:vt},getSize:Ut,truncateChunk:Jp,addChunk:Xp,getFinalChunk:Zp,finalize:$t};Object.assign(_e,{on:wa.on,finished:xa.finished});var Ea=({error:e,stream:t,readableObjectMode:r,lines:n,encoding:o,fdNumber:i})=>{if(!(e instanceof _))throw e;if(i==="all")return e;let s=eu(r,n,o);throw e.maxBufferInfo={fdNumber:i,unit:s},t.destroy(),e},eu=(e,t,r)=>e?"objects":t?"lines":r==="buffer"?"bytes":"characters",Ta=(e,t,r)=>{if(t.length!==r)return;let n=new _;throw n.maxBufferInfo={fdNumber:"ipc"},n},Aa=(e,t)=>{let{streamName:r,threshold:n,unit:o}=tu(e,t);return`Command's ${r} was larger than ${n} ${o}`},tu=(e,t)=>{if(e?.maxBufferInfo===void 0)return{streamName:"output",threshold:t[1],unit:"bytes"};let{maxBufferInfo:{fdNumber:r,unit:n}}=e;delete e.maxBufferInfo;let o=V(t,r);return r==="ipc"?{streamName:"IPC output",threshold:o,unit:"messages"}:{streamName:ot(r),threshold:o,unit:n}},Oa=(e,t,r)=>e?.code==="ENOBUFS"&&t!==null&&t.some(n=>n!==null&&n.length>Gt(r)),Ia=(e,t,r)=>{if(!t)return e;let n=Gt(r);return e.length>n?e.slice(0,n):e},Gt=([,e])=>e;var Ca=({stdio:e,all:t,ipcOutput:r,originalError:n,signal:o,signalDescription:i,exitCode:s,escapedCommand:a,timedOut:c,isCanceled:f,isGracefullyCanceled:l,isMaxBuffer:d,isForcefullyTerminated:p,forceKillAfterDelay:u,killSignal:m,maxBuffer:S,timeout:E,cwd:b})=>{let O=n?.code,I=ru({originalError:n,timedOut:c,timeout:E,isMaxBuffer:d,maxBuffer:S,errorCode:O,signal:o,signalDescription:i,exitCode:s,isCanceled:f,isGracefullyCanceled:l,isForcefullyTerminated:p,forceKillAfterDelay:u,killSignal:m}),C=ou(n,b),G=C===void 0?"":`
${C}`,H=`${I}: ${a}${G}`,ne=t===void 0?[e[2],e[1]]:[t],de=[H,...ne,...e.slice(3),r.map(K=>iu(K)).join(`
`)].map(K=>Le(xe(su(K)))).filter(Boolean).join(`

`);return{originalMessage:C,shortMessage:H,message:de}},ru=({originalError:e,timedOut:t,timeout:r,isMaxBuffer:n,maxBuffer:o,errorCode:i,signal:s,signalDescription:a,exitCode:c,isCanceled:f,isGracefullyCanceled:l,isForcefullyTerminated:d,forceKillAfterDelay:p,killSignal:u})=>{let m=nu(d,p);return t?`Command timed out after ${r} milliseconds${m}`:l?s===void 0?`Command was gracefully canceled with exit code ${c}`:d?`Command was gracefully canceled${m}`:`Command was gracefully canceled with ${s} (${a})`:f?`Command was canceled${m}`:n?`${Aa(e,o)}${m}`:i!==void 0?`Command failed with ${i}${m}`:d?`Command was killed with ${u} (${yt(u)})${m}`:s!==void 0?`Command was killed with ${s} (${a})`:c!==void 0?`Command failed with exit code ${c}`:"Command failed"},nu=(e,t)=>e?` and was forcefully terminated after ${t} milliseconds`:"",ou=(e,t)=>{if(e instanceof L)return;let r=ji(e)?e.originalMessage:String(e?.message??e),n=Le(oa(r,t));return n===""?void 0:n},iu=e=>typeof e=="string"?e:(0,Ra.inspect)(e),su=e=>Array.isArray(e)?e.map(t=>xe(Da(t))).filter(Boolean).join(`
`):Da(e),Da=e=>typeof e=="string"?e:A(e)?tt(e):"";var zt=({command:e,escapedCommand:t,stdio:r,all:n,ipcOutput:o,options:{cwd:i},startTime:s})=>Ma({command:e,escapedCommand:t,cwd:i,durationMs:Br(s),failed:!1,timedOut:!1,isCanceled:!1,isGracefullyCanceled:!1,isTerminated:!1,isMaxBuffer:!1,isForcefullyTerminated:!1,exitCode:0,stdout:r[1],stderr:r[2],all:n,stdio:r,ipcOutput:o,pipedFrom:[]}),Te=({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,isSync:s})=>ke({error:e,command:t,escapedCommand:r,startTime:i,timedOut:!1,isCanceled:!1,isGracefullyCanceled:!1,isMaxBuffer:!1,isForcefullyTerminated:!1,stdio:Array.from({length:n.length}),ipcOutput:[],options:o,isSync:s}),ke=({error:e,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:a,isForcefullyTerminated:c,exitCode:f,signal:l,stdio:d,all:p,ipcOutput:u,options:{timeoutDuration:m,timeout:S=m,forceKillAfterDelay:E,killSignal:b,cwd:O,maxBuffer:I},isSync:C})=>{let{exitCode:G,signal:H,signalDescription:ne}=cu(f,l),{originalMessage:de,shortMessage:K,message:ur}=Ca({stdio:d,all:p,ipcOutput:u,originalError:e,signal:H,signalDescription:ne,exitCode:G,escapedCommand:r,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:a,isForcefullyTerminated:c,forceKillAfterDelay:E,killSignal:b,maxBuffer:I,timeout:S,cwd:O}),Me=Ni(e,ur,C);return Object.assign(Me,au({error:Me,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:a,isForcefullyTerminated:c,exitCode:G,signal:H,signalDescription:ne,stdio:d,all:p,ipcOutput:u,cwd:O,originalMessage:de,shortMessage:K})),Me},au=({error:e,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:a,isForcefullyTerminated:c,exitCode:f,signal:l,signalDescription:d,stdio:p,all:u,ipcOutput:m,cwd:S,originalMessage:E,shortMessage:b})=>Ma({shortMessage:b,originalMessage:E,command:t,escapedCommand:r,cwd:S,durationMs:Br(n),failed:!0,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isTerminated:l!==void 0,isMaxBuffer:a,isForcefullyTerminated:c,exitCode:f,signal:l,signalDescription:d,code:e.cause?.code,stdout:p[1],stderr:p[2],all:u,stdio:p,ipcOutput:m,pipedFrom:[]}),Ma=e=>Object.fromEntries(Object.entries(e).filter(([,t])=>t!==void 0)),cu=(e,t)=>{let r=e===null?void 0:e,n=t===null?void 0:t,o=n===void 0?void 0:yt(t);return{exitCode:r,signal:n,signalDescription:o}};var Pa=e=>Number.isFinite(e)?e:0;function lu(e){return{days:Math.trunc(e/864e5),hours:Math.trunc(e/36e5%24),minutes:Math.trunc(e/6e4%60),seconds:Math.trunc(e/1e3%60),milliseconds:Math.trunc(e%1e3),microseconds:Math.trunc(Pa(e*1e3)%1e3),nanoseconds:Math.trunc(Pa(e*1e6)%1e3)}}function fu(e){return{days:e/86400000n,hours:e/3600000n%24n,minutes:e/60000n%60n,seconds:e/1000n%60n,milliseconds:e%1000n,microseconds:0n,nanoseconds:0n}}function ln(e){switch(typeof e){case"number":{if(Number.isFinite(e))return lu(e);break}case"bigint":return fu(e)}throw new TypeError("Expected a finite number or bigint")}var du=e=>e===0||e===0n,pu=(e,t)=>t===1||t===1n?e:`${e}s`,uu=1e-7,mu=24n*60n*60n*1000n;function fn(e,t){let r=typeof e=="bigint";if(!r&&!Number.isFinite(e))throw new TypeError("Expected a finite number or bigint");t={...t};let n=e<0?"-":"";e=e<0?-e:e,t.colonNotation&&(t.compact=!1,t.formatSubMilliseconds=!1,t.separateMilliseconds=!1,t.verbose=!1),t.compact&&(t.unitCount=1,t.secondsDecimalDigits=0,t.millisecondsDecimalDigits=0);let o=[],i=(l,d)=>{let p=Math.floor(l*10**d+uu);return(Math.round(p)/10**d).toFixed(d)},s=(l,d,p,u)=>{if(!((o.length===0||!t.colonNotation)&&du(l)&&!(t.colonNotation&&p==="m"))){if(u??=String(l),t.colonNotation){let m=u.includes(".")?u.split(".")[0].length:u.length,S=o.length>0?2:1;u="0".repeat(Math.max(0,S-m))+u}else u+=t.verbose?" "+pu(d,l):p;o.push(u)}},a=ln(e),c=BigInt(a.days);if(t.hideYearAndDays?s(BigInt(c)*24n+BigInt(a.hours),"hour","h"):(t.hideYear?s(c,"day","d"):(s(c/365n,"year","y"),s(c%365n,"day","d")),s(Number(a.hours),"hour","h")),s(Number(a.minutes),"minute","m"),!t.hideSeconds)if(t.separateMilliseconds||t.formatSubMilliseconds||!t.colonNotation&&e<1e3){let l=Number(a.seconds),d=Number(a.milliseconds),p=Number(a.microseconds),u=Number(a.nanoseconds);if(s(l,"second","s"),t.formatSubMilliseconds)s(d,"millisecond","ms"),s(p,"microsecond","\xB5s"),s(u,"nanosecond","ns");else{let m=d+p/1e3+u/1e6,S=typeof t.millisecondsDecimalDigits=="number"?t.millisecondsDecimalDigits:0,E=m>=1?Math.round(m):Math.ceil(m),b=S?m.toFixed(S):E;s(Number.parseFloat(b),"millisecond","ms",b)}}else{let l=(r?Number(e%mu):e)/1e3%60,d=typeof t.secondsDecimalDigits=="number"?t.secondsDecimalDigits:1,p=i(l,d),u=t.keepDecimalsOnWholeSeconds?p:p.replace(/\.0+$/,"");s(Number.parseFloat(u),"second","s",u)}if(o.length===0)return n+"0"+(t.verbose?" milliseconds":"ms");let f=t.colonNotation?":":" ";return typeof t.unitCount=="number"&&(o=o.slice(0,Math.max(t.unitCount,1))),n+o.join(f)}var Ba=(e,t)=>{e.failed&&v({type:"error",verboseMessage:e.shortMessage,verboseInfo:t,result:e})};var La=(e,t)=>{ue(t)&&(Ba(e,t),hu(e,t))},hu=(e,t)=>{let r=`(done in ${fn(e.durationMs)})`;v({type:"duration",verboseMessage:r,verboseInfo:t,result:e})};var Ae=(e,t,{reject:r})=>{if(La(e,t),e.failed&&r)throw e;return e};var Sn=require("node:fs");var va=(e,t)=>ce(e)?"asyncGenerator":_a(e)?"generator":Wt(e)?"fileUrl":wu(e)?"filePath":Tu(e)?"webStream":$(e,{checkOpen:!1})?"native":A(e)?"uint8Array":Au(e)?"asyncIterable":Ou(e)?"iterable":un(e)?Ua({transform:e},t):bu(e)?gu(e,t):"native",gu=(e,t)=>on(e.transform,{checkOpen:!1})?yu(e,t):un(e.transform)?Ua(e,t):Su(e,t),yu=(e,t)=>(Na(e,t,"Duplex stream"),"duplex"),Ua=(e,t)=>(Na(e,t,"web TransformStream"),"webTransform"),Na=({final:e,binary:t,objectMode:r},n,o)=>{Fa(e,`${n}.final`,o),Fa(t,`${n}.binary`,o),dn(r,`${n}.objectMode`)},Fa=(e,t,r)=>{if(e!==void 0)throw new TypeError(`The \`${t}\` option can only be defined when using a generator, not a ${r}.`)},Su=({transform:e,final:t,binary:r,objectMode:n},o)=>{if(e!==void 0&&!$a(e))throw new TypeError(`The \`${o}.transform\` option must be a generator, a Duplex stream or a web TransformStream.`);if(on(t,{checkOpen:!1}))throw new TypeError(`The \`${o}.final\` option must not be a Duplex stream.`);if(un(t))throw new TypeError(`The \`${o}.final\` option must not be a web TransformStream.`);if(t!==void 0&&!$a(t))throw new TypeError(`The \`${o}.final\` option must be a generator.`);return dn(r,`${o}.binary`),dn(n,`${o}.objectMode`),ce(e)||ce(t)?"asyncGenerator":"generator"},dn=(e,t)=>{if(e!==void 0&&typeof e!="boolean")throw new TypeError(`The \`${t}\` option must use a boolean.`)},$a=e=>ce(e)||_a(e),ce=e=>Object.prototype.toString.call(e)==="[object AsyncGeneratorFunction]",_a=e=>Object.prototype.toString.call(e)==="[object GeneratorFunction]",bu=e=>x(e)&&(e.transform!==void 0||e.final!==void 0),Wt=e=>Object.prototype.toString.call(e)==="[object URL]",ja=e=>Wt(e)&&e.protocol!=="file:",wu=e=>x(e)&&Object.keys(e).length>0&&Object.keys(e).every(t=>xu.has(t))&&pn(e.file),xu=new Set(["file","append"]),pn=e=>typeof e=="string",ka=(e,t)=>e==="native"&&typeof t=="string"&&!Eu.has(t),Eu=new Set(["ipc","ignore","inherit","overlapped","pipe"]),Ga=e=>Object.prototype.toString.call(e)==="[object ReadableStream]",Vt=e=>Object.prototype.toString.call(e)==="[object WritableStream]",Tu=e=>Ga(e)||Vt(e),un=e=>Ga(e?.readable)&&Vt(e?.writable),Au=e=>za(e)&&typeof e[Symbol.asyncIterator]=="function",Ou=e=>za(e)&&typeof e[Symbol.iterator]=="function",za=e=>typeof e=="object"&&e!==null,P=new Set(["generator","asyncGenerator","duplex","webTransform"]),Yt=new Set(["fileUrl","filePath","fileNumber"]),mn=new Set(["fileUrl","filePath"]),Wa=new Set([...mn,"webStream","nodeStream"]),Va=new Set(["webTransform","duplex"]),ee={generator:"a generator",asyncGenerator:"an async generator",fileUrl:"a file URL",filePath:"a file path string",fileNumber:"a file descriptor number",webStream:"a web stream",nodeStream:"a Node.js stream",webTransform:"a web TransformStream",duplex:"a Duplex stream",native:"any value",iterable:"an iterable",asyncIterable:"an async iterable",string:"a string",uint8Array:"a Uint8Array"};var hn=(e,t,r,n)=>n==="output"?Iu(e,t,r):Du(e,t,r),Iu=(e,t,r)=>{let n=t!==0&&r[t-1].value.readableObjectMode;return{writableObjectMode:n,readableObjectMode:e??n}},Du=(e,t,r)=>{let n=t===0?e===!0:r[t-1].value.readableObjectMode,o=t!==r.length-1&&(e??n);return{writableObjectMode:n,readableObjectMode:o}},Ya=(e,t)=>{let r=e.findLast(({type:n})=>P.has(n));return r===void 0?!1:t==="input"?r.value.writableObjectMode:r.value.readableObjectMode};var qa=(e,t,r,n)=>[...e.filter(({type:o})=>!P.has(o)),...Ru(e,t,r,n)],Ru=(e,t,r,{encoding:n})=>{let o=e.filter(({type:s})=>P.has(s)),i=Array.from({length:o.length});for(let[s,a]of Object.entries(o))i[s]=Cu({stdioItem:a,index:Number(s),newTransforms:i,optionName:t,direction:r,encoding:n});return Lu(i,r)},Cu=({stdioItem:e,stdioItem:{type:t},index:r,newTransforms:n,optionName:o,direction:i,encoding:s})=>t==="duplex"?Mu({stdioItem:e,optionName:o}):t==="webTransform"?Pu({stdioItem:e,index:r,newTransforms:n,direction:i}):Bu({stdioItem:e,index:r,newTransforms:n,direction:i,encoding:s}),Mu=({stdioItem:e,stdioItem:{value:{transform:t,transform:{writableObjectMode:r,readableObjectMode:n},objectMode:o=n}},optionName:i})=>{if(o&&!n)throw new TypeError(`The \`${i}.objectMode\` option can only be \`true\` if \`new Duplex({objectMode: true})\` is used.`);if(!o&&n)throw new TypeError(`The \`${i}.objectMode\` option cannot be \`false\` if \`new Duplex({objectMode: true})\` is used.`);return{...e,value:{transform:t,writableObjectMode:r,readableObjectMode:n}}},Pu=({stdioItem:e,stdioItem:{value:t},index:r,newTransforms:n,direction:o})=>{let{transform:i,objectMode:s}=x(t)?t:{transform:t},{writableObjectMode:a,readableObjectMode:c}=hn(s,r,n,o);return{...e,value:{transform:i,writableObjectMode:a,readableObjectMode:c}}},Bu=({stdioItem:e,stdioItem:{value:t},index:r,newTransforms:n,direction:o,encoding:i})=>{let{transform:s,final:a,binary:c=!1,preserveNewlines:f=!1,objectMode:l}=x(t)?t:{transform:t},d=c||R.has(i),{writableObjectMode:p,readableObjectMode:u}=hn(l,r,n,o);return{...e,value:{transform:s,final:a,binary:d,preserveNewlines:f,writableObjectMode:p,readableObjectMode:u}}},Lu=(e,t)=>t==="input"?e.reverse():e;var qt=T(require("node:process"),1);var Ha=(e,t,r)=>{let n=e.map(o=>Fu(o,t));if(n.includes("input")&&n.includes("output"))throw new TypeError(`The \`${r}\` option must not be an array of both readable and writable values.`);return n.find(Boolean)??Uu},Fu=({type:e,value:t},r)=>$u[r]??Ka[e](t),$u=["input","output","output"],Oe=()=>{},gn=()=>"input",Ka={generator:Oe,asyncGenerator:Oe,fileUrl:Oe,filePath:Oe,iterable:gn,asyncIterable:gn,uint8Array:gn,webStream:e=>Vt(e)?"output":"input",nodeStream(e){return ae(e,{checkOpen:!1})?nn(e,{checkOpen:!1})?void 0:"input":"output"},webTransform:Oe,duplex:Oe,native(e){let t=vu(e);if(t!==void 0)return t;if($(e,{checkOpen:!1}))return Ka.nodeStream(e)}},vu=e=>{if([0,qt.default.stdin].includes(e))return"input";if([1,2,qt.default.stdout,qt.default.stderr].includes(e))return"output"},Uu="output";var Xa=(e,t)=>t&&!e.includes("ipc")?[...e,"ipc"]:e;var Ja=({stdio:e,ipc:t,buffer:r,...n},o,i)=>{let s=Nu(e,n).map((a,c)=>Za(a,c));return i?ju(s,r,o):Xa(s,t)},Nu=(e,t)=>{if(e===void 0)return M.map(n=>t[n]);if(_u(t))throw new Error(`It's not possible to provide \`stdio\` in combination with one of ${M.map(n=>`\`${n}\``).join(", ")}`);if(typeof e=="string")return[e,e,e];if(!Array.isArray(e))throw new TypeError(`Expected \`stdio\` to be of type \`string\` or \`Array\`, got \`${typeof e}\``);let r=Math.max(e.length,M.length);return Array.from({length:r},(n,o)=>e[o])},_u=e=>M.some(t=>e[t]!==void 0),Za=(e,t)=>Array.isArray(e)?e.map(r=>Za(r,t)):e??(t>=M.length?"ignore":"pipe"),ju=(e,t,r)=>e.map((n,o)=>!t[o]&&o!==0&&!me(r,o)&&ku(n)?"ignore":n),ku=e=>e==="pipe"||Array.isArray(e)&&e.every(t=>t==="pipe");var ec=require("node:fs"),tc=T(require("node:tty"),1);var rc=({stdioItem:e,stdioItem:{type:t},isStdioArray:r,fdNumber:n,direction:o,isSync:i})=>!r||t!=="native"?e:i?Gu({stdioItem:e,fdNumber:n,direction:o}):Vu({stdioItem:e,fdNumber:n}),Gu=({stdioItem:e,stdioItem:{value:t,optionName:r},fdNumber:n,direction:o})=>{let i=zu({value:t,optionName:r,fdNumber:n,direction:o});if(i!==void 0)return i;if($(t,{checkOpen:!1}))throw new TypeError(`The \`${r}: Stream\` option cannot both be an array and include a stream with synchronous methods.`);return e},zu=({value:e,optionName:t,fdNumber:r,direction:n})=>{let o=Wu(e,r);if(o!==void 0){if(n==="output")return{type:"fileNumber",value:o,optionName:t};if(tc.default.isatty(o))throw new TypeError(`The \`${t}: ${wt(e)}\` option is invalid: it cannot be a TTY with synchronous methods.`);return{type:"uint8Array",value:W((0,ec.readFileSync)(o)),optionName:t}}},Wu=(e,t)=>{if(e==="inherit")return t;if(typeof e=="number")return e;let r=nt.indexOf(e);if(r!==-1)return r},Vu=({stdioItem:e,stdioItem:{value:t,optionName:r},fdNumber:n})=>t==="inherit"?{type:"nodeStream",value:Qa(n,t,r),optionName:r}:typeof t=="number"?{type:"nodeStream",value:Qa(t,t,r),optionName:r}:$(t,{checkOpen:!1})?{type:"nodeStream",value:t,optionName:r}:e,Qa=(e,t,r)=>{let n=nt[e];if(n===void 0)throw new TypeError(`The \`${r}: ${t}\` option is invalid: no such standard stream.`);return n};var nc=({input:e,inputFile:t},r)=>r===0?[...Yu(e),...Hu(t)]:[],Yu=e=>e===void 0?[]:[{type:qu(e),value:e,optionName:"input"}],qu=e=>{if(ae(e,{checkOpen:!1}))return"nodeStream";if(typeof e=="string")return"string";if(A(e))return"uint8Array";throw new Error("The `input` option must be a string, a Uint8Array or a Node.js Readable stream.")},Hu=e=>e===void 0?[]:[{...Ku(e),optionName:"inputFile"}],Ku=e=>{if(Wt(e))return{type:"fileUrl",value:e};if(pn(e))return{type:"filePath",value:{file:e}};throw new Error("The `inputFile` option must be a file path string or a file URL.")};var oc=e=>e.filter((t,r)=>e.every((n,o)=>t.value!==n.value||r>=o||t.type==="generator"||t.type==="asyncGenerator")),ic=({stdioItem:{type:e,value:t,optionName:r},direction:n,fileDescriptors:o,isSync:i})=>{let s=Xu(o,e);if(s.length!==0){if(i){Ju({otherStdioItems:s,type:e,value:t,optionName:r,direction:n});return}if(Wa.has(e))return sc({otherStdioItems:s,type:e,value:t,optionName:r,direction:n});Va.has(e)&&Qu({otherStdioItems:s,type:e,value:t,optionName:r})}},Xu=(e,t)=>e.flatMap(({direction:r,stdioItems:n})=>n.filter(o=>o.type===t).map(o=>({...o,direction:r}))),Ju=({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})=>{mn.has(t)&&sc({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})},sc=({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})=>{let i=e.filter(a=>Zu(a,r));if(i.length===0)return;let s=i.find(a=>a.direction!==o);return ac(s,n,t),o==="output"?i[0].stream:void 0},Zu=({type:e,value:t},r)=>e==="filePath"?t.file===r.file:e==="fileUrl"?t.href===r.href:t===r,Qu=({otherStdioItems:e,type:t,value:r,optionName:n})=>{let o=e.find(({value:{transform:i}})=>i===r.transform);ac(o,n,t)},ac=(e,t,r)=>{if(e!==void 0)throw new TypeError(`The \`${e.optionName}\` and \`${t}\` options must not target ${ee[r]} that is the same.`)};var Ht=(e,t,r,n)=>{let i=Ja(t,r,n).map((a,c)=>em({stdioOption:a,fdNumber:c,options:t,isSync:n})),s=cm({initialFileDescriptors:i,addProperties:e,options:t,isSync:n});return t.stdio=s.map(({stdioItems:a})=>dm(a)),s},em=({stdioOption:e,fdNumber:t,options:r,isSync:n})=>{let o=ot(t),{stdioItems:i,isStdioArray:s}=tm({stdioOption:e,fdNumber:t,options:r,optionName:o}),a=Ha(i,t,o),c=i.map(d=>rc({stdioItem:d,isStdioArray:s,fdNumber:t,direction:a,isSync:n})),f=qa(c,o,a,r),l=Ya(f,a);return am(f,l),{direction:a,objectMode:l,stdioItems:f}},tm=({stdioOption:e,fdNumber:t,options:r,optionName:n})=>{let i=[...(Array.isArray(e)?e:[e]).map(c=>rm(c,n)),...nc(r,t)],s=oc(i),a=s.length>1;return nm(s,a,n),im(s),{stdioItems:s,isStdioArray:a}},rm=(e,t)=>({type:va(e,t),value:e,optionName:t}),nm=(e,t,r)=>{if(e.length===0)throw new TypeError(`The \`${r}\` option must not be an empty array.`);if(t){for(let{value:n,optionName:o}of e)if(om.has(n))throw new Error(`The \`${o}\` option must not include \`${n}\`.`)}},om=new Set(["ignore","ipc"]),im=e=>{for(let t of e)sm(t)},sm=({type:e,value:t,optionName:r})=>{if(ja(t))throw new TypeError(`The \`${r}: URL\` option must use the \`file:\` scheme.
For example, you can use the \`pathToFileURL()\` method of the \`url\` core module.`);if(ka(e,t))throw new TypeError(`The \`${r}: { file: '...' }\` option must be used instead of \`${r}: '...'\`.`)},am=(e,t)=>{if(!t)return;let r=e.find(({type:n})=>Yt.has(n));if(r!==void 0)throw new TypeError(`The \`${r.optionName}\` option cannot use both files and transforms in objectMode.`)},cm=({initialFileDescriptors:e,addProperties:t,options:r,isSync:n})=>{let o=[];try{for(let i of e)o.push(lm({fileDescriptor:i,fileDescriptors:o,addProperties:t,options:r,isSync:n}));return o}catch(i){throw yn(o),i}},lm=({fileDescriptor:{direction:e,objectMode:t,stdioItems:r},fileDescriptors:n,addProperties:o,options:i,isSync:s})=>{let a=r.map(c=>fm({stdioItem:c,addProperties:o,direction:e,options:i,fileDescriptors:n,isSync:s}));return{direction:e,objectMode:t,stdioItems:a}},fm=({stdioItem:e,addProperties:t,direction:r,options:n,fileDescriptors:o,isSync:i})=>{let s=ic({stdioItem:e,direction:r,fileDescriptors:o,isSync:i});return s!==void 0?{...e,stream:s}:{...e,...t[r][e.type](e,n)}},yn=e=>{for(let{stdioItems:t}of e)for(let{stream:r}of t)r!==void 0&&!B(r)&&r.destroy()},dm=e=>{if(e.length>1)return e.some(({value:n})=>n==="overlapped")?"overlapped":"pipe";let[{type:t,value:r}]=e;return t==="native"?r:"pipe"};var lc=(e,t)=>Ht(um,e,t,!0),j=({type:e,optionName:t})=>{fc(t,ee[e])},pm=({optionName:e,value:t})=>((t==="ipc"||t==="overlapped")&&fc(e,`"${t}"`),{}),fc=(e,t)=>{throw new TypeError(`The \`${e}\` option cannot be ${t} with synchronous methods.`)},cc={generator(){},asyncGenerator:j,webStream:j,nodeStream:j,webTransform:j,duplex:j,asyncIterable:j,native:pm},um={input:{...cc,fileUrl:({value:e})=>({contents:[W((0,Sn.readFileSync)(e))]}),filePath:({value:{file:e}})=>({contents:[W((0,Sn.readFileSync)(e))]}),fileNumber:j,iterable:({value:e})=>({contents:[...e]}),string:({value:e})=>({contents:[e]}),uint8Array:({value:e})=>({contents:[e]})},output:{...cc,fileUrl:({value:e})=>({path:e}),filePath:({value:{file:e,append:t}})=>({path:e,append:t}),fileNumber:({value:e})=>({path:e}),iterable:j,string:j,uint8Array:j}};var q=(e,{stripFinalNewline:t},r)=>bn(t,r)&&e!==void 0&&!Array.isArray(e)?xe(e):e,bn=(e,t)=>t==="all"?e[1]||e[2]:e[t];var ze=require("node:stream");var Kt=(e,t,r,n)=>e||r?void 0:pc(t,n),xn=(e,t,r)=>r?e.flatMap(n=>dc(n,t)):dc(e,t),dc=(e,t)=>{let{transform:r,final:n}=pc(t,{});return[...r(e),...n()]},pc=(e,t)=>(t.previousChunks="",{transform:mm.bind(void 0,t,e),final:gm.bind(void 0,t)}),mm=function*(e,t,r){if(typeof r!="string"){yield r;return}let{previousChunks:n}=e,o=-1;for(let i=0;i<r.length;i+=1)if(r[i]===`
`){let s=hm(r,i,t,e),a=r.slice(o+1,i+1-s);n.length>0&&(a=wn(n,a),n=""),yield a,o=i}o!==r.length-1&&(n=wn(n,r.slice(o+1))),e.previousChunks=n},hm=(e,t,r,n)=>r?0:(n.isWindowsNewline=t!==0&&e[t-1]==="\r",n.isWindowsNewline?2:1),gm=function*({previousChunks:e}){e.length>0&&(yield e)},uc=({binary:e,preserveNewlines:t,readableObjectMode:r,state:n})=>e||t||r?void 0:{transform:ym.bind(void 0,n)},ym=function*({isWindowsNewline:e=!1},t){let{unixNewline:r,windowsNewline:n,LF:o,concatBytes:i}=typeof t=="string"?Sm:wm;if(t.at(-1)===o){yield t;return}yield i(t,e?n:r)},wn=(e,t)=>`${e}${t}`,Sm={windowsNewline:`\r
`,unixNewline:`
`,LF:`
`,concatBytes:wn},bm=(e,t)=>{let r=new Uint8Array(e.length+t.length);return r.set(e,0),r.set(t,e.length),r},wm={windowsNewline:new Uint8Array([13,10]),unixNewline:new Uint8Array([10]),LF:10,concatBytes:bm};var mc=require("node:buffer");var hc=(e,t)=>e?void 0:xm.bind(void 0,t),xm=function*(e,t){if(typeof t!="string"&&!A(t)&&!mc.Buffer.isBuffer(t))throw new TypeError(`The \`${e}\` option's transform must use "objectMode: true" to receive as input: ${typeof t}.`);yield t},gc=(e,t)=>e?Em.bind(void 0,t):Tm.bind(void 0,t),Em=function*(e,t){yc(e,t),yield t},Tm=function*(e,t){if(yc(e,t),typeof t!="string"&&!A(t))throw new TypeError(`The \`${e}\` option's function must yield a string or an Uint8Array, not ${typeof t}.`);yield t},yc=(e,t)=>{if(t==null)throw new TypeError(`The \`${e}\` option's function must not call \`yield ${t}\`.
Instead, \`yield\` should either be called with a value, or not be called at all. For example:
  if (condition) { yield value; }`)};var Sc=require("node:buffer"),bc=require("node:string_decoder");var Xt=(e,t,r)=>{if(r)return;if(e)return{transform:Am.bind(void 0,new TextEncoder)};let n=new bc.StringDecoder(t);return{transform:Om.bind(void 0,n),final:Im.bind(void 0,n)}},Am=function*(e,t){Sc.Buffer.isBuffer(t)?yield W(t):typeof t=="string"?yield e.encode(t):yield t},Om=function*(e,t){yield A(t)?e.write(t):t},Im=function*(e){let t=e.end();t!==""&&(yield t)};var En=require("node:util"),Tn=(0,En.callbackify)(async(e,t,r,n)=>{t.currentIterable=e(...r);try{for await(let o of t.currentIterable)n.push(o)}finally{delete t.currentIterable}}),Jt=async function*(e,t,r){if(r===t.length){yield e;return}let{transform:n=Rm}=t[r];for await(let o of n(e))yield*Jt(o,t,r+1)},wc=async function*(e){for(let[t,{final:r}]of Object.entries(e))yield*Dm(r,Number(t),e)},Dm=async function*(e,t,r){if(e!==void 0)for await(let n of e())yield*Jt(n,r,t+1)},xc=(0,En.callbackify)(async({currentIterable:e},t)=>{if(e!==void 0){await(t?e.throw(t):e.return());return}if(t)throw t}),Rm=function*(e){yield e};var An=(e,t,r,n)=>{try{for(let o of e(...t))r.push(o);n()}catch(o){n(o)}},Ec=(e,t)=>[...t.flatMap(r=>[...le(r,e,0)]),...Ge(e)],le=function*(e,t,r){if(r===t.length){yield e;return}let{transform:n=Mm}=t[r];for(let o of n(e))yield*le(o,t,r+1)},Ge=function*(e){for(let[t,{final:r}]of Object.entries(e))yield*Cm(r,Number(t),e)},Cm=function*(e,t,r){if(e!==void 0)for(let n of e())yield*le(n,r,t+1)},Mm=function*(e){yield e};var On=({value:e,value:{transform:t,final:r,writableObjectMode:n,readableObjectMode:o},optionName:i},{encoding:s})=>{let a={},c=Tc(e,s,i),f=ce(t),l=ce(r),d=f?Tn.bind(void 0,Jt,a):An.bind(void 0,le),p=f||l?Tn.bind(void 0,wc,a):An.bind(void 0,Ge),u=f||l?xc.bind(void 0,a):void 0;return{stream:new ze.Transform({writableObjectMode:n,writableHighWaterMark:(0,ze.getDefaultHighWaterMark)(n),readableObjectMode:o,readableHighWaterMark:(0,ze.getDefaultHighWaterMark)(o),transform(S,E,b){d([S,c,0],this,b)},flush(S){p([c],this,S)},destroy:u})}},Zt=(e,t,r,n)=>{let o=t.filter(({type:s})=>s==="generator"),i=n?o.reverse():o;for(let{value:s,optionName:a}of i){let c=Tc(s,r,a);e=Ec(c,e)}return e},Tc=({transform:e,final:t,binary:r,writableObjectMode:n,readableObjectMode:o,preserveNewlines:i},s,a)=>{let c={};return[{transform:hc(n,a)},Xt(r,s,n),Kt(r,i,n,c),{transform:e,final:t},{transform:gc(o,a)},uc({binary:r,preserveNewlines:i,readableObjectMode:o,state:c})].filter(Boolean)};var Ac=(e,t)=>{for(let r of Pm(e))Bm(e,r,t)},Pm=e=>new Set(Object.entries(e).filter(([,{direction:t}])=>t==="input").map(([t])=>Number(t))),Bm=(e,t,r)=>{let{stdioItems:n}=e[t],o=n.filter(({contents:a})=>a!==void 0);if(o.length===0)return;if(t!==0){let[{type:a,optionName:c}]=o;throw new TypeError(`Only the \`stdin\` option, not \`${c}\`, can be ${ee[a]} with synchronous methods.`)}let s=o.map(({contents:a})=>a).map(a=>Lm(a,n));r.input=Be(s)},Lm=(e,t)=>{let r=Zt(e,t,"utf8",!0);return Fm(r),Be(r)},Fm=e=>{let t=e.find(r=>typeof r!="string"&&!A(r));if(t!==void 0)throw new TypeError(`The \`stdin\` option is invalid: when passing objects as input, a transform must be used to serialize them to strings or Uint8Arrays: ${t}.`)};var er=require("node:fs");var Qt=({stdioItems:e,encoding:t,verboseInfo:r,fdNumber:n})=>n!=="all"&&me(r,n)&&!R.has(t)&&$m(n)&&(e.some(({type:o,value:i})=>o==="native"&&vm.has(i))||e.every(({type:o})=>P.has(o))),$m=e=>e===1||e===2,vm=new Set(["pipe","overlapped"]),Oc=async(e,t,r,n)=>{for await(let o of e)Um(t)||Dc(o,r,n)},Ic=(e,t,r)=>{for(let n of e)Dc(n,t,r)},Um=e=>e._readableState.pipes.length>0,Dc=(e,t,r)=>{let n=ft(e);v({type:"output",verboseMessage:n,fdNumber:t,verboseInfo:r})};var Rc=({fileDescriptors:e,syncResult:{output:t},options:r,isMaxBuffer:n,verboseInfo:o})=>{if(t===null)return{output:Array.from({length:3})};let i={},s=new Set([]);return{output:t.map((c,f)=>Nm({result:c,fileDescriptors:e,fdNumber:f,state:i,outputFiles:s,isMaxBuffer:n,verboseInfo:o},r)),...i}},Nm=({result:e,fileDescriptors:t,fdNumber:r,state:n,outputFiles:o,isMaxBuffer:i,verboseInfo:s},{buffer:a,encoding:c,lines:f,stripFinalNewline:l,maxBuffer:d})=>{if(e===null)return;let p=Ia(e,i,d),u=W(p),{stdioItems:m,objectMode:S}=t[r],E=_m([u],m,c,n),{serializedResult:b,finalResult:O=b}=jm({chunks:E,objectMode:S,encoding:c,lines:f,stripFinalNewline:l,fdNumber:r});km({serializedResult:b,fdNumber:r,state:n,verboseInfo:s,encoding:c,stdioItems:m,objectMode:S});let I=a[r]?O:void 0;try{return n.error===void 0&&Gm(b,m,o),I}catch(C){return n.error=C,I}},_m=(e,t,r,n)=>{try{return Zt(e,t,r,!1)}catch(o){return n.error=o,e}},jm=({chunks:e,objectMode:t,encoding:r,lines:n,stripFinalNewline:o,fdNumber:i})=>{if(t)return{serializedResult:e};if(r==="buffer")return{serializedResult:Be(e)};let s=So(e,r);return n[i]?{serializedResult:s,finalResult:xn(s,!o[i],t)}:{serializedResult:s}},km=({serializedResult:e,fdNumber:t,state:r,verboseInfo:n,encoding:o,stdioItems:i,objectMode:s})=>{if(!Qt({stdioItems:i,encoding:o,verboseInfo:n,fdNumber:t}))return;let a=xn(e,!1,s);try{Ic(a,t,n)}catch(c){r.error??=c}},Gm=(e,t,r)=>{for(let{path:n,append:o}of t.filter(({type:i})=>Yt.has(i))){let i=typeof n=="string"?n:n.toString();o||r.has(i)?(0,er.appendFileSync)(n,e):(r.add(i),(0,er.writeFileSync)(n,e))}};var Cc=([,e,t],r)=>{if(r.all)return e===void 0?t:t===void 0?e:Array.isArray(e)?Array.isArray(t)?[...e,...t]:[...e,q(t,r,"all")]:Array.isArray(t)?[q(e,r,"all"),...t]:A(e)&&A(t)?Ir([e,t]):`${e}${t}`};var tr=require("node:events");var Mc=async(e,t)=>{let[r,n]=await zm(e);return t.isForcefullyTerminated??=!1,[r,n]},zm=async e=>{let[t,r]=await Promise.allSettled([(0,tr.once)(e,"spawn"),(0,tr.once)(e,"exit")]);return t.status==="rejected"?[]:r.status==="rejected"?Pc(e):r.value},Pc=async e=>{try{return await(0,tr.once)(e,"exit")}catch{return Pc(e)}},Bc=async e=>{let[t,r]=await e;if(!Wm(t,r)&&In(t,r))throw new L;return[t,r]},Wm=(e,t)=>e===void 0&&t===void 0,In=(e,t)=>e!==0||t!==null;var Lc=({error:e,status:t,signal:r,output:n},{maxBuffer:o})=>{let i=Vm(e,t,r),s=i?.code==="ETIMEDOUT",a=Oa(i,n,o);return{resultError:i,exitCode:t,signal:r,timedOut:s,isMaxBuffer:a}},Vm=(e,t,r)=>e!==void 0?e:In(t,r)?new L:void 0;var $c=(e,t,r)=>{let{file:n,commandArguments:o,command:i,escapedCommand:s,startTime:a,verboseInfo:c,options:f,fileDescriptors:l}=Ym(e,t,r),d=Km({file:n,commandArguments:o,options:f,command:i,escapedCommand:s,verboseInfo:c,fileDescriptors:l,startTime:a});return Ae(d,c,f)},Ym=(e,t,r)=>{let{command:n,escapedCommand:o,startTime:i,verboseInfo:s}=pt(e,t,r),a=qm(r),{file:c,commandArguments:f,options:l}=Lt(e,t,a);Hm(l);let d=lc(l,s);return{file:c,commandArguments:f,command:n,escapedCommand:o,startTime:i,verboseInfo:s,options:l,fileDescriptors:d}},qm=e=>e.node&&!e.ipc?{...e,ipc:!1}:e,Hm=({ipc:e,ipcInput:t,detached:r,cancelSignal:n})=>{t&&rr("ipcInput"),e&&rr("ipc: true"),r&&rr("detached: true"),n&&rr("cancelSignal")},rr=e=>{throw new TypeError(`The "${e}" option cannot be used with synchronous methods.`)},Km=({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,verboseInfo:i,fileDescriptors:s,startTime:a})=>{let c=Xm({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,fileDescriptors:s,startTime:a});if(c.failed)return c;let{resultError:f,exitCode:l,signal:d,timedOut:p,isMaxBuffer:u}=Lc(c,r),{output:m,error:S=f}=Rc({fileDescriptors:s,syncResult:c,options:r,isMaxBuffer:u,verboseInfo:i}),E=m.map((O,I)=>q(O,r,I)),b=q(Cc(m,r),r,"all");return Zm({error:S,exitCode:l,signal:d,timedOut:p,isMaxBuffer:u,stdio:E,all:b,options:r,command:n,escapedCommand:o,startTime:a})},Xm=({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,fileDescriptors:i,startTime:s})=>{try{Ac(i,r);let a=Jm(r);return(0,Fc.spawnSync)(...Ft(e,t,a))}catch(a){return Te({error:a,command:n,escapedCommand:o,fileDescriptors:i,options:r,startTime:s,isSync:!0})}},Jm=({encoding:e,maxBuffer:t,...r})=>({...r,encoding:"buffer",maxBuffer:Gt(t)}),Zm=({error:e,exitCode:t,signal:r,timedOut:n,isMaxBuffer:o,stdio:i,all:s,options:a,command:c,escapedCommand:f,startTime:l})=>e===void 0?zt({command:c,escapedCommand:f,stdio:i,all:s,ipcOutput:[],options:a,startTime:l}):ke({error:e,command:c,escapedCommand:f,timedOut:n,isCanceled:!1,isGracefullyCanceled:!1,isMaxBuffer:o,isForcefullyTerminated:!1,exitCode:t,signal:r,stdio:i,all:s,ipcOutput:[],options:a,startTime:l,isSync:!0});var Gl=require("node:events"),zl=require("node:child_process");var Rn=T(require("node:process"),1);var Ie=require("node:events");var vc=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},{reference:o=!0,filter:i}={})=>(Se({methodName:"getOneMessage",isSubprocess:r,ipc:n,isConnected:It(e)}),Qm({anyProcess:e,channel:t,isSubprocess:r,filter:i,reference:o})),Qm=async({anyProcess:e,channel:t,isSubprocess:r,filter:n,reference:o})=>{Et(t,o);let i=Q(e,t,r),s=new AbortController;try{return await Promise.race([eh(i,n,s),th(i,r,s),rh(i,r,s)])}catch(a){throw be(e),a}finally{s.abort(),Tt(t,o)}},eh=async(e,t,{signal:r})=>{if(t===void 0){let[n]=await(0,Ie.once)(e,"message",{signal:r});return n}for await(let[n]of(0,Ie.on)(e,"message",{signal:r}))if(t(n))return n},th=async(e,t,{signal:r})=>{await(0,Ie.once)(e,"disconnect",{signal:r}),is(t)},rh=async(e,t,{signal:r})=>{let[n]=await(0,Ie.once)(e,"strict:error",{signal:r});throw bt(n,t)};var We=require("node:events");var Nc=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},{reference:o=!0}={})=>Dn({anyProcess:e,channel:t,isSubprocess:r,ipc:n,shouldAwait:!r,reference:o}),Dn=({anyProcess:e,channel:t,isSubprocess:r,ipc:n,shouldAwait:o,reference:i})=>{Se({methodName:"getEachMessage",isSubprocess:r,ipc:n,isConnected:It(e)}),Et(t,i);let s=Q(e,t,r),a=new AbortController,c={};return nh(e,s,a),oh({ipcEmitter:s,isSubprocess:r,controller:a,state:c}),ih({anyProcess:e,channel:t,ipcEmitter:s,isSubprocess:r,shouldAwait:o,controller:a,state:c,reference:i})},nh=async(e,t,r)=>{try{await(0,We.once)(t,"disconnect",{signal:r.signal}),r.abort()}catch{}},oh=async({ipcEmitter:e,isSubprocess:t,controller:r,state:n})=>{try{let[o]=await(0,We.once)(e,"strict:error",{signal:r.signal});n.error=bt(o,t),r.abort()}catch{}},ih=async function*({anyProcess:e,channel:t,ipcEmitter:r,isSubprocess:n,shouldAwait:o,controller:i,state:s,reference:a}){try{for await(let[c]of(0,We.on)(r,"message",{signal:i.signal}))Uc(s),yield c}catch{Uc(s)}finally{i.abort(),Tt(t,a),n||be(e),o&&await e}},Uc=({error:e})=>{if(e)throw e};var _c=(e,{ipc:t})=>{Object.assign(e,kc(e,!1,t))},jc=()=>{let e=Rn.default,t=!0,r=Rn.default.channel!==void 0;return{...kc(e,t,r),getCancelSignal:_s.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r})}},kc=(e,t,r)=>({sendMessage:Mt.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r}),getOneMessage:vc.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r}),getEachMessage:Nc.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r})});var Gc=require("node:child_process"),te=require("node:stream");var zc=({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,verboseInfo:s})=>{yn(n);let a=new Gc.ChildProcess;sh(a,n),Object.assign(a,{readable:ah,writable:ch,duplex:lh});let c=Te({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,isSync:!1}),f=fh(c,s,o);return{subprocess:a,promise:f}},sh=(e,t)=>{let r=Ve(),n=Ve(),o=Ve(),i=Array.from({length:t.length-3},Ve),s=Ve(),a=[r,n,o,...i];Object.assign(e,{stdin:r,stdout:n,stderr:o,all:s,stdio:a})},Ve=()=>{let e=new te.PassThrough;return e.end(),e},ah=()=>new te.Readable({read(){}}),ch=()=>new te.Writable({write(){}}),lh=()=>new te.Duplex({read(){},write(){}}),fh=async(e,t,r)=>Ae(e,t,r);var De=require("node:fs"),Vc=require("node:buffer"),k=require("node:stream");var Yc=(e,t)=>Ht(dh,e,t,!1),Ye=({type:e,optionName:t})=>{throw new TypeError(`The \`${t}\` option cannot be ${ee[e]}.`)},Wc={fileNumber:Ye,generator:On,asyncGenerator:On,nodeStream:({value:e})=>({stream:e}),webTransform({value:{transform:e,writableObjectMode:t,readableObjectMode:r}}){let n=t||r;return{stream:k.Duplex.fromWeb(e,{objectMode:n})}},duplex:({value:{transform:e}})=>({stream:e}),native(){}},dh={input:{...Wc,fileUrl:({value:e})=>({stream:(0,De.createReadStream)(e)}),filePath:({value:{file:e}})=>({stream:(0,De.createReadStream)(e)}),webStream:({value:e})=>({stream:k.Readable.fromWeb(e)}),iterable:({value:e})=>({stream:k.Readable.from(e)}),asyncIterable:({value:e})=>({stream:k.Readable.from(e)}),string:({value:e})=>({stream:k.Readable.from(e)}),uint8Array:({value:e})=>({stream:k.Readable.from(Vc.Buffer.from(e))})},output:{...Wc,fileUrl:({value:e})=>({stream:(0,De.createWriteStream)(e)}),filePath:({value:{file:e,append:t}})=>({stream:(0,De.createWriteStream)(e,t?{flags:"a"}:{})}),webStream:({value:e})=>({stream:k.Writable.fromWeb(e)}),iterable:Ye,asyncIterable:Ye,string:Ye,uint8Array:Ye}};var qe=require("node:events"),or=require("node:stream"),Pn=require("node:stream/promises");function fe(e){if(!Array.isArray(e))throw new TypeError(`Expected an array, got \`${typeof e}\`.`);for(let o of e)Mn(o);let t=e.some(({readableObjectMode:o})=>o),r=ph(e,t),n=new Cn({objectMode:t,writableHighWaterMark:r,readableHighWaterMark:r});for(let o of e)n.add(o);return n}var ph=(e,t)=>{if(e.length===0)return(0,or.getDefaultHighWaterMark)(t);let r=e.filter(({readableObjectMode:n})=>n===t).map(({readableHighWaterMark:n})=>n);return Math.max(...r)},Cn=class extends or.PassThrough{#t=new Set([]);#r=new Set([]);#e=new Set([]);#n;#i=Symbol("unpipe");#o=new WeakMap;add(t){if(Mn(t),this.#t.has(t))return;this.#t.add(t),this.#n??=uh(this,this.#t,this.#i);let r=gh({passThroughStream:this,stream:t,streams:this.#t,ended:this.#r,aborted:this.#e,onFinished:this.#n,unpipeEvent:this.#i});this.#o.set(t,r),t.pipe(this,{end:!1})}async remove(t){if(Mn(t),!this.#t.has(t))return!1;let r=this.#o.get(t);return r===void 0?!1:(this.#o.delete(t),t.unpipe(this),await r,!0)}},uh=async(e,t,r)=>{nr(e,qc);let n=new AbortController;try{await Promise.race([mh(e,n),hh(e,t,r,n)])}finally{n.abort(),nr(e,-qc)}},mh=async(e,{signal:t})=>{try{await(0,Pn.finished)(e,{signal:t,cleanup:!0})}catch(r){throw Kc(e,r),r}},hh=async(e,t,r,{signal:n})=>{for await(let[o]of(0,qe.on)(e,"unpipe",{signal:n}))t.has(o)&&o.emit(r)},Mn=e=>{if(typeof e?.pipe!="function")throw new TypeError(`Expected a readable stream, got: \`${typeof e}\`.`)},gh=async({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,onFinished:i,unpipeEvent:s})=>{nr(e,Hc);let a=new AbortController;try{await Promise.race([yh(i,t,a),Sh({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,controller:a}),bh({stream:t,streams:r,ended:n,aborted:o,unpipeEvent:s,controller:a})])}finally{a.abort(),nr(e,-Hc)}r.size>0&&r.size===n.size+o.size&&(n.size===0&&o.size>0?Bn(e):wh(e))},yh=async(e,t,{signal:r})=>{try{await e,r.aborted||Bn(t)}catch(n){r.aborted||Kc(t,n)}},Sh=async({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,controller:{signal:i}})=>{try{await(0,Pn.finished)(t,{signal:i,cleanup:!0,readable:!0,writable:!1}),r.has(t)&&n.add(t)}catch(s){if(i.aborted||!r.has(t))return;Xc(s)?o.add(t):Jc(e,s)}},bh=async({stream:e,streams:t,ended:r,aborted:n,unpipeEvent:o,controller:{signal:i}})=>{if(await(0,qe.once)(e,o,{signal:i}),!e.readable)return(0,qe.once)(i,"abort",{signal:i});t.delete(e),r.delete(e),n.delete(e)},wh=e=>{e.writable&&e.end()},Kc=(e,t)=>{Xc(t)?Bn(e):Jc(e,t)},Xc=e=>e?.code==="ERR_STREAM_PREMATURE_CLOSE",Bn=e=>{(e.readable||e.writable)&&e.destroy()},Jc=(e,t)=>{e.destroyed||(e.once("error",xh),e.destroy(t))},xh=()=>{},nr=(e,t)=>{let r=e.getMaxListeners();r!==0&&r!==Number.POSITIVE_INFINITY&&e.setMaxListeners(r+t)},qc=2,Hc=1;var Ln=require("node:stream/promises");var Re=(e,t)=>{e.pipe(t),Eh(e,t),Th(e,t)},Eh=async(e,t)=>{if(!(B(e)||B(t))){try{await(0,Ln.finished)(e,{cleanup:!0,readable:!0,writable:!1})}catch{}Fn(t)}},Fn=e=>{e.writable&&e.end()},Th=async(e,t)=>{if(!(B(e)||B(t))){try{await(0,Ln.finished)(t,{cleanup:!0,readable:!1,writable:!0})}catch{}$n(e)}},$n=e=>{e.readable&&e.destroy()};var Zc=(e,t,r)=>{let n=new Map;for(let[o,{stdioItems:i,direction:s}]of Object.entries(t)){for(let{stream:a}of i.filter(({type:c})=>P.has(c)))Ah(e,a,s,o);for(let{stream:a}of i.filter(({type:c})=>!P.has(c)))Ih({subprocess:e,stream:a,direction:s,fdNumber:o,pipeGroups:n,controller:r})}for(let[o,i]of n.entries()){let s=i.length===1?i[0]:fe(i);Re(s,o)}},Ah=(e,t,r,n)=>{r==="output"?Re(e.stdio[n],t):Re(t,e.stdio[n]);let o=Oh[n];o!==void 0&&(e[o]=t),e.stdio[n]=t},Oh=["stdin","stdout","stderr"],Ih=({subprocess:e,stream:t,direction:r,fdNumber:n,pipeGroups:o,controller:i})=>{if(t===void 0)return;Dh(t,i);let[s,a]=r==="output"?[t,e.stdio[n]]:[e.stdio[n],t],c=o.get(s)??[];o.set(s,[...c,a])},Dh=(e,{signal:t})=>{B(e)&&se(e,Rh,t)},Rh=2;var Qc=require("node:events");var el=(e,{cleanup:t,detached:r},{signal:n})=>{if(!t||r)return;let o=fo(()=>{e.kill()});(0,Qc.addAbortListener)(n,()=>{o()})};var rl=({source:e,sourcePromise:t,boundOptions:r,createNested:n},...o)=>{let i=dt(),{destination:s,destinationStream:a,destinationError:c,from:f,unpipeSignal:l}=Ch(r,n,o),{sourceStream:d,sourceError:p}=Ph(e,f),{options:u,fileDescriptors:m}=N.get(e);return{sourcePromise:t,sourceStream:d,sourceOptions:u,sourceError:p,destination:s,destinationStream:a,destinationError:c,unpipeSignal:l,fileDescriptors:m,startTime:i}},Ch=(e,t,r)=>{try{let{destination:n,pipeOptions:{from:o,to:i,unpipeSignal:s}={}}=Mh(e,t,...r),a=xt(n,i);return{destination:n,destinationStream:a,from:o,unpipeSignal:s}}catch(n){return{destinationError:n}}},Mh=(e,t,r,...n)=>{if(Array.isArray(r))return{destination:t(tl,e)(r,...n),pipeOptions:e};if(typeof r=="string"||r instanceof URL||Ar(r)){if(Object.keys(e).length>0)throw new TypeError('Please use .pipe("file", ..., options) or .pipe(execa("file", ..., options)) instead of .pipe(options)("file", ...).');let[o,i,s]=et(r,...n);return{destination:t(tl)(o,i,s),pipeOptions:s}}if(N.has(r)){if(Object.keys(e).length>0)throw new TypeError("Please use .pipe(options)`command` or .pipe($(options)`command`) instead of .pipe(options)($`command`).");return{destination:r,pipeOptions:n[0]}}throw new TypeError(`The first argument must be a template string, an options object, or an Execa subprocess: ${r}`)},tl=({options:e})=>({options:{...e,stdin:"pipe",piped:!0}}),Ph=(e,t)=>{try{return{sourceStream:we(e,t)}}catch(r){return{sourceError:r}}};var ol=({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n,fileDescriptors:o,sourceOptions:i,startTime:s})=>{let a=Bh({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n});if(a!==void 0)throw vn({error:a,fileDescriptors:o,sourceOptions:i,startTime:s})},Bh=({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n})=>{if(t!==void 0&&n!==void 0)return n;if(n!==void 0)return $n(e),n;if(t!==void 0)return Fn(r),t},vn=({error:e,fileDescriptors:t,sourceOptions:r,startTime:n})=>Te({error:e,command:nl,escapedCommand:nl,fileDescriptors:t,options:r,startTime:n,isSync:!1}),nl="source.pipe(destination)";var il=async e=>{let[{status:t,reason:r,value:n=r},{status:o,reason:i,value:s=i}]=await e;if(s.pipedFrom.includes(n)||s.pipedFrom.push(n),o==="rejected")throw s;if(t==="rejected")throw n;return s};var sl=require("node:stream/promises");var al=(e,t,r)=>{let n=ir.has(t)?Fh(e,t):Lh(e,t);return se(e,vh,r.signal),se(t,Uh,r.signal),$h(t),n},Lh=(e,t)=>{let r=fe([e]);return Re(r,t),ir.set(t,r),r},Fh=(e,t)=>{let r=ir.get(t);return r.add(e),r},$h=async e=>{try{await(0,sl.finished)(e,{cleanup:!0,readable:!1,writable:!0})}catch{}ir.delete(e)},ir=new WeakMap,vh=2,Uh=1;var cl=require("node:util");var ll=(e,t)=>e===void 0?[]:[Nh(e,t)],Nh=async(e,{sourceStream:t,mergedStream:r,fileDescriptors:n,sourceOptions:o,startTime:i})=>{await(0,cl.aborted)(e,t),await r.remove(t);let s=new Error("Pipe canceled by `unpipeSignal` option.");throw vn({error:s,fileDescriptors:n,sourceOptions:o,startTime:i})};var sr=(e,...t)=>{if(x(t[0]))return sr.bind(void 0,{...e,boundOptions:{...e.boundOptions,...t[0]}});let{destination:r,...n}=rl(e,...t),o=_h({...n,destination:r});return o.pipe=sr.bind(void 0,{...e,source:r,sourcePromise:o,boundOptions:{}}),o},_h=async({sourcePromise:e,sourceStream:t,sourceOptions:r,sourceError:n,destination:o,destinationStream:i,destinationError:s,unpipeSignal:a,fileDescriptors:c,startTime:f})=>{let l=jh(e,o);ol({sourceStream:t,sourceError:n,destinationStream:i,destinationError:s,fileDescriptors:c,sourceOptions:r,startTime:f});let d=new AbortController;try{let p=al(t,i,d);return await Promise.race([il(l),...ll(a,{sourceStream:t,mergedStream:p,sourceOptions:r,fileDescriptors:c,startTime:f})])}finally{d.abort()}},jh=(e,t)=>Promise.allSettled([e,t]);var ml=require("node:timers/promises");var dl=require("node:events"),pl=require("node:stream");var ar=({subprocessStdout:e,subprocess:t,binary:r,shouldEncode:n,encoding:o,preserveNewlines:i})=>{let s=new AbortController;return kh(t,s),ul({stream:e,controller:s,binary:r,shouldEncode:!e.readableObjectMode&&n,encoding:o,shouldSplit:!e.readableObjectMode,preserveNewlines:i})},kh=async(e,t)=>{try{await e}catch{}finally{t.abort()}},Un=({stream:e,onStreamEnd:t,lines:r,encoding:n,stripFinalNewline:o,allMixed:i})=>{let s=new AbortController;Gh(t,s,e);let a=e.readableObjectMode&&!i;return ul({stream:e,controller:s,binary:n==="buffer",shouldEncode:!a,encoding:n,shouldSplit:!a&&r,preserveNewlines:!o})},Gh=async(e,t,r)=>{try{await e}catch{r.destroy()}finally{t.abort()}},ul=({stream:e,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s})=>{let a=(0,dl.on)(e,"data",{signal:t.signal,highWaterMark:fl,highWatermark:fl});return zh({onStdoutChunk:a,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s})},Nn=(0,pl.getDefaultHighWaterMark)(!0),fl=Nn,zh=async function*({onStdoutChunk:e,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s}){let a=Wh({binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s});try{for await(let[c]of e)yield*le(c,a,0)}catch(c){if(!t.signal.aborted)throw c}finally{yield*Ge(a)}},Wh=({binary:e,shouldEncode:t,encoding:r,shouldSplit:n,preserveNewlines:o})=>[Xt(e,r,!t),Kt(e,o,!n,{})].filter(Boolean);var hl=async({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,buffer:o,maxBuffer:i,lines:s,allMixed:a,stripFinalNewline:c,verboseInfo:f,streamInfo:l})=>{let d=Vh({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,allMixed:a,verboseInfo:f,streamInfo:l});if(!o){await Promise.all([Yh(e),d]);return}let p=bn(c,r),u=Un({stream:e,onStreamEnd:t,lines:s,encoding:n,stripFinalNewline:p,allMixed:a}),[m]=await Promise.all([qh({stream:e,iterable:u,fdNumber:r,encoding:n,maxBuffer:i,lines:s}),d]);return m},Vh=async({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,allMixed:o,verboseInfo:i,streamInfo:{fileDescriptors:s}})=>{if(!Qt({stdioItems:s[r]?.stdioItems,encoding:n,verboseInfo:i,fdNumber:r}))return;let a=Un({stream:e,onStreamEnd:t,lines:!0,encoding:n,stripFinalNewline:!0,allMixed:o});await Oc(a,e,r,i)},Yh=async e=>{await(0,ml.setImmediate)(),e.readableFlowing===null&&e.resume()},qh=async({stream:e,stream:{readableObjectMode:t},iterable:r,fdNumber:n,encoding:o,maxBuffer:i,lines:s})=>{try{return t||s?await Nt(r,{maxBuffer:i}):o==="buffer"?new Uint8Array(await _t(r,{maxBuffer:i})):await kt(r,{maxBuffer:i})}catch(a){return gl(Ea({error:a,stream:e,readableObjectMode:t,lines:s,encoding:o,fdNumber:n}))}},_n=async e=>{try{return await e}catch(t){return gl(t)}},gl=({bufferedData:e})=>go(e)?new Uint8Array(e):e;var Sl=require("node:stream/promises"),He=async(e,t,r,{isSameDirection:n,stopOnExit:o=!1}={})=>{let i=Hh(e,r),s=new AbortController;try{await Promise.race([...o?[r.exitPromise]:[],(0,Sl.finished)(e,{cleanup:!0,signal:s.signal})])}catch(a){i.stdinCleanedUp||Jh(a,t,r,n)}finally{s.abort()}},Hh=(e,{originalStreams:[t],subprocess:r})=>{let n={stdinCleanedUp:!1};return e===t&&Kh(e,r,n),n},Kh=(e,t,r)=>{let{_destroy:n}=e;e._destroy=(...o)=>{Xh(t,r),n.call(e,...o)}},Xh=({exitCode:e,signalCode:t},r)=>{(e!==null||t!==null)&&(r.stdinCleanedUp=!0)},Jh=(e,t,r,n)=>{if(!Zh(e,t,r,n))throw e},Zh=(e,t,r,n=!0)=>r.propagating?yl(e)||cr(e):(r.propagating=!0,jn(r,t)===n?yl(e):cr(e)),jn=({fileDescriptors:e},t)=>t!=="all"&&e[t].direction==="input",cr=e=>e?.code==="ERR_STREAM_PREMATURE_CLOSE",yl=e=>e?.code==="EPIPE";var bl=({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:i,verboseInfo:s,streamInfo:a})=>e.stdio.map((c,f)=>kn({stream:c,fdNumber:f,encoding:t,buffer:r[f],maxBuffer:n[f],lines:o[f],allMixed:!1,stripFinalNewline:i,verboseInfo:s,streamInfo:a})),kn=async({stream:e,fdNumber:t,encoding:r,buffer:n,maxBuffer:o,lines:i,allMixed:s,stripFinalNewline:a,verboseInfo:c,streamInfo:f})=>{if(!e)return;let l=He(e,t,f);if(jn(f,t)){await l;return}let[d]=await Promise.all([hl({stream:e,onStreamEnd:l,fdNumber:t,encoding:r,buffer:n,maxBuffer:o,lines:i,allMixed:s,stripFinalNewline:a,verboseInfo:c,streamInfo:f}),l]);return d};var wl=({stdout:e,stderr:t},{all:r})=>r&&(e||t)?fe([e,t].filter(Boolean)):void 0,xl=({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:i,verboseInfo:s,streamInfo:a})=>kn({...Qh(e,r),fdNumber:"all",encoding:t,maxBuffer:n[1]+n[2],lines:o[1]||o[2],allMixed:eg(e),stripFinalNewline:i,verboseInfo:s,streamInfo:a}),Qh=({stdout:e,stderr:t,all:r},[,n,o])=>{let i=n||o;return i?n?o?{stream:r,buffer:i}:{stream:e,buffer:i}:{stream:t,buffer:i}:{stream:r,buffer:i}},eg=({all:e,stdout:t,stderr:r})=>e&&t&&r&&t.readableObjectMode!==r.readableObjectMode;var Il=require("node:events");var El=e=>me(e,"ipc"),Tl=(e,t)=>{let r=ft(e);v({type:"ipc",verboseMessage:r,fdNumber:"ipc",verboseInfo:t})};var Al=async({subprocess:e,buffer:t,maxBuffer:r,ipc:n,ipcOutput:o,verboseInfo:i})=>{if(!n)return o;let s=El(i),a=V(t,"ipc"),c=V(r,"ipc");for await(let f of Dn({anyProcess:e,channel:e.channel,isSubprocess:!1,ipc:n,shouldAwait:!1,reference:!0}))a&&(Ta(e,o,c),o.push(f)),s&&Tl(f,i);return o},Ol=async(e,t)=>(await Promise.allSettled([e]),t);var Dl=async({subprocess:e,options:{encoding:t,buffer:r,maxBuffer:n,lines:o,timeoutDuration:i,cancelSignal:s,gracefulCancel:a,forceKillAfterDelay:c,stripFinalNewline:f,ipc:l,ipcInput:d},context:p,verboseInfo:u,fileDescriptors:m,originalStreams:S,onInternalError:E,controller:b})=>{let O=Mc(e,p),I={originalStreams:S,fileDescriptors:m,subprocess:e,exitPromise:O,propagating:!1},C=bl({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:f,verboseInfo:u,streamInfo:I}),G=xl({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:f,verboseInfo:u,streamInfo:I}),H=[],ne=Al({subprocess:e,buffer:r,maxBuffer:n,ipc:l,ipcOutput:H,verboseInfo:u}),de=tg(S,e,I),K=rg(m,I);try{return await Promise.race([Promise.all([{},Bc(O),Promise.all(C),G,ne,Xs(e,d),...de,...K]),E,ng(e,b),...Vs(e,i,p,b),...os({subprocess:e,cancelSignal:s,gracefulCancel:a,context:p,controller:b}),...Gs({subprocess:e,cancelSignal:s,gracefulCancel:a,forceKillAfterDelay:c,context:p,controller:b})])}catch(ur){return p.terminationReason??="other",Promise.all([{error:ur},O,Promise.all(C.map(Me=>_n(Me))),_n(G),Ol(ne,H),Promise.allSettled(de),Promise.allSettled(K)])}},tg=(e,t,r)=>e.map((n,o)=>n===t.stdio[o]?void 0:He(n,o,r)),rg=(e,t)=>e.flatMap(({stdioItems:r},n)=>r.filter(({value:o,stream:i=o})=>$(i,{checkOpen:!1})&&!B(i)).map(({type:o,value:i,stream:s=i})=>He(s,n,t,{isSameDirection:P.has(o),stopOnExit:o==="native"}))),ng=async(e,{signal:t})=>{let[r]=await(0,Il.once)(e,"error",{signal:t});throw r};var Rl=()=>({readableDestroy:new WeakMap,writableFinal:new WeakMap,writableDestroy:new WeakMap}),Ke=(e,t,r)=>{let n=e[r];n.has(t)||n.set(t,[]);let o=n.get(t),i=U();return o.push(i),{resolve:i.resolve.bind(i),promises:o}},Ce=async({resolve:e,promises:t},r)=>{e();let[n]=await Promise.race([Promise.allSettled([!0,r]),Promise.all([!1,...t])]);return!n};var Ml=require("node:stream"),Pl=require("node:util");var Gn=require("node:stream/promises");var zn=async e=>{if(e!==void 0)try{await Wn(e)}catch{}},Cl=async e=>{if(e!==void 0)try{await Vn(e)}catch{}},Wn=async e=>{await(0,Gn.finished)(e,{cleanup:!0,readable:!1,writable:!0})},Vn=async e=>{await(0,Gn.finished)(e,{cleanup:!0,readable:!0,writable:!1})},lr=async(e,t)=>{if(await e,t)throw t},fr=(e,t,r)=>{r&&!cr(r)?e.destroy(r):t&&e.destroy()};var Bl=({subprocess:e,concurrentStreams:t,encoding:r},{from:n,binary:o=!0,preserveNewlines:i=!0}={})=>{let s=o||R.has(r),{subprocessStdout:a,waitReadableDestroy:c}=Yn(e,n,t),{readableEncoding:f,readableObjectMode:l,readableHighWaterMark:d}=qn(a,s),{read:p,onStdoutDataDone:u}=Hn({subprocessStdout:a,subprocess:e,binary:s,encoding:r,preserveNewlines:i}),m=new Ml.Readable({read:p,destroy:(0,Pl.callbackify)(Xn.bind(void 0,{subprocessStdout:a,subprocess:e,waitReadableDestroy:c})),highWaterMark:d,objectMode:l,encoding:f});return Kn({subprocessStdout:a,onStdoutDataDone:u,readable:m,subprocess:e}),m},Yn=(e,t,r)=>{let n=we(e,t),o=Ke(r,n,"readableDestroy");return{subprocessStdout:n,waitReadableDestroy:o}},qn=({readableEncoding:e,readableObjectMode:t,readableHighWaterMark:r},n)=>n?{readableEncoding:e,readableObjectMode:t,readableHighWaterMark:r}:{readableEncoding:e,readableObjectMode:!0,readableHighWaterMark:Nn},Hn=({subprocessStdout:e,subprocess:t,binary:r,encoding:n,preserveNewlines:o})=>{let i=U(),s=ar({subprocessStdout:e,subprocess:t,binary:r,shouldEncode:!r,encoding:n,preserveNewlines:o});return{read(){og(this,s,i)},onStdoutDataDone:i}},og=async(e,t,r)=>{try{let{value:n,done:o}=await t.next();o?r.resolve():e.push(n)}catch{}},Kn=async({subprocessStdout:e,onStdoutDataDone:t,readable:r,subprocess:n,subprocessStdin:o})=>{try{await Vn(e),await n,await zn(o),await t,r.readable&&r.push(null)}catch(i){await zn(o),Ll(r,i)}},Xn=async({subprocessStdout:e,subprocess:t,waitReadableDestroy:r},n)=>{await Ce(r,t)&&(Ll(e,n),await lr(t,n))},Ll=(e,t)=>{fr(e,e.readable,t)};var Fl=require("node:stream"),Jn=require("node:util");var $l=({subprocess:e,concurrentStreams:t},{to:r}={})=>{let{subprocessStdin:n,waitWritableFinal:o,waitWritableDestroy:i}=Zn(e,r,t),s=new Fl.Writable({...Qn(n,e,o),destroy:(0,Jn.callbackify)(to.bind(void 0,{subprocessStdin:n,subprocess:e,waitWritableFinal:o,waitWritableDestroy:i})),highWaterMark:n.writableHighWaterMark,objectMode:n.writableObjectMode});return eo(n,s),s},Zn=(e,t,r)=>{let n=xt(e,t),o=Ke(r,n,"writableFinal"),i=Ke(r,n,"writableDestroy");return{subprocessStdin:n,waitWritableFinal:o,waitWritableDestroy:i}},Qn=(e,t,r)=>({write:ig.bind(void 0,e),final:(0,Jn.callbackify)(sg.bind(void 0,e,t,r))}),ig=(e,t,r,n)=>{e.write(t,r)?n():e.once("drain",n)},sg=async(e,t,r)=>{await Ce(r,t)&&(e.writable&&e.end(),await t)},eo=async(e,t,r)=>{try{await Wn(e),t.writable&&t.end()}catch(n){await Cl(r),vl(t,n)}},to=async({subprocessStdin:e,subprocess:t,waitWritableFinal:r,waitWritableDestroy:n},o)=>{await Ce(r,t),await Ce(n,t)&&(vl(e,o),await lr(t,o))},vl=(e,t)=>{fr(e,e.writable,t)};var Ul=require("node:stream"),Nl=require("node:util");var _l=({subprocess:e,concurrentStreams:t,encoding:r},{from:n,to:o,binary:i=!0,preserveNewlines:s=!0}={})=>{let a=i||R.has(r),{subprocessStdout:c,waitReadableDestroy:f}=Yn(e,n,t),{subprocessStdin:l,waitWritableFinal:d,waitWritableDestroy:p}=Zn(e,o,t),{readableEncoding:u,readableObjectMode:m,readableHighWaterMark:S}=qn(c,a),{read:E,onStdoutDataDone:b}=Hn({subprocessStdout:c,subprocess:e,binary:a,encoding:r,preserveNewlines:s}),O=new Ul.Duplex({read:E,...Qn(l,e,d),destroy:(0,Nl.callbackify)(ag.bind(void 0,{subprocessStdout:c,subprocessStdin:l,subprocess:e,waitReadableDestroy:f,waitWritableFinal:d,waitWritableDestroy:p})),readableHighWaterMark:S,writableHighWaterMark:l.writableHighWaterMark,readableObjectMode:m,writableObjectMode:l.writableObjectMode,encoding:u});return Kn({subprocessStdout:c,onStdoutDataDone:b,readable:O,subprocess:e,subprocessStdin:l}),eo(l,O,c),O},ag=async({subprocessStdout:e,subprocessStdin:t,subprocess:r,waitReadableDestroy:n,waitWritableFinal:o,waitWritableDestroy:i},s)=>{await Promise.all([Xn({subprocessStdout:e,subprocess:r,waitReadableDestroy:n},s),to({subprocessStdin:t,subprocess:r,waitWritableFinal:o,waitWritableDestroy:i},s)])};var ro=(e,t,{from:r,binary:n=!1,preserveNewlines:o=!1}={})=>{let i=n||R.has(t),s=we(e,r),a=ar({subprocessStdout:s,subprocess:e,binary:i,shouldEncode:!0,encoding:t,preserveNewlines:o});return cg(a,s,e)},cg=async function*(e,t,r){try{yield*e}finally{t.readable&&t.destroy(),await r}};var jl=(e,{encoding:t})=>{let r=Rl();e.readable=Bl.bind(void 0,{subprocess:e,concurrentStreams:r,encoding:t}),e.writable=$l.bind(void 0,{subprocess:e,concurrentStreams:r}),e.duplex=_l.bind(void 0,{subprocess:e,concurrentStreams:r,encoding:t}),e.iterable=ro.bind(void 0,e,t),e[Symbol.asyncIterator]=ro.bind(void 0,e,t,{})};var kl=(e,t)=>{for(let[r,n]of fg){let o=n.value.bind(t);Reflect.defineProperty(e,r,{...n,value:o})}},lg=(async()=>{})().constructor.prototype,fg=["then","catch","finally"].map(e=>[e,Reflect.getOwnPropertyDescriptor(lg,e)]);var Wl=(e,t,r,n)=>{let{file:o,commandArguments:i,command:s,escapedCommand:a,startTime:c,verboseInfo:f,options:l,fileDescriptors:d}=dg(e,t,r),{subprocess:p,promise:u}=ug({file:o,commandArguments:i,options:l,startTime:c,verboseInfo:f,command:s,escapedCommand:a,fileDescriptors:d});return p.pipe=sr.bind(void 0,{source:p,sourcePromise:u,boundOptions:{},createNested:n}),kl(p,u),N.set(p,{options:l,fileDescriptors:d}),p},dg=(e,t,r)=>{let{command:n,escapedCommand:o,startTime:i,verboseInfo:s}=pt(e,t,r),{file:a,commandArguments:c,options:f}=Lt(e,t,r),l=pg(f),d=Yc(l,s);return{file:a,commandArguments:c,command:n,escapedCommand:o,startTime:i,verboseInfo:s,options:l,fileDescriptors:d}},pg=({timeout:e,signal:t,...r})=>{if(t!==void 0)throw new TypeError('The "signal" option has been renamed to "cancelSignal" instead.');return{...r,timeoutDuration:e}},ug=({file:e,commandArguments:t,options:r,startTime:n,verboseInfo:o,command:i,escapedCommand:s,fileDescriptors:a})=>{let c;try{c=(0,zl.spawn)(...Ft(e,t,r))}catch(m){return zc({error:m,command:i,escapedCommand:s,fileDescriptors:a,options:r,startTime:n,verboseInfo:o})}let f=new AbortController;(0,Gl.setMaxListeners)(Number.POSITIVE_INFINITY,f.signal);let l=[...c.stdio];Zc(c,a,f),el(c,r,f);let d={},p=U();c.kill=ts.bind(void 0,{kill:c.kill.bind(c),options:r,onInternalError:p,context:d,controller:f}),c.all=wl(c,r),jl(c,r),_c(c,r);let u=mg({subprocess:c,options:r,startTime:n,verboseInfo:o,fileDescriptors:a,originalStreams:l,command:i,escapedCommand:s,context:d,onInternalError:p,controller:f});return{subprocess:c,promise:u}},mg=async({subprocess:e,options:t,startTime:r,verboseInfo:n,fileDescriptors:o,originalStreams:i,command:s,escapedCommand:a,context:c,onInternalError:f,controller:l})=>{let[d,[p,u],m,S,E]=await Dl({subprocess:e,options:t,context:c,verboseInfo:n,fileDescriptors:o,originalStreams:i,onInternalError:f,controller:l});l.abort(),f.resolve();let b=m.map((C,G)=>q(C,t,G)),O=q(S,t,"all"),I=hg({errorInfo:d,exitCode:p,signal:u,stdio:b,all:O,ipcOutput:E,context:c,options:t,command:s,escapedCommand:a,startTime:r});return Ae(I,n,t)},hg=({errorInfo:e,exitCode:t,signal:r,stdio:n,all:o,ipcOutput:i,context:s,options:a,command:c,escapedCommand:f,startTime:l})=>"error"in e?ke({error:e.error,command:c,escapedCommand:f,timedOut:s.terminationReason==="timeout",isCanceled:s.terminationReason==="cancel"||s.terminationReason==="gracefulCancel",isGracefullyCanceled:s.terminationReason==="gracefulCancel",isMaxBuffer:e.error instanceof _,isForcefullyTerminated:s.isForcefullyTerminated,exitCode:t,signal:r,stdio:n,all:o,ipcOutput:i,options:a,startTime:l,isSync:!1}):zt({command:c,escapedCommand:f,stdio:n,all:o,ipcOutput:i,options:a,startTime:l});var dr=(e,t)=>{let r=Object.fromEntries(Object.entries(t).map(([n,o])=>[n,gg(n,e[n],o)]));return{...e,...r}},gg=(e,t,r)=>yg.has(e)&&x(t)&&x(r)?{...t,...r}:r,yg=new Set(["env",...Cr]);var re=(e,t,r,n)=>{let o=(s,a,c)=>re(s,a,r,c),i=(...s)=>Sg({mapArguments:e,deepOptions:r,boundOptions:t,setBoundExeca:n,createNested:o},...s);return n!==void 0&&n(i,o,t),i},Sg=({mapArguments:e,deepOptions:t={},boundOptions:r={},setBoundExeca:n,createNested:o},i,...s)=>{if(x(i))return o(e,dr(r,i),n);let{file:a,commandArguments:c,options:f,isSync:l}=bg({mapArguments:e,firstArgument:i,nextArguments:s,deepOptions:t,boundOptions:r});return l?$c(a,c,f):Wl(a,c,f,o)},bg=({mapArguments:e,firstArgument:t,nextArguments:r,deepOptions:n,boundOptions:o})=>{let i=To(t)?Ao(t,r):[t,...r],[s,a,c]=et(...i),f=dr(dr(n,o),c),{file:l=s,commandArguments:d=a,options:p=f,isSync:u=!1}=e({file:s,commandArguments:a,options:f});return{file:l,commandArguments:d,options:p,isSync:u}};var Vl=({file:e,commandArguments:t})=>ql(e,t),Yl=({file:e,commandArguments:t})=>({...ql(e,t),isSync:!0}),ql=(e,t)=>{if(t.length>0)throw new TypeError(`The command and its arguments must be passed as a single string: ${e} ${t}.`);let[r,...n]=wg(e);return{file:r,commandArguments:n}},wg=e=>{if(typeof e!="string")throw new TypeError(`The command must be a string: ${String(e)}.`);let t=e.trim();if(t==="")return[];let r=[];for(let n of t.split(xg)){let o=r.at(-1);o&&o.endsWith("\\")?r[r.length-1]=`${o.slice(0,-1)} ${n}`:r.push(n)}return r},xg=/ +/g;var Hl=(e,t,r)=>{e.sync=t(Eg,r),e.s=e.sync},Kl=({options:e})=>Xl(e),Eg=({options:e})=>({...Xl(e),isSync:!0}),Xl=e=>({options:{...Tg(e),...e}}),Tg=({input:e,inputFile:t,stdio:r})=>e===void 0&&t===void 0&&r===void 0?{stdin:"inherit"}:{},Jl={preferLocal:!0};var Zl=re(()=>({})),tI=re(()=>({isSync:!0})),rI=re(Vl),nI=re(Yl),oI=re(Ys),iI=re(Kl,{},Jl,Hl),{sendMessage:sI,getOneMessage:aI,getEachMessage:cI,getCancelSignal:lI}=jc();var pr=class e extends Qe{static summary="Migrate the extension to a newer version of the Raycast API";static description=`Migrate the extension to a newer version of the Raycast API
Internally, the command makes use of https://www.npmjs.com/package/@raycast/migration`;static flags={path:Ql.Flags.string({char:"p",description:"Path to extension root",default:"."})};async run(){let{flags:t}=await this.parse(e);await Zl("@raycast/migration@latest",[t.path],{stderr:"inherit",stdout:"inherit",stdin:"inherit"})}};
