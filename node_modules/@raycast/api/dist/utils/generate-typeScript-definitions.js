"use strict";var y=Object.create;var f=Object.defineProperty;var x=Object.getOwnPropertyDescriptor;var v=Object.getOwnPropertyNames;var w=Object.getPrototypeOf,L=Object.prototype.hasOwnProperty;var P=(e,s)=>{for(var a in s)f(e,a,{get:s[a],enumerable:!0})},u=(e,s,a,n)=>{if(s&&typeof s=="object"||typeof s=="function")for(let t of v(s))!L.call(e,t)&&t!==a&&f(e,t,{get:()=>s[t],enumerable:!(n=x(s,t))||n.enumerable});return e};var $=(e,s,a)=>(a=e!=null?y(w(e)):{},u(s||!e||!e.__esModule?f(a,"default",{value:e,enumerable:!0}):a,e)),I=e=>u(f({},"__esModule",{value:!0}),e);var N={};P(N,{generateTypeScriptDefinitions:()=>D});module.exports=I(N);var g=$(require("fs"));var R=/[\p{Lu}]/u,S=/[\p{Ll}]/u,h=/^[\p{Lu}](?![\p{Lu}])/gu,E=/([\p{Alpha}\p{N}_]|$)/u,p=/[_.\- ]+/,b=new RegExp("^"+p.source),m=new RegExp(p.source+E.source,"gu"),A=new RegExp("\\d+"+E.source,"gu"),k=(e,s,a,n)=>{let t=!1,r=!1,o=!1,d=!1;for(let l=0;l<e.length;l++){let c=e[l];d=l>2?e[l-3]==="-":!0,t&&R.test(c)?(e=e.slice(0,l)+"-"+e.slice(l),t=!1,o=r,r=!0,l++):r&&o&&S.test(c)&&(!d||n)?(e=e.slice(0,l-1)+"-"+e.slice(l-1),o=r,r=!1,t=!0):(t=s(c)===c&&a(c)!==c,o=r,r=a(c)===c&&s(c)!==c)}return e},T=(e,s)=>(h.lastIndex=0,e.replaceAll(h,a=>s(a))),U=(e,s)=>(m.lastIndex=0,A.lastIndex=0,e.replaceAll(A,(a,n,t)=>["_","-"].includes(e.charAt(t+a.length))?a:s(a)).replaceAll(m,(a,n)=>s(n)));function i(e,s){if(!(typeof e=="string"||Array.isArray(e)))throw new TypeError("Expected the input to be `string | string[]`");if(s={pascalCase:!1,preserveConsecutiveUppercase:!1,...s},Array.isArray(e)?e=e.map(r=>r.trim()).filter(r=>r.length).join("-"):e=e.trim(),e.length===0)return"";let a=s.locale===!1?r=>r.toLowerCase():r=>r.toLocaleLowerCase(s.locale),n=s.locale===!1?r=>r.toUpperCase():r=>r.toLocaleUpperCase(s.locale);return e.length===1?p.test(e)?"":s.pascalCase?n(e):a(e):(e!==a(e)&&(e=k(e,a,n,s.preserveConsecutiveUppercase)),e=e.replace(b,""),e=s.preserveConsecutiveUppercase?T(e,a):a(e),s.pascalCase&&(e=n(e.charAt(0))+e.slice(1)),U(e,n))}function D(e,s){let a=`type ExtensionPreferences = ${C(e.preferences)}

/** Preferences accessible in all the extension's commands */
declare type Preferences = ExtensionPreferences
`,n="",t=!1;e.commands.forEach((r,o)=>{t=!0,o===0&&(a+=`
declare namespace Preferences {
`,n+=`declare namespace Arguments {
`),a+=`  /** Preferences accessible in the \`${r.name}\` command */
  export type ${i(r.name,{preserveConsecutiveUppercase:!0,pascalCase:!0})} = ExtensionPreferences & ${C(r.preferences||[])}
`,n+=`  /** Arguments passed to the \`${r.name}\` command */
  export type ${i(r.name,{preserveConsecutiveUppercase:!0,pascalCase:!0})} = ${_(r.arguments)}
`,o===e.commands.length-1&&(n+=`}
`)}),t&&(a+=`}
`),g.writeFileSync("raycast-env.d.ts",`/// <reference types="@raycast/api">

/* \u{1F6A7} \u{1F6A7} \u{1F6A7}
 * This file is auto-generated from the extension's manifest.
 * Do not modify manually. Instead, update the \`package.json\` file.
 * \u{1F6A7} \u{1F6A7} \u{1F6A7} */

/* eslint-disable @typescript-eslint/ban-types */

${a}
${n}
${s.join(`

`)}`)}function C(e){let s="{";return(e||[]).forEach((a,n)=>{switch(n===0&&(s+=`
`),s+=`  /** ${a.title} - ${a.description?.replace(/\*\//g,"*\u200B/")} */
  "${a.name}"`,(!a.required&&a.default==null||a.type==="appPicker")&&(s+="?"),s+=": ",a.type){case"textfield":case"password":case"file":case"directory":s+="string";break;case"checkbox":s+="boolean";break;case"appPicker":s+='import("@raycast/api").Application';break;case"dropdown":s+=a.data?.map(t=>`"${t.value}"`).join(" | ")||"unknown";break;default:s+="unknown"}n<(e||[]).length-1&&(s+=","),s+=`
`}),s+="}",s}function _(e){let s="{";return(e||[]).forEach((a,n)=>{switch(n===0&&(s+=`
`),s+=`  /** ${a.placeholder} */
  "${a.name}": `,a.type){case"text":case"password":s+="string";break;case"dropdown":s+=a.data?.map(t=>`"${t.value}"`).join(" | ")||"unknown";break;default:s+="unknown"}n<(e||[]).length-1&&(s+=","),s+=`
`}),s+="}",s}0&&(module.exports={generateTypeScriptDefinitions});
