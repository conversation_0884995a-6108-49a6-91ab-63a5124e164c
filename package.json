{"$schema": "https://www.raycast.com/schemas/extension.json", "name": "flash-memo", "title": "Flash Memo", "description": "Quickly record and review daily work items", "icon": "icon.png", "author": "user", "categories": ["Productivity"], "license": "MIT", "commands": [{"name": "record", "title": "Record Memo", "subtitle": "Quickly record current work", "description": "Record what you're currently working on", "mode": "no-view", "keywords": ["r"], "arguments": [{"name": "content", "placeholder": "What are you working on?", "type": "text"}]}, {"name": "show-records", "title": "Show Records", "subtitle": "View today's records", "description": "Show all records for today", "mode": "view", "keywords": ["rs"]}], "dependencies": {"@raycast/api": "^1.80.0", "@raycast/utils": "^1.17.0", "date-fns": "^3.6.0"}, "devDependencies": {"@raycast/eslint-config": "^1.0.0", "@types/node": "^20.8.10", "eslint": "^8.51.0", "prettier": "^3.0.3", "typescript": "^5.2.2"}, "scripts": {"build": "ray build -e dist", "dev": "ray develop", "fix-lint": "ray lint --fix", "lint": "ray lint", "publish": "ray publish"}}