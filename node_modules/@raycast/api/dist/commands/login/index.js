"use strict";var Ds=Object.create;var fr=Object.defineProperty;var Ws=Object.getOwnPropertyDescriptor;var Fs=Object.getOwnPropertyNames;var Is=Object.getPrototypeOf,Ls=Object.prototype.hasOwnProperty;var Ke=(r,n)=>()=>(n||r((n={exports:{}}).exports,n),n.exports),xs=(r,n)=>{for(var a in n)fr(r,a,{get:n[a],enumerable:!0})},ea=(r,n,a,i)=>{if(n&&typeof n=="object"||typeof n=="function")for(let l of Fs(n))!Ls.call(r,l)&&l!==a&&fr(r,l,{get:()=>n[l],enumerable:!(i=Ws(n,l))||i.enumerable});return r};var oe=(r,n,a)=>(a=r!=null?Ds(Is(r)):{},ea(n||!r||!r.__esModule?fr(a,"default",{value:r,enumerable:!0}):a,r)),$s=r=>ea(fr({},"__esModule",{value:!0}),r);var sa=Ke((Xl,ia)=>{var Ms=require("node:tty"),Us=Ms?.WriteStream?.prototype?.hasColors?.()??!1,T=(r,n)=>{if(!Us)return l=>l;let a=`\x1B[${r}m`,i=`\x1B[${n}m`;return l=>{let c=l+"",m=c.indexOf(i);if(m===-1)return a+c+i;let h=a,g=0;for(;m!==-1;)h+=c.slice(g,m)+a,g=m+i.length,m=c.indexOf(i,g);return h+=c.slice(g)+i,h}},R={};R.reset=T(0,0);R.bold=T(1,22);R.dim=T(2,22);R.italic=T(3,23);R.underline=T(4,24);R.overline=T(53,55);R.inverse=T(7,27);R.hidden=T(8,28);R.strikethrough=T(9,29);R.black=T(30,39);R.red=T(31,39);R.green=T(32,39);R.yellow=T(33,39);R.blue=T(34,39);R.magenta=T(35,39);R.cyan=T(36,39);R.white=T(37,39);R.gray=T(90,39);R.bgBlack=T(40,49);R.bgRed=T(41,49);R.bgGreen=T(42,49);R.bgYellow=T(43,49);R.bgBlue=T(44,49);R.bgMagenta=T(45,49);R.bgCyan=T(46,49);R.bgWhite=T(47,49);R.bgGray=T(100,49);R.redBright=T(91,39);R.greenBright=T(92,39);R.yellowBright=T(93,39);R.blueBright=T(94,39);R.magentaBright=T(95,39);R.cyanBright=T(96,39);R.whiteBright=T(97,39);R.bgRedBright=T(101,49);R.bgGreenBright=T(102,49);R.bgYellowBright=T(103,49);R.bgBlueBright=T(104,49);R.bgMagentaBright=T(105,49);R.bgCyanBright=T(106,49);R.bgWhiteBright=T(107,49);ia.exports=R});var Pn=Ke(xa=>{var Hs=Object.create,vr=Object.defineProperty,Vs=Object.getOwnPropertyDescriptor,Ys=Object.getOwnPropertyNames,Gs=Object.getPrototypeOf,Qs=Object.prototype.hasOwnProperty,Ra=r=>vr(r,"__esModule",{value:!0}),Bt=(r,n)=>function(){return r&&(n=(0,r[Object.keys(r)[0]])(r=0)),n},En=(r,n)=>function(){return n||(0,r[Object.keys(r)[0]])((n={exports:{}}).exports,n),n.exports},Ta=(r,n)=>{Ra(r);for(var a in n)vr(r,a,{get:n[a],enumerable:!0})},Js=(r,n,a)=>{if(n&&typeof n=="object"||typeof n=="function")for(let i of Ys(n))!Qs.call(r,i)&&i!=="default"&&vr(r,i,{get:()=>n[i],enumerable:!(a=Vs(n,i))||a.enumerable});return r},H=r=>Js(Ra(vr(r!=null?Hs(Gs(r)):{},"default",r&&r.__esModule&&"default"in r?{get:()=>r.default,enumerable:!0}:{value:r,enumerable:!0})),r),Ks=En({"node_modules/web-streams-polyfill/dist/ponyfill.es2018.js"(r,n){(function(a,i){typeof r=="object"&&typeof n<"u"?i(r):typeof define=="function"&&define.amd?define(["exports"],i):(a=typeof globalThis<"u"?globalThis:a||self,i(a.WebStreamsPolyfill={}))})(r,function(a){"use strict";let i=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Symbol:e=>`Symbol(${e})`;function l(){}function c(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global}let m=c();function h(e){return typeof e=="object"&&e!==null||typeof e=="function"}let g=l,y=Promise,q=Promise.prototype.then,I=Promise.resolve.bind(y),O=Promise.reject.bind(y);function S(e){return new y(e)}function p(e){return I(e)}function b(e){return O(e)}function C(e,t,o){return q.call(e,t,o)}function v(e,t,o){C(C(e,t,o),void 0,g)}function U(e,t){v(e,t)}function N(e,t){v(e,void 0,t)}function D(e,t,o){return C(e,t,o)}function L(e){C(e,void 0,g)}let z=(()=>{let e=m&&m.queueMicrotask;if(typeof e=="function")return e;let t=p(void 0);return o=>C(t,o)})();function qe(e,t,o){if(typeof e!="function")throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,o)}function fe(e,t,o){try{return p(qe(e,t,o))}catch(s){return b(s)}}let Ln=16384;class Z{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(t){let o=this._back,s=o;o._elements.length===Ln-1&&(s={_elements:[],_next:void 0}),o._elements.push(t),s!==o&&(this._back=s,o._next=s),++this._size}shift(){let t=this._front,o=t,s=this._cursor,u=s+1,f=t._elements,d=f[s];return u===Ln&&(o=t._next,u=0),--this._size,this._cursor=u,t!==o&&(this._front=o),f[s]=void 0,d}forEach(t){let o=this._cursor,s=this._front,u=s._elements;for(;(o!==u.length||s._next!==void 0)&&!(o===u.length&&(s=s._next,u=s._elements,o=0,u.length===0));)t(u[o]),++o}peek(){let t=this._front,o=this._cursor;return t._elements[o]}}function xn(e,t){e._ownerReadableStream=t,t._reader=e,t._state==="readable"?Dr(e):t._state==="closed"?li(e):$n(e,t._storedError)}function qr(e,t){let o=e._ownerReadableStream;return re(o,t)}function de(e){e._ownerReadableStream._state==="readable"?Wr(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):ui(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),e._ownerReadableStream._reader=void 0,e._ownerReadableStream=void 0}function $e(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function Dr(e){e._closedPromise=S((t,o)=>{e._closedPromise_resolve=t,e._closedPromise_reject=o})}function $n(e,t){Dr(e),Wr(e,t)}function li(e){Dr(e),zn(e)}function Wr(e,t){e._closedPromise_reject!==void 0&&(L(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function ui(e,t){$n(e,t)}function zn(e){e._closedPromise_resolve!==void 0&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}let jn=i("[[AbortSteps]]"),Mn=i("[[ErrorSteps]]"),Fr=i("[[CancelSteps]]"),Ir=i("[[PullSteps]]"),Un=Number.isFinite||function(e){return typeof e=="number"&&isFinite(e)},ci=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function fi(e){return typeof e=="object"||typeof e=="function"}function he(e,t){if(e!==void 0&&!fi(e))throw new TypeError(`${t} is not an object.`)}function ee(e,t){if(typeof e!="function")throw new TypeError(`${t} is not a function.`)}function di(e){return typeof e=="object"&&e!==null||typeof e=="function"}function Nn(e,t){if(!di(e))throw new TypeError(`${t} is not an object.`)}function me(e,t,o){if(e===void 0)throw new TypeError(`Parameter ${t} is required in '${o}'.`)}function Lr(e,t,o){if(e===void 0)throw new TypeError(`${t} is required in '${o}'.`)}function xr(e){return Number(e)}function Hn(e){return e===0?0:e}function hi(e){return Hn(ci(e))}function Vn(e,t){let s=Number.MAX_SAFE_INTEGER,u=Number(e);if(u=Hn(u),!Un(u))throw new TypeError(`${t} is not a finite number`);if(u=hi(u),u<0||u>s)throw new TypeError(`${t} is outside the accepted range of 0 to ${s}, inclusive`);return!Un(u)||u===0?0:u}function $r(e,t){if(!Te(e))throw new TypeError(`${t} is not a ReadableStream.`)}function ze(e){return new lt(e)}function Yn(e,t){e._reader._readRequests.push(t)}function zr(e,t,o){let u=e._reader._readRequests.shift();o?u._closeSteps():u._chunkSteps(t)}function Dt(e){return e._reader._readRequests.length}function Gn(e){let t=e._reader;return!(t===void 0||!Se(t))}class lt{constructor(t){if(me(t,1,"ReadableStreamDefaultReader"),$r(t,"First parameter"),Ee(t))throw new TypeError("This stream has already been locked for exclusive reading by another reader");xn(this,t),this._readRequests=new Z}get closed(){return Se(this)?this._closedPromise:b(Wt("closed"))}cancel(t=void 0){return Se(this)?this._ownerReadableStream===void 0?b($e("cancel")):qr(this,t):b(Wt("cancel"))}read(){if(!Se(this))return b(Wt("read"));if(this._ownerReadableStream===void 0)return b($e("read from"));let t,o,s=S((f,d)=>{t=f,o=d});return ut(this,{_chunkSteps:f=>t({value:f,done:!1}),_closeSteps:()=>t({value:void 0,done:!0}),_errorSteps:f=>o(f)}),s}releaseLock(){if(!Se(this))throw Wt("releaseLock");if(this._ownerReadableStream!==void 0){if(this._readRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");de(this)}}}Object.defineProperties(lt.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(lt.prototype,i.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});function Se(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_readRequests")?!1:e instanceof lt}function ut(e,t){let o=e._ownerReadableStream;o._disturbed=!0,o._state==="closed"?t._closeSteps():o._state==="errored"?t._errorSteps(o._storedError):o._readableStreamController[Ir](t)}function Wt(e){return new TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}let Qn=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype);class Jn{constructor(t,o){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=t,this._preventCancel=o}next(){let t=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?D(this._ongoingPromise,t,t):t(),this._ongoingPromise}return(t){let o=()=>this._returnSteps(t);return this._ongoingPromise?D(this._ongoingPromise,o,o):o()}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});let t=this._reader;if(t._ownerReadableStream===void 0)return b($e("iterate"));let o,s,u=S((d,_)=>{o=d,s=_});return ut(t,{_chunkSteps:d=>{this._ongoingPromise=void 0,z(()=>o({value:d,done:!1}))},_closeSteps:()=>{this._ongoingPromise=void 0,this._isFinished=!0,de(t),o({value:void 0,done:!0})},_errorSteps:d=>{this._ongoingPromise=void 0,this._isFinished=!0,de(t),s(d)}}),u}_returnSteps(t){if(this._isFinished)return Promise.resolve({value:t,done:!0});this._isFinished=!0;let o=this._reader;if(o._ownerReadableStream===void 0)return b($e("finish iterating"));if(!this._preventCancel){let s=qr(o,t);return de(o),D(s,()=>({value:t,done:!0}))}return de(o),p({value:t,done:!0})}}let Kn={next(){return Xn(this)?this._asyncIteratorImpl.next():b(Zn("next"))},return(e){return Xn(this)?this._asyncIteratorImpl.return(e):b(Zn("return"))}};Qn!==void 0&&Object.setPrototypeOf(Kn,Qn);function mi(e,t){let o=ze(e),s=new Jn(o,t),u=Object.create(Kn);return u._asyncIteratorImpl=s,u}function Xn(e){if(!h(e)||!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof Jn}catch{return!1}}function Zn(e){return new TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}let eo=Number.isNaN||function(e){return e!==e};function ct(e){return e.slice()}function to(e,t,o,s,u){new Uint8Array(e).set(new Uint8Array(o,s,u),t)}function Vl(e){return e}function Ft(e){return!1}function ro(e,t,o){if(e.slice)return e.slice(t,o);let s=o-t,u=new ArrayBuffer(s);return to(u,0,e,t,s),u}function pi(e){return!(typeof e!="number"||eo(e)||e<0)}function no(e){let t=ro(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(t)}function jr(e){let t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function Mr(e,t,o){if(!pi(o)||o===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");e._queue.push({value:t,size:o}),e._queueTotalSize+=o}function bi(e){return e._queue.peek().value}function we(e){e._queue=new Z,e._queueTotalSize=0}class ft{constructor(){throw new TypeError("Illegal constructor")}get view(){if(!Ur(this))throw Yr("view");return this._view}respond(t){if(!Ur(this))throw Yr("respond");if(me(t,1,"respond"),t=Vn(t,"First parameter"),this._associatedReadableByteStreamController===void 0)throw new TypeError("This BYOB request has been invalidated");Ft(this._view.buffer),zt(this._associatedReadableByteStreamController,t)}respondWithNewView(t){if(!Ur(this))throw Yr("respondWithNewView");if(me(t,1,"respondWithNewView"),!ArrayBuffer.isView(t))throw new TypeError("You can only respond with array buffer views");if(this._associatedReadableByteStreamController===void 0)throw new TypeError("This BYOB request has been invalidated");Ft(t.buffer),jt(this._associatedReadableByteStreamController,t)}}Object.defineProperties(ft.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(ft.prototype,i.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class je{constructor(){throw new TypeError("Illegal constructor")}get byobRequest(){if(!De(this))throw ht("byobRequest");return Vr(this)}get desiredSize(){if(!De(this))throw ht("desiredSize");return fo(this)}close(){if(!De(this))throw ht("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");let t=this._controlledReadableByteStream._state;if(t!=="readable")throw new TypeError(`The stream (in ${t} state) is not in the readable state and cannot be closed`);dt(this)}enqueue(t){if(!De(this))throw ht("enqueue");if(me(t,1,"enqueue"),!ArrayBuffer.isView(t))throw new TypeError("chunk must be an array buffer view");if(t.byteLength===0)throw new TypeError("chunk must have non-zero byteLength");if(t.buffer.byteLength===0)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");let o=this._controlledReadableByteStream._state;if(o!=="readable")throw new TypeError(`The stream (in ${o} state) is not in the readable state and cannot be enqueued to`);$t(this,t)}error(t=void 0){if(!De(this))throw ht("error");te(this,t)}[Fr](t){oo(this),we(this);let o=this._cancelAlgorithm(t);return xt(this),o}[Ir](t){let o=this._controlledReadableByteStream;if(this._queueTotalSize>0){let u=this._queue.shift();this._queueTotalSize-=u.byteLength,lo(this);let f=new Uint8Array(u.buffer,u.byteOffset,u.byteLength);t._chunkSteps(f);return}let s=this._autoAllocateChunkSize;if(s!==void 0){let u;try{u=new ArrayBuffer(s)}catch(d){t._errorSteps(d);return}let f={buffer:u,bufferByteLength:s,byteOffset:0,byteLength:s,bytesFilled:0,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(f)}Yn(o,t),We(this)}}Object.defineProperties(je.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(je.prototype,i.toStringTag,{value:"ReadableByteStreamController",configurable:!0});function De(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")?!1:e instanceof je}function Ur(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")?!1:e instanceof ft}function We(e){if(!Si(e))return;if(e._pulling){e._pullAgain=!0;return}e._pulling=!0;let o=e._pullAlgorithm();v(o,()=>{e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,We(e))},s=>{te(e,s)})}function oo(e){Hr(e),e._pendingPullIntos=new Z}function Nr(e,t){let o=!1;e._state==="closed"&&(o=!0);let s=ao(t);t.readerType==="default"?zr(e,s,o):Ri(e,s,o)}function ao(e){let t=e.bytesFilled,o=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/o)}function It(e,t,o,s){e._queue.push({buffer:t,byteOffset:o,byteLength:s}),e._queueTotalSize+=s}function io(e,t){let o=t.elementSize,s=t.bytesFilled-t.bytesFilled%o,u=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),f=t.bytesFilled+u,d=f-f%o,_=u,E=!1;d>s&&(_=d-t.bytesFilled,E=!0);let P=e._queue;for(;_>0;){let k=P.peek(),B=Math.min(_,k.byteLength),j=t.byteOffset+t.bytesFilled;to(t.buffer,j,k.buffer,k.byteOffset,B),k.byteLength===B?P.shift():(k.byteOffset+=B,k.byteLength-=B),e._queueTotalSize-=B,so(e,B,t),_-=B}return E}function so(e,t,o){o.bytesFilled+=t}function lo(e){e._queueTotalSize===0&&e._closeRequested?(xt(e),wt(e._controlledReadableByteStream)):We(e)}function Hr(e){e._byobRequest!==null&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function uo(e){for(;e._pendingPullIntos.length>0;){if(e._queueTotalSize===0)return;let t=e._pendingPullIntos.peek();io(e,t)&&(Lt(e),Nr(e._controlledReadableByteStream,t))}}function gi(e,t,o){let s=e._controlledReadableByteStream,u=1;t.constructor!==DataView&&(u=t.constructor.BYTES_PER_ELEMENT);let f=t.constructor,d=t.buffer,_={buffer:d,bufferByteLength:d.byteLength,byteOffset:t.byteOffset,byteLength:t.byteLength,bytesFilled:0,elementSize:u,viewConstructor:f,readerType:"byob"};if(e._pendingPullIntos.length>0){e._pendingPullIntos.push(_),po(s,o);return}if(s._state==="closed"){let E=new f(_.buffer,_.byteOffset,0);o._closeSteps(E);return}if(e._queueTotalSize>0){if(io(e,_)){let E=ao(_);lo(e),o._chunkSteps(E);return}if(e._closeRequested){let E=new TypeError("Insufficient bytes to fill elements in the given buffer");te(e,E),o._errorSteps(E);return}}e._pendingPullIntos.push(_),po(s,o),We(e)}function yi(e,t){let o=e._controlledReadableByteStream;if(Gr(o))for(;bo(o)>0;){let s=Lt(e);Nr(o,s)}}function _i(e,t,o){if(so(e,t,o),o.bytesFilled<o.elementSize)return;Lt(e);let s=o.bytesFilled%o.elementSize;if(s>0){let u=o.byteOffset+o.bytesFilled,f=ro(o.buffer,u-s,u);It(e,f,0,f.byteLength)}o.bytesFilled-=s,Nr(e._controlledReadableByteStream,o),uo(e)}function co(e,t){let o=e._pendingPullIntos.peek();Hr(e),e._controlledReadableByteStream._state==="closed"?yi(e):_i(e,t,o),We(e)}function Lt(e){return e._pendingPullIntos.shift()}function Si(e){let t=e._controlledReadableByteStream;return t._state!=="readable"||e._closeRequested||!e._started?!1:!!(Gn(t)&&Dt(t)>0||Gr(t)&&bo(t)>0||fo(e)>0)}function xt(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function dt(e){let t=e._controlledReadableByteStream;if(!(e._closeRequested||t._state!=="readable")){if(e._queueTotalSize>0){e._closeRequested=!0;return}if(e._pendingPullIntos.length>0&&e._pendingPullIntos.peek().bytesFilled>0){let s=new TypeError("Insufficient bytes to fill elements in the given buffer");throw te(e,s),s}xt(e),wt(t)}}function $t(e,t){let o=e._controlledReadableByteStream;if(e._closeRequested||o._state!=="readable")return;let s=t.buffer,u=t.byteOffset,f=t.byteLength,d=s;if(e._pendingPullIntos.length>0){let _=e._pendingPullIntos.peek();Ft(_.buffer),_.buffer=_.buffer}if(Hr(e),Gn(o))if(Dt(o)===0)It(e,d,u,f);else{e._pendingPullIntos.length>0&&Lt(e);let _=new Uint8Array(d,u,f);zr(o,_,!1)}else Gr(o)?(It(e,d,u,f),uo(e)):It(e,d,u,f);We(e)}function te(e,t){let o=e._controlledReadableByteStream;o._state==="readable"&&(oo(e),we(e),xt(e),zo(o,t))}function Vr(e){if(e._byobRequest===null&&e._pendingPullIntos.length>0){let t=e._pendingPullIntos.peek(),o=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),s=Object.create(ft.prototype);vi(s,e,o),e._byobRequest=s}return e._byobRequest}function fo(e){let t=e._controlledReadableByteStream._state;return t==="errored"?null:t==="closed"?0:e._strategyHWM-e._queueTotalSize}function zt(e,t){let o=e._pendingPullIntos.peek();if(e._controlledReadableByteStream._state==="closed"){if(t!==0)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(t===0)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(o.bytesFilled+t>o.byteLength)throw new RangeError("bytesWritten out of range")}o.buffer=o.buffer,co(e,t)}function jt(e,t){let o=e._pendingPullIntos.peek();if(e._controlledReadableByteStream._state==="closed"){if(t.byteLength!==0)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(t.byteLength===0)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(o.byteOffset+o.bytesFilled!==t.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(o.bufferByteLength!==t.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(o.bytesFilled+t.byteLength>o.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");let u=t.byteLength;o.buffer=t.buffer,co(e,u)}function ho(e,t,o,s,u,f,d){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=null,t._queue=t._queueTotalSize=void 0,we(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=f,t._pullAlgorithm=s,t._cancelAlgorithm=u,t._autoAllocateChunkSize=d,t._pendingPullIntos=new Z,e._readableStreamController=t;let _=o();v(p(_),()=>{t._started=!0,We(t)},E=>{te(t,E)})}function wi(e,t,o){let s=Object.create(je.prototype),u=()=>{},f=()=>p(void 0),d=()=>p(void 0);t.start!==void 0&&(u=()=>t.start(s)),t.pull!==void 0&&(f=()=>t.pull(s)),t.cancel!==void 0&&(d=E=>t.cancel(E));let _=t.autoAllocateChunkSize;if(_===0)throw new TypeError("autoAllocateChunkSize must be greater than 0");ho(e,s,u,f,d,o,_)}function vi(e,t,o){e._associatedReadableByteStreamController=t,e._view=o}function Yr(e){return new TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}function ht(e){return new TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}function mo(e){return new mt(e)}function po(e,t){e._reader._readIntoRequests.push(t)}function Ri(e,t,o){let u=e._reader._readIntoRequests.shift();o?u._closeSteps(t):u._chunkSteps(t)}function bo(e){return e._reader._readIntoRequests.length}function Gr(e){let t=e._reader;return!(t===void 0||!Fe(t))}class mt{constructor(t){if(me(t,1,"ReadableStreamBYOBReader"),$r(t,"First parameter"),Ee(t))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!De(t._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");xn(this,t),this._readIntoRequests=new Z}get closed(){return Fe(this)?this._closedPromise:b(Mt("closed"))}cancel(t=void 0){return Fe(this)?this._ownerReadableStream===void 0?b($e("cancel")):qr(this,t):b(Mt("cancel"))}read(t){if(!Fe(this))return b(Mt("read"));if(!ArrayBuffer.isView(t))return b(new TypeError("view must be an array buffer view"));if(t.byteLength===0)return b(new TypeError("view must have non-zero byteLength"));if(t.buffer.byteLength===0)return b(new TypeError("view's buffer must have non-zero byteLength"));if(Ft(t.buffer),this._ownerReadableStream===void 0)return b($e("read from"));let o,s,u=S((d,_)=>{o=d,s=_});return go(this,t,{_chunkSteps:d=>o({value:d,done:!1}),_closeSteps:d=>o({value:d,done:!0}),_errorSteps:d=>s(d)}),u}releaseLock(){if(!Fe(this))throw Mt("releaseLock");if(this._ownerReadableStream!==void 0){if(this._readIntoRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");de(this)}}}Object.defineProperties(mt.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(mt.prototype,i.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});function Fe(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")?!1:e instanceof mt}function go(e,t,o){let s=e._ownerReadableStream;s._disturbed=!0,s._state==="errored"?o._errorSteps(s._storedError):gi(s._readableStreamController,t,o)}function Mt(e){return new TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}function pt(e,t){let{highWaterMark:o}=e;if(o===void 0)return t;if(eo(o)||o<0)throw new RangeError("Invalid highWaterMark");return o}function Ut(e){let{size:t}=e;return t||(()=>1)}function Nt(e,t){he(e,t);let o=e?.highWaterMark,s=e?.size;return{highWaterMark:o===void 0?void 0:xr(o),size:s===void 0?void 0:Ti(s,`${t} has member 'size' that`)}}function Ti(e,t){return ee(e,t),o=>xr(e(o))}function Ei(e,t){he(e,t);let o=e?.abort,s=e?.close,u=e?.start,f=e?.type,d=e?.write;return{abort:o===void 0?void 0:Ci(o,e,`${t} has member 'abort' that`),close:s===void 0?void 0:Ai(s,e,`${t} has member 'close' that`),start:u===void 0?void 0:Pi(u,e,`${t} has member 'start' that`),write:d===void 0?void 0:ki(d,e,`${t} has member 'write' that`),type:f}}function Ci(e,t,o){return ee(e,o),s=>fe(e,t,[s])}function Ai(e,t,o){return ee(e,o),()=>fe(e,t,[])}function Pi(e,t,o){return ee(e,o),s=>qe(e,t,[s])}function ki(e,t,o){return ee(e,o),(s,u)=>fe(e,t,[s,u])}function yo(e,t){if(!Me(e))throw new TypeError(`${t} is not a WritableStream.`)}function Bi(e){if(typeof e!="object"||e===null)return!1;try{return typeof e.aborted=="boolean"}catch{return!1}}let Oi=typeof AbortController=="function";function qi(){if(Oi)return new AbortController}class bt{constructor(t={},o={}){t===void 0?t=null:Nn(t,"First parameter");let s=Nt(o,"Second parameter"),u=Ei(t,"First parameter");if(So(this),u.type!==void 0)throw new RangeError("Invalid type is specified");let d=Ut(s),_=pt(s,1);Vi(this,u,_,d)}get locked(){if(!Me(this))throw Qt("locked");return Ue(this)}abort(t=void 0){return Me(this)?Ue(this)?b(new TypeError("Cannot abort a stream that already has a writer")):Ht(this,t):b(Qt("abort"))}close(){return Me(this)?Ue(this)?b(new TypeError("Cannot close a stream that already has a writer")):le(this)?b(new TypeError("Cannot close an already-closing stream")):wo(this):b(Qt("close"))}getWriter(){if(!Me(this))throw Qt("getWriter");return _o(this)}}Object.defineProperties(bt.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(bt.prototype,i.toStringTag,{value:"WritableStream",configurable:!0});function _o(e){return new gt(e)}function Di(e,t,o,s,u=1,f=()=>1){let d=Object.create(bt.prototype);So(d);let _=Object.create(Ne.prototype);return Ao(d,_,e,t,o,s,u,f),d}function So(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new Z,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function Me(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")?!1:e instanceof bt}function Ue(e){return e._writer!==void 0}function Ht(e,t){var o;if(e._state==="closed"||e._state==="errored")return p(void 0);e._writableStreamController._abortReason=t,(o=e._writableStreamController._abortController)===null||o===void 0||o.abort();let s=e._state;if(s==="closed"||s==="errored")return p(void 0);if(e._pendingAbortRequest!==void 0)return e._pendingAbortRequest._promise;let u=!1;s==="erroring"&&(u=!0,t=void 0);let f=S((d,_)=>{e._pendingAbortRequest={_promise:void 0,_resolve:d,_reject:_,_reason:t,_wasAlreadyErroring:u}});return e._pendingAbortRequest._promise=f,u||Jr(e,t),f}function wo(e){let t=e._state;if(t==="closed"||t==="errored")return b(new TypeError(`The stream (in ${t} state) is not in the writable state and cannot be closed`));let o=S((u,f)=>{let d={_resolve:u,_reject:f};e._closeRequest=d}),s=e._writer;return s!==void 0&&e._backpressure&&t==="writable"&&an(s),Yi(e._writableStreamController),o}function Wi(e){return S((o,s)=>{let u={_resolve:o,_reject:s};e._writeRequests.push(u)})}function Qr(e,t){if(e._state==="writable"){Jr(e,t);return}Kr(e)}function Jr(e,t){let o=e._writableStreamController;e._state="erroring",e._storedError=t;let s=e._writer;s!==void 0&&Ro(s,t),!$i(e)&&o._started&&Kr(e)}function Kr(e){e._state="errored",e._writableStreamController[Mn]();let t=e._storedError;if(e._writeRequests.forEach(u=>{u._reject(t)}),e._writeRequests=new Z,e._pendingAbortRequest===void 0){Vt(e);return}let o=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,o._wasAlreadyErroring){o._reject(t),Vt(e);return}let s=e._writableStreamController[jn](o._reason);v(s,()=>{o._resolve(),Vt(e)},u=>{o._reject(u),Vt(e)})}function Fi(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}function Ii(e,t){e._inFlightWriteRequest._reject(t),e._inFlightWriteRequest=void 0,Qr(e,t)}function Li(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,e._state==="erroring"&&(e._storedError=void 0,e._pendingAbortRequest!==void 0&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";let o=e._writer;o!==void 0&&Oo(o)}function xi(e,t){e._inFlightCloseRequest._reject(t),e._inFlightCloseRequest=void 0,e._pendingAbortRequest!==void 0&&(e._pendingAbortRequest._reject(t),e._pendingAbortRequest=void 0),Qr(e,t)}function le(e){return!(e._closeRequest===void 0&&e._inFlightCloseRequest===void 0)}function $i(e){return!(e._inFlightWriteRequest===void 0&&e._inFlightCloseRequest===void 0)}function zi(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0}function ji(e){e._inFlightWriteRequest=e._writeRequests.shift()}function Vt(e){e._closeRequest!==void 0&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);let t=e._writer;t!==void 0&&nn(t,e._storedError)}function Xr(e,t){let o=e._writer;o!==void 0&&t!==e._backpressure&&(t?es(o):an(o)),e._backpressure=t}class gt{constructor(t){if(me(t,1,"WritableStreamDefaultWriter"),yo(t,"First parameter"),Ue(t))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=t,t._writer=this;let o=t._state;if(o==="writable")!le(t)&&t._backpressure?Kt(this):qo(this),Jt(this);else if(o==="erroring")on(this,t._storedError),Jt(this);else if(o==="closed")qo(this),Xi(this);else{let s=t._storedError;on(this,s),Bo(this,s)}}get closed(){return Ie(this)?this._closedPromise:b(Le("closed"))}get desiredSize(){if(!Ie(this))throw Le("desiredSize");if(this._ownerWritableStream===void 0)throw yt("desiredSize");return Hi(this)}get ready(){return Ie(this)?this._readyPromise:b(Le("ready"))}abort(t=void 0){return Ie(this)?this._ownerWritableStream===void 0?b(yt("abort")):Mi(this,t):b(Le("abort"))}close(){if(!Ie(this))return b(Le("close"));let t=this._ownerWritableStream;return t===void 0?b(yt("close")):le(t)?b(new TypeError("Cannot close an already-closing stream")):vo(this)}releaseLock(){if(!Ie(this))throw Le("releaseLock");this._ownerWritableStream!==void 0&&To(this)}write(t=void 0){return Ie(this)?this._ownerWritableStream===void 0?b(yt("write to")):Eo(this,t):b(Le("write"))}}Object.defineProperties(gt.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(gt.prototype,i.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});function Ie(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")?!1:e instanceof gt}function Mi(e,t){let o=e._ownerWritableStream;return Ht(o,t)}function vo(e){let t=e._ownerWritableStream;return wo(t)}function Ui(e){let t=e._ownerWritableStream,o=t._state;return le(t)||o==="closed"?p(void 0):o==="errored"?b(t._storedError):vo(e)}function Ni(e,t){e._closedPromiseState==="pending"?nn(e,t):Zi(e,t)}function Ro(e,t){e._readyPromiseState==="pending"?Do(e,t):ts(e,t)}function Hi(e){let t=e._ownerWritableStream,o=t._state;return o==="errored"||o==="erroring"?null:o==="closed"?0:Po(t._writableStreamController)}function To(e){let t=e._ownerWritableStream,o=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");Ro(e,o),Ni(e,o),t._writer=void 0,e._ownerWritableStream=void 0}function Eo(e,t){let o=e._ownerWritableStream,s=o._writableStreamController,u=Gi(s,t);if(o!==e._ownerWritableStream)return b(yt("write to"));let f=o._state;if(f==="errored")return b(o._storedError);if(le(o)||f==="closed")return b(new TypeError("The stream is closing or closed and cannot be written to"));if(f==="erroring")return b(o._storedError);let d=Wi(o);return Qi(s,t,u),d}let Co={};class Ne{constructor(){throw new TypeError("Illegal constructor")}get abortReason(){if(!Zr(this))throw rn("abortReason");return this._abortReason}get signal(){if(!Zr(this))throw rn("signal");if(this._abortController===void 0)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(t=void 0){if(!Zr(this))throw rn("error");this._controlledWritableStream._state==="writable"&&ko(this,t)}[jn](t){let o=this._abortAlgorithm(t);return Yt(this),o}[Mn](){we(this)}}Object.defineProperties(Ne.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(Ne.prototype,i.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});function Zr(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")?!1:e instanceof Ne}function Ao(e,t,o,s,u,f,d,_){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,we(t),t._abortReason=void 0,t._abortController=qi(),t._started=!1,t._strategySizeAlgorithm=_,t._strategyHWM=d,t._writeAlgorithm=s,t._closeAlgorithm=u,t._abortAlgorithm=f;let E=tn(t);Xr(e,E);let P=o(),k=p(P);v(k,()=>{t._started=!0,Gt(t)},B=>{t._started=!0,Qr(e,B)})}function Vi(e,t,o,s){let u=Object.create(Ne.prototype),f=()=>{},d=()=>p(void 0),_=()=>p(void 0),E=()=>p(void 0);t.start!==void 0&&(f=()=>t.start(u)),t.write!==void 0&&(d=P=>t.write(P,u)),t.close!==void 0&&(_=()=>t.close()),t.abort!==void 0&&(E=P=>t.abort(P)),Ao(e,u,f,d,_,E,o,s)}function Yt(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function Yi(e){Mr(e,Co,0),Gt(e)}function Gi(e,t){try{return e._strategySizeAlgorithm(t)}catch(o){return en(e,o),1}}function Po(e){return e._strategyHWM-e._queueTotalSize}function Qi(e,t,o){try{Mr(e,t,o)}catch(u){en(e,u);return}let s=e._controlledWritableStream;if(!le(s)&&s._state==="writable"){let u=tn(e);Xr(s,u)}Gt(e)}function Gt(e){let t=e._controlledWritableStream;if(!e._started||t._inFlightWriteRequest!==void 0)return;if(t._state==="erroring"){Kr(t);return}if(e._queue.length===0)return;let s=bi(e);s===Co?Ji(e):Ki(e,s)}function en(e,t){e._controlledWritableStream._state==="writable"&&ko(e,t)}function Ji(e){let t=e._controlledWritableStream;zi(t),jr(e);let o=e._closeAlgorithm();Yt(e),v(o,()=>{Li(t)},s=>{xi(t,s)})}function Ki(e,t){let o=e._controlledWritableStream;ji(o);let s=e._writeAlgorithm(t);v(s,()=>{Fi(o);let u=o._state;if(jr(e),!le(o)&&u==="writable"){let f=tn(e);Xr(o,f)}Gt(e)},u=>{o._state==="writable"&&Yt(e),Ii(o,u)})}function tn(e){return Po(e)<=0}function ko(e,t){let o=e._controlledWritableStream;Yt(e),Jr(o,t)}function Qt(e){return new TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}function rn(e){return new TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}function Le(e){return new TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}function yt(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function Jt(e){e._closedPromise=S((t,o)=>{e._closedPromise_resolve=t,e._closedPromise_reject=o,e._closedPromiseState="pending"})}function Bo(e,t){Jt(e),nn(e,t)}function Xi(e){Jt(e),Oo(e)}function nn(e,t){e._closedPromise_reject!==void 0&&(L(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function Zi(e,t){Bo(e,t)}function Oo(e){e._closedPromise_resolve!==void 0&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function Kt(e){e._readyPromise=S((t,o)=>{e._readyPromise_resolve=t,e._readyPromise_reject=o}),e._readyPromiseState="pending"}function on(e,t){Kt(e),Do(e,t)}function qo(e){Kt(e),an(e)}function Do(e,t){e._readyPromise_reject!==void 0&&(L(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function es(e){Kt(e)}function ts(e,t){on(e,t)}function an(e){e._readyPromise_resolve!==void 0&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}let Wo=typeof DOMException<"u"?DOMException:void 0;function rs(e){if(!(typeof e=="function"||typeof e=="object"))return!1;try{return new e,!0}catch{return!1}}function ns(){let e=function(o,s){this.message=o||"",this.name=s||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}let os=rs(Wo)?Wo:ns();function Fo(e,t,o,s,u,f){let d=ze(e),_=_o(t);e._disturbed=!0;let E=!1,P=p(void 0);return S((k,B)=>{let j;if(f!==void 0){if(j=()=>{let w=new os("Aborted","AbortError"),A=[];s||A.push(()=>t._state==="writable"?Ht(t,w):p(void 0)),u||A.push(()=>e._state==="readable"?re(e,w):p(void 0)),Y(()=>Promise.all(A.map(F=>F())),!0,w)},f.aborted){j();return}f.addEventListener("abort",j)}function ne(){return S((w,A)=>{function F(Q){Q?w():C(Ye(),F,A)}F(!1)})}function Ye(){return E?p(!0):C(_._readyPromise,()=>S((w,A)=>{ut(d,{_chunkSteps:F=>{P=C(Eo(_,F),void 0,l),w(!1)},_closeSteps:()=>w(!0),_errorSteps:A})}))}if(pe(e,d._closedPromise,w=>{s?K(!0,w):Y(()=>Ht(t,w),!0,w)}),pe(t,_._closedPromise,w=>{u?K(!0,w):Y(()=>re(e,w),!0,w)}),V(e,d._closedPromise,()=>{o?K():Y(()=>Ui(_))}),le(t)||t._state==="closed"){let w=new TypeError("the destination writable stream closed before all data could be piped to it");u?K(!0,w):Y(()=>re(e,w),!0,w)}L(ne());function Ce(){let w=P;return C(P,()=>w!==P?Ce():void 0)}function pe(w,A,F){w._state==="errored"?F(w._storedError):N(A,F)}function V(w,A,F){w._state==="closed"?F():U(A,F)}function Y(w,A,F){if(E)return;E=!0,t._state==="writable"&&!le(t)?U(Ce(),Q):Q();function Q(){v(w(),()=>be(A,F),Ge=>be(!0,Ge))}}function K(w,A){E||(E=!0,t._state==="writable"&&!le(t)?U(Ce(),()=>be(w,A)):be(w,A))}function be(w,A){To(_),de(d),f!==void 0&&f.removeEventListener("abort",j),w?B(A):k(void 0)}})}class He{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!Xt(this))throw tr("desiredSize");return sn(this)}close(){if(!Xt(this))throw tr("close");if(!Ve(this))throw new TypeError("The stream is not in a state that permits close");St(this)}enqueue(t=void 0){if(!Xt(this))throw tr("enqueue");if(!Ve(this))throw new TypeError("The stream is not in a state that permits enqueue");return er(this,t)}error(t=void 0){if(!Xt(this))throw tr("error");ve(this,t)}[Fr](t){we(this);let o=this._cancelAlgorithm(t);return Zt(this),o}[Ir](t){let o=this._controlledReadableStream;if(this._queue.length>0){let s=jr(this);this._closeRequested&&this._queue.length===0?(Zt(this),wt(o)):_t(this),t._chunkSteps(s)}else Yn(o,t),_t(this)}}Object.defineProperties(He.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(He.prototype,i.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});function Xt(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")?!1:e instanceof He}function _t(e){if(!Io(e))return;if(e._pulling){e._pullAgain=!0;return}e._pulling=!0;let o=e._pullAlgorithm();v(o,()=>{e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,_t(e))},s=>{ve(e,s)})}function Io(e){let t=e._controlledReadableStream;return!Ve(e)||!e._started?!1:!!(Ee(t)&&Dt(t)>0||sn(e)>0)}function Zt(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function St(e){if(!Ve(e))return;let t=e._controlledReadableStream;e._closeRequested=!0,e._queue.length===0&&(Zt(e),wt(t))}function er(e,t){if(!Ve(e))return;let o=e._controlledReadableStream;if(Ee(o)&&Dt(o)>0)zr(o,t,!1);else{let s;try{s=e._strategySizeAlgorithm(t)}catch(u){throw ve(e,u),u}try{Mr(e,t,s)}catch(u){throw ve(e,u),u}}_t(e)}function ve(e,t){let o=e._controlledReadableStream;o._state==="readable"&&(we(e),Zt(e),zo(o,t))}function sn(e){let t=e._controlledReadableStream._state;return t==="errored"?null:t==="closed"?0:e._strategyHWM-e._queueTotalSize}function as(e){return!Io(e)}function Ve(e){let t=e._controlledReadableStream._state;return!e._closeRequested&&t==="readable"}function Lo(e,t,o,s,u,f,d){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,we(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=d,t._strategyHWM=f,t._pullAlgorithm=s,t._cancelAlgorithm=u,e._readableStreamController=t;let _=o();v(p(_),()=>{t._started=!0,_t(t)},E=>{ve(t,E)})}function is(e,t,o,s){let u=Object.create(He.prototype),f=()=>{},d=()=>p(void 0),_=()=>p(void 0);t.start!==void 0&&(f=()=>t.start(u)),t.pull!==void 0&&(d=()=>t.pull(u)),t.cancel!==void 0&&(_=E=>t.cancel(E)),Lo(e,u,f,d,_,o,s)}function tr(e){return new TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}function ss(e,t){return De(e._readableStreamController)?us(e):ls(e)}function ls(e,t){let o=ze(e),s=!1,u=!1,f=!1,d=!1,_,E,P,k,B,j=S(V=>{B=V});function ne(){return s?(u=!0,p(void 0)):(s=!0,ut(o,{_chunkSteps:Y=>{z(()=>{u=!1;let K=Y,be=Y;f||er(P._readableStreamController,K),d||er(k._readableStreamController,be),s=!1,u&&ne()})},_closeSteps:()=>{s=!1,f||St(P._readableStreamController),d||St(k._readableStreamController),(!f||!d)&&B(void 0)},_errorSteps:()=>{s=!1}}),p(void 0))}function Ye(V){if(f=!0,_=V,d){let Y=ct([_,E]),K=re(e,Y);B(K)}return j}function Ce(V){if(d=!0,E=V,f){let Y=ct([_,E]),K=re(e,Y);B(K)}return j}function pe(){}return P=ln(pe,ne,Ye),k=ln(pe,ne,Ce),N(o._closedPromise,V=>{ve(P._readableStreamController,V),ve(k._readableStreamController,V),(!f||!d)&&B(void 0)}),[P,k]}function us(e){let t=ze(e),o=!1,s=!1,u=!1,f=!1,d=!1,_,E,P,k,B,j=S(w=>{B=w});function ne(w){N(w._closedPromise,A=>{w===t&&(te(P._readableStreamController,A),te(k._readableStreamController,A),(!f||!d)&&B(void 0))})}function Ye(){Fe(t)&&(de(t),t=ze(e),ne(t)),ut(t,{_chunkSteps:A=>{z(()=>{s=!1,u=!1;let F=A,Q=A;if(!f&&!d)try{Q=no(A)}catch(Ge){te(P._readableStreamController,Ge),te(k._readableStreamController,Ge),B(re(e,Ge));return}f||$t(P._readableStreamController,F),d||$t(k._readableStreamController,Q),o=!1,s?pe():u&&V()})},_closeSteps:()=>{o=!1,f||dt(P._readableStreamController),d||dt(k._readableStreamController),P._readableStreamController._pendingPullIntos.length>0&&zt(P._readableStreamController,0),k._readableStreamController._pendingPullIntos.length>0&&zt(k._readableStreamController,0),(!f||!d)&&B(void 0)},_errorSteps:()=>{o=!1}})}function Ce(w,A){Se(t)&&(de(t),t=mo(e),ne(t));let F=A?k:P,Q=A?P:k;go(t,w,{_chunkSteps:Qe=>{z(()=>{s=!1,u=!1;let Je=A?d:f;if(A?f:d)Je||jt(F._readableStreamController,Qe);else{let Zo;try{Zo=no(Qe)}catch(cn){te(F._readableStreamController,cn),te(Q._readableStreamController,cn),B(re(e,cn));return}Je||jt(F._readableStreamController,Qe),$t(Q._readableStreamController,Zo)}o=!1,s?pe():u&&V()})},_closeSteps:Qe=>{o=!1;let Je=A?d:f,cr=A?f:d;Je||dt(F._readableStreamController),cr||dt(Q._readableStreamController),Qe!==void 0&&(Je||jt(F._readableStreamController,Qe),!cr&&Q._readableStreamController._pendingPullIntos.length>0&&zt(Q._readableStreamController,0)),(!Je||!cr)&&B(void 0)},_errorSteps:()=>{o=!1}})}function pe(){if(o)return s=!0,p(void 0);o=!0;let w=Vr(P._readableStreamController);return w===null?Ye():Ce(w._view,!1),p(void 0)}function V(){if(o)return u=!0,p(void 0);o=!0;let w=Vr(k._readableStreamController);return w===null?Ye():Ce(w._view,!0),p(void 0)}function Y(w){if(f=!0,_=w,d){let A=ct([_,E]),F=re(e,A);B(F)}return j}function K(w){if(d=!0,E=w,f){let A=ct([_,E]),F=re(e,A);B(F)}return j}function be(){}return P=$o(be,pe,Y),k=$o(be,V,K),ne(t),[P,k]}function cs(e,t){he(e,t);let o=e,s=o?.autoAllocateChunkSize,u=o?.cancel,f=o?.pull,d=o?.start,_=o?.type;return{autoAllocateChunkSize:s===void 0?void 0:Vn(s,`${t} has member 'autoAllocateChunkSize' that`),cancel:u===void 0?void 0:fs(u,o,`${t} has member 'cancel' that`),pull:f===void 0?void 0:ds(f,o,`${t} has member 'pull' that`),start:d===void 0?void 0:hs(d,o,`${t} has member 'start' that`),type:_===void 0?void 0:ms(_,`${t} has member 'type' that`)}}function fs(e,t,o){return ee(e,o),s=>fe(e,t,[s])}function ds(e,t,o){return ee(e,o),s=>fe(e,t,[s])}function hs(e,t,o){return ee(e,o),s=>qe(e,t,[s])}function ms(e,t){if(e=`${e}`,e!=="bytes")throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}function ps(e,t){he(e,t);let o=e?.mode;return{mode:o===void 0?void 0:bs(o,`${t} has member 'mode' that`)}}function bs(e,t){if(e=`${e}`,e!=="byob")throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}function gs(e,t){return he(e,t),{preventCancel:!!e?.preventCancel}}function xo(e,t){he(e,t);let o=e?.preventAbort,s=e?.preventCancel,u=e?.preventClose,f=e?.signal;return f!==void 0&&ys(f,`${t} has member 'signal' that`),{preventAbort:!!o,preventCancel:!!s,preventClose:!!u,signal:f}}function ys(e,t){if(!Bi(e))throw new TypeError(`${t} is not an AbortSignal.`)}function _s(e,t){he(e,t);let o=e?.readable;Lr(o,"readable","ReadableWritablePair"),$r(o,`${t} has member 'readable' that`);let s=e?.writable;return Lr(s,"writable","ReadableWritablePair"),yo(s,`${t} has member 'writable' that`),{readable:o,writable:s}}class Re{constructor(t={},o={}){t===void 0?t=null:Nn(t,"First parameter");let s=Nt(o,"Second parameter"),u=cs(t,"First parameter");if(un(this),u.type==="bytes"){if(s.size!==void 0)throw new RangeError("The strategy for a byte stream cannot have a size function");let f=pt(s,0);wi(this,u,f)}else{let f=Ut(s),d=pt(s,1);is(this,u,d,f)}}get locked(){if(!Te(this))throw xe("locked");return Ee(this)}cancel(t=void 0){return Te(this)?Ee(this)?b(new TypeError("Cannot cancel a stream that already has a reader")):re(this,t):b(xe("cancel"))}getReader(t=void 0){if(!Te(this))throw xe("getReader");return ps(t,"First parameter").mode===void 0?ze(this):mo(this)}pipeThrough(t,o={}){if(!Te(this))throw xe("pipeThrough");me(t,1,"pipeThrough");let s=_s(t,"First parameter"),u=xo(o,"Second parameter");if(Ee(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(Ue(s.writable))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");let f=Fo(this,s.writable,u.preventClose,u.preventAbort,u.preventCancel,u.signal);return L(f),s.readable}pipeTo(t,o={}){if(!Te(this))return b(xe("pipeTo"));if(t===void 0)return b("Parameter 1 is required in 'pipeTo'.");if(!Me(t))return b(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));let s;try{s=xo(o,"Second parameter")}catch(u){return b(u)}return Ee(this)?b(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):Ue(t)?b(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):Fo(this,t,s.preventClose,s.preventAbort,s.preventCancel,s.signal)}tee(){if(!Te(this))throw xe("tee");let t=ss(this);return ct(t)}values(t=void 0){if(!Te(this))throw xe("values");let o=gs(t,"First parameter");return mi(this,o.preventCancel)}}Object.defineProperties(Re.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(Re.prototype,i.toStringTag,{value:"ReadableStream",configurable:!0}),typeof i.asyncIterator=="symbol"&&Object.defineProperty(Re.prototype,i.asyncIterator,{value:Re.prototype.values,writable:!0,configurable:!0});function ln(e,t,o,s=1,u=()=>1){let f=Object.create(Re.prototype);un(f);let d=Object.create(He.prototype);return Lo(f,d,e,t,o,s,u),f}function $o(e,t,o){let s=Object.create(Re.prototype);un(s);let u=Object.create(je.prototype);return ho(s,u,e,t,o,0,void 0),s}function un(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function Te(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")?!1:e instanceof Re}function Ee(e){return e._reader!==void 0}function re(e,t){if(e._disturbed=!0,e._state==="closed")return p(void 0);if(e._state==="errored")return b(e._storedError);wt(e);let o=e._reader;o!==void 0&&Fe(o)&&(o._readIntoRequests.forEach(u=>{u._closeSteps(void 0)}),o._readIntoRequests=new Z);let s=e._readableStreamController[Fr](t);return D(s,l)}function wt(e){e._state="closed";let t=e._reader;t!==void 0&&(zn(t),Se(t)&&(t._readRequests.forEach(o=>{o._closeSteps()}),t._readRequests=new Z))}function zo(e,t){e._state="errored",e._storedError=t;let o=e._reader;o!==void 0&&(Wr(o,t),Se(o)?(o._readRequests.forEach(s=>{s._errorSteps(t)}),o._readRequests=new Z):(o._readIntoRequests.forEach(s=>{s._errorSteps(t)}),o._readIntoRequests=new Z))}function xe(e){return new TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}function jo(e,t){he(e,t);let o=e?.highWaterMark;return Lr(o,"highWaterMark","QueuingStrategyInit"),{highWaterMark:xr(o)}}let Mo=e=>e.byteLength;try{Object.defineProperty(Mo,"name",{value:"size",configurable:!0})}catch{}class rr{constructor(t){me(t,1,"ByteLengthQueuingStrategy"),t=jo(t,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=t.highWaterMark}get highWaterMark(){if(!No(this))throw Uo("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!No(this))throw Uo("size");return Mo}}Object.defineProperties(rr.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(rr.prototype,i.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});function Uo(e){return new TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}function No(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")?!1:e instanceof rr}let Ho=()=>1;try{Object.defineProperty(Ho,"name",{value:"size",configurable:!0})}catch{}class nr{constructor(t){me(t,1,"CountQueuingStrategy"),t=jo(t,"First parameter"),this._countQueuingStrategyHighWaterMark=t.highWaterMark}get highWaterMark(){if(!Yo(this))throw Vo("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!Yo(this))throw Vo("size");return Ho}}Object.defineProperties(nr.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(nr.prototype,i.toStringTag,{value:"CountQueuingStrategy",configurable:!0});function Vo(e){return new TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}function Yo(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")?!1:e instanceof nr}function Ss(e,t){he(e,t);let o=e?.flush,s=e?.readableType,u=e?.start,f=e?.transform,d=e?.writableType;return{flush:o===void 0?void 0:ws(o,e,`${t} has member 'flush' that`),readableType:s,start:u===void 0?void 0:vs(u,e,`${t} has member 'start' that`),transform:f===void 0?void 0:Rs(f,e,`${t} has member 'transform' that`),writableType:d}}function ws(e,t,o){return ee(e,o),s=>fe(e,t,[s])}function vs(e,t,o){return ee(e,o),s=>qe(e,t,[s])}function Rs(e,t,o){return ee(e,o),(s,u)=>fe(e,t,[s,u])}class or{constructor(t={},o={},s={}){t===void 0&&(t=null);let u=Nt(o,"Second parameter"),f=Nt(s,"Third parameter"),d=Ss(t,"First parameter");if(d.readableType!==void 0)throw new RangeError("Invalid readableType specified");if(d.writableType!==void 0)throw new RangeError("Invalid writableType specified");let _=pt(f,0),E=Ut(f),P=pt(u,1),k=Ut(u),B,j=S(ne=>{B=ne});Ts(this,j,P,k,_,E),Cs(this,d),d.start!==void 0?B(d.start(this._transformStreamController)):B(void 0)}get readable(){if(!Go(this))throw Xo("readable");return this._readable}get writable(){if(!Go(this))throw Xo("writable");return this._writable}}Object.defineProperties(or.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(or.prototype,i.toStringTag,{value:"TransformStream",configurable:!0});function Ts(e,t,o,s,u,f){function d(){return t}function _(j){return ks(e,j)}function E(j){return Bs(e,j)}function P(){return Os(e)}e._writable=Di(d,_,P,E,o,s);function k(){return qs(e)}function B(j){return ir(e,j),p(void 0)}e._readable=ln(d,k,B,u,f),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,sr(e,!0),e._transformStreamController=void 0}function Go(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")?!1:e instanceof or}function ar(e,t){ve(e._readable._readableStreamController,t),ir(e,t)}function ir(e,t){Qo(e._transformStreamController),en(e._writable._writableStreamController,t),e._backpressure&&sr(e,!1)}function sr(e,t){e._backpressureChangePromise!==void 0&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=S(o=>{e._backpressureChangePromise_resolve=o}),e._backpressure=t}class vt{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!lr(this))throw ur("desiredSize");let t=this._controlledTransformStream._readable._readableStreamController;return sn(t)}enqueue(t=void 0){if(!lr(this))throw ur("enqueue");Jo(this,t)}error(t=void 0){if(!lr(this))throw ur("error");As(this,t)}terminate(){if(!lr(this))throw ur("terminate");Ps(this)}}Object.defineProperties(vt.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(vt.prototype,i.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});function lr(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")?!1:e instanceof vt}function Es(e,t,o,s){t._controlledTransformStream=e,e._transformStreamController=t,t._transformAlgorithm=o,t._flushAlgorithm=s}function Cs(e,t){let o=Object.create(vt.prototype),s=f=>{try{return Jo(o,f),p(void 0)}catch(d){return b(d)}},u=()=>p(void 0);t.transform!==void 0&&(s=f=>t.transform(f,o)),t.flush!==void 0&&(u=()=>t.flush(o)),Es(e,o,s,u)}function Qo(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0}function Jo(e,t){let o=e._controlledTransformStream,s=o._readable._readableStreamController;if(!Ve(s))throw new TypeError("Readable side is not in a state that permits enqueue");try{er(s,t)}catch(f){throw ir(o,f),o._readable._storedError}as(s)!==o._backpressure&&sr(o,!0)}function As(e,t){ar(e._controlledTransformStream,t)}function Ko(e,t){let o=e._transformAlgorithm(t);return D(o,void 0,s=>{throw ar(e._controlledTransformStream,s),s})}function Ps(e){let t=e._controlledTransformStream,o=t._readable._readableStreamController;St(o);let s=new TypeError("TransformStream terminated");ir(t,s)}function ks(e,t){let o=e._transformStreamController;if(e._backpressure){let s=e._backpressureChangePromise;return D(s,()=>{let u=e._writable;if(u._state==="erroring")throw u._storedError;return Ko(o,t)})}return Ko(o,t)}function Bs(e,t){return ar(e,t),p(void 0)}function Os(e){let t=e._readable,o=e._transformStreamController,s=o._flushAlgorithm();return Qo(o),D(s,()=>{if(t._state==="errored")throw t._storedError;St(t._readableStreamController)},u=>{throw ar(e,u),t._storedError})}function qs(e){return sr(e,!1),e._backpressureChangePromise}function ur(e){return new TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}function Xo(e){return new TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}a.ByteLengthQueuingStrategy=rr,a.CountQueuingStrategy=nr,a.ReadableByteStreamController=je,a.ReadableStream=Re,a.ReadableStreamBYOBReader=mt,a.ReadableStreamBYOBRequest=ft,a.ReadableStreamDefaultController=He,a.ReadableStreamDefaultReader=lt,a.TransformStream=or,a.TransformStreamDefaultController=vt,a.WritableStream=bt,a.WritableStreamDefaultController=Ne,a.WritableStreamDefaultWriter=gt,Object.defineProperty(a,"__esModule",{value:!0})})}}),Xs=En({"node_modules/fetch-blob/streams.cjs"(){var r=65536;if(!globalThis.ReadableStream)try{let n=require("process"),{emitWarning:a}=n;try{n.emitWarning=()=>{},Object.assign(globalThis,require("stream/web")),n.emitWarning=a}catch(i){throw n.emitWarning=a,i}}catch{Object.assign(globalThis,Ks())}try{let{Blob:n}=require("buffer");n&&!n.prototype.stream&&(n.prototype.stream=function(i){let l=0,c=this;return new ReadableStream({type:"bytes",async pull(m){let g=await c.slice(l,Math.min(c.size,l+r)).arrayBuffer();l+=g.byteLength,m.enqueue(new Uint8Array(g)),l===c.size&&m.close()}})})}catch{}}});async function*hn(r,n=!0){for(let a of r)if("stream"in a)yield*a.stream();else if(ArrayBuffer.isView(a))if(n){let i=a.byteOffset,l=a.byteOffset+a.byteLength;for(;i!==l;){let c=Math.min(l-i,Sn),m=a.buffer.slice(i,i+c);i+=m.byteLength,yield new Uint8Array(m)}}else yield a;else{let i=0,l=a;for(;i!==l.size;){let m=await l.slice(i,Math.min(l.size,i+Sn)).arrayBuffer();i+=m.byteLength,yield new Uint8Array(m)}}}var Zs,Sn,mn,wn,nt,Ot=Bt({"node_modules/fetch-blob/index.js"(){Zs=H(Xs()),Sn=65536,mn=class vn{#e=[];#t="";#r=0;#n="transparent";constructor(n=[],a={}){if(typeof n!="object"||n===null)throw new TypeError("Failed to construct 'Blob': The provided value cannot be converted to a sequence.");if(typeof n[Symbol.iterator]!="function")throw new TypeError("Failed to construct 'Blob': The object must have a callable @@iterator property.");if(typeof a!="object"&&typeof a!="function")throw new TypeError("Failed to construct 'Blob': parameter 2 cannot convert to dictionary.");a===null&&(a={});let i=new TextEncoder;for(let c of n){let m;ArrayBuffer.isView(c)?m=new Uint8Array(c.buffer.slice(c.byteOffset,c.byteOffset+c.byteLength)):c instanceof ArrayBuffer?m=new Uint8Array(c.slice(0)):c instanceof vn?m=c:m=i.encode(`${c}`),this.#r+=ArrayBuffer.isView(m)?m.byteLength:m.size,this.#e.push(m)}this.#n=`${a.endings===void 0?"transparent":a.endings}`;let l=a.type===void 0?"":String(a.type);this.#t=/^[\x20-\x7E]*$/.test(l)?l:""}get size(){return this.#r}get type(){return this.#t}async text(){let n=new TextDecoder,a="";for await(let i of hn(this.#e,!1))a+=n.decode(i,{stream:!0});return a+=n.decode(),a}async arrayBuffer(){let n=new Uint8Array(this.size),a=0;for await(let i of hn(this.#e,!1))n.set(i,a),a+=i.length;return n.buffer}stream(){let n=hn(this.#e,!0);return new globalThis.ReadableStream({type:"bytes",async pull(a){let i=await n.next();i.done?a.close():a.enqueue(i.value)},async cancel(){await n.return()}})}slice(n=0,a=this.size,i=""){let{size:l}=this,c=n<0?Math.max(l+n,0):Math.min(n,l),m=a<0?Math.max(l+a,0):Math.min(a,l),h=Math.max(m-c,0),g=this.#e,y=[],q=0;for(let O of g){if(q>=h)break;let S=ArrayBuffer.isView(O)?O.byteLength:O.size;if(c&&S<=c)c-=S,m-=S;else{let p;ArrayBuffer.isView(O)?(p=O.subarray(c,Math.min(S,m)),q+=p.byteLength):(p=O.slice(c,Math.min(S,m)),q+=p.size),m-=S,y.push(p),c=0}}let I=new vn([],{type:String(i).toLowerCase()});return I.#r=h,I.#e=y,I}get[Symbol.toStringTag](){return"Blob"}static[Symbol.hasInstance](n){return n&&typeof n=="object"&&typeof n.constructor=="function"&&(typeof n.stream=="function"||typeof n.arrayBuffer=="function")&&/^(Blob|File)$/.test(n[Symbol.toStringTag])}},Object.defineProperties(mn.prototype,{size:{enumerable:!0},type:{enumerable:!0},slice:{enumerable:!0}}),wn=mn,nt=wn}}),ua,ca,qt,Ea=Bt({"node_modules/fetch-blob/file.js"(){Ot(),ua=class extends nt{#e=0;#t="";constructor(n,a,i={}){if(arguments.length<2)throw new TypeError(`Failed to construct 'File': 2 arguments required, but only ${arguments.length} present.`);super(n,i),i===null&&(i={});let l=i.lastModified===void 0?Date.now():Number(i.lastModified);Number.isNaN(l)||(this.#e=l),this.#t=String(a)}get name(){return this.#t}get lastModified(){return this.#e}get[Symbol.toStringTag](){return"File"}static[Symbol.hasInstance](n){return!!n&&n instanceof nt&&/^(File)$/.test(n[Symbol.toStringTag])}},ca=ua,qt=ca}});function el(r,n=nt){var a=`${Rn()}${Rn()}`.replace(/\./g,"").slice(-28).padStart(32,"-"),i=[],l=`--${a}\r
Content-Disposition: form-data; name="`;return r.forEach((c,m)=>typeof c=="string"?i.push(l+gr(m)+`"\r
\r
${c.replace(/\r(?!\n)|(?<!\r)\n/g,`\r
`)}\r
`):i.push(l+gr(m)+`"; filename="${gr(c.name,1)}"\r
Content-Type: ${c.type||"application/octet-stream"}\r
\r
`,c,`\r
`)),i.push(`--${a}--`),new n(i,{type:"multipart/form-data; boundary="+a})}var Ze,fa,da,Rn,ha,pn,gr,Be,ot,Rr=Bt({"node_modules/formdata-polyfill/esm.min.js"(){Ot(),Ea(),{toStringTag:Ze,iterator:fa,hasInstance:da}=Symbol,Rn=Math.random,ha="append,set,get,getAll,delete,keys,values,entries,forEach,constructor".split(","),pn=(r,n,a)=>(r+="",/^(Blob|File)$/.test(n&&n[Ze])?[(a=a!==void 0?a+"":n[Ze]=="File"?n.name:"blob",r),n.name!==a||n[Ze]=="blob"?new qt([n],a,n):n]:[r,n+""]),gr=(r,n)=>(n?r:r.replace(/\r?\n|\r/g,`\r
`)).replace(/\n/g,"%0A").replace(/\r/g,"%0D").replace(/"/g,"%22"),Be=(r,n,a)=>{if(n.length<a)throw new TypeError(`Failed to execute '${r}' on 'FormData': ${a} arguments required, but only ${n.length} present.`)},ot=class{#e=[];constructor(...n){if(n.length)throw new TypeError("Failed to construct 'FormData': parameter 1 is not of type 'HTMLFormElement'.")}get[Ze](){return"FormData"}[fa](){return this.entries()}static[da](n){return n&&typeof n=="object"&&n[Ze]==="FormData"&&!ha.some(a=>typeof n[a]!="function")}append(...n){Be("append",arguments,2),this.#e.push(pn(...n))}delete(n){Be("delete",arguments,1),n+="",this.#e=this.#e.filter(([a])=>a!==n)}get(n){Be("get",arguments,1),n+="";for(var a=this.#e,i=a.length,l=0;l<i;l++)if(a[l][0]===n)return a[l][1];return null}getAll(n,a){return Be("getAll",arguments,1),a=[],n+="",this.#e.forEach(i=>i[0]===n&&a.push(i[1])),a}has(n){return Be("has",arguments,1),n+="",this.#e.some(a=>a[0]===n)}forEach(n,a){Be("forEach",arguments,1);for(var[i,l]of this)n.call(a,l,i,this)}set(...n){Be("set",arguments,2);var a=[],i=!0;n=pn(...n),this.#e.forEach(l=>{l[0]===n[0]?i&&(i=!a.push(n)):a.push(l)}),i&&a.push(n),this.#e=a}*entries(){yield*this.#e}*keys(){for(var[n]of this)yield n}*values(){for(var[,n]of this)yield n}}}}),tl=En({"node_modules/node-domexception/index.js"(r,n){if(!globalThis.DOMException)try{let{MessageChannel:a}=require("worker_threads"),i=new a().port1,l=new ArrayBuffer;i.postMessage(l,[l,l])}catch(a){a.constructor.name==="DOMException"&&(globalThis.DOMException=a.constructor)}n.exports=globalThis.DOMException}}),Tt,ma,pa,mr,Ca,Aa,Pa,ka,bn,gn,pr,Ba=Bt({"node_modules/fetch-blob/from.js"(){Tt=H(require("fs")),ma=H(require("path")),pa=H(tl()),Ea(),Ot(),{stat:mr}=Tt.promises,Ca=(r,n)=>bn((0,Tt.statSync)(r),r,n),Aa=(r,n)=>mr(r).then(a=>bn(a,r,n)),Pa=(r,n)=>mr(r).then(a=>gn(a,r,n)),ka=(r,n)=>gn((0,Tt.statSync)(r),r,n),bn=(r,n,a="")=>new nt([new pr({path:n,size:r.size,lastModified:r.mtimeMs,start:0})],{type:a}),gn=(r,n,a="")=>new qt([new pr({path:n,size:r.size,lastModified:r.mtimeMs,start:0})],(0,ma.basename)(n),{type:a,lastModified:r.mtimeMs}),pr=class{#e;#t;constructor(r){this.#e=r.path,this.#t=r.start,this.size=r.size,this.lastModified=r.lastModified}slice(r,n){return new pr({path:this.#e,lastModified:this.lastModified,size:n-r,start:this.#t+r})}async*stream(){let{mtimeMs:r}=await mr(this.#e);if(r>this.lastModified)throw new pa.default("The requested file could not be read, typically due to permission problems that have occurred after a reference to a file was acquired.","NotReadableError");yield*(0,Tt.createReadStream)(this.#e,{start:this.#t,end:this.#t+this.size-1})}get[Symbol.toStringTag](){return"Blob"}}}}),Oa={};Ta(Oa,{toFormData:()=>nl});function rl(r){let n=r.match(/\bfilename=("(.*?)"|([^()<>@,;:\\"/[\]?={}\s\t]+))($|;\s)/i);if(!n)return;let a=n[2]||n[3]||"",i=a.slice(a.lastIndexOf("\\")+1);return i=i.replace(/%22/g,'"'),i=i.replace(/&#(\d{4});/g,(l,c)=>String.fromCharCode(c)),i}async function nl(r,n){if(!/multipart/i.test(n))throw new TypeError("Failed to fetch");let a=n.match(/boundary=(?:"([^"]+)"|([^;]+))/i);if(!a)throw new TypeError("no or bad content-type header, no multipart boundary");let i=new qa(a[1]||a[2]),l,c,m,h,g,y,q=[],I=new ot,O=v=>{m+=C.decode(v,{stream:!0})},S=v=>{q.push(v)},p=()=>{let v=new qt(q,y,{type:g});I.append(h,v)},b=()=>{I.append(h,m)},C=new TextDecoder("utf-8");C.decode(),i.onPartBegin=function(){i.onPartData=O,i.onPartEnd=b,l="",c="",m="",h="",g="",y=null,q.length=0},i.onHeaderField=function(v){l+=C.decode(v,{stream:!0})},i.onHeaderValue=function(v){c+=C.decode(v,{stream:!0})},i.onHeaderEnd=function(){if(c+=C.decode(),l=l.toLowerCase(),l==="content-disposition"){let v=c.match(/\bname=("([^"]*)"|([^()<>@,;:\\"/[\]?={}\s\t]+))/i);v&&(h=v[2]||v[3]||""),y=rl(c),y&&(i.onPartData=S,i.onPartEnd=p)}else l==="content-type"&&(g=c);c="",l=""};for await(let v of r)i.write(v);return i.end(),I}var ae,W,yn,ge,Et,Ct,ba,et,ga,ya,_a,Sa,Oe,qa,ol=Bt({"node_modules/node-fetch/src/utils/multipart-parser.js"(){Ba(),Rr(),ae=0,W={START_BOUNDARY:ae++,HEADER_FIELD_START:ae++,HEADER_FIELD:ae++,HEADER_VALUE_START:ae++,HEADER_VALUE:ae++,HEADER_VALUE_ALMOST_DONE:ae++,HEADERS_ALMOST_DONE:ae++,PART_DATA_START:ae++,PART_DATA:ae++,END:ae++},yn=1,ge={PART_BOUNDARY:yn,LAST_BOUNDARY:yn*=2},Et=10,Ct=13,ba=32,et=45,ga=58,ya=97,_a=122,Sa=r=>r|32,Oe=()=>{},qa=class{constructor(r){this.index=0,this.flags=0,this.onHeaderEnd=Oe,this.onHeaderField=Oe,this.onHeadersEnd=Oe,this.onHeaderValue=Oe,this.onPartBegin=Oe,this.onPartData=Oe,this.onPartEnd=Oe,this.boundaryChars={},r=`\r
--`+r;let n=new Uint8Array(r.length);for(let a=0;a<r.length;a++)n[a]=r.charCodeAt(a),this.boundaryChars[n[a]]=!0;this.boundary=n,this.lookbehind=new Uint8Array(this.boundary.length+8),this.state=W.START_BOUNDARY}write(r){let n=0,a=r.length,i=this.index,{lookbehind:l,boundary:c,boundaryChars:m,index:h,state:g,flags:y}=this,q=this.boundary.length,I=q-1,O=r.length,S,p,b=N=>{this[N+"Mark"]=n},C=N=>{delete this[N+"Mark"]},v=(N,D,L,z)=>{(D===void 0||D!==L)&&this[N](z&&z.subarray(D,L))},U=(N,D)=>{let L=N+"Mark";L in this&&(D?(v(N,this[L],n,r),delete this[L]):(v(N,this[L],r.length,r),this[L]=0))};for(n=0;n<a;n++)switch(S=r[n],g){case W.START_BOUNDARY:if(h===c.length-2){if(S===et)y|=ge.LAST_BOUNDARY;else if(S!==Ct)return;h++;break}else if(h-1===c.length-2){if(y&ge.LAST_BOUNDARY&&S===et)g=W.END,y=0;else if(!(y&ge.LAST_BOUNDARY)&&S===Et)h=0,v("onPartBegin"),g=W.HEADER_FIELD_START;else return;break}S!==c[h+2]&&(h=-2),S===c[h+2]&&h++;break;case W.HEADER_FIELD_START:g=W.HEADER_FIELD,b("onHeaderField"),h=0;case W.HEADER_FIELD:if(S===Ct){C("onHeaderField"),g=W.HEADERS_ALMOST_DONE;break}if(h++,S===et)break;if(S===ga){if(h===1)return;U("onHeaderField",!0),g=W.HEADER_VALUE_START;break}if(p=Sa(S),p<ya||p>_a)return;break;case W.HEADER_VALUE_START:if(S===ba)break;b("onHeaderValue"),g=W.HEADER_VALUE;case W.HEADER_VALUE:S===Ct&&(U("onHeaderValue",!0),v("onHeaderEnd"),g=W.HEADER_VALUE_ALMOST_DONE);break;case W.HEADER_VALUE_ALMOST_DONE:if(S!==Et)return;g=W.HEADER_FIELD_START;break;case W.HEADERS_ALMOST_DONE:if(S!==Et)return;v("onHeadersEnd"),g=W.PART_DATA_START;break;case W.PART_DATA_START:g=W.PART_DATA,b("onPartData");case W.PART_DATA:if(i=h,h===0){for(n+=I;n<O&&!(r[n]in m);)n+=q;n-=I,S=r[n]}if(h<c.length)c[h]===S?(h===0&&U("onPartData",!0),h++):h=0;else if(h===c.length)h++,S===Ct?y|=ge.PART_BOUNDARY:S===et?y|=ge.LAST_BOUNDARY:h=0;else if(h-1===c.length)if(y&ge.PART_BOUNDARY){if(h=0,S===Et){y&=~ge.PART_BOUNDARY,v("onPartEnd"),v("onPartBegin"),g=W.HEADER_FIELD_START;break}}else y&ge.LAST_BOUNDARY&&S===et?(v("onPartEnd"),g=W.END,y=0):h=0;if(h>0)l[h-1]=S;else if(i>0){let N=new Uint8Array(l.buffer,l.byteOffset,l.byteLength);v("onPartData",0,i,N),i=0,b("onPartData"),n--}break;case W.END:break;default:throw new Error(`Unexpected state entered: ${g}`)}U("onHeaderField"),U("onHeaderValue"),U("onPartData"),this.index=h,this.state=g,this.flags=y}end(){if(this.state===W.HEADER_FIELD_START&&this.index===0||this.state===W.PART_DATA&&this.index===this.boundary.length)this.onPartEnd();else if(this.state!==W.END)throw new Error("MultipartParser.end(): stream ended unexpectedly")}}}});Ta(xa,{AbortError:()=>Ia,Blob:()=>wn,FetchError:()=>se,File:()=>qt,FormData:()=>ot,Headers:()=>ye,Request:()=>kt,Response:()=>J,blobFrom:()=>Aa,blobFromSync:()=>Ca,default:()=>La,fileFrom:()=>Pa,fileFromSync:()=>ka,isRedirect:()=>An});var al=H(require("http")),il=H(require("https")),tt=H(require("zlib")),ue=H(require("stream")),br=H(require("buffer"));function sl(r){if(!/^data:/i.test(r))throw new TypeError('`uri` does not appear to be a Data URI (must begin with "data:")');r=r.replace(/\r?\n/g,"");let n=r.indexOf(",");if(n===-1||n<=4)throw new TypeError("malformed data: URI");let a=r.substring(5,n).split(";"),i="",l=!1,c=a[0]||"text/plain",m=c;for(let q=1;q<a.length;q++)a[q]==="base64"?l=!0:a[q]&&(m+=`;${a[q]}`,a[q].indexOf("charset=")===0&&(i=a[q].substring(8)));!a[0]&&!i.length&&(m+=";charset=US-ASCII",i="US-ASCII");let h=l?"base64":"ascii",g=unescape(r.substring(n+1)),y=Buffer.from(g,h);return y.type=c,y.typeFull=m,y.charset=i,y}var ll=sl,ce=H(require("stream")),at=H(require("util")),X=H(require("buffer"));Ot();Rr();var Tr=class extends Error{constructor(r,n){super(r),Error.captureStackTrace(this,this.constructor),this.type=n}get name(){return this.constructor.name}get[Symbol.toStringTag](){return this.constructor.name}},se=class extends Tr{constructor(r,n,a){super(r,n),a&&(this.code=this.errno=a.code,this.erroredSysCall=a.syscall)}},_r=Symbol.toStringTag,Da=r=>typeof r=="object"&&typeof r.append=="function"&&typeof r.delete=="function"&&typeof r.get=="function"&&typeof r.getAll=="function"&&typeof r.has=="function"&&typeof r.set=="function"&&typeof r.sort=="function"&&r[_r]==="URLSearchParams",Sr=r=>r&&typeof r=="object"&&typeof r.arrayBuffer=="function"&&typeof r.type=="string"&&typeof r.stream=="function"&&typeof r.constructor=="function"&&/^(Blob|File)$/.test(r[_r]),ul=r=>typeof r=="object"&&(r[_r]==="AbortSignal"||r[_r]==="EventTarget"),cl=(r,n)=>{let a=new URL(n).hostname,i=new URL(r).hostname;return a===i||a.endsWith(`.${i}`)},fl=(r,n)=>{let a=new URL(n).protocol,i=new URL(r).protocol;return a===i},dl=(0,at.promisify)(ce.default.pipeline),G=Symbol("Body internals"),Pt=class{constructor(r,{size:n=0}={}){let a=null;r===null?r=null:Da(r)?r=X.Buffer.from(r.toString()):Sr(r)||X.Buffer.isBuffer(r)||(at.types.isAnyArrayBuffer(r)?r=X.Buffer.from(r):ArrayBuffer.isView(r)?r=X.Buffer.from(r.buffer,r.byteOffset,r.byteLength):r instanceof ce.default||(r instanceof ot?(r=el(r),a=r.type.split("=")[1]):r=X.Buffer.from(String(r))));let i=r;X.Buffer.isBuffer(r)?i=ce.default.Readable.from(r):Sr(r)&&(i=ce.default.Readable.from(r.stream())),this[G]={body:r,stream:i,boundary:a,disturbed:!1,error:null},this.size=n,r instanceof ce.default&&r.on("error",l=>{let c=l instanceof Tr?l:new se(`Invalid response body while trying to fetch ${this.url}: ${l.message}`,"system",l);this[G].error=c})}get body(){return this[G].stream}get bodyUsed(){return this[G].disturbed}async arrayBuffer(){let{buffer:r,byteOffset:n,byteLength:a}=await _n(this);return r.slice(n,n+a)}async formData(){let r=this.headers.get("content-type");if(r.startsWith("application/x-www-form-urlencoded")){let a=new ot,i=new URLSearchParams(await this.text());for(let[l,c]of i)a.append(l,c);return a}let{toFormData:n}=await Promise.resolve().then(()=>(ol(),Oa));return n(this.body,r)}async blob(){let r=this.headers&&this.headers.get("content-type")||this[G].body&&this[G].body.type||"",n=await this.arrayBuffer();return new nt([n],{type:r})}async json(){let r=await this.text();return JSON.parse(r)}async text(){let r=await _n(this);return new TextDecoder().decode(r)}buffer(){return _n(this)}};Pt.prototype.buffer=(0,at.deprecate)(Pt.prototype.buffer,"Please use 'response.arrayBuffer()' instead of 'response.buffer()'","node-fetch#buffer");Object.defineProperties(Pt.prototype,{body:{enumerable:!0},bodyUsed:{enumerable:!0},arrayBuffer:{enumerable:!0},blob:{enumerable:!0},json:{enumerable:!0},text:{enumerable:!0},data:{get:(0,at.deprecate)(()=>{},"data doesn't exist, use json(), text(), arrayBuffer(), or body instead","https://github.com/node-fetch/node-fetch/issues/1000 (response)")}});async function _n(r){if(r[G].disturbed)throw new TypeError(`body used already for: ${r.url}`);if(r[G].disturbed=!0,r[G].error)throw r[G].error;let{body:n}=r;if(n===null||!(n instanceof ce.default))return X.Buffer.alloc(0);let a=[],i=0;try{for await(let l of n){if(r.size>0&&i+l.length>r.size){let c=new se(`content size at ${r.url} over limit: ${r.size}`,"max-size");throw n.destroy(c),c}i+=l.length,a.push(l)}}catch(l){throw l instanceof Tr?l:new se(`Invalid response body while trying to fetch ${r.url}: ${l.message}`,"system",l)}if(n.readableEnded===!0||n._readableState.ended===!0)try{return a.every(l=>typeof l=="string")?X.Buffer.from(a.join("")):X.Buffer.concat(a,i)}catch(l){throw new se(`Could not create Buffer from response body for ${r.url}: ${l.message}`,"system",l)}else throw new se(`Premature close of server response while trying to fetch ${r.url}`)}var Cn=(r,n)=>{let a,i,{body:l}=r[G];if(r.bodyUsed)throw new Error("cannot clone body after it is used");return l instanceof ce.default&&typeof l.getBoundary!="function"&&(a=new ce.PassThrough({highWaterMark:n}),i=new ce.PassThrough({highWaterMark:n}),l.pipe(a),l.pipe(i),r[G].stream=a,l=i),l},hl=(0,at.deprecate)(r=>r.getBoundary(),"form-data doesn't follow the spec and requires special treatment. Use alternative package","https://github.com/node-fetch/node-fetch/issues/1167"),Wa=(r,n)=>r===null?null:typeof r=="string"?"text/plain;charset=UTF-8":Da(r)?"application/x-www-form-urlencoded;charset=UTF-8":Sr(r)?r.type||null:X.Buffer.isBuffer(r)||at.types.isAnyArrayBuffer(r)||ArrayBuffer.isView(r)?null:r instanceof ot?`multipart/form-data; boundary=${n[G].boundary}`:r&&typeof r.getBoundary=="function"?`multipart/form-data;boundary=${hl(r)}`:r instanceof ce.default?null:"text/plain;charset=UTF-8",ml=r=>{let{body:n}=r[G];return n===null?0:Sr(n)?n.size:X.Buffer.isBuffer(n)?n.length:n&&typeof n.getLengthSync=="function"&&n.hasKnownLength&&n.hasKnownLength()?n.getLengthSync():null},pl=async(r,{body:n})=>{n===null?r.end():await dl(n,r)},wa=H(require("util")),wr=H(require("http")),yr=typeof wr.default.validateHeaderName=="function"?wr.default.validateHeaderName:r=>{if(!/^[\^`\-\w!#$%&'*+.|~]+$/.test(r)){let n=new TypeError(`Header name must be a valid HTTP token [${r}]`);throw Object.defineProperty(n,"code",{value:"ERR_INVALID_HTTP_TOKEN"}),n}},Tn=typeof wr.default.validateHeaderValue=="function"?wr.default.validateHeaderValue:(r,n)=>{if(/[^\t\u0020-\u007E\u0080-\u00FF]/.test(n)){let a=new TypeError(`Invalid character in header content ["${r}"]`);throw Object.defineProperty(a,"code",{value:"ERR_INVALID_CHAR"}),a}},ye=class extends URLSearchParams{constructor(r){let n=[];if(r instanceof ye){let a=r.raw();for(let[i,l]of Object.entries(a))n.push(...l.map(c=>[i,c]))}else if(r!=null)if(typeof r=="object"&&!wa.types.isBoxedPrimitive(r)){let a=r[Symbol.iterator];if(a==null)n.push(...Object.entries(r));else{if(typeof a!="function")throw new TypeError("Header pairs must be iterable");n=[...r].map(i=>{if(typeof i!="object"||wa.types.isBoxedPrimitive(i))throw new TypeError("Each header pair must be an iterable object");return[...i]}).map(i=>{if(i.length!==2)throw new TypeError("Each header pair must be a name/value tuple");return[...i]})}}else throw new TypeError("Failed to construct 'Headers': The provided value is not of type '(sequence<sequence<ByteString>> or record<ByteString, ByteString>)");return n=n.length>0?n.map(([a,i])=>(yr(a),Tn(a,String(i)),[String(a).toLowerCase(),String(i)])):void 0,super(n),new Proxy(this,{get(a,i,l){switch(i){case"append":case"set":return(c,m)=>(yr(c),Tn(c,String(m)),URLSearchParams.prototype[i].call(a,String(c).toLowerCase(),String(m)));case"delete":case"has":case"getAll":return c=>(yr(c),URLSearchParams.prototype[i].call(a,String(c).toLowerCase()));case"keys":return()=>(a.sort(),new Set(URLSearchParams.prototype.keys.call(a)).keys());default:return Reflect.get(a,i,l)}}})}get[Symbol.toStringTag](){return this.constructor.name}toString(){return Object.prototype.toString.call(this)}get(r){let n=this.getAll(r);if(n.length===0)return null;let a=n.join(", ");return/^content-encoding$/i.test(r)&&(a=a.toLowerCase()),a}forEach(r,n=void 0){for(let a of this.keys())Reflect.apply(r,n,[this.get(a),a,this])}*values(){for(let r of this.keys())yield this.get(r)}*entries(){for(let r of this.keys())yield[r,this.get(r)]}[Symbol.iterator](){return this.entries()}raw(){return[...this.keys()].reduce((r,n)=>(r[n]=this.getAll(n),r),{})}[Symbol.for("nodejs.util.inspect.custom")](){return[...this.keys()].reduce((r,n)=>{let a=this.getAll(n);return n==="host"?r[n]=a[0]:r[n]=a.length>1?a:a[0],r},{})}};Object.defineProperties(ye.prototype,["get","entries","forEach","values"].reduce((r,n)=>(r[n]={enumerable:!0},r),{}));function bl(r=[]){return new ye(r.reduce((n,a,i,l)=>(i%2===0&&n.push(l.slice(i,i+2)),n),[]).filter(([n,a])=>{try{return yr(n),Tn(n,String(a)),!0}catch{return!1}}))}var gl=new Set([301,302,303,307,308]),An=r=>gl.has(r),ie=Symbol("Response internals"),J=class extends Pt{constructor(r=null,n={}){super(r,n);let a=n.status!=null?n.status:200,i=new ye(n.headers);if(r!==null&&!i.has("Content-Type")){let l=Wa(r,this);l&&i.append("Content-Type",l)}this[ie]={type:"default",url:n.url,status:a,statusText:n.statusText||"",headers:i,counter:n.counter,highWaterMark:n.highWaterMark}}get type(){return this[ie].type}get url(){return this[ie].url||""}get status(){return this[ie].status}get ok(){return this[ie].status>=200&&this[ie].status<300}get redirected(){return this[ie].counter>0}get statusText(){return this[ie].statusText}get headers(){return this[ie].headers}get highWaterMark(){return this[ie].highWaterMark}clone(){return new J(Cn(this,this.highWaterMark),{type:this.type,url:this.url,status:this.status,statusText:this.statusText,headers:this.headers,ok:this.ok,redirected:this.redirected,size:this.size,highWaterMark:this.highWaterMark})}static redirect(r,n=302){if(!An(n))throw new RangeError('Failed to execute "redirect" on "response": Invalid status code');return new J(null,{headers:{location:new URL(r).toString()},status:n})}static error(){let r=new J(null,{status:0,statusText:""});return r[ie].type="error",r}static json(r=void 0,n={}){let a=JSON.stringify(r);if(a===void 0)throw new TypeError("data is not JSON serializable");let i=new ye(n&&n.headers);return i.has("content-type")||i.set("content-type","application/json"),new J(a,{...n,headers:i})}get[Symbol.toStringTag](){return"Response"}};Object.defineProperties(J.prototype,{type:{enumerable:!0},url:{enumerable:!0},status:{enumerable:!0},ok:{enumerable:!0},redirected:{enumerable:!0},statusText:{enumerable:!0},headers:{enumerable:!0},clone:{enumerable:!0}});var yl=H(require("url")),_l=H(require("util")),Sl=r=>{if(r.search)return r.search;let n=r.href.length-1,a=r.hash||(r.href[n]==="#"?"#":"");return r.href[n-a.length]==="?"?"?":""},wl=H(require("net"));function va(r,n=!1){return r==null||(r=new URL(r),/^(about|blob|data):$/.test(r.protocol))?"no-referrer":(r.username="",r.password="",r.hash="",n&&(r.pathname="",r.search=""),r)}var Fa=new Set(["","no-referrer","no-referrer-when-downgrade","same-origin","origin","strict-origin","origin-when-cross-origin","strict-origin-when-cross-origin","unsafe-url"]),vl="strict-origin-when-cross-origin";function Rl(r){if(!Fa.has(r))throw new TypeError(`Invalid referrerPolicy: ${r}`);return r}function Tl(r){if(/^(http|ws)s:$/.test(r.protocol))return!0;let n=r.host.replace(/(^\[)|(]$)/g,""),a=(0,wl.isIP)(n);return a===4&&/^127\./.test(n)||a===6&&/^(((0+:){7})|(::(0+:){0,6}))0*1$/.test(n)?!0:r.host==="localhost"||r.host.endsWith(".localhost")?!1:r.protocol==="file:"}function rt(r){return/^about:(blank|srcdoc)$/.test(r)||r.protocol==="data:"||/^(blob|filesystem):$/.test(r.protocol)?!0:Tl(r)}function El(r,{referrerURLCallback:n,referrerOriginCallback:a}={}){if(r.referrer==="no-referrer"||r.referrerPolicy==="")return null;let i=r.referrerPolicy;if(r.referrer==="about:client")return"no-referrer";let l=r.referrer,c=va(l),m=va(l,!0);c.toString().length>4096&&(c=m),n&&(c=n(c)),a&&(m=a(m));let h=new URL(r.url);switch(i){case"no-referrer":return"no-referrer";case"origin":return m;case"unsafe-url":return c;case"strict-origin":return rt(c)&&!rt(h)?"no-referrer":m.toString();case"strict-origin-when-cross-origin":return c.origin===h.origin?c:rt(c)&&!rt(h)?"no-referrer":m;case"same-origin":return c.origin===h.origin?c:"no-referrer";case"origin-when-cross-origin":return c.origin===h.origin?c:m;case"no-referrer-when-downgrade":return rt(c)&&!rt(h)?"no-referrer":c;default:throw new TypeError(`Invalid referrerPolicy: ${i}`)}}function Cl(r){let n=(r.get("referrer-policy")||"").split(/[,\s]+/),a="";for(let i of n)i&&Fa.has(i)&&(a=i);return a}var M=Symbol("Request internals"),At=r=>typeof r=="object"&&typeof r[M]=="object",Al=(0,_l.deprecate)(()=>{},".data is not a valid RequestInit property, use .body instead","https://github.com/node-fetch/node-fetch/issues/1000 (request)"),kt=class extends Pt{constructor(r,n={}){let a;if(At(r)?a=new URL(r.url):(a=new URL(r),r={}),a.username!==""||a.password!=="")throw new TypeError(`${a} is an url with embedded credentials.`);let i=n.method||r.method||"GET";if(/^(delete|get|head|options|post|put)$/i.test(i)&&(i=i.toUpperCase()),!At(n)&&"data"in n&&Al(),(n.body!=null||At(r)&&r.body!==null)&&(i==="GET"||i==="HEAD"))throw new TypeError("Request with GET/HEAD method cannot have body");let l=n.body?n.body:At(r)&&r.body!==null?Cn(r):null;super(l,{size:n.size||r.size||0});let c=new ye(n.headers||r.headers||{});if(l!==null&&!c.has("Content-Type")){let g=Wa(l,this);g&&c.set("Content-Type",g)}let m=At(r)?r.signal:null;if("signal"in n&&(m=n.signal),m!=null&&!ul(m))throw new TypeError("Expected signal to be an instanceof AbortSignal or EventTarget");let h=n.referrer==null?r.referrer:n.referrer;if(h==="")h="no-referrer";else if(h){let g=new URL(h);h=/^about:(\/\/)?client$/.test(g)?"client":g}else h=void 0;this[M]={method:i,redirect:n.redirect||r.redirect||"follow",headers:c,parsedURL:a,signal:m,referrer:h},this.follow=n.follow===void 0?r.follow===void 0?20:r.follow:n.follow,this.compress=n.compress===void 0?r.compress===void 0?!0:r.compress:n.compress,this.counter=n.counter||r.counter||0,this.agent=n.agent||r.agent,this.highWaterMark=n.highWaterMark||r.highWaterMark||16384,this.insecureHTTPParser=n.insecureHTTPParser||r.insecureHTTPParser||!1,this.referrerPolicy=n.referrerPolicy||r.referrerPolicy||""}get method(){return this[M].method}get url(){return(0,yl.format)(this[M].parsedURL)}get headers(){return this[M].headers}get redirect(){return this[M].redirect}get signal(){return this[M].signal}get referrer(){if(this[M].referrer==="no-referrer")return"";if(this[M].referrer==="client")return"about:client";if(this[M].referrer)return this[M].referrer.toString()}get referrerPolicy(){return this[M].referrerPolicy}set referrerPolicy(r){this[M].referrerPolicy=Rl(r)}clone(){return new kt(this)}get[Symbol.toStringTag](){return"Request"}};Object.defineProperties(kt.prototype,{method:{enumerable:!0},url:{enumerable:!0},headers:{enumerable:!0},redirect:{enumerable:!0},clone:{enumerable:!0},signal:{enumerable:!0},referrer:{enumerable:!0},referrerPolicy:{enumerable:!0}});var Pl=r=>{let{parsedURL:n}=r[M],a=new ye(r[M].headers);a.has("Accept")||a.set("Accept","*/*");let i=null;if(r.body===null&&/^(post|put)$/i.test(r.method)&&(i="0"),r.body!==null){let h=ml(r);typeof h=="number"&&!Number.isNaN(h)&&(i=String(h))}i&&a.set("Content-Length",i),r.referrerPolicy===""&&(r.referrerPolicy=vl),r.referrer&&r.referrer!=="no-referrer"?r[M].referrer=El(r):r[M].referrer="no-referrer",r[M].referrer instanceof URL&&a.set("Referer",r.referrer),a.has("User-Agent")||a.set("User-Agent","node-fetch"),r.compress&&!a.has("Accept-Encoding")&&a.set("Accept-Encoding","gzip, deflate, br");let{agent:l}=r;typeof l=="function"&&(l=l(n));let c=Sl(n),m={path:n.pathname+c,method:r.method,headers:a[Symbol.for("nodejs.util.inspect.custom")](),insecureHTTPParser:r.insecureHTTPParser,agent:l};return{parsedURL:n,options:m}},Ia=class extends Tr{constructor(r,n="aborted"){super(r,n)}};Rr();Ba();var kl=new Set(["data:","http:","https:"]);async function La(r,n){return new Promise((a,i)=>{let l=new kt(r,n),{parsedURL:c,options:m}=Pl(l);if(!kl.has(c.protocol))throw new TypeError(`node-fetch cannot load ${r}. URL scheme "${c.protocol.replace(/:$/,"")}" is not supported.`);if(c.protocol==="data:"){let p=ll(l.url),b=new J(p,{headers:{"Content-Type":p.typeFull}});a(b);return}let h=(c.protocol==="https:"?il.default:al.default).request,{signal:g}=l,y=null,q=()=>{let p=new Ia("The operation was aborted.");i(p),l.body&&l.body instanceof ue.default.Readable&&l.body.destroy(p),!(!y||!y.body)&&y.body.emit("error",p)};if(g&&g.aborted){q();return}let I=()=>{q(),S()},O=h(c.toString(),m);g&&g.addEventListener("abort",I);let S=()=>{O.abort(),g&&g.removeEventListener("abort",I)};O.on("error",p=>{i(new se(`request to ${l.url} failed, reason: ${p.message}`,"system",p)),S()}),Bl(O,p=>{y&&y.body&&y.body.destroy(p)}),process.version<"v14"&&O.on("socket",p=>{let b;p.prependListener("end",()=>{b=p._eventsCount}),p.prependListener("close",C=>{if(y&&b<p._eventsCount&&!C){let v=new Error("Premature close");v.code="ERR_STREAM_PREMATURE_CLOSE",y.body.emit("error",v)}})}),O.on("response",p=>{O.setTimeout(0);let b=bl(p.rawHeaders);if(An(p.statusCode)){let D=b.get("Location"),L=null;try{L=D===null?null:new URL(D,l.url)}catch{if(l.redirect!=="manual"){i(new se(`uri requested responds with an invalid redirect URL: ${D}`,"invalid-redirect")),S();return}}switch(l.redirect){case"error":i(new se(`uri requested responds with a redirect, redirect mode is set to error: ${l.url}`,"no-redirect")),S();return;case"manual":break;case"follow":{if(L===null)break;if(l.counter>=l.follow){i(new se(`maximum redirect reached at: ${l.url}`,"max-redirect")),S();return}let z={headers:new ye(l.headers),follow:l.follow,counter:l.counter+1,agent:l.agent,compress:l.compress,method:l.method,body:Cn(l),signal:l.signal,size:l.size,referrer:l.referrer,referrerPolicy:l.referrerPolicy};if(!cl(l.url,L)||!fl(l.url,L))for(let fe of["authorization","www-authenticate","cookie","cookie2"])z.headers.delete(fe);if(p.statusCode!==303&&l.body&&n.body instanceof ue.default.Readable){i(new se("Cannot follow redirect with body being a readable stream","unsupported-redirect")),S();return}(p.statusCode===303||(p.statusCode===301||p.statusCode===302)&&l.method==="POST")&&(z.method="GET",z.body=void 0,z.headers.delete("content-length"));let qe=Cl(b);qe&&(z.referrerPolicy=qe),a(La(new kt(L,z))),S();return}default:return i(new TypeError(`Redirect option '${l.redirect}' is not a valid value of RequestRedirect`))}}g&&p.once("end",()=>{g.removeEventListener("abort",I)});let C=(0,ue.pipeline)(p,new ue.PassThrough,D=>{D&&i(D)});process.version<"v12.10"&&p.on("aborted",I);let v={url:l.url,status:p.statusCode,statusText:p.statusMessage,headers:b,size:l.size,counter:l.counter,highWaterMark:l.highWaterMark},U=b.get("Content-Encoding");if(!l.compress||l.method==="HEAD"||U===null||p.statusCode===204||p.statusCode===304){y=new J(C,v),a(y);return}let N={flush:tt.default.Z_SYNC_FLUSH,finishFlush:tt.default.Z_SYNC_FLUSH};if(U==="gzip"||U==="x-gzip"){C=(0,ue.pipeline)(C,tt.default.createGunzip(N),D=>{D&&i(D)}),y=new J(C,v),a(y);return}if(U==="deflate"||U==="x-deflate"){let D=(0,ue.pipeline)(p,new ue.PassThrough,L=>{L&&i(L)});D.once("data",L=>{(L[0]&15)===8?C=(0,ue.pipeline)(C,tt.default.createInflate(),z=>{z&&i(z)}):C=(0,ue.pipeline)(C,tt.default.createInflateRaw(),z=>{z&&i(z)}),y=new J(C,v),a(y)}),D.once("end",()=>{y||(y=new J(C,v),a(y))});return}if(U==="br"){C=(0,ue.pipeline)(C,tt.default.createBrotliDecompress(),D=>{D&&i(D)}),y=new J(C,v),a(y);return}y=new J(C,v),a(y)}),pl(O,l).catch(i)})}function Bl(r,n){let a=br.Buffer.from(`0\r
\r
`),i=!1,l=!1,c;r.on("response",m=>{let{headers:h}=m;i=h["transfer-encoding"]==="chunked"&&!h["content-length"]}),r.on("socket",m=>{let h=()=>{if(i&&!l){let y=new Error("Premature close");y.code="ERR_STREAM_PREMATURE_CLOSE",n(y)}},g=y=>{l=br.Buffer.compare(y.slice(-5),a)===0,!l&&c&&(l=br.Buffer.compare(c.slice(-3),a.slice(0,3))===0&&br.Buffer.compare(y.slice(-2),a.slice(3))===0),c=y};m.prependListener("close",h),m.on("data",g),r.on("close",()=>{m.removeListener("close",h),m.removeListener("data",g)})})}Ot();Rr();});var Bn=Ke((gu,za)=>{"use strict";var $a=require("fs"),kn;function Ol(){try{return $a.statSync("/.dockerenv"),!0}catch{return!1}}function ql(){try{return $a.readFileSync("/proc/self/cgroup","utf8").includes("docker")}catch{return!1}}za.exports=()=>(kn===void 0&&(kn=Ol()||ql()),kn)});var Ua=Ke((yu,On)=>{"use strict";var Dl=require("os"),Wl=require("fs"),ja=Bn(),Ma=()=>{if(process.platform!=="linux")return!1;if(Dl.release().toLowerCase().includes("microsoft"))return!ja();try{return Wl.readFileSync("/proc/version","utf8").toLowerCase().includes("microsoft")?!ja():!1}catch{return!1}};process.env.__IS_WSL_TEST__?On.exports=Ma:On.exports=Ma()});var Ha=Ke((_u,Na)=>{"use strict";Na.exports=(r,n,a)=>{let i=l=>Object.defineProperty(r,n,{value:l,enumerable:!0,writable:!0});return Object.defineProperty(r,n,{configurable:!0,enumerable:!0,get(){let l=a();return i(l),l},set(l){i(l)}}),r}});var Xa=Ke((Su,Ka)=>{var Fl=require("path"),Il=require("child_process"),{promises:Cr,constants:Ja}=require("fs"),Er=Ua(),Ll=Bn(),Dn=Ha(),Va=Fl.join(__dirname,"xdg-open"),{platform:it,arch:Ya}=process,xl=()=>{try{return Cr.statSync("/run/.containerenv"),!0}catch{return!1}},qn;function $l(){return qn===void 0&&(qn=xl()||Ll()),qn}var zl=(()=>{let r="/mnt/",n;return async function(){if(n)return n;let a="/etc/wsl.conf",i=!1;try{await Cr.access(a,Ja.F_OK),i=!0}catch{}if(!i)return r;let l=await Cr.readFile(a,{encoding:"utf8"}),c=/(?<!#.*)root\s*=\s*(?<mountPoint>.*)/g.exec(l);return c?(n=c.groups.mountPoint.trim(),n=n.endsWith("/")?n:`${n}/`,n):r}})(),Ga=async(r,n)=>{let a;for(let i of r)try{return await n(i)}catch(l){a=l}throw a},Ar=async r=>{if(r={wait:!1,background:!1,newInstance:!1,allowNonzeroExitCode:!1,...r},Array.isArray(r.app))return Ga(r.app,h=>Ar({...r,app:h}));let{name:n,arguments:a=[]}=r.app||{};if(a=[...a],Array.isArray(n))return Ga(n,h=>Ar({...r,app:{name:h,arguments:a}}));let i,l=[],c={};if(it==="darwin")i="open",r.wait&&l.push("--wait-apps"),r.background&&l.push("--background"),r.newInstance&&l.push("--new"),n&&l.push("-a",n);else if(it==="win32"||Er&&!$l()&&!n){let h=await zl();i=Er?`${h}c/Windows/System32/WindowsPowerShell/v1.0/powershell.exe`:`${process.env.SYSTEMROOT}\\System32\\WindowsPowerShell\\v1.0\\powershell`,l.push("-NoProfile","-NonInteractive","\u2013ExecutionPolicy","Bypass","-EncodedCommand"),Er||(c.windowsVerbatimArguments=!0);let g=["Start"];r.wait&&g.push("-Wait"),n?(g.push(`"\`"${n}\`""`,"-ArgumentList"),r.target&&a.unshift(r.target)):r.target&&g.push(`"${r.target}"`),a.length>0&&(a=a.map(y=>`"\`"${y}\`""`),g.push(a.join(","))),r.target=Buffer.from(g.join(" "),"utf16le").toString("base64")}else{if(n)i=n;else{let h=!__dirname||__dirname==="/",g=!1;try{await Cr.access(Va,Ja.X_OK),g=!0}catch{}i=process.versions.electron||it==="android"||h||!g?"xdg-open":Va}a.length>0&&l.push(...a),r.wait||(c.stdio="ignore",c.detached=!0)}r.target&&l.push(r.target),it==="darwin"&&a.length>0&&l.push("--args",...a);let m=Il.spawn(i,l,c);return r.wait?new Promise((h,g)=>{m.once("error",g),m.once("close",y=>{if(!r.allowNonzeroExitCode&&y>0){g(new Error(`Exited with code ${y}`));return}h(m)})}):(m.unref(),m)},Wn=(r,n)=>{if(typeof r!="string")throw new TypeError("Expected a `target`");return Ar({...n,target:r})},jl=(r,n)=>{if(typeof r!="string")throw new TypeError("Expected a `name`");let{arguments:a=[]}=n||{};if(a!=null&&!Array.isArray(a))throw new TypeError("Expected `appArguments` as Array type");return Ar({...n,app:{name:r,arguments:a}})};function Qa(r){if(typeof r=="string"||Array.isArray(r))return r;let{[Ya]:n}=r;if(!n)throw new Error(`${Ya} is not supported`);return n}function Fn({[it]:r},{wsl:n}){if(n&&Er)return Qa(n);if(!r)throw new Error(`${it} is not supported`);return Qa(r)}var Pr={};Dn(Pr,"chrome",()=>Fn({darwin:"google chrome",win32:"chrome",linux:["google-chrome","google-chrome-stable","chromium"]},{wsl:{ia32:"/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe",x64:["/mnt/c/Program Files/Google/Chrome/Application/chrome.exe","/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe"]}}));Dn(Pr,"firefox",()=>Fn({darwin:"firefox",win32:"C:\\Program Files\\Mozilla Firefox\\firefox.exe",linux:"firefox"},{wsl:"/mnt/c/Program Files/Mozilla Firefox/firefox.exe"}));Dn(Pr,"edge",()=>Fn({darwin:"microsoft edge",win32:"msedge",linux:["microsoft-edge","microsoft-edge-dev"]},{wsl:"/mnt/c/Program Files (x86)/Microsoft/Edge/Application/msedge.exe"}));Wn.apps=Pr;Wn.openApp=jl;Ka.exports=Wn});var Hl={};xs(Hl,{default:()=>Or});module.exports=$s(Hl);var ke=require("@oclif/core");var Xe=oe(require("node:fs")),dr=oe(require("node:path")),ta=oe(require("node:os"));var Rt={production:{url:"https://www.raycast.com",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},staging:{url:"https://www.rayca.st",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},development:{url:"http://localhost:3000",clientID:"rOCD_LhnXFObSLu9v7788qOGu25FZDqEzGjet4Q45ZY"}}[process.env.API_ENV||"production"],zs="e69bae0ec90f5e838555",$={},ra;function na(r){ra=r;try{$=JSON.parse(Xe.readFileSync(dr.join(aa(),"config.json"),"utf8"))}catch(n){if(n instanceof Error&&n.code==="ENOENT")return;throw new Error(`Failed to read config file: ${n}`)}}function Ae(r){switch(r){case"raycastApiURL":return process.env.RAY_APIURL||$.APIURL||Rt.url;case"raycastAccessToken":return process.env.RAY_TOKEN||$.Token||$.AccessToken||"";case"raycastClientId":return process.env.RAY_CLIENT_ID||$.ClientID||Rt.clientID;case"githubClientId":return process.env.RAY_GithubClientID||$.GithubClientID||zs;case"githubAccessToken":return process.env.GITHUB_ACCESS_TOKEN||$.GithubAccessToken||"";case"flavorName":return process.env.RAY_Target?process.env.RAY_Target:typeof $.Target<"u"?$.Target:fn(process.platform==="win32"?"x":"release")}}function oa(r,n){switch(r){case"raycastApiURL":n===void 0?delete $.APIURL:$.APIURL=n;break;case"raycastAccessToken":n===void 0?delete $.Token:$.Token=n,delete $.AccessToken;break;case"raycastClientId":n===void 0?delete $.ClientID:$.ClientID=n;break;case"githubAccessToken":n===void 0?delete $.GithubAccessToken:$.GithubAccessToken=n;break;case"flavorName":n===void 0?delete $.Target:$.Target=n;break}let a=aa();Xe.writeFileSync(dr.join(a,"config.json"),JSON.stringify($,null,"  "),"utf8")}function fn(r){switch(r){case"debug":return"debug";case"internal":return"internal";case"release":return"";case"x":return"x";case"x-internal":return"x-internal";case"x-development":return"x-development";case void 0:return Ae("flavorName")}}function js(){let r=fn(ra);return r==""?"raycast":`raycast-${r}`}function aa(){let r=dr.join(ta.default.homedir(),".config",js());return Xe.mkdirSync(r,{recursive:!0}),r}var x=oe(sa());var Zl=(0,x.blue)((0,x.dim)("internal only"));function dn(r,n,a){console.log(Pe[r]+n),typeof a?.exit<"u"&&process.exit(a.exit)}var Pe={wait:`\u{1F550}${(0,x.blue)("wait")}  - `,info:`${process.env.GITHUB_ACTIONS==="true"?"\u2139\uFE0F":"\u2139\uFE0F "}${(0,x.cyan)("info")}  - `,success:`\u2705${(0,x.green)("ready")}  - `,warn:`${process.env.GITHUB_ACTIONS==="true"?"\u26A0\uFE0F":"\u26A0\uFE0F "}${(0,x.yellow)("warn")}  - `,error:`\u{1F4A5}${(0,x.red)("error")}  - `,event:`\u26A1\uFE0F${(0,x.magenta)("event")}  - `,paymentPrompt:`\u{1F4B0}${(0,x.yellowBright)("plan")}  - `},Ns=!0;function la(r,n){r||(Pe.wait=`${(0,x.blue)("wait")}  - `,Pe.info=`${(0,x.cyan)("info")}  - `,Pe.success=`${(0,x.green)("ready")}  - `,Pe.warn=`${(0,x.yellow)("warn")}  - `,Pe.error=`${(0,x.red)("error")}  - `,Pe.event=`${(0,x.magenta)("event")}  - `,Pe.paymentPrompt=`${(0,x.yellowBright)("plan")}  - `),n&&(Ns=!1)}var hr=class extends ke.Command{static baseFlags={"exit-on-error":ke.Flags.boolean({default:!0,helpGroup:"GLOBAL",aliases:["exitOnError"],deprecateAliases:!0,summary:"Always exit with non-zero code on error",allowNo:!0}),emoji:ke.Flags.boolean({default:!1,helpGroup:"GLOBAL",summary:"Prefix output with emojis \u{1F308}"}),help:ke.Flags.boolean({default:!1,helpGroup:"GLOBAL",summary:"Show the help message for the command"}),"non-interactive":ke.Flags.boolean({char:"I",default:!1,helpGroup:"GLOBAL",summary:"Disable interactive outputs, useful for CI"}),target:ke.Flags.option({char:"t",description:"Raycast app target",helpGroup:"GLOBAL",multiple:!1,options:["debug","internal","release","x","x-development","x-internal"],hidden:!0})()};flags;args;async init(){await super.init(),process.on("SIGINT",()=>process.exit(1));let{args:n,flags:a}=await this.parse({flags:this.ctor.flags,baseFlags:super.ctor.baseFlags,enableJsonFlag:this.ctor.enableJsonFlag,args:this.ctor.args,strict:this.ctor.strict});this.flags=a,this.args=n,na(this.flags.target),la(this.flags.emoji,this.flags["non-interactive"])}error(n,a){return a?.message&&n instanceof Error&&(n.message=`${a.message} (${n.message})`,delete a.message),super.error(n,a)}async catch(n){return super.catch(n)}async finally(n){return super.finally(n)}};var oi=oe(require("http")),ai=oe(require("url")),kr=oe(require("querystring")),Br=oe(require("crypto"));var ii=oe(Pn()),st=require("@oclif/core");var Za=require("@oclif/core"),ei=oe(Pn()),ti=oe(Xa());async function ri(r,n){let a;try{a=await(0,ei.default)(r,{method:n.method||"GET",headers:{"Content-Type":"application/json",Accept:"application/json",...n.token?{Authorization:`Bearer ${n.token}`}:void 0},body:n.body})}catch(i){throw new Error(`HTTP request: ${i.message}`)}if(!a.ok){switch(a.status){case 401:throw new _e(a,"not authorized - please log in first using `npx ray login`");case 403:throw new _e(a,"forbidden - you don't have permissions to perform the request");case 402:throw new _e(a,"the limit of free commands has been reached")}let i=await a.text(),l;try{l=JSON.parse(i)}catch{throw new _e(a,`HTTP error: ${a.status} - ${i}`)}throw Array.isArray(l.errors)&&l.errors.length>0?new _e(a,`error: ${l.errors[0].status} - ${l.errors[0].title}`):new _e(a,`HTTP error: ${a.status} - ${i}`)}return await a.json()}var _e=class extends Error{constructor(n,a){let i=n.headers.get("X-Request-Id");i?super(`${a} (${n.url} RequestID: ${i})`):super(a),this.name="HTTPError"}};function ni(r){(0,ti.default)(r).catch(n=>{Za.ux.error(new Error(`failed opening browser to URL ${r}: ${n.message}`),{exit:1})})}var Ml=`${Rt.url}/sessions/success`,In=`${Rt.url}/sessions/failure`;async function si(){let r=Ae("raycastClientId"),n=Ae("raycastApiURL"),a=Br.randomBytes(32).toString("hex"),l=Br.createHash("sha256").update(a).digest("base64url").replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,""),c=oi.createServer(),g=`http://127.0.0.1:${(await new Promise((I,O)=>{let S=c.listen(0,"127.0.0.1",()=>I(S));S.on("error",O)})).address().port}`,y=`${n}/oauth/authorize?response_type=code&client_id=${r}&code_challenge=${l}&code_challenge_method=S256&redirect_uri=${g}`;function q(I){I.close()}c.on("request",async(I,O)=>{try{let p=kr.parse(ai.parse(I.url).query).code;p||(O.writeHead(302,{Location:In}),O.end(),q(c),st.ux.error(new Error("failed authorizing app"),{exit:1}));let b=await Ul(a,p,g);b||(O.writeHead(302,{Location:In}),O.end(),q(c),st.ux.error(new Error("failed getting access token"),{exit:1}));try{oa("raycastAccessToken",b)}catch(v){console.log(v),O.writeHead(302,{Location:In}),O.end(),q(c),st.ux.error(new Error("failed saving access token"),{exit:1})}O.writeHead(302,{Location:Ml}),O.end();let C=await Nl();C||st.ux.error(new Error("failed fetching profile"),{exit:1}),dn("success",`logged in as '${C.username}'`),q(c)}catch(S){q(c),st.ux.error(S,{exit:1})}}),dn("info","continue login in your browser"),ni(y),await new Promise(I=>c.on("close",I))}async function Ul(r,n,a){let i=Ae("raycastClientId"),c=`${Ae("raycastApiURL")}/oauth/token`,m=kr.stringify({grant_type:"authorization_code",client_id:i,code_verifier:r,code:n,redirect_uri:a});try{let h=await(0,ii.default)(c,{method:"POST",body:m,headers:{"Content-Type":"application/x-www-form-urlencoded"}});if(!h.ok)throw new Error(h.statusText);return(await h.json()).access_token}catch(h){throw new Error(`failed getting access token: ${h.message}`)}}function Nl(){let r=Ae("raycastApiURL"),n=Ae("raycastAccessToken");return ri(`${r}/api/v1/me`,{token:n})}var Or=class extends hr{static description="Log into your Raycast account";async run(){await si()}};
/*! Bundled license information:

node-fetch-cjs/dist/index.js:
  (*! fetch-blob. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
  (*! formdata-polyfill. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
  (*! node-domexception. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
*/
