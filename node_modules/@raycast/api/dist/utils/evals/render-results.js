"use strict";var Be=Object.create;var R=Object.defineProperty;var Ae=Object.getOwnPropertyDescriptor;var $e=Object.getOwnPropertyNames;var Ie=Object.getPrototypeOf,Se=Object.prototype.hasOwnProperty;var m=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Pe=(e,t)=>{for(var r in t)R(e,r,{get:t[r],enumerable:!0})},U=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of $e(t))!Se.call(e,o)&&o!==r&&R(e,o,{get:()=>t[o],enumerable:!(n=Ae(t,o))||n.enumerable});return e};var _e=(e,t,r)=>(r=e!=null?Be(Ie(e)):{},U(t||!e||!e.__esModule?R(r,"default",{value:e,enumerable:!0}):r,e)),Fe=e=>U(R({},"__esModule",{value:!0}),e);var W=m((pt,Y)=>{"use strict";Y.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}});var P=m((bt,X)=>{var E=W(),K={};for(let e of Object.keys(E))K[E[e]]=e;var i={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};X.exports=i;for(let e of Object.keys(i)){if(!("channels"in i[e]))throw new Error("missing channels property: "+e);if(!("labels"in i[e]))throw new Error("missing channel labels property: "+e);if(i[e].labels.length!==i[e].channels)throw new Error("channel and label counts mismatch: "+e);let{channels:t,labels:r}=i[e];delete i[e].channels,delete i[e].labels,Object.defineProperty(i[e],"channels",{value:t}),Object.defineProperty(i[e],"labels",{value:r})}i.rgb.hsl=function(e){let t=e[0]/255,r=e[1]/255,n=e[2]/255,o=Math.min(t,r,n),s=Math.max(t,r,n),l=s-o,c,a;s===o?c=0:t===s?c=(r-n)/l:r===s?c=2+(n-t)/l:n===s&&(c=4+(t-r)/l),c=Math.min(c*60,360),c<0&&(c+=360);let u=(o+s)/2;return s===o?a=0:u<=.5?a=l/(s+o):a=l/(2-s-o),[c,a*100,u*100]};i.rgb.hsv=function(e){let t,r,n,o,s,l=e[0]/255,c=e[1]/255,a=e[2]/255,u=Math.max(l,c,a),g=u-Math.min(l,c,a),h=function(b){return(u-b)/6/g+1/2};return g===0?(o=0,s=0):(s=g/u,t=h(l),r=h(c),n=h(a),l===u?o=n-r:c===u?o=1/3+t-n:a===u&&(o=2/3+r-t),o<0?o+=1:o>1&&(o-=1)),[o*360,s*100,u*100]};i.rgb.hwb=function(e){let t=e[0],r=e[1],n=e[2],o=i.rgb.hsl(e)[0],s=1/255*Math.min(t,Math.min(r,n));return n=1-1/255*Math.max(t,Math.max(r,n)),[o,s*100,n*100]};i.rgb.cmyk=function(e){let t=e[0]/255,r=e[1]/255,n=e[2]/255,o=Math.min(1-t,1-r,1-n),s=(1-t-o)/(1-o)||0,l=(1-r-o)/(1-o)||0,c=(1-n-o)/(1-o)||0;return[s*100,l*100,c*100,o*100]};function Le(e,t){return(e[0]-t[0])**2+(e[1]-t[1])**2+(e[2]-t[2])**2}i.rgb.keyword=function(e){let t=K[e];if(t)return t;let r=1/0,n;for(let o of Object.keys(E)){let s=E[o],l=Le(e,s);l<r&&(r=l,n=o)}return n};i.keyword.rgb=function(e){return E[e]};i.rgb.xyz=function(e){let t=e[0]/255,r=e[1]/255,n=e[2]/255;t=t>.04045?((t+.055)/1.055)**2.4:t/12.92,r=r>.04045?((r+.055)/1.055)**2.4:r/12.92,n=n>.04045?((n+.055)/1.055)**2.4:n/12.92;let o=t*.4124+r*.3576+n*.1805,s=t*.2126+r*.7152+n*.0722,l=t*.0193+r*.1192+n*.9505;return[o*100,s*100,l*100]};i.rgb.lab=function(e){let t=i.rgb.xyz(e),r=t[0],n=t[1],o=t[2];r/=95.047,n/=100,o/=108.883,r=r>.008856?r**(1/3):7.787*r+16/116,n=n>.008856?n**(1/3):7.787*n+16/116,o=o>.008856?o**(1/3):7.787*o+16/116;let s=116*n-16,l=500*(r-n),c=200*(n-o);return[s,l,c]};i.hsl.rgb=function(e){let t=e[0]/360,r=e[1]/100,n=e[2]/100,o,s,l;if(r===0)return l=n*255,[l,l,l];n<.5?o=n*(1+r):o=n+r-n*r;let c=2*n-o,a=[0,0,0];for(let u=0;u<3;u++)s=t+1/3*-(u-1),s<0&&s++,s>1&&s--,6*s<1?l=c+(o-c)*6*s:2*s<1?l=o:3*s<2?l=c+(o-c)*(2/3-s)*6:l=c,a[u]=l*255;return a};i.hsl.hsv=function(e){let t=e[0],r=e[1]/100,n=e[2]/100,o=r,s=Math.max(n,.01);n*=2,r*=n<=1?n:2-n,o*=s<=1?s:2-s;let l=(n+r)/2,c=n===0?2*o/(s+o):2*r/(n+r);return[t,c*100,l*100]};i.hsv.rgb=function(e){let t=e[0]/60,r=e[1]/100,n=e[2]/100,o=Math.floor(t)%6,s=t-Math.floor(t),l=255*n*(1-r),c=255*n*(1-r*s),a=255*n*(1-r*(1-s));switch(n*=255,o){case 0:return[n,a,l];case 1:return[c,n,l];case 2:return[l,n,a];case 3:return[l,c,n];case 4:return[a,l,n];case 5:return[n,l,c]}};i.hsv.hsl=function(e){let t=e[0],r=e[1]/100,n=e[2]/100,o=Math.max(n,.01),s,l;l=(2-r)*n;let c=(2-r)*o;return s=r*o,s/=c<=1?c:2-c,s=s||0,l/=2,[t,s*100,l*100]};i.hwb.rgb=function(e){let t=e[0]/360,r=e[1]/100,n=e[2]/100,o=r+n,s;o>1&&(r/=o,n/=o);let l=Math.floor(6*t),c=1-n;s=6*t-l,(l&1)!==0&&(s=1-s);let a=r+s*(c-r),u,g,h;switch(l){default:case 6:case 0:u=c,g=a,h=r;break;case 1:u=a,g=c,h=r;break;case 2:u=r,g=c,h=a;break;case 3:u=r,g=a,h=c;break;case 4:u=a,g=r,h=c;break;case 5:u=c,g=r,h=a;break}return[u*255,g*255,h*255]};i.cmyk.rgb=function(e){let t=e[0]/100,r=e[1]/100,n=e[2]/100,o=e[3]/100,s=1-Math.min(1,t*(1-o)+o),l=1-Math.min(1,r*(1-o)+o),c=1-Math.min(1,n*(1-o)+o);return[s*255,l*255,c*255]};i.xyz.rgb=function(e){let t=e[0]/100,r=e[1]/100,n=e[2]/100,o,s,l;return o=t*3.2406+r*-1.5372+n*-.4986,s=t*-.9689+r*1.8758+n*.0415,l=t*.0557+r*-.204+n*1.057,o=o>.0031308?1.055*o**(1/2.4)-.055:o*12.92,s=s>.0031308?1.055*s**(1/2.4)-.055:s*12.92,l=l>.0031308?1.055*l**(1/2.4)-.055:l*12.92,o=Math.min(Math.max(0,o),1),s=Math.min(Math.max(0,s),1),l=Math.min(Math.max(0,l),1),[o*255,s*255,l*255]};i.xyz.lab=function(e){let t=e[0],r=e[1],n=e[2];t/=95.047,r/=100,n/=108.883,t=t>.008856?t**(1/3):7.787*t+16/116,r=r>.008856?r**(1/3):7.787*r+16/116,n=n>.008856?n**(1/3):7.787*n+16/116;let o=116*r-16,s=500*(t-r),l=200*(r-n);return[o,s,l]};i.lab.xyz=function(e){let t=e[0],r=e[1],n=e[2],o,s,l;s=(t+16)/116,o=r/500+s,l=s-n/200;let c=s**3,a=o**3,u=l**3;return s=c>.008856?c:(s-16/116)/7.787,o=a>.008856?a:(o-16/116)/7.787,l=u>.008856?u:(l-16/116)/7.787,o*=95.047,s*=100,l*=108.883,[o,s,l]};i.lab.lch=function(e){let t=e[0],r=e[1],n=e[2],o;o=Math.atan2(n,r)*360/2/Math.PI,o<0&&(o+=360);let l=Math.sqrt(r*r+n*n);return[t,l,o]};i.lch.lab=function(e){let t=e[0],r=e[1],o=e[2]/360*2*Math.PI,s=r*Math.cos(o),l=r*Math.sin(o);return[t,s,l]};i.rgb.ansi16=function(e,t=null){let[r,n,o]=e,s=t===null?i.rgb.hsv(e)[2]:t;if(s=Math.round(s/50),s===0)return 30;let l=30+(Math.round(o/255)<<2|Math.round(n/255)<<1|Math.round(r/255));return s===2&&(l+=60),l};i.hsv.ansi16=function(e){return i.rgb.ansi16(i.hsv.rgb(e),e[2])};i.rgb.ansi256=function(e){let t=e[0],r=e[1],n=e[2];return t===r&&r===n?t<8?16:t>248?231:Math.round((t-8)/247*24)+232:16+36*Math.round(t/255*5)+6*Math.round(r/255*5)+Math.round(n/255*5)};i.ansi16.rgb=function(e){let t=e%10;if(t===0||t===7)return e>50&&(t+=3.5),t=t/10.5*255,[t,t,t];let r=(~~(e>50)+1)*.5,n=(t&1)*r*255,o=(t>>1&1)*r*255,s=(t>>2&1)*r*255;return[n,o,s]};i.ansi256.rgb=function(e){if(e>=232){let s=(e-232)*10+8;return[s,s,s]}e-=16;let t,r=Math.floor(e/36)/5*255,n=Math.floor((t=e%36)/6)/5*255,o=t%6/5*255;return[r,n,o]};i.rgb.hex=function(e){let r=(((Math.round(e[0])&255)<<16)+((Math.round(e[1])&255)<<8)+(Math.round(e[2])&255)).toString(16).toUpperCase();return"000000".substring(r.length)+r};i.hex.rgb=function(e){let t=e.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!t)return[0,0,0];let r=t[0];t[0].length===3&&(r=r.split("").map(c=>c+c).join(""));let n=parseInt(r,16),o=n>>16&255,s=n>>8&255,l=n&255;return[o,s,l]};i.rgb.hcg=function(e){let t=e[0]/255,r=e[1]/255,n=e[2]/255,o=Math.max(Math.max(t,r),n),s=Math.min(Math.min(t,r),n),l=o-s,c,a;return l<1?c=s/(1-l):c=0,l<=0?a=0:o===t?a=(r-n)/l%6:o===r?a=2+(n-t)/l:a=4+(t-r)/l,a/=6,a%=1,[a*360,l*100,c*100]};i.hsl.hcg=function(e){let t=e[1]/100,r=e[2]/100,n=r<.5?2*t*r:2*t*(1-r),o=0;return n<1&&(o=(r-.5*n)/(1-n)),[e[0],n*100,o*100]};i.hsv.hcg=function(e){let t=e[1]/100,r=e[2]/100,n=t*r,o=0;return n<1&&(o=(r-n)/(1-n)),[e[0],n*100,o*100]};i.hcg.rgb=function(e){let t=e[0]/360,r=e[1]/100,n=e[2]/100;if(r===0)return[n*255,n*255,n*255];let o=[0,0,0],s=t%1*6,l=s%1,c=1-l,a=0;switch(Math.floor(s)){case 0:o[0]=1,o[1]=l,o[2]=0;break;case 1:o[0]=c,o[1]=1,o[2]=0;break;case 2:o[0]=0,o[1]=1,o[2]=l;break;case 3:o[0]=0,o[1]=c,o[2]=1;break;case 4:o[0]=l,o[1]=0,o[2]=1;break;default:o[0]=1,o[1]=0,o[2]=c}return a=(1-r)*n,[(r*o[0]+a)*255,(r*o[1]+a)*255,(r*o[2]+a)*255]};i.hcg.hsv=function(e){let t=e[1]/100,r=e[2]/100,n=t+r*(1-t),o=0;return n>0&&(o=t/n),[e[0],o*100,n*100]};i.hcg.hsl=function(e){let t=e[1]/100,n=e[2]/100*(1-t)+.5*t,o=0;return n>0&&n<.5?o=t/(2*n):n>=.5&&n<1&&(o=t/(2*(1-n))),[e[0],o*100,n*100]};i.hcg.hwb=function(e){let t=e[1]/100,r=e[2]/100,n=t+r*(1-t);return[e[0],(n-t)*100,(1-n)*100]};i.hwb.hcg=function(e){let t=e[1]/100,n=1-e[2]/100,o=n-t,s=0;return o<1&&(s=(n-o)/(1-o)),[e[0],o*100,s*100]};i.apple.rgb=function(e){return[e[0]/65535*255,e[1]/65535*255,e[2]/65535*255]};i.rgb.apple=function(e){return[e[0]/255*65535,e[1]/255*65535,e[2]/255*65535]};i.gray.rgb=function(e){return[e[0]/100*255,e[0]/100*255,e[0]/100*255]};i.gray.hsl=function(e){return[0,0,e[0]]};i.gray.hsv=i.gray.hsl;i.gray.hwb=function(e){return[0,100,e[0]]};i.gray.cmyk=function(e){return[0,0,0,e[0]]};i.gray.lab=function(e){return[e[0],0,0]};i.gray.hex=function(e){let t=Math.round(e[0]/100*255)&255,n=((t<<16)+(t<<8)+t).toString(16).toUpperCase();return"000000".substring(n.length)+n};i.rgb.gray=function(e){return[(e[0]+e[1]+e[2])/3/255*100]}});var Z=m((mt,H)=>{var j=P();function qe(){let e={},t=Object.keys(j);for(let r=t.length,n=0;n<r;n++)e[t[n]]={distance:-1,parent:null};return e}function Ne(e){let t=qe(),r=[e];for(t[e].distance=0;r.length;){let n=r.pop(),o=Object.keys(j[n]);for(let s=o.length,l=0;l<s;l++){let c=o[l],a=t[c];a.distance===-1&&(a.distance=t[n].distance+1,a.parent=n,r.unshift(c))}}return t}function Ge(e,t){return function(r){return t(e(r))}}function ze(e,t){let r=[t[e].parent,e],n=j[t[e].parent][e],o=t[e].parent;for(;t[o].parent;)r.unshift(t[o].parent),n=Ge(j[t[o].parent][o],n),o=t[o].parent;return n.conversion=r,n}H.exports=function(e){let t=Ne(e),r={},n=Object.keys(t);for(let o=n.length,s=0;s<o;s++){let l=n[s];t[l].parent!==null&&(r[l]=ze(l,t))}return r}});var Q=m((yt,J)=>{var _=P(),Ve=Z(),k={},De=Object.keys(_);function Ue(e){let t=function(...r){let n=r[0];return n==null?n:(n.length>1&&(r=n),e(r))};return"conversion"in e&&(t.conversion=e.conversion),t}function Ye(e){let t=function(...r){let n=r[0];if(n==null)return n;n.length>1&&(r=n);let o=e(r);if(typeof o=="object")for(let s=o.length,l=0;l<s;l++)o[l]=Math.round(o[l]);return o};return"conversion"in e&&(t.conversion=e.conversion),t}De.forEach(e=>{k[e]={},Object.defineProperty(k[e],"channels",{value:_[e].channels}),Object.defineProperty(k[e],"labels",{value:_[e].labels});let t=Ve(e);Object.keys(t).forEach(n=>{let o=t[n];k[e][n]=Ye(o),k[e][n].raw=Ue(o)})});J.exports=k});var se=m((vt,oe)=>{"use strict";var ee=(e,t)=>(...r)=>`\x1B[${e(...r)+t}m`,te=(e,t)=>(...r)=>{let n=e(...r);return`\x1B[${38+t};5;${n}m`},ne=(e,t)=>(...r)=>{let n=e(...r);return`\x1B[${38+t};2;${n[0]};${n[1]};${n[2]}m`},T=e=>e,re=(e,t,r)=>[e,t,r],C=(e,t,r)=>{Object.defineProperty(e,t,{get:()=>{let n=r();return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0}),n},enumerable:!0,configurable:!0})},F,x=(e,t,r,n)=>{F===void 0&&(F=Q());let o=n?10:0,s={};for(let[l,c]of Object.entries(F)){let a=l==="ansi16"?"ansi":l;l===t?s[a]=e(r,o):typeof c=="object"&&(s[a]=e(c[t],o))}return s};function We(){let e=new Map,t={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};t.color.gray=t.color.blackBright,t.bgColor.bgGray=t.bgColor.bgBlackBright,t.color.grey=t.color.blackBright,t.bgColor.bgGrey=t.bgColor.bgBlackBright;for(let[r,n]of Object.entries(t)){for(let[o,s]of Object.entries(n))t[o]={open:`\x1B[${s[0]}m`,close:`\x1B[${s[1]}m`},n[o]=t[o],e.set(s[0],s[1]);Object.defineProperty(t,r,{value:n,enumerable:!1})}return Object.defineProperty(t,"codes",{value:e,enumerable:!1}),t.color.close="\x1B[39m",t.bgColor.close="\x1B[49m",C(t.color,"ansi",()=>x(ee,"ansi16",T,!1)),C(t.color,"ansi256",()=>x(te,"ansi256",T,!1)),C(t.color,"ansi16m",()=>x(ne,"rgb",re,!1)),C(t.bgColor,"ansi",()=>x(ee,"ansi16",T,!0)),C(t.bgColor,"ansi256",()=>x(te,"ansi256",T,!0)),C(t.bgColor,"ansi16m",()=>x(ne,"rgb",re,!0)),t}Object.defineProperty(oe,"exports",{enumerable:!0,get:We})});var ce=m((wt,le)=>{"use strict";le.exports=(e,t=process.argv)=>{let r=e.startsWith("-")?"":e.length===1?"-":"--",n=t.indexOf(r+e),o=t.indexOf("--");return n!==-1&&(o===-1||n<o)}});var ue=m((kt,ae)=>{"use strict";var Ke=require("os"),ie=require("tty"),p=ce(),{env:d}=process,y;p("no-color")||p("no-colors")||p("color=false")||p("color=never")?y=0:(p("color")||p("colors")||p("color=true")||p("color=always"))&&(y=1);"FORCE_COLOR"in d&&(d.FORCE_COLOR==="true"?y=1:d.FORCE_COLOR==="false"?y=0:y=d.FORCE_COLOR.length===0?1:Math.min(parseInt(d.FORCE_COLOR,10),3));function L(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function q(e,t){if(y===0)return 0;if(p("color=16m")||p("color=full")||p("color=truecolor"))return 3;if(p("color=256"))return 2;if(e&&!t&&y===void 0)return 0;let r=y||0;if(d.TERM==="dumb")return r;if(process.platform==="win32"){let n=Ke.release().split(".");return Number(n[0])>=10&&Number(n[2])>=10586?Number(n[2])>=14931?3:2:1}if("CI"in d)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(n=>n in d)||d.CI_NAME==="codeship"?1:r;if("TEAMCITY_VERSION"in d)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(d.TEAMCITY_VERSION)?1:0;if(d.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in d){let n=parseInt((d.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(d.TERM_PROGRAM){case"iTerm.app":return n>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(d.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(d.TERM)||"COLORTERM"in d?1:r}function Xe(e){let t=q(e,e&&e.isTTY);return L(t)}ae.exports={supportsColor:Xe,stdout:L(q(!0,ie.isatty(1))),stderr:L(q(!0,ie.isatty(2)))}});var he=m((Ct,fe)=>{"use strict";var He=(e,t,r)=>{let n=e.indexOf(t);if(n===-1)return e;let o=t.length,s=0,l="";do l+=e.substr(s,n-s)+t+r,s=n+o,n=e.indexOf(t,s);while(n!==-1);return l+=e.substr(s),l},Ze=(e,t,r,n)=>{let o=0,s="";do{let l=e[n-1]==="\r";s+=e.substr(o,(l?n-1:n)-o)+t+(l?`\r
`:`
`)+r,o=n+1,n=e.indexOf(`
`,o)}while(n!==-1);return s+=e.substr(o),s};fe.exports={stringReplaceAll:He,stringEncaseCRLFWithFirstIndex:Ze}});var me=m((xt,be)=>{"use strict";var Je=/(?:\\(u(?:[a-f\d]{4}|\{[a-f\d]{1,6}\})|x[a-f\d]{2}|.))|(?:\{(~)?(\w+(?:\([^)]*\))?(?:\.\w+(?:\([^)]*\))?)*)(?:[ \t]|(?=\r?\n)))|(\})|((?:.|[\r\n\f])+?)/gi,ge=/(?:^|\.)(\w+)(?:\(([^)]*)\))?/g,Qe=/^(['"])((?:\\.|(?!\1)[^\\])*)\1$/,et=/\\(u(?:[a-f\d]{4}|{[a-f\d]{1,6}})|x[a-f\d]{2}|.)|([^\\])/gi,tt=new Map([["n",`
`],["r","\r"],["t","	"],["b","\b"],["f","\f"],["v","\v"],["0","\0"],["\\","\\"],["e","\x1B"],["a","\x07"]]);function pe(e){let t=e[0]==="u",r=e[1]==="{";return t&&!r&&e.length===5||e[0]==="x"&&e.length===3?String.fromCharCode(parseInt(e.slice(1),16)):t&&r?String.fromCodePoint(parseInt(e.slice(2,-1),16)):tt.get(e)||e}function nt(e,t){let r=[],n=t.trim().split(/\s*,\s*/g),o;for(let s of n){let l=Number(s);if(!Number.isNaN(l))r.push(l);else if(o=s.match(Qe))r.push(o[2].replace(et,(c,a,u)=>a?pe(a):u));else throw new Error(`Invalid Chalk template style argument: ${s} (in style '${e}')`)}return r}function rt(e){ge.lastIndex=0;let t=[],r;for(;(r=ge.exec(e))!==null;){let n=r[1];if(r[2]){let o=nt(n,r[2]);t.push([n].concat(o))}else t.push([n])}return t}function de(e,t){let r={};for(let o of t)for(let s of o.styles)r[s[0]]=o.inverse?null:s.slice(1);let n=e;for(let[o,s]of Object.entries(r))if(Array.isArray(s)){if(!(o in n))throw new Error(`Unknown Chalk style: ${o}`);n=s.length>0?n[o](...s):n[o]}return n}be.exports=(e,t)=>{let r=[],n=[],o=[];if(t.replace(Je,(s,l,c,a,u,g)=>{if(l)o.push(pe(l));else if(a){let h=o.join("");o=[],n.push(r.length===0?h:de(e,r)(h)),r.push({inverse:c,styles:rt(a)})}else if(u){if(r.length===0)throw new Error("Found extraneous } in Chalk template literal");n.push(de(e,r)(o.join(""))),o=[],r.pop()}else o.push(g)}),n.push(o.join("")),r.length>0){let s=`Chalk template literal is missing ${r.length} closing bracket${r.length===1?"":"s"} (\`}\`)`;throw new Error(s)}return n.join("")}});var Oe=m((Ot,xe)=>{"use strict";var M=se(),{stdout:G,stderr:z}=ue(),{stringReplaceAll:ot,stringEncaseCRLFWithFirstIndex:st}=he(),{isArray:B}=Array,ve=["ansi","ansi","ansi256","ansi16m"],O=Object.create(null),lt=(e,t={})=>{if(t.level&&!(Number.isInteger(t.level)&&t.level>=0&&t.level<=3))throw new Error("The `level` option should be an integer from 0 to 3");let r=G?G.level:0;e.level=t.level===void 0?r:t.level},V=class{constructor(t){return we(t)}},we=e=>{let t={};return lt(t,e),t.template=(...r)=>Ce(t.template,...r),Object.setPrototypeOf(t,A.prototype),Object.setPrototypeOf(t.template,t),t.template.constructor=()=>{throw new Error("`chalk.constructor()` is deprecated. Use `new chalk.Instance()` instead.")},t.template.Instance=V,t.template};function A(e){return we(e)}for(let[e,t]of Object.entries(M))O[e]={get(){let r=$(this,D(t.open,t.close,this._styler),this._isEmpty);return Object.defineProperty(this,e,{value:r}),r}};O.visible={get(){let e=$(this,this._styler,!0);return Object.defineProperty(this,"visible",{value:e}),e}};var ke=["rgb","hex","keyword","hsl","hsv","hwb","ansi","ansi256"];for(let e of ke)O[e]={get(){let{level:t}=this;return function(...r){let n=D(M.color[ve[t]][e](...r),M.color.close,this._styler);return $(this,n,this._isEmpty)}}};for(let e of ke){let t="bg"+e[0].toUpperCase()+e.slice(1);O[t]={get(){let{level:r}=this;return function(...n){let o=D(M.bgColor[ve[r]][e](...n),M.bgColor.close,this._styler);return $(this,o,this._isEmpty)}}}}var ct=Object.defineProperties(()=>{},{...O,level:{enumerable:!0,get(){return this._generator.level},set(e){this._generator.level=e}}}),D=(e,t,r)=>{let n,o;return r===void 0?(n=e,o=t):(n=r.openAll+e,o=t+r.closeAll),{open:e,close:t,openAll:n,closeAll:o,parent:r}},$=(e,t,r)=>{let n=(...o)=>B(o[0])&&B(o[0].raw)?ye(n,Ce(n,...o)):ye(n,o.length===1?""+o[0]:o.join(" "));return Object.setPrototypeOf(n,ct),n._generator=e,n._styler=t,n._isEmpty=r,n},ye=(e,t)=>{if(e.level<=0||!t)return e._isEmpty?"":t;let r=e._styler;if(r===void 0)return t;let{openAll:n,closeAll:o}=r;if(t.indexOf("\x1B")!==-1)for(;r!==void 0;)t=ot(t,r.close,r.open),r=r.parent;let s=t.indexOf(`
`);return s!==-1&&(t=st(t,o,n,s)),n+t+o},N,Ce=(e,...t)=>{let[r]=t;if(!B(r)||!B(r.raw))return t.join(" ");let n=t.slice(1),o=[r.raw[0]];for(let s=1;s<r.length;s++)o.push(String(n[s-1]).replace(/[{}\\]/g,"\\$&"),String(r.raw[s]));return N===void 0&&(N=me()),N(e,o.join(""))};Object.defineProperties(A.prototype,O);var I=A();I.supportsColor=G;I.stderr=A({level:z?z.level:0});I.stderr.supportsColor=z;xe.exports=I});var gt={};Pe(gt,{renderResults:()=>ht});module.exports=Fe(gt);var f=_e(Oe());var it=e=>e==="meetsCriteria"?"":e.replace(/([A-Z])/g," $1").trim().split(" ").map(t=>t.charAt(0).toUpperCase()+t.slice(1)).join(" ");function Ee(e){if(typeof e=="string")return Ee({name:e});let t=`Calls Tool ${e.name}`;return e.arguments&&(t+=` with arguments ${Object.keys(e.arguments).join(", ")}`),t}var S=e=>{if("meetsCriteria"in e)return e.meetsCriteria;if("callsTool"in e)return Ee(e.callsTool);if("not"in e){let s=S(e.not).split(" ");return s[0].endsWith("s")&&(s[0]=s[0].slice(0,-1)),`Doesn't ${s.join(" ")}`}if("or"in e)return`${e.or.map(S).join(" or ")}`;let t=Object.keys(e)[0],r=e[t],n=it(t);return r?`${n} "${r}"`:n};var v=100,w=e=>{let t=e.replace(/\u001b\[\d+m/g,"");return Array.from(t).reduce((r,n)=>r+(/\p{Emoji_Presentation}/u.test(n)?2:1),0)},Me=(e,t=75)=>e?w(e)<=t?e:e.slice(0,t)+"...":"";function at(e,t,r=" "){let n=w(e);return n>=t?e:e+r.repeat(t-n)}function ut(e,t,r=" "){let n=w(e);return n>=t?e:r.repeat(t-n)+e}function Re(e){let t=e.reduce((l,c)=>Math.max(l,w(c.name)),0),r=f.default.grey("\u250C"+"\u2500".repeat(t+2)+"\u252C"+"\u2500".repeat(v)+"\u2510")+`
`,n=f.default.grey("\u251C"+"\u2500".repeat(t+2)+"\u253C"+"\u2500".repeat(v)+"\u2524")+`
`,o=f.default.grey("\u2514"+"\u2500".repeat(t+2)+"\u2534"+"\u2500".repeat(v)+"\u2518")+`
`,s=r;return e.forEach((l,c)=>{let a=l.content.split(`
`).flatMap(g=>{let h=[];for(let b=0;b<w(g);b+=v)h.push(f.default.grey(g.slice(b,b+v)));return h.length?h:[""]});s+=f.default.grey("\u2502")+at(l.name,t+2)+f.default.grey("\u2502");let u=a[0];s+=u+" ".repeat(v-w(u))+f.default.grey(`\u2502
`);for(let g=1;g<a.length;g++){s+=f.default.grey("\u2502")+" ".repeat(t+2)+f.default.grey("\u2502");let h=a[g];s+=h+" ".repeat(v-w(h))+f.default.grey(`\u2502
`)}c===e.length-1?s+=o:s+=n}),s}function ft(e){let t=e.input,r="messages"in t?t.messages[0]?.content.text:t,n=e.score*100,o=e.assertionResults||[],s;e.status==="skipped"?s=f.default.yellow:e.status==="completed"&&n>=100?s=f.default.green:s=f.default.red;let c=(e.status==="error"||e.status==="completed"&&n<100?f.default.red:f.default.white)(`${e.index+1}. ${Me(r,200)}
`),a=e.status==="skipped"?"skipped":`Score: ${Math.round(n)}%`;if(c+=s(`   ${ut(a,4)}    `),c+=`

`,e.status==="error")c+=Re([{name:"Error",content:s(e.error)}]);else if(e.status==="completed"){let g=[{name:"Criteria",content:e.expected.map((h,b)=>{let je=o[b]?.result===1,Te=S(h);return`${je?f.default.green("\u2714"):f.default.red("\u2718")} ${f.default.grey(Te)}`}).join(`
`)},{name:"Output",content:f.default.grey(Me(e.output.text,400))}];if(e.output.calledTools.length>0){let h=e.output.calledTools.map(b=>f.default.grey(`${b.name}(${b.arguments})`)).join(`
`);g.push({name:"Tools",content:h})}c+=Re(g),c+=`
`}return c}function ht(e){console.log(),e.forEach(c=>{process.stdout.write(ft(c))});let t=e.length?e.reduce((c,a)=>c+a.score,0)/e.filter(c=>c.status!=="skipped").length*100:0;console.log();let r=e.length,n=e.filter(c=>c.status==="error"||c.status==="completed"&&c.score<1).length,o=e.filter(c=>c.status==="completed"&&c.score===1).length,s=e.filter(c=>c.status==="skipped").length,l="Total Score: ";l+=t===100?f.default.green(`${Math.round(t)}%`):f.default.red(`${Math.round(t)}%`),l+=" \u2502 ",l+=f.default.blue(`${o}/${r} Passed`),s>0&&(l+=" \u2502 ",l+=f.default.yellow(`${s} Skipped`)),n>0&&(l+=" \u2502 ",l+=f.default.red(`\u26A0\uFE0F ${n} Failed`)),console.log(l),console.log()}0&&(module.exports={renderResults});
