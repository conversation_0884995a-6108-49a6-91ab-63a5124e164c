"use strict";var e=Object.defineProperty;var c=Object.getOwnPropertyDescriptor;var m=Object.getOwnPropertyNames;var f=Object.prototype.hasOwnProperty;var p=(t,r)=>{for(var s in r)e(t,s,{get:r[s],enumerable:!0})},u=(t,r,s,o)=>{if(r&&typeof r=="object"||typeof r=="function")for(let i of m(r))!f.call(t,i)&&i!==s&&e(t,i,{get:()=>r[i],enumerable:!(o=c(r,i))||o.enumerable});return t};var T=t=>u(e({},"__esModule",{value:!0}),t);var y={};p(y,{formatExpectation:()=>l});module.exports=T(y);var g=t=>t==="meetsCriteria"?"":t.replace(/([A-Z])/g," $1").trim().split(" ").map(r=>r.charAt(0).toUpperCase()+r.slice(1)).join(" ");function a(t){if(typeof t=="string")return a({name:t});let r=`Calls Tool ${t.name}`;return t.arguments&&(r+=` with arguments ${Object.keys(t.arguments).join(", ")}`),r}var l=t=>{if("meetsCriteria"in t)return t.meetsCriteria;if("callsTool"in t)return a(t.callsTool);if("not"in t){let n=l(t.not).split(" ");return n[0].endsWith("s")&&(n[0]=n[0].slice(0,-1)),`Doesn't ${n.join(" ")}`}if("or"in t)return`${t.or.map(l).join(" or ")}`;let r=Object.keys(t)[0],s=t[r],o=g(r);return s?`${o} "${s}"`:o};0&&(module.exports={formatExpectation});
