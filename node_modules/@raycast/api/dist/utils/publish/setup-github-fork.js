"use strict";var Ql=Object.create;var Wr=Object.defineProperty;var Kl=Object.getOwnPropertyDescriptor;var Jl=Object.getOwnPropertyNames;var Zl=Object.getPrototypeOf,Xl=Object.prototype.hasOwnProperty;var Be=(e,r)=>()=>(r||e((r={exports:{}}).exports,r),r.exports),ec=(e,r)=>{for(var o in r)Wr(e,o,{get:r[o],enumerable:!0})},gs=(e,r,o,i)=>{if(r&&typeof r=="object"||typeof r=="function")for(let u of Jl(r))!Xl.call(e,u)&&u!==o&&Wr(e,u,{get:()=>r[u],enumerable:!(i=Kl(r,u))||i.enumerable});return e};var W=(e,r,o)=>(o=e!=null?Ql(Zl(e)):{},gs(r||!e||!e.__esModule?Wr(o,"default",{value:e,enumerable:!0}):o,e)),tc=e=>gs(Wr({},"__esModule",{value:!0}),e);var Fs=Be((sd,bs)=>{var rc=require("node:tty"),nc=rc?.WriteStream?.prototype?.hasColors?.()??!1,w=(e,r)=>{if(!nc)return u=>u;let o=`\x1B[${e}m`,i=`\x1B[${r}m`;return u=>{let l=u+"",f=l.indexOf(i);if(f===-1)return o+l+i;let d=o,p=0;for(;f!==-1;)d+=l.slice(p,f)+o,p=f+i.length,f=l.indexOf(i,p);return d+=l.slice(p)+i,d}},C={};C.reset=w(0,0);C.bold=w(1,22);C.dim=w(2,22);C.italic=w(3,23);C.underline=w(4,24);C.overline=w(53,55);C.inverse=w(7,27);C.hidden=w(8,28);C.strikethrough=w(9,29);C.black=w(30,39);C.red=w(31,39);C.green=w(32,39);C.yellow=w(33,39);C.blue=w(34,39);C.magenta=w(35,39);C.cyan=w(36,39);C.white=w(37,39);C.gray=w(90,39);C.bgBlack=w(40,49);C.bgRed=w(41,49);C.bgGreen=w(42,49);C.bgYellow=w(43,49);C.bgBlue=w(44,49);C.bgMagenta=w(45,49);C.bgCyan=w(46,49);C.bgWhite=w(47,49);C.bgGray=w(100,49);C.redBright=w(91,39);C.greenBright=w(92,39);C.yellowBright=w(93,39);C.blueBright=w(94,39);C.magentaBright=w(95,39);C.cyanBright=w(96,39);C.whiteBright=w(97,39);C.bgRedBright=w(101,49);C.bgGreenBright=w(102,49);C.bgYellowBright=w(103,49);C.bgBlueBright=w(104,49);C.bgMagentaBright=w(105,49);C.bgCyanBright=w(106,49);C.bgWhiteBright=w(107,49);bs.exports=C});var $s=Be((Td,Rc)=>{Rc.exports={dots:{interval:80,frames:["\u280B","\u2819","\u2839","\u2838","\u283C","\u2834","\u2826","\u2827","\u2807","\u280F"]},dots2:{interval:80,frames:["\u28FE","\u28FD","\u28FB","\u28BF","\u287F","\u28DF","\u28EF","\u28F7"]},dots3:{interval:80,frames:["\u280B","\u2819","\u281A","\u281E","\u2816","\u2826","\u2834","\u2832","\u2833","\u2813"]},dots4:{interval:80,frames:["\u2804","\u2806","\u2807","\u280B","\u2819","\u2838","\u2830","\u2820","\u2830","\u2838","\u2819","\u280B","\u2807","\u2806"]},dots5:{interval:80,frames:["\u280B","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B"]},dots6:{interval:80,frames:["\u2801","\u2809","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2824","\u2804","\u2804","\u2824","\u2834","\u2832","\u2812","\u2802","\u2802","\u2812","\u281A","\u2819","\u2809","\u2801"]},dots7:{interval:80,frames:["\u2808","\u2809","\u280B","\u2813","\u2812","\u2810","\u2810","\u2812","\u2816","\u2826","\u2824","\u2820","\u2820","\u2824","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B","\u2809","\u2808"]},dots8:{interval:80,frames:["\u2801","\u2801","\u2809","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2824","\u2804","\u2804","\u2824","\u2820","\u2820","\u2824","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B","\u2809","\u2808","\u2808"]},dots9:{interval:80,frames:["\u28B9","\u28BA","\u28BC","\u28F8","\u28C7","\u2867","\u2857","\u284F"]},dots10:{interval:80,frames:["\u2884","\u2882","\u2881","\u2841","\u2848","\u2850","\u2860"]},dots11:{interval:100,frames:["\u2801","\u2802","\u2804","\u2840","\u2880","\u2820","\u2810","\u2808"]},dots12:{interval:80,frames:["\u2880\u2800","\u2840\u2800","\u2804\u2800","\u2882\u2800","\u2842\u2800","\u2805\u2800","\u2883\u2800","\u2843\u2800","\u280D\u2800","\u288B\u2800","\u284B\u2800","\u280D\u2801","\u288B\u2801","\u284B\u2801","\u280D\u2809","\u280B\u2809","\u280B\u2809","\u2809\u2819","\u2809\u2819","\u2809\u2829","\u2808\u2899","\u2808\u2859","\u2888\u2829","\u2840\u2899","\u2804\u2859","\u2882\u2829","\u2842\u2898","\u2805\u2858","\u2883\u2828","\u2843\u2890","\u280D\u2850","\u288B\u2820","\u284B\u2880","\u280D\u2841","\u288B\u2801","\u284B\u2801","\u280D\u2809","\u280B\u2809","\u280B\u2809","\u2809\u2819","\u2809\u2819","\u2809\u2829","\u2808\u2899","\u2808\u2859","\u2808\u2829","\u2800\u2899","\u2800\u2859","\u2800\u2829","\u2800\u2898","\u2800\u2858","\u2800\u2828","\u2800\u2890","\u2800\u2850","\u2800\u2820","\u2800\u2880","\u2800\u2840"]},dots13:{interval:80,frames:["\u28FC","\u28F9","\u28BB","\u283F","\u285F","\u28CF","\u28E7","\u28F6"]},dots8Bit:{interval:80,frames:["\u2800","\u2801","\u2802","\u2803","\u2804","\u2805","\u2806","\u2807","\u2840","\u2841","\u2842","\u2843","\u2844","\u2845","\u2846","\u2847","\u2808","\u2809","\u280A","\u280B","\u280C","\u280D","\u280E","\u280F","\u2848","\u2849","\u284A","\u284B","\u284C","\u284D","\u284E","\u284F","\u2810","\u2811","\u2812","\u2813","\u2814","\u2815","\u2816","\u2817","\u2850","\u2851","\u2852","\u2853","\u2854","\u2855","\u2856","\u2857","\u2818","\u2819","\u281A","\u281B","\u281C","\u281D","\u281E","\u281F","\u2858","\u2859","\u285A","\u285B","\u285C","\u285D","\u285E","\u285F","\u2820","\u2821","\u2822","\u2823","\u2824","\u2825","\u2826","\u2827","\u2860","\u2861","\u2862","\u2863","\u2864","\u2865","\u2866","\u2867","\u2828","\u2829","\u282A","\u282B","\u282C","\u282D","\u282E","\u282F","\u2868","\u2869","\u286A","\u286B","\u286C","\u286D","\u286E","\u286F","\u2830","\u2831","\u2832","\u2833","\u2834","\u2835","\u2836","\u2837","\u2870","\u2871","\u2872","\u2873","\u2874","\u2875","\u2876","\u2877","\u2838","\u2839","\u283A","\u283B","\u283C","\u283D","\u283E","\u283F","\u2878","\u2879","\u287A","\u287B","\u287C","\u287D","\u287E","\u287F","\u2880","\u2881","\u2882","\u2883","\u2884","\u2885","\u2886","\u2887","\u28C0","\u28C1","\u28C2","\u28C3","\u28C4","\u28C5","\u28C6","\u28C7","\u2888","\u2889","\u288A","\u288B","\u288C","\u288D","\u288E","\u288F","\u28C8","\u28C9","\u28CA","\u28CB","\u28CC","\u28CD","\u28CE","\u28CF","\u2890","\u2891","\u2892","\u2893","\u2894","\u2895","\u2896","\u2897","\u28D0","\u28D1","\u28D2","\u28D3","\u28D4","\u28D5","\u28D6","\u28D7","\u2898","\u2899","\u289A","\u289B","\u289C","\u289D","\u289E","\u289F","\u28D8","\u28D9","\u28DA","\u28DB","\u28DC","\u28DD","\u28DE","\u28DF","\u28A0","\u28A1","\u28A2","\u28A3","\u28A4","\u28A5","\u28A6","\u28A7","\u28E0","\u28E1","\u28E2","\u28E3","\u28E4","\u28E5","\u28E6","\u28E7","\u28A8","\u28A9","\u28AA","\u28AB","\u28AC","\u28AD","\u28AE","\u28AF","\u28E8","\u28E9","\u28EA","\u28EB","\u28EC","\u28ED","\u28EE","\u28EF","\u28B0","\u28B1","\u28B2","\u28B3","\u28B4","\u28B5","\u28B6","\u28B7","\u28F0","\u28F1","\u28F2","\u28F3","\u28F4","\u28F5","\u28F6","\u28F7","\u28B8","\u28B9","\u28BA","\u28BB","\u28BC","\u28BD","\u28BE","\u28BF","\u28F8","\u28F9","\u28FA","\u28FB","\u28FC","\u28FD","\u28FE","\u28FF"]},sand:{interval:80,frames:["\u2801","\u2802","\u2804","\u2840","\u2848","\u2850","\u2860","\u28C0","\u28C1","\u28C2","\u28C4","\u28CC","\u28D4","\u28E4","\u28E5","\u28E6","\u28EE","\u28F6","\u28F7","\u28FF","\u287F","\u283F","\u289F","\u281F","\u285B","\u281B","\u282B","\u288B","\u280B","\u280D","\u2849","\u2809","\u2811","\u2821","\u2881"]},line:{interval:130,frames:["-","\\","|","/"]},line2:{interval:100,frames:["\u2802","-","\u2013","\u2014","\u2013","-"]},pipe:{interval:100,frames:["\u2524","\u2518","\u2534","\u2514","\u251C","\u250C","\u252C","\u2510"]},simpleDots:{interval:400,frames:[".  ",".. ","...","   "]},simpleDotsScrolling:{interval:200,frames:[".  ",".. ","..."," ..","  .","   "]},star:{interval:70,frames:["\u2736","\u2738","\u2739","\u273A","\u2739","\u2737"]},star2:{interval:80,frames:["+","x","*"]},flip:{interval:70,frames:["_","_","_","-","`","`","'","\xB4","-","_","_","_"]},hamburger:{interval:100,frames:["\u2631","\u2632","\u2634"]},growVertical:{interval:120,frames:["\u2581","\u2583","\u2584","\u2585","\u2586","\u2587","\u2586","\u2585","\u2584","\u2583"]},growHorizontal:{interval:120,frames:["\u258F","\u258E","\u258D","\u258C","\u258B","\u258A","\u2589","\u258A","\u258B","\u258C","\u258D","\u258E"]},balloon:{interval:140,frames:[" ",".","o","O","@","*"," "]},balloon2:{interval:120,frames:[".","o","O","\xB0","O","o","."]},noise:{interval:100,frames:["\u2593","\u2592","\u2591"]},bounce:{interval:120,frames:["\u2801","\u2802","\u2804","\u2802"]},boxBounce:{interval:120,frames:["\u2596","\u2598","\u259D","\u2597"]},boxBounce2:{interval:100,frames:["\u258C","\u2580","\u2590","\u2584"]},triangle:{interval:50,frames:["\u25E2","\u25E3","\u25E4","\u25E5"]},binary:{interval:80,frames:["010010","001100","100101","111010","111101","010111","101011","111000","110011","110101"]},arc:{interval:100,frames:["\u25DC","\u25E0","\u25DD","\u25DE","\u25E1","\u25DF"]},circle:{interval:120,frames:["\u25E1","\u2299","\u25E0"]},squareCorners:{interval:180,frames:["\u25F0","\u25F3","\u25F2","\u25F1"]},circleQuarters:{interval:120,frames:["\u25F4","\u25F7","\u25F6","\u25F5"]},circleHalves:{interval:50,frames:["\u25D0","\u25D3","\u25D1","\u25D2"]},squish:{interval:100,frames:["\u256B","\u256A"]},toggle:{interval:250,frames:["\u22B6","\u22B7"]},toggle2:{interval:80,frames:["\u25AB","\u25AA"]},toggle3:{interval:120,frames:["\u25A1","\u25A0"]},toggle4:{interval:100,frames:["\u25A0","\u25A1","\u25AA","\u25AB"]},toggle5:{interval:100,frames:["\u25AE","\u25AF"]},toggle6:{interval:300,frames:["\u101D","\u1040"]},toggle7:{interval:80,frames:["\u29BE","\u29BF"]},toggle8:{interval:100,frames:["\u25CD","\u25CC"]},toggle9:{interval:100,frames:["\u25C9","\u25CE"]},toggle10:{interval:100,frames:["\u3282","\u3280","\u3281"]},toggle11:{interval:50,frames:["\u29C7","\u29C6"]},toggle12:{interval:120,frames:["\u2617","\u2616"]},toggle13:{interval:80,frames:["=","*","-"]},arrow:{interval:100,frames:["\u2190","\u2196","\u2191","\u2197","\u2192","\u2198","\u2193","\u2199"]},arrow2:{interval:80,frames:["\u2B06\uFE0F ","\u2197\uFE0F ","\u27A1\uFE0F ","\u2198\uFE0F ","\u2B07\uFE0F ","\u2199\uFE0F ","\u2B05\uFE0F ","\u2196\uFE0F "]},arrow3:{interval:120,frames:["\u25B9\u25B9\u25B9\u25B9\u25B9","\u25B8\u25B9\u25B9\u25B9\u25B9","\u25B9\u25B8\u25B9\u25B9\u25B9","\u25B9\u25B9\u25B8\u25B9\u25B9","\u25B9\u25B9\u25B9\u25B8\u25B9","\u25B9\u25B9\u25B9\u25B9\u25B8"]},bouncingBar:{interval:80,frames:["[    ]","[=   ]","[==  ]","[=== ]","[====]","[ ===]","[  ==]","[   =]","[    ]","[   =]","[  ==]","[ ===]","[====]","[=== ]","[==  ]","[=   ]"]},bouncingBall:{interval:80,frames:["( \u25CF    )","(  \u25CF   )","(   \u25CF  )","(    \u25CF )","(     \u25CF)","(    \u25CF )","(   \u25CF  )","(  \u25CF   )","( \u25CF    )","(\u25CF     )"]},smiley:{interval:200,frames:["\u{1F604} ","\u{1F61D} "]},monkey:{interval:300,frames:["\u{1F648} ","\u{1F648} ","\u{1F649} ","\u{1F64A} "]},hearts:{interval:100,frames:["\u{1F49B} ","\u{1F499} ","\u{1F49C} ","\u{1F49A} ","\u2764\uFE0F "]},clock:{interval:100,frames:["\u{1F55B} ","\u{1F550} ","\u{1F551} ","\u{1F552} ","\u{1F553} ","\u{1F554} ","\u{1F555} ","\u{1F556} ","\u{1F557} ","\u{1F558} ","\u{1F559} ","\u{1F55A} "]},earth:{interval:180,frames:["\u{1F30D} ","\u{1F30E} ","\u{1F30F} "]},material:{interval:17,frames:["\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581"]},moon:{interval:80,frames:["\u{1F311} ","\u{1F312} ","\u{1F313} ","\u{1F314} ","\u{1F315} ","\u{1F316} ","\u{1F317} ","\u{1F318} "]},runner:{interval:140,frames:["\u{1F6B6} ","\u{1F3C3} "]},pong:{interval:80,frames:["\u2590\u2802       \u258C","\u2590\u2808       \u258C","\u2590 \u2802      \u258C","\u2590 \u2820      \u258C","\u2590  \u2840     \u258C","\u2590  \u2820     \u258C","\u2590   \u2802    \u258C","\u2590   \u2808    \u258C","\u2590    \u2802   \u258C","\u2590    \u2820   \u258C","\u2590     \u2840  \u258C","\u2590     \u2820  \u258C","\u2590      \u2802 \u258C","\u2590      \u2808 \u258C","\u2590       \u2802\u258C","\u2590       \u2820\u258C","\u2590       \u2840\u258C","\u2590      \u2820 \u258C","\u2590      \u2802 \u258C","\u2590     \u2808  \u258C","\u2590     \u2802  \u258C","\u2590    \u2820   \u258C","\u2590    \u2840   \u258C","\u2590   \u2820    \u258C","\u2590   \u2802    \u258C","\u2590  \u2808     \u258C","\u2590  \u2802     \u258C","\u2590 \u2820      \u258C","\u2590 \u2840      \u258C","\u2590\u2820       \u258C"]},shark:{interval:120,frames:["\u2590|\\____________\u258C","\u2590_|\\___________\u258C","\u2590__|\\__________\u258C","\u2590___|\\_________\u258C","\u2590____|\\________\u258C","\u2590_____|\\_______\u258C","\u2590______|\\______\u258C","\u2590_______|\\_____\u258C","\u2590________|\\____\u258C","\u2590_________|\\___\u258C","\u2590__________|\\__\u258C","\u2590___________|\\_\u258C","\u2590____________|\\\u258C","\u2590____________/|\u258C","\u2590___________/|_\u258C","\u2590__________/|__\u258C","\u2590_________/|___\u258C","\u2590________/|____\u258C","\u2590_______/|_____\u258C","\u2590______/|______\u258C","\u2590_____/|_______\u258C","\u2590____/|________\u258C","\u2590___/|_________\u258C","\u2590__/|__________\u258C","\u2590_/|___________\u258C","\u2590/|____________\u258C"]},dqpb:{interval:100,frames:["d","q","p","b"]},weather:{interval:100,frames:["\u2600\uFE0F ","\u2600\uFE0F ","\u2600\uFE0F ","\u{1F324} ","\u26C5\uFE0F ","\u{1F325} ","\u2601\uFE0F ","\u{1F327} ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u26C8 ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u2601\uFE0F ","\u{1F325} ","\u26C5\uFE0F ","\u{1F324} ","\u2600\uFE0F ","\u2600\uFE0F "]},christmas:{interval:400,frames:["\u{1F332}","\u{1F384}"]},grenade:{interval:80,frames:["\u060C  ","\u2032  "," \xB4 "," \u203E ","  \u2E0C","  \u2E0A","  |","  \u204E","  \u2055"," \u0DF4 ","  \u2053","   ","   ","   "]},point:{interval:125,frames:["\u2219\u2219\u2219","\u25CF\u2219\u2219","\u2219\u25CF\u2219","\u2219\u2219\u25CF","\u2219\u2219\u2219"]},layer:{interval:150,frames:["-","=","\u2261"]},betaWave:{interval:80,frames:["\u03C1\u03B2\u03B2\u03B2\u03B2\u03B2\u03B2","\u03B2\u03C1\u03B2\u03B2\u03B2\u03B2\u03B2","\u03B2\u03B2\u03C1\u03B2\u03B2\u03B2\u03B2","\u03B2\u03B2\u03B2\u03C1\u03B2\u03B2\u03B2","\u03B2\u03B2\u03B2\u03B2\u03C1\u03B2\u03B2","\u03B2\u03B2\u03B2\u03B2\u03B2\u03C1\u03B2","\u03B2\u03B2\u03B2\u03B2\u03B2\u03B2\u03C1"]},fingerDance:{interval:160,frames:["\u{1F918} ","\u{1F91F} ","\u{1F596} ","\u270B ","\u{1F91A} ","\u{1F446} "]},fistBump:{interval:80,frames:["\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u3000\u{1F91C}\u3000\u3000\u{1F91B}\u3000 ","\u3000\u3000\u{1F91C}\u{1F91B}\u3000\u3000 ","\u3000\u{1F91C}\u2728\u{1F91B}\u3000\u3000 ","\u{1F91C}\u3000\u2728\u3000\u{1F91B}\u3000 "]},soccerHeader:{interval:80,frames:[" \u{1F9D1}\u26BD\uFE0F       \u{1F9D1} ","\u{1F9D1}  \u26BD\uFE0F      \u{1F9D1} ","\u{1F9D1}   \u26BD\uFE0F     \u{1F9D1} ","\u{1F9D1}    \u26BD\uFE0F    \u{1F9D1} ","\u{1F9D1}     \u26BD\uFE0F   \u{1F9D1} ","\u{1F9D1}      \u26BD\uFE0F  \u{1F9D1} ","\u{1F9D1}       \u26BD\uFE0F\u{1F9D1}  ","\u{1F9D1}      \u26BD\uFE0F  \u{1F9D1} ","\u{1F9D1}     \u26BD\uFE0F   \u{1F9D1} ","\u{1F9D1}    \u26BD\uFE0F    \u{1F9D1} ","\u{1F9D1}   \u26BD\uFE0F     \u{1F9D1} ","\u{1F9D1}  \u26BD\uFE0F      \u{1F9D1} "]},mindblown:{interval:160,frames:["\u{1F610} ","\u{1F610} ","\u{1F62E} ","\u{1F62E} ","\u{1F626} ","\u{1F626} ","\u{1F627} ","\u{1F627} ","\u{1F92F} ","\u{1F4A5} ","\u2728 ","\u3000 ","\u3000 ","\u3000 "]},speaker:{interval:160,frames:["\u{1F508} ","\u{1F509} ","\u{1F50A} ","\u{1F509} "]},orangePulse:{interval:100,frames:["\u{1F538} ","\u{1F536} ","\u{1F7E0} ","\u{1F7E0} ","\u{1F536} "]},bluePulse:{interval:100,frames:["\u{1F539} ","\u{1F537} ","\u{1F535} ","\u{1F535} ","\u{1F537} "]},orangeBluePulse:{interval:100,frames:["\u{1F538} ","\u{1F536} ","\u{1F7E0} ","\u{1F7E0} ","\u{1F536} ","\u{1F539} ","\u{1F537} ","\u{1F535} ","\u{1F535} ","\u{1F537} "]},timeTravel:{interval:100,frames:["\u{1F55B} ","\u{1F55A} ","\u{1F559} ","\u{1F558} ","\u{1F557} ","\u{1F556} ","\u{1F555} ","\u{1F554} ","\u{1F553} ","\u{1F552} ","\u{1F551} ","\u{1F550} "]},aesthetic:{interval:80,frames:["\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0","\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1\u25B1"]},dwarfFortress:{interval:80,frames:[" \u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2593\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2593\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2592\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2592\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2591\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2591\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A \u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2593\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2593\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2592\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2592\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2591\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2591\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A \u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2593\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2593\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2592\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2592\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2591\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2591\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A \u2588\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2593\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2593\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2592\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2592\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2591\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2591\u2588\u2588\xA3\xA3\xA3  ","   \u263A \u2588\u2588\xA3\xA3\xA3  ","    \u263A\u2588\u2588\xA3\xA3\xA3  ","    \u263A\u2588\u2588\xA3\xA3\xA3  ","    \u263A\u2593\u2588\xA3\xA3\xA3  ","    \u263A\u2593\u2588\xA3\xA3\xA3  ","    \u263A\u2592\u2588\xA3\xA3\xA3  ","    \u263A\u2592\u2588\xA3\xA3\xA3  ","    \u263A\u2591\u2588\xA3\xA3\xA3  ","    \u263A\u2591\u2588\xA3\xA3\xA3  ","    \u263A \u2588\xA3\xA3\xA3  ","     \u263A\u2588\xA3\xA3\xA3  ","     \u263A\u2588\xA3\xA3\xA3  ","     \u263A\u2593\xA3\xA3\xA3  ","     \u263A\u2593\xA3\xA3\xA3  ","     \u263A\u2592\xA3\xA3\xA3  ","     \u263A\u2592\xA3\xA3\xA3  ","     \u263A\u2591\xA3\xA3\xA3  ","     \u263A\u2591\xA3\xA3\xA3  ","     \u263A \xA3\xA3\xA3  ","      \u263A\xA3\xA3\xA3  ","      \u263A\xA3\xA3\xA3  ","      \u263A\u2593\xA3\xA3  ","      \u263A\u2593\xA3\xA3  ","      \u263A\u2592\xA3\xA3  ","      \u263A\u2592\xA3\xA3  ","      \u263A\u2591\xA3\xA3  ","      \u263A\u2591\xA3\xA3  ","      \u263A \xA3\xA3  ","       \u263A\xA3\xA3  ","       \u263A\xA3\xA3  ","       \u263A\u2593\xA3  ","       \u263A\u2593\xA3  ","       \u263A\u2592\xA3  ","       \u263A\u2592\xA3  ","       \u263A\u2591\xA3  ","       \u263A\u2591\xA3  ","       \u263A \xA3  ","        \u263A\xA3  ","        \u263A\xA3  ","        \u263A\u2593  ","        \u263A\u2593  ","        \u263A\u2592  ","        \u263A\u2592  ","        \u263A\u2591  ","        \u263A\u2591  ","        \u263A   ","        \u263A  &","        \u263A \u263C&","       \u263A \u263C &","       \u263A\u263C  &","      \u263A\u263C  & ","      \u203C   & ","     \u263A   &  ","    \u203C    &  ","   \u263A    &   ","  \u203C     &   "," \u263A     &    ","\u203C      &    ","      &     ","      &     ","     &   \u2591  ","     &   \u2592  ","    &    \u2593  ","    &    \xA3  ","   &    \u2591\xA3  ","   &    \u2592\xA3  ","  &     \u2593\xA3  ","  &     \xA3\xA3  "," &     \u2591\xA3\xA3  "," &     \u2592\xA3\xA3  ","&      \u2593\xA3\xA3  ","&      \xA3\xA3\xA3  ","      \u2591\xA3\xA3\xA3  ","      \u2592\xA3\xA3\xA3  ","      \u2593\xA3\xA3\xA3  ","      \u2588\xA3\xA3\xA3  ","     \u2591\u2588\xA3\xA3\xA3  ","     \u2592\u2588\xA3\xA3\xA3  ","     \u2593\u2588\xA3\xA3\xA3  ","     \u2588\u2588\xA3\xA3\xA3  ","    \u2591\u2588\u2588\xA3\xA3\xA3  ","    \u2592\u2588\u2588\xA3\xA3\xA3  ","    \u2593\u2588\u2588\xA3\xA3\xA3  ","    \u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2591\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2592\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2593\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2591\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2592\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2593\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2591\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2592\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2593\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "]}}});var io=Be((Pd,Ms)=>{"use strict";var Ur=Object.assign({},$s()),Ls=Object.keys(Ur);Object.defineProperty(Ur,"random",{get(){let e=Math.floor(Math.random()*Ls.length),r=Ls[e];return Ur[r]}});Ms.exports=Ur});var ru=Be((Xd,tu)=>{tu.exports=()=>/[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26D3\uFE0F?(?:\u200D\uD83D\uDCA5)?|\u26F9(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF43\uDF45-\uDF4A\uDF4C-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDF44(?:\u200D\uD83D\uDFEB)?|\uDF4B(?:\u200D\uD83D\uDFE9)?|\uDFC3(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4\uDEB5](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE41\uDE43\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC08(?:\u200D\u2B1B)?|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC26(?:\u200D(?:\u2B1B|\uD83D\uDD25))?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?|\uDE42(?:\u200D[\u2194\u2195]\uFE0F?)?|\uDEB6(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE89\uDE8F-\uDEC2\uDEC6\uDECE-\uDEDC\uDEDF-\uDEE9]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDCE(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1|\uDDD1\u200D\uD83E\uDDD2(?:\u200D\uD83E\uDDD2)?|\uDDD2(?:\u200D\uD83E\uDDD2)?))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g});var Qu=Be(Vu=>{var sf=Object.create,un=Object.defineProperty,uf=Object.getOwnPropertyDescriptor,af=Object.getOwnPropertyNames,lf=Object.getPrototypeOf,cf=Object.prototype.hasOwnProperty,xu=e=>un(e,"__esModule",{value:!0}),tr=(e,r)=>function(){return e&&(r=(0,e[Object.keys(e)[0]])(e=0)),r},jo=(e,r)=>function(){return r||(0,e[Object.keys(e)[0]])((r={exports:{}}).exports,r),r.exports},ku=(e,r)=>{xu(e);for(var o in r)un(e,o,{get:r[o],enumerable:!0})},ff=(e,r,o)=>{if(r&&typeof r=="object"||typeof r=="function")for(let i of af(r))!cf.call(e,i)&&i!=="default"&&un(e,i,{get:()=>r[i],enumerable:!(o=uf(r,i))||o.enumerable});return e},J=e=>ff(xu(un(e!=null?sf(lf(e)):{},"default",e&&e.__esModule&&"default"in e?{get:()=>e.default,enumerable:!0}:{value:e,enumerable:!0})),e),df=jo({"node_modules/web-streams-polyfill/dist/ponyfill.es2018.js"(e,r){(function(o,i){typeof e=="object"&&typeof r<"u"?i(e):typeof define=="function"&&define.amd?define(["exports"],i):(o=typeof globalThis<"u"?globalThis:o||self,i(o.WebStreamsPolyfill={}))})(e,function(o){"use strict";let i=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Symbol:t=>`Symbol(${t})`;function u(){}function l(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global}let f=l();function d(t){return typeof t=="object"&&t!==null||typeof t=="function"}let p=u,g=Promise,O=Promise.prototype.then,H=Promise.resolve.bind(g),N=Promise.reject.bind(g);function _(t){return new g(t)}function m(t){return H(t)}function b(t){return N(t)}function R(t,n,s){return O.call(t,n,s)}function E(t,n,s){R(R(t,n,s),void 0,p)}function V(t,n){E(t,n)}function Q(t,n){E(t,void 0,n)}function P(t,n,s){return R(t,n,s)}function I(t){R(t,void 0,p)}let j=(()=>{let t=f&&f.queueMicrotask;if(typeof t=="function")return t;let n=m(void 0);return s=>R(n,s)})();function He(t,n,s){if(typeof t!="function")throw new TypeError("Argument is not a function");return Function.prototype.apply.call(t,n,s)}function Ce(t,n,s){try{return m(He(t,n,s))}catch(a){return b(a)}}let ti=16384;class ae{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(n){let s=this._back,a=s;s._elements.length===ti-1&&(a={_elements:[],_next:void 0}),s._elements.push(n),a!==s&&(this._back=a,s._next=a),++this._size}shift(){let n=this._front,s=n,a=this._cursor,c=a+1,D=n._elements,h=D[a];return c===ti&&(s=n._next,c=0),--this._size,this._cursor=c,n!==s&&(this._front=s),D[a]=void 0,h}forEach(n){let s=this._cursor,a=this._front,c=a._elements;for(;(s!==c.length||a._next!==void 0)&&!(s===c.length&&(a=a._next,c=a._elements,s=0,c.length===0));)n(c[s]),++s}peek(){let n=this._front,s=this._cursor;return n._elements[s]}}function ri(t,n){t._ownerReadableStream=n,n._reader=t,n._state==="readable"?mn(t):n._state==="closed"?wa(t):ni(t,n._storedError)}function hn(t,n){let s=t._ownerReadableStream;return fe(s,n)}function we(t){t._ownerReadableStream._state==="readable"?pn(t,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):Sa(t,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),t._ownerReadableStream._reader=void 0,t._ownerReadableStream=void 0}function Xe(t){return new TypeError("Cannot "+t+" a stream using a released reader")}function mn(t){t._closedPromise=_((n,s)=>{t._closedPromise_resolve=n,t._closedPromise_reject=s})}function ni(t,n){mn(t),pn(t,n)}function wa(t){mn(t),oi(t)}function pn(t,n){t._closedPromise_reject!==void 0&&(I(t._closedPromise),t._closedPromise_reject(n),t._closedPromise_resolve=void 0,t._closedPromise_reject=void 0)}function Sa(t,n){ni(t,n)}function oi(t){t._closedPromise_resolve!==void 0&&(t._closedPromise_resolve(void 0),t._closedPromise_resolve=void 0,t._closedPromise_reject=void 0)}let ii=i("[[AbortSteps]]"),si=i("[[ErrorSteps]]"),gn=i("[[CancelSteps]]"),bn=i("[[PullSteps]]"),ui=Number.isFinite||function(t){return typeof t=="number"&&isFinite(t)},va=Math.trunc||function(t){return t<0?Math.ceil(t):Math.floor(t)};function Aa(t){return typeof t=="object"||typeof t=="function"}function Se(t,n){if(t!==void 0&&!Aa(t))throw new TypeError(`${n} is not an object.`)}function le(t,n){if(typeof t!="function")throw new TypeError(`${n} is not a function.`)}function Ra(t){return typeof t=="object"&&t!==null||typeof t=="function"}function ai(t,n){if(!Ra(t))throw new TypeError(`${n} is not an object.`)}function ve(t,n,s){if(t===void 0)throw new TypeError(`Parameter ${n} is required in '${s}'.`)}function Fn(t,n,s){if(t===void 0)throw new TypeError(`${n} is required in '${s}'.`)}function _n(t){return Number(t)}function li(t){return t===0?0:t}function Ba(t){return li(va(t))}function ci(t,n){let a=Number.MAX_SAFE_INTEGER,c=Number(t);if(c=li(c),!ui(c))throw new TypeError(`${n} is not a finite number`);if(c=Ba(c),c<0||c>a)throw new TypeError(`${n} is outside the accepted range of 0 to ${a}, inclusive`);return!ui(c)||c===0?0:c}function yn(t,n){if(!Me(t))throw new TypeError(`${n} is not a ReadableStream.`)}function et(t){return new wt(t)}function fi(t,n){t._reader._readRequests.push(n)}function En(t,n,s){let c=t._reader._readRequests.shift();s?c._closeSteps():c._chunkSteps(n)}function or(t){return t._reader._readRequests.length}function di(t){let n=t._reader;return!(n===void 0||!Ie(n))}class wt{constructor(n){if(ve(n,1,"ReadableStreamDefaultReader"),yn(n,"First parameter"),qe(n))throw new TypeError("This stream has already been locked for exclusive reading by another reader");ri(this,n),this._readRequests=new ae}get closed(){return Ie(this)?this._closedPromise:b(ir("closed"))}cancel(n=void 0){return Ie(this)?this._ownerReadableStream===void 0?b(Xe("cancel")):hn(this,n):b(ir("cancel"))}read(){if(!Ie(this))return b(ir("read"));if(this._ownerReadableStream===void 0)return b(Xe("read from"));let n,s,a=_((D,h)=>{n=D,s=h});return St(this,{_chunkSteps:D=>n({value:D,done:!1}),_closeSteps:()=>n({value:void 0,done:!0}),_errorSteps:D=>s(D)}),a}releaseLock(){if(!Ie(this))throw ir("releaseLock");if(this._ownerReadableStream!==void 0){if(this._readRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");we(this)}}}Object.defineProperties(wt.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(wt.prototype,i.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});function Ie(t){return!d(t)||!Object.prototype.hasOwnProperty.call(t,"_readRequests")?!1:t instanceof wt}function St(t,n){let s=t._ownerReadableStream;s._disturbed=!0,s._state==="closed"?n._closeSteps():s._state==="errored"?n._errorSteps(s._storedError):s._readableStreamController[bn](n)}function ir(t){return new TypeError(`ReadableStreamDefaultReader.prototype.${t} can only be used on a ReadableStreamDefaultReader`)}let Di=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype);class hi{constructor(n,s){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=n,this._preventCancel=s}next(){let n=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?P(this._ongoingPromise,n,n):n(),this._ongoingPromise}return(n){let s=()=>this._returnSteps(n);return this._ongoingPromise?P(this._ongoingPromise,s,s):s()}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});let n=this._reader;if(n._ownerReadableStream===void 0)return b(Xe("iterate"));let s,a,c=_((h,F)=>{s=h,a=F});return St(n,{_chunkSteps:h=>{this._ongoingPromise=void 0,j(()=>s({value:h,done:!1}))},_closeSteps:()=>{this._ongoingPromise=void 0,this._isFinished=!0,we(n),s({value:void 0,done:!0})},_errorSteps:h=>{this._ongoingPromise=void 0,this._isFinished=!0,we(n),a(h)}}),c}_returnSteps(n){if(this._isFinished)return Promise.resolve({value:n,done:!0});this._isFinished=!0;let s=this._reader;if(s._ownerReadableStream===void 0)return b(Xe("finish iterating"));if(!this._preventCancel){let a=hn(s,n);return we(s),P(a,()=>({value:n,done:!0}))}return we(s),m({value:n,done:!0})}}let mi={next(){return pi(this)?this._asyncIteratorImpl.next():b(gi("next"))},return(t){return pi(this)?this._asyncIteratorImpl.return(t):b(gi("return"))}};Di!==void 0&&Object.setPrototypeOf(mi,Di);function Ta(t,n){let s=et(t),a=new hi(s,n),c=Object.create(mi);return c._asyncIteratorImpl=a,c}function pi(t){if(!d(t)||!Object.prototype.hasOwnProperty.call(t,"_asyncIteratorImpl"))return!1;try{return t._asyncIteratorImpl instanceof hi}catch{return!1}}function gi(t){return new TypeError(`ReadableStreamAsyncIterator.${t} can only be used on a ReadableSteamAsyncIterator`)}let bi=Number.isNaN||function(t){return t!==t};function vt(t){return t.slice()}function Fi(t,n,s,a,c){new Uint8Array(t).set(new Uint8Array(s,a,c),n)}function od(t){return t}function sr(t){return!1}function _i(t,n,s){if(t.slice)return t.slice(n,s);let a=s-n,c=new ArrayBuffer(a);return Fi(c,0,t,n,a),c}function Pa(t){return!(typeof t!="number"||bi(t)||t<0)}function yi(t){let n=_i(t.buffer,t.byteOffset,t.byteOffset+t.byteLength);return new Uint8Array(n)}function Cn(t){let n=t._queue.shift();return t._queueTotalSize-=n.size,t._queueTotalSize<0&&(t._queueTotalSize=0),n.value}function wn(t,n,s){if(!Pa(s)||s===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");t._queue.push({value:n,size:s}),t._queueTotalSize+=s}function Oa(t){return t._queue.peek().value}function We(t){t._queue=new ae,t._queueTotalSize=0}class At{constructor(){throw new TypeError("Illegal constructor")}get view(){if(!Sn(this))throw Bn("view");return this._view}respond(n){if(!Sn(this))throw Bn("respond");if(ve(n,1,"respond"),n=ci(n,"First parameter"),this._associatedReadableByteStreamController===void 0)throw new TypeError("This BYOB request has been invalidated");sr(this._view.buffer),fr(this._associatedReadableByteStreamController,n)}respondWithNewView(n){if(!Sn(this))throw Bn("respondWithNewView");if(ve(n,1,"respondWithNewView"),!ArrayBuffer.isView(n))throw new TypeError("You can only respond with array buffer views");if(this._associatedReadableByteStreamController===void 0)throw new TypeError("This BYOB request has been invalidated");sr(n.buffer),dr(this._associatedReadableByteStreamController,n)}}Object.defineProperties(At.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(At.prototype,i.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class tt{constructor(){throw new TypeError("Illegal constructor")}get byobRequest(){if(!Ge(this))throw Bt("byobRequest");return Rn(this)}get desiredSize(){if(!Ge(this))throw Bt("desiredSize");return Bi(this)}close(){if(!Ge(this))throw Bt("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");let n=this._controlledReadableByteStream._state;if(n!=="readable")throw new TypeError(`The stream (in ${n} state) is not in the readable state and cannot be closed`);Rt(this)}enqueue(n){if(!Ge(this))throw Bt("enqueue");if(ve(n,1,"enqueue"),!ArrayBuffer.isView(n))throw new TypeError("chunk must be an array buffer view");if(n.byteLength===0)throw new TypeError("chunk must have non-zero byteLength");if(n.buffer.byteLength===0)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");let s=this._controlledReadableByteStream._state;if(s!=="readable")throw new TypeError(`The stream (in ${s} state) is not in the readable state and cannot be enqueued to`);cr(this,n)}error(n=void 0){if(!Ge(this))throw Bt("error");ce(this,n)}[gn](n){Ei(this),We(this);let s=this._cancelAlgorithm(n);return lr(this),s}[bn](n){let s=this._controlledReadableByteStream;if(this._queueTotalSize>0){let c=this._queue.shift();this._queueTotalSize-=c.byteLength,vi(this);let D=new Uint8Array(c.buffer,c.byteOffset,c.byteLength);n._chunkSteps(D);return}let a=this._autoAllocateChunkSize;if(a!==void 0){let c;try{c=new ArrayBuffer(a)}catch(h){n._errorSteps(h);return}let D={buffer:c,bufferByteLength:a,byteOffset:0,byteLength:a,bytesFilled:0,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(D)}fi(s,n),Ye(this)}}Object.defineProperties(tt.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(tt.prototype,i.toStringTag,{value:"ReadableByteStreamController",configurable:!0});function Ge(t){return!d(t)||!Object.prototype.hasOwnProperty.call(t,"_controlledReadableByteStream")?!1:t instanceof tt}function Sn(t){return!d(t)||!Object.prototype.hasOwnProperty.call(t,"_associatedReadableByteStreamController")?!1:t instanceof At}function Ye(t){if(!Wa(t))return;if(t._pulling){t._pullAgain=!0;return}t._pulling=!0;let s=t._pullAlgorithm();E(s,()=>{t._pulling=!1,t._pullAgain&&(t._pullAgain=!1,Ye(t))},a=>{ce(t,a)})}function Ei(t){An(t),t._pendingPullIntos=new ae}function vn(t,n){let s=!1;t._state==="closed"&&(s=!0);let a=Ci(n);n.readerType==="default"?En(t,a,s):Ma(t,a,s)}function Ci(t){let n=t.bytesFilled,s=t.elementSize;return new t.viewConstructor(t.buffer,t.byteOffset,n/s)}function ur(t,n,s,a){t._queue.push({buffer:n,byteOffset:s,byteLength:a}),t._queueTotalSize+=a}function wi(t,n){let s=n.elementSize,a=n.bytesFilled-n.bytesFilled%s,c=Math.min(t._queueTotalSize,n.byteLength-n.bytesFilled),D=n.bytesFilled+c,h=D-D%s,F=c,S=!1;h>a&&(F=h-n.bytesFilled,S=!0);let A=t._queue;for(;F>0;){let B=A.peek(),T=Math.min(F,B.byteLength),z=n.byteOffset+n.bytesFilled;Fi(n.buffer,z,B.buffer,B.byteOffset,T),B.byteLength===T?A.shift():(B.byteOffset+=T,B.byteLength-=T),t._queueTotalSize-=T,Si(t,T,n),F-=T}return S}function Si(t,n,s){s.bytesFilled+=n}function vi(t){t._queueTotalSize===0&&t._closeRequested?(lr(t),$t(t._controlledReadableByteStream)):Ye(t)}function An(t){t._byobRequest!==null&&(t._byobRequest._associatedReadableByteStreamController=void 0,t._byobRequest._view=null,t._byobRequest=null)}function Ai(t){for(;t._pendingPullIntos.length>0;){if(t._queueTotalSize===0)return;let n=t._pendingPullIntos.peek();wi(t,n)&&(ar(t),vn(t._controlledReadableByteStream,n))}}function xa(t,n,s){let a=t._controlledReadableByteStream,c=1;n.constructor!==DataView&&(c=n.constructor.BYTES_PER_ELEMENT);let D=n.constructor,h=n.buffer,F={buffer:h,bufferByteLength:h.byteLength,byteOffset:n.byteOffset,byteLength:n.byteLength,bytesFilled:0,elementSize:c,viewConstructor:D,readerType:"byob"};if(t._pendingPullIntos.length>0){t._pendingPullIntos.push(F),Oi(a,s);return}if(a._state==="closed"){let S=new D(F.buffer,F.byteOffset,0);s._closeSteps(S);return}if(t._queueTotalSize>0){if(wi(t,F)){let S=Ci(F);vi(t),s._chunkSteps(S);return}if(t._closeRequested){let S=new TypeError("Insufficient bytes to fill elements in the given buffer");ce(t,S),s._errorSteps(S);return}}t._pendingPullIntos.push(F),Oi(a,s),Ye(t)}function ka(t,n){let s=t._controlledReadableByteStream;if(Tn(s))for(;xi(s)>0;){let a=ar(t);vn(s,a)}}function Ia(t,n,s){if(Si(t,n,s),s.bytesFilled<s.elementSize)return;ar(t);let a=s.bytesFilled%s.elementSize;if(a>0){let c=s.byteOffset+s.bytesFilled,D=_i(s.buffer,c-a,c);ur(t,D,0,D.byteLength)}s.bytesFilled-=a,vn(t._controlledReadableByteStream,s),Ai(t)}function Ri(t,n){let s=t._pendingPullIntos.peek();An(t),t._controlledReadableByteStream._state==="closed"?ka(t):Ia(t,n,s),Ye(t)}function ar(t){return t._pendingPullIntos.shift()}function Wa(t){let n=t._controlledReadableByteStream;return n._state!=="readable"||t._closeRequested||!t._started?!1:!!(di(n)&&or(n)>0||Tn(n)&&xi(n)>0||Bi(t)>0)}function lr(t){t._pullAlgorithm=void 0,t._cancelAlgorithm=void 0}function Rt(t){let n=t._controlledReadableByteStream;if(!(t._closeRequested||n._state!=="readable")){if(t._queueTotalSize>0){t._closeRequested=!0;return}if(t._pendingPullIntos.length>0&&t._pendingPullIntos.peek().bytesFilled>0){let a=new TypeError("Insufficient bytes to fill elements in the given buffer");throw ce(t,a),a}lr(t),$t(n)}}function cr(t,n){let s=t._controlledReadableByteStream;if(t._closeRequested||s._state!=="readable")return;let a=n.buffer,c=n.byteOffset,D=n.byteLength,h=a;if(t._pendingPullIntos.length>0){let F=t._pendingPullIntos.peek();sr(F.buffer),F.buffer=F.buffer}if(An(t),di(s))if(or(s)===0)ur(t,h,c,D);else{t._pendingPullIntos.length>0&&ar(t);let F=new Uint8Array(h,c,D);En(s,F,!1)}else Tn(s)?(ur(t,h,c,D),Ai(t)):ur(t,h,c,D);Ye(t)}function ce(t,n){let s=t._controlledReadableByteStream;s._state==="readable"&&(Ei(t),We(t),lr(t),ns(s,n))}function Rn(t){if(t._byobRequest===null&&t._pendingPullIntos.length>0){let n=t._pendingPullIntos.peek(),s=new Uint8Array(n.buffer,n.byteOffset+n.bytesFilled,n.byteLength-n.bytesFilled),a=Object.create(At.prototype);La(a,t,s),t._byobRequest=a}return t._byobRequest}function Bi(t){let n=t._controlledReadableByteStream._state;return n==="errored"?null:n==="closed"?0:t._strategyHWM-t._queueTotalSize}function fr(t,n){let s=t._pendingPullIntos.peek();if(t._controlledReadableByteStream._state==="closed"){if(n!==0)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(n===0)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(s.bytesFilled+n>s.byteLength)throw new RangeError("bytesWritten out of range")}s.buffer=s.buffer,Ri(t,n)}function dr(t,n){let s=t._pendingPullIntos.peek();if(t._controlledReadableByteStream._state==="closed"){if(n.byteLength!==0)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(n.byteLength===0)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(s.byteOffset+s.bytesFilled!==n.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(s.bufferByteLength!==n.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(s.bytesFilled+n.byteLength>s.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");let c=n.byteLength;s.buffer=n.buffer,Ri(t,c)}function Ti(t,n,s,a,c,D,h){n._controlledReadableByteStream=t,n._pullAgain=!1,n._pulling=!1,n._byobRequest=null,n._queue=n._queueTotalSize=void 0,We(n),n._closeRequested=!1,n._started=!1,n._strategyHWM=D,n._pullAlgorithm=a,n._cancelAlgorithm=c,n._autoAllocateChunkSize=h,n._pendingPullIntos=new ae,t._readableStreamController=n;let F=s();E(m(F),()=>{n._started=!0,Ye(n)},S=>{ce(n,S)})}function $a(t,n,s){let a=Object.create(tt.prototype),c=()=>{},D=()=>m(void 0),h=()=>m(void 0);n.start!==void 0&&(c=()=>n.start(a)),n.pull!==void 0&&(D=()=>n.pull(a)),n.cancel!==void 0&&(h=S=>n.cancel(S));let F=n.autoAllocateChunkSize;if(F===0)throw new TypeError("autoAllocateChunkSize must be greater than 0");Ti(t,a,c,D,h,s,F)}function La(t,n,s){t._associatedReadableByteStreamController=n,t._view=s}function Bn(t){return new TypeError(`ReadableStreamBYOBRequest.prototype.${t} can only be used on a ReadableStreamBYOBRequest`)}function Bt(t){return new TypeError(`ReadableByteStreamController.prototype.${t} can only be used on a ReadableByteStreamController`)}function Pi(t){return new Tt(t)}function Oi(t,n){t._reader._readIntoRequests.push(n)}function Ma(t,n,s){let c=t._reader._readIntoRequests.shift();s?c._closeSteps(n):c._chunkSteps(n)}function xi(t){return t._reader._readIntoRequests.length}function Tn(t){let n=t._reader;return!(n===void 0||!Ve(n))}class Tt{constructor(n){if(ve(n,1,"ReadableStreamBYOBReader"),yn(n,"First parameter"),qe(n))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!Ge(n._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");ri(this,n),this._readIntoRequests=new ae}get closed(){return Ve(this)?this._closedPromise:b(Dr("closed"))}cancel(n=void 0){return Ve(this)?this._ownerReadableStream===void 0?b(Xe("cancel")):hn(this,n):b(Dr("cancel"))}read(n){if(!Ve(this))return b(Dr("read"));if(!ArrayBuffer.isView(n))return b(new TypeError("view must be an array buffer view"));if(n.byteLength===0)return b(new TypeError("view must have non-zero byteLength"));if(n.buffer.byteLength===0)return b(new TypeError("view's buffer must have non-zero byteLength"));if(sr(n.buffer),this._ownerReadableStream===void 0)return b(Xe("read from"));let s,a,c=_((h,F)=>{s=h,a=F});return ki(this,n,{_chunkSteps:h=>s({value:h,done:!1}),_closeSteps:h=>s({value:h,done:!0}),_errorSteps:h=>a(h)}),c}releaseLock(){if(!Ve(this))throw Dr("releaseLock");if(this._ownerReadableStream!==void 0){if(this._readIntoRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");we(this)}}}Object.defineProperties(Tt.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(Tt.prototype,i.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});function Ve(t){return!d(t)||!Object.prototype.hasOwnProperty.call(t,"_readIntoRequests")?!1:t instanceof Tt}function ki(t,n,s){let a=t._ownerReadableStream;a._disturbed=!0,a._state==="errored"?s._errorSteps(a._storedError):xa(a._readableStreamController,n,s)}function Dr(t){return new TypeError(`ReadableStreamBYOBReader.prototype.${t} can only be used on a ReadableStreamBYOBReader`)}function Pt(t,n){let{highWaterMark:s}=t;if(s===void 0)return n;if(bi(s)||s<0)throw new RangeError("Invalid highWaterMark");return s}function hr(t){let{size:n}=t;return n||(()=>1)}function mr(t,n){Se(t,n);let s=t?.highWaterMark,a=t?.size;return{highWaterMark:s===void 0?void 0:_n(s),size:a===void 0?void 0:qa(a,`${n} has member 'size' that`)}}function qa(t,n){return le(t,n),s=>_n(t(s))}function Na(t,n){Se(t,n);let s=t?.abort,a=t?.close,c=t?.start,D=t?.type,h=t?.write;return{abort:s===void 0?void 0:ja(s,t,`${n} has member 'abort' that`),close:a===void 0?void 0:za(a,t,`${n} has member 'close' that`),start:c===void 0?void 0:Ua(c,t,`${n} has member 'start' that`),write:h===void 0?void 0:Ha(h,t,`${n} has member 'write' that`),type:D}}function ja(t,n,s){return le(t,s),a=>Ce(t,n,[a])}function za(t,n,s){return le(t,s),()=>Ce(t,n,[])}function Ua(t,n,s){return le(t,s),a=>He(t,n,[a])}function Ha(t,n,s){return le(t,s),(a,c)=>Ce(t,n,[a,c])}function Ii(t,n){if(!rt(t))throw new TypeError(`${n} is not a WritableStream.`)}function Ga(t){if(typeof t!="object"||t===null)return!1;try{return typeof t.aborted=="boolean"}catch{return!1}}let Ya=typeof AbortController=="function";function Va(){if(Ya)return new AbortController}class Ot{constructor(n={},s={}){n===void 0?n=null:ai(n,"First parameter");let a=mr(s,"Second parameter"),c=Na(n,"First parameter");if($i(this),c.type!==void 0)throw new RangeError("Invalid type is specified");let h=hr(a),F=Pt(a,1);al(this,c,F,h)}get locked(){if(!rt(this))throw _r("locked");return nt(this)}abort(n=void 0){return rt(this)?nt(this)?b(new TypeError("Cannot abort a stream that already has a writer")):pr(this,n):b(_r("abort"))}close(){return rt(this)?nt(this)?b(new TypeError("Cannot close a stream that already has a writer")):_e(this)?b(new TypeError("Cannot close an already-closing stream")):Li(this):b(_r("close"))}getWriter(){if(!rt(this))throw _r("getWriter");return Wi(this)}}Object.defineProperties(Ot.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(Ot.prototype,i.toStringTag,{value:"WritableStream",configurable:!0});function Wi(t){return new xt(t)}function Qa(t,n,s,a,c=1,D=()=>1){let h=Object.create(Ot.prototype);$i(h);let F=Object.create(ot.prototype);return Ui(h,F,t,n,s,a,c,D),h}function $i(t){t._state="writable",t._storedError=void 0,t._writer=void 0,t._writableStreamController=void 0,t._writeRequests=new ae,t._inFlightWriteRequest=void 0,t._closeRequest=void 0,t._inFlightCloseRequest=void 0,t._pendingAbortRequest=void 0,t._backpressure=!1}function rt(t){return!d(t)||!Object.prototype.hasOwnProperty.call(t,"_writableStreamController")?!1:t instanceof Ot}function nt(t){return t._writer!==void 0}function pr(t,n){var s;if(t._state==="closed"||t._state==="errored")return m(void 0);t._writableStreamController._abortReason=n,(s=t._writableStreamController._abortController)===null||s===void 0||s.abort();let a=t._state;if(a==="closed"||a==="errored")return m(void 0);if(t._pendingAbortRequest!==void 0)return t._pendingAbortRequest._promise;let c=!1;a==="erroring"&&(c=!0,n=void 0);let D=_((h,F)=>{t._pendingAbortRequest={_promise:void 0,_resolve:h,_reject:F,_reason:n,_wasAlreadyErroring:c}});return t._pendingAbortRequest._promise=D,c||On(t,n),D}function Li(t){let n=t._state;if(n==="closed"||n==="errored")return b(new TypeError(`The stream (in ${n} state) is not in the writable state and cannot be closed`));let s=_((c,D)=>{let h={_resolve:c,_reject:D};t._closeRequest=h}),a=t._writer;return a!==void 0&&t._backpressure&&n==="writable"&&Nn(a),ll(t._writableStreamController),s}function Ka(t){return _((s,a)=>{let c={_resolve:s,_reject:a};t._writeRequests.push(c)})}function Pn(t,n){if(t._state==="writable"){On(t,n);return}xn(t)}function On(t,n){let s=t._writableStreamController;t._state="erroring",t._storedError=n;let a=t._writer;a!==void 0&&qi(a,n),!tl(t)&&s._started&&xn(t)}function xn(t){t._state="errored",t._writableStreamController[si]();let n=t._storedError;if(t._writeRequests.forEach(c=>{c._reject(n)}),t._writeRequests=new ae,t._pendingAbortRequest===void 0){gr(t);return}let s=t._pendingAbortRequest;if(t._pendingAbortRequest=void 0,s._wasAlreadyErroring){s._reject(n),gr(t);return}let a=t._writableStreamController[ii](s._reason);E(a,()=>{s._resolve(),gr(t)},c=>{s._reject(c),gr(t)})}function Ja(t){t._inFlightWriteRequest._resolve(void 0),t._inFlightWriteRequest=void 0}function Za(t,n){t._inFlightWriteRequest._reject(n),t._inFlightWriteRequest=void 0,Pn(t,n)}function Xa(t){t._inFlightCloseRequest._resolve(void 0),t._inFlightCloseRequest=void 0,t._state==="erroring"&&(t._storedError=void 0,t._pendingAbortRequest!==void 0&&(t._pendingAbortRequest._resolve(),t._pendingAbortRequest=void 0)),t._state="closed";let s=t._writer;s!==void 0&&Vi(s)}function el(t,n){t._inFlightCloseRequest._reject(n),t._inFlightCloseRequest=void 0,t._pendingAbortRequest!==void 0&&(t._pendingAbortRequest._reject(n),t._pendingAbortRequest=void 0),Pn(t,n)}function _e(t){return!(t._closeRequest===void 0&&t._inFlightCloseRequest===void 0)}function tl(t){return!(t._inFlightWriteRequest===void 0&&t._inFlightCloseRequest===void 0)}function rl(t){t._inFlightCloseRequest=t._closeRequest,t._closeRequest=void 0}function nl(t){t._inFlightWriteRequest=t._writeRequests.shift()}function gr(t){t._closeRequest!==void 0&&(t._closeRequest._reject(t._storedError),t._closeRequest=void 0);let n=t._writer;n!==void 0&&Mn(n,t._storedError)}function kn(t,n){let s=t._writer;s!==void 0&&n!==t._backpressure&&(n?pl(s):Nn(s)),t._backpressure=n}class xt{constructor(n){if(ve(n,1,"WritableStreamDefaultWriter"),Ii(n,"First parameter"),nt(n))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=n,n._writer=this;let s=n._state;if(s==="writable")!_e(n)&&n._backpressure?Er(this):Qi(this),yr(this);else if(s==="erroring")qn(this,n._storedError),yr(this);else if(s==="closed")Qi(this),hl(this);else{let a=n._storedError;qn(this,a),Yi(this,a)}}get closed(){return Qe(this)?this._closedPromise:b(Ke("closed"))}get desiredSize(){if(!Qe(this))throw Ke("desiredSize");if(this._ownerWritableStream===void 0)throw kt("desiredSize");return ul(this)}get ready(){return Qe(this)?this._readyPromise:b(Ke("ready"))}abort(n=void 0){return Qe(this)?this._ownerWritableStream===void 0?b(kt("abort")):ol(this,n):b(Ke("abort"))}close(){if(!Qe(this))return b(Ke("close"));let n=this._ownerWritableStream;return n===void 0?b(kt("close")):_e(n)?b(new TypeError("Cannot close an already-closing stream")):Mi(this)}releaseLock(){if(!Qe(this))throw Ke("releaseLock");this._ownerWritableStream!==void 0&&Ni(this)}write(n=void 0){return Qe(this)?this._ownerWritableStream===void 0?b(kt("write to")):ji(this,n):b(Ke("write"))}}Object.defineProperties(xt.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(xt.prototype,i.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});function Qe(t){return!d(t)||!Object.prototype.hasOwnProperty.call(t,"_ownerWritableStream")?!1:t instanceof xt}function ol(t,n){let s=t._ownerWritableStream;return pr(s,n)}function Mi(t){let n=t._ownerWritableStream;return Li(n)}function il(t){let n=t._ownerWritableStream,s=n._state;return _e(n)||s==="closed"?m(void 0):s==="errored"?b(n._storedError):Mi(t)}function sl(t,n){t._closedPromiseState==="pending"?Mn(t,n):ml(t,n)}function qi(t,n){t._readyPromiseState==="pending"?Ki(t,n):gl(t,n)}function ul(t){let n=t._ownerWritableStream,s=n._state;return s==="errored"||s==="erroring"?null:s==="closed"?0:Hi(n._writableStreamController)}function Ni(t){let n=t._ownerWritableStream,s=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");qi(t,s),sl(t,s),n._writer=void 0,t._ownerWritableStream=void 0}function ji(t,n){let s=t._ownerWritableStream,a=s._writableStreamController,c=cl(a,n);if(s!==t._ownerWritableStream)return b(kt("write to"));let D=s._state;if(D==="errored")return b(s._storedError);if(_e(s)||D==="closed")return b(new TypeError("The stream is closing or closed and cannot be written to"));if(D==="erroring")return b(s._storedError);let h=Ka(s);return fl(a,n,c),h}let zi={};class ot{constructor(){throw new TypeError("Illegal constructor")}get abortReason(){if(!In(this))throw Ln("abortReason");return this._abortReason}get signal(){if(!In(this))throw Ln("signal");if(this._abortController===void 0)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(n=void 0){if(!In(this))throw Ln("error");this._controlledWritableStream._state==="writable"&&Gi(this,n)}[ii](n){let s=this._abortAlgorithm(n);return br(this),s}[si](){We(this)}}Object.defineProperties(ot.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(ot.prototype,i.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});function In(t){return!d(t)||!Object.prototype.hasOwnProperty.call(t,"_controlledWritableStream")?!1:t instanceof ot}function Ui(t,n,s,a,c,D,h,F){n._controlledWritableStream=t,t._writableStreamController=n,n._queue=void 0,n._queueTotalSize=void 0,We(n),n._abortReason=void 0,n._abortController=Va(),n._started=!1,n._strategySizeAlgorithm=F,n._strategyHWM=h,n._writeAlgorithm=a,n._closeAlgorithm=c,n._abortAlgorithm=D;let S=$n(n);kn(t,S);let A=s(),B=m(A);E(B,()=>{n._started=!0,Fr(n)},T=>{n._started=!0,Pn(t,T)})}function al(t,n,s,a){let c=Object.create(ot.prototype),D=()=>{},h=()=>m(void 0),F=()=>m(void 0),S=()=>m(void 0);n.start!==void 0&&(D=()=>n.start(c)),n.write!==void 0&&(h=A=>n.write(A,c)),n.close!==void 0&&(F=()=>n.close()),n.abort!==void 0&&(S=A=>n.abort(A)),Ui(t,c,D,h,F,S,s,a)}function br(t){t._writeAlgorithm=void 0,t._closeAlgorithm=void 0,t._abortAlgorithm=void 0,t._strategySizeAlgorithm=void 0}function ll(t){wn(t,zi,0),Fr(t)}function cl(t,n){try{return t._strategySizeAlgorithm(n)}catch(s){return Wn(t,s),1}}function Hi(t){return t._strategyHWM-t._queueTotalSize}function fl(t,n,s){try{wn(t,n,s)}catch(c){Wn(t,c);return}let a=t._controlledWritableStream;if(!_e(a)&&a._state==="writable"){let c=$n(t);kn(a,c)}Fr(t)}function Fr(t){let n=t._controlledWritableStream;if(!t._started||n._inFlightWriteRequest!==void 0)return;if(n._state==="erroring"){xn(n);return}if(t._queue.length===0)return;let a=Oa(t);a===zi?dl(t):Dl(t,a)}function Wn(t,n){t._controlledWritableStream._state==="writable"&&Gi(t,n)}function dl(t){let n=t._controlledWritableStream;rl(n),Cn(t);let s=t._closeAlgorithm();br(t),E(s,()=>{Xa(n)},a=>{el(n,a)})}function Dl(t,n){let s=t._controlledWritableStream;nl(s);let a=t._writeAlgorithm(n);E(a,()=>{Ja(s);let c=s._state;if(Cn(t),!_e(s)&&c==="writable"){let D=$n(t);kn(s,D)}Fr(t)},c=>{s._state==="writable"&&br(t),Za(s,c)})}function $n(t){return Hi(t)<=0}function Gi(t,n){let s=t._controlledWritableStream;br(t),On(s,n)}function _r(t){return new TypeError(`WritableStream.prototype.${t} can only be used on a WritableStream`)}function Ln(t){return new TypeError(`WritableStreamDefaultController.prototype.${t} can only be used on a WritableStreamDefaultController`)}function Ke(t){return new TypeError(`WritableStreamDefaultWriter.prototype.${t} can only be used on a WritableStreamDefaultWriter`)}function kt(t){return new TypeError("Cannot "+t+" a stream using a released writer")}function yr(t){t._closedPromise=_((n,s)=>{t._closedPromise_resolve=n,t._closedPromise_reject=s,t._closedPromiseState="pending"})}function Yi(t,n){yr(t),Mn(t,n)}function hl(t){yr(t),Vi(t)}function Mn(t,n){t._closedPromise_reject!==void 0&&(I(t._closedPromise),t._closedPromise_reject(n),t._closedPromise_resolve=void 0,t._closedPromise_reject=void 0,t._closedPromiseState="rejected")}function ml(t,n){Yi(t,n)}function Vi(t){t._closedPromise_resolve!==void 0&&(t._closedPromise_resolve(void 0),t._closedPromise_resolve=void 0,t._closedPromise_reject=void 0,t._closedPromiseState="resolved")}function Er(t){t._readyPromise=_((n,s)=>{t._readyPromise_resolve=n,t._readyPromise_reject=s}),t._readyPromiseState="pending"}function qn(t,n){Er(t),Ki(t,n)}function Qi(t){Er(t),Nn(t)}function Ki(t,n){t._readyPromise_reject!==void 0&&(I(t._readyPromise),t._readyPromise_reject(n),t._readyPromise_resolve=void 0,t._readyPromise_reject=void 0,t._readyPromiseState="rejected")}function pl(t){Er(t)}function gl(t,n){qn(t,n)}function Nn(t){t._readyPromise_resolve!==void 0&&(t._readyPromise_resolve(void 0),t._readyPromise_resolve=void 0,t._readyPromise_reject=void 0,t._readyPromiseState="fulfilled")}let Ji=typeof DOMException<"u"?DOMException:void 0;function bl(t){if(!(typeof t=="function"||typeof t=="object"))return!1;try{return new t,!0}catch{return!1}}function Fl(){let t=function(s,a){this.message=s||"",this.name=a||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return t.prototype=Object.create(Error.prototype),Object.defineProperty(t.prototype,"constructor",{value:t,writable:!0,configurable:!0}),t}let _l=bl(Ji)?Ji:Fl();function Zi(t,n,s,a,c,D){let h=et(t),F=Wi(n);t._disturbed=!0;let S=!1,A=m(void 0);return _((B,T)=>{let z;if(D!==void 0){if(z=()=>{let y=new _l("Aborted","AbortError"),v=[];a||v.push(()=>n._state==="writable"?pr(n,y):m(void 0)),c||v.push(()=>t._state==="readable"?fe(t,y):m(void 0)),X(()=>Promise.all(v.map(k=>k())),!0,y)},D.aborted){z();return}D.addEventListener("abort",z)}function de(){return _((y,v)=>{function k(te){te?y():R(ut(),k,v)}k(!1)})}function ut(){return S?m(!0):R(F._readyPromise,()=>_((y,v)=>{St(h,{_chunkSteps:k=>{A=R(ji(F,k),void 0,u),y(!1)},_closeSteps:()=>y(!0),_errorSteps:v})}))}if(Ae(t,h._closedPromise,y=>{a?ne(!0,y):X(()=>pr(n,y),!0,y)}),Ae(n,F._closedPromise,y=>{c?ne(!0,y):X(()=>fe(t,y),!0,y)}),Z(t,h._closedPromise,()=>{s?ne():X(()=>il(F))}),_e(n)||n._state==="closed"){let y=new TypeError("the destination writable stream closed before all data could be piped to it");c?ne(!0,y):X(()=>fe(t,y),!0,y)}I(de());function Ne(){let y=A;return R(A,()=>y!==A?Ne():void 0)}function Ae(y,v,k){y._state==="errored"?k(y._storedError):Q(v,k)}function Z(y,v,k){y._state==="closed"?k():V(v,k)}function X(y,v,k){if(S)return;S=!0,n._state==="writable"&&!_e(n)?V(Ne(),te):te();function te(){E(y(),()=>Re(v,k),at=>Re(!0,at))}}function ne(y,v){S||(S=!0,n._state==="writable"&&!_e(n)?V(Ne(),()=>Re(y,v)):Re(y,v))}function Re(y,v){Ni(F),we(h),D!==void 0&&D.removeEventListener("abort",z),y?T(v):B(void 0)}})}class it{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!Cr(this))throw vr("desiredSize");return jn(this)}close(){if(!Cr(this))throw vr("close");if(!st(this))throw new TypeError("The stream is not in a state that permits close");Wt(this)}enqueue(n=void 0){if(!Cr(this))throw vr("enqueue");if(!st(this))throw new TypeError("The stream is not in a state that permits enqueue");return Sr(this,n)}error(n=void 0){if(!Cr(this))throw vr("error");$e(this,n)}[gn](n){We(this);let s=this._cancelAlgorithm(n);return wr(this),s}[bn](n){let s=this._controlledReadableStream;if(this._queue.length>0){let a=Cn(this);this._closeRequested&&this._queue.length===0?(wr(this),$t(s)):It(this),n._chunkSteps(a)}else fi(s,n),It(this)}}Object.defineProperties(it.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(it.prototype,i.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});function Cr(t){return!d(t)||!Object.prototype.hasOwnProperty.call(t,"_controlledReadableStream")?!1:t instanceof it}function It(t){if(!Xi(t))return;if(t._pulling){t._pullAgain=!0;return}t._pulling=!0;let s=t._pullAlgorithm();E(s,()=>{t._pulling=!1,t._pullAgain&&(t._pullAgain=!1,It(t))},a=>{$e(t,a)})}function Xi(t){let n=t._controlledReadableStream;return!st(t)||!t._started?!1:!!(qe(n)&&or(n)>0||jn(t)>0)}function wr(t){t._pullAlgorithm=void 0,t._cancelAlgorithm=void 0,t._strategySizeAlgorithm=void 0}function Wt(t){if(!st(t))return;let n=t._controlledReadableStream;t._closeRequested=!0,t._queue.length===0&&(wr(t),$t(n))}function Sr(t,n){if(!st(t))return;let s=t._controlledReadableStream;if(qe(s)&&or(s)>0)En(s,n,!1);else{let a;try{a=t._strategySizeAlgorithm(n)}catch(c){throw $e(t,c),c}try{wn(t,n,a)}catch(c){throw $e(t,c),c}}It(t)}function $e(t,n){let s=t._controlledReadableStream;s._state==="readable"&&(We(t),wr(t),ns(s,n))}function jn(t){let n=t._controlledReadableStream._state;return n==="errored"?null:n==="closed"?0:t._strategyHWM-t._queueTotalSize}function yl(t){return!Xi(t)}function st(t){let n=t._controlledReadableStream._state;return!t._closeRequested&&n==="readable"}function es(t,n,s,a,c,D,h){n._controlledReadableStream=t,n._queue=void 0,n._queueTotalSize=void 0,We(n),n._started=!1,n._closeRequested=!1,n._pullAgain=!1,n._pulling=!1,n._strategySizeAlgorithm=h,n._strategyHWM=D,n._pullAlgorithm=a,n._cancelAlgorithm=c,t._readableStreamController=n;let F=s();E(m(F),()=>{n._started=!0,It(n)},S=>{$e(n,S)})}function El(t,n,s,a){let c=Object.create(it.prototype),D=()=>{},h=()=>m(void 0),F=()=>m(void 0);n.start!==void 0&&(D=()=>n.start(c)),n.pull!==void 0&&(h=()=>n.pull(c)),n.cancel!==void 0&&(F=S=>n.cancel(S)),es(t,c,D,h,F,s,a)}function vr(t){return new TypeError(`ReadableStreamDefaultController.prototype.${t} can only be used on a ReadableStreamDefaultController`)}function Cl(t,n){return Ge(t._readableStreamController)?Sl(t):wl(t)}function wl(t,n){let s=et(t),a=!1,c=!1,D=!1,h=!1,F,S,A,B,T,z=_(Z=>{T=Z});function de(){return a?(c=!0,m(void 0)):(a=!0,St(s,{_chunkSteps:X=>{j(()=>{c=!1;let ne=X,Re=X;D||Sr(A._readableStreamController,ne),h||Sr(B._readableStreamController,Re),a=!1,c&&de()})},_closeSteps:()=>{a=!1,D||Wt(A._readableStreamController),h||Wt(B._readableStreamController),(!D||!h)&&T(void 0)},_errorSteps:()=>{a=!1}}),m(void 0))}function ut(Z){if(D=!0,F=Z,h){let X=vt([F,S]),ne=fe(t,X);T(ne)}return z}function Ne(Z){if(h=!0,S=Z,D){let X=vt([F,S]),ne=fe(t,X);T(ne)}return z}function Ae(){}return A=zn(Ae,de,ut),B=zn(Ae,de,Ne),Q(s._closedPromise,Z=>{$e(A._readableStreamController,Z),$e(B._readableStreamController,Z),(!D||!h)&&T(void 0)}),[A,B]}function Sl(t){let n=et(t),s=!1,a=!1,c=!1,D=!1,h=!1,F,S,A,B,T,z=_(y=>{T=y});function de(y){Q(y._closedPromise,v=>{y===n&&(ce(A._readableStreamController,v),ce(B._readableStreamController,v),(!D||!h)&&T(void 0))})}function ut(){Ve(n)&&(we(n),n=et(t),de(n)),St(n,{_chunkSteps:v=>{j(()=>{a=!1,c=!1;let k=v,te=v;if(!D&&!h)try{te=yi(v)}catch(at){ce(A._readableStreamController,at),ce(B._readableStreamController,at),T(fe(t,at));return}D||cr(A._readableStreamController,k),h||cr(B._readableStreamController,te),s=!1,a?Ae():c&&Z()})},_closeSteps:()=>{s=!1,D||Rt(A._readableStreamController),h||Rt(B._readableStreamController),A._readableStreamController._pendingPullIntos.length>0&&fr(A._readableStreamController,0),B._readableStreamController._pendingPullIntos.length>0&&fr(B._readableStreamController,0),(!D||!h)&&T(void 0)},_errorSteps:()=>{s=!1}})}function Ne(y,v){Ie(n)&&(we(n),n=Pi(t),de(n));let k=v?B:A,te=v?A:B;ki(n,y,{_chunkSteps:lt=>{j(()=>{a=!1,c=!1;let ct=v?h:D;if(v?D:h)ct||dr(k._readableStreamController,lt);else{let ps;try{ps=yi(lt)}catch(Hn){ce(k._readableStreamController,Hn),ce(te._readableStreamController,Hn),T(fe(t,Hn));return}ct||dr(k._readableStreamController,lt),cr(te._readableStreamController,ps)}s=!1,a?Ae():c&&Z()})},_closeSteps:lt=>{s=!1;let ct=v?h:D,Ir=v?D:h;ct||Rt(k._readableStreamController),Ir||Rt(te._readableStreamController),lt!==void 0&&(ct||dr(k._readableStreamController,lt),!Ir&&te._readableStreamController._pendingPullIntos.length>0&&fr(te._readableStreamController,0)),(!ct||!Ir)&&T(void 0)},_errorSteps:()=>{s=!1}})}function Ae(){if(s)return a=!0,m(void 0);s=!0;let y=Rn(A._readableStreamController);return y===null?ut():Ne(y._view,!1),m(void 0)}function Z(){if(s)return c=!0,m(void 0);s=!0;let y=Rn(B._readableStreamController);return y===null?ut():Ne(y._view,!0),m(void 0)}function X(y){if(D=!0,F=y,h){let v=vt([F,S]),k=fe(t,v);T(k)}return z}function ne(y){if(h=!0,S=y,D){let v=vt([F,S]),k=fe(t,v);T(k)}return z}function Re(){}return A=rs(Re,Ae,X),B=rs(Re,Z,ne),de(n),[A,B]}function vl(t,n){Se(t,n);let s=t,a=s?.autoAllocateChunkSize,c=s?.cancel,D=s?.pull,h=s?.start,F=s?.type;return{autoAllocateChunkSize:a===void 0?void 0:ci(a,`${n} has member 'autoAllocateChunkSize' that`),cancel:c===void 0?void 0:Al(c,s,`${n} has member 'cancel' that`),pull:D===void 0?void 0:Rl(D,s,`${n} has member 'pull' that`),start:h===void 0?void 0:Bl(h,s,`${n} has member 'start' that`),type:F===void 0?void 0:Tl(F,`${n} has member 'type' that`)}}function Al(t,n,s){return le(t,s),a=>Ce(t,n,[a])}function Rl(t,n,s){return le(t,s),a=>Ce(t,n,[a])}function Bl(t,n,s){return le(t,s),a=>He(t,n,[a])}function Tl(t,n){if(t=`${t}`,t!=="bytes")throw new TypeError(`${n} '${t}' is not a valid enumeration value for ReadableStreamType`);return t}function Pl(t,n){Se(t,n);let s=t?.mode;return{mode:s===void 0?void 0:Ol(s,`${n} has member 'mode' that`)}}function Ol(t,n){if(t=`${t}`,t!=="byob")throw new TypeError(`${n} '${t}' is not a valid enumeration value for ReadableStreamReaderMode`);return t}function xl(t,n){return Se(t,n),{preventCancel:!!t?.preventCancel}}function ts(t,n){Se(t,n);let s=t?.preventAbort,a=t?.preventCancel,c=t?.preventClose,D=t?.signal;return D!==void 0&&kl(D,`${n} has member 'signal' that`),{preventAbort:!!s,preventCancel:!!a,preventClose:!!c,signal:D}}function kl(t,n){if(!Ga(t))throw new TypeError(`${n} is not an AbortSignal.`)}function Il(t,n){Se(t,n);let s=t?.readable;Fn(s,"readable","ReadableWritablePair"),yn(s,`${n} has member 'readable' that`);let a=t?.writable;return Fn(a,"writable","ReadableWritablePair"),Ii(a,`${n} has member 'writable' that`),{readable:s,writable:a}}class Le{constructor(n={},s={}){n===void 0?n=null:ai(n,"First parameter");let a=mr(s,"Second parameter"),c=vl(n,"First parameter");if(Un(this),c.type==="bytes"){if(a.size!==void 0)throw new RangeError("The strategy for a byte stream cannot have a size function");let D=Pt(a,0);$a(this,c,D)}else{let D=hr(a),h=Pt(a,1);El(this,c,h,D)}}get locked(){if(!Me(this))throw Je("locked");return qe(this)}cancel(n=void 0){return Me(this)?qe(this)?b(new TypeError("Cannot cancel a stream that already has a reader")):fe(this,n):b(Je("cancel"))}getReader(n=void 0){if(!Me(this))throw Je("getReader");return Pl(n,"First parameter").mode===void 0?et(this):Pi(this)}pipeThrough(n,s={}){if(!Me(this))throw Je("pipeThrough");ve(n,1,"pipeThrough");let a=Il(n,"First parameter"),c=ts(s,"Second parameter");if(qe(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(nt(a.writable))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");let D=Zi(this,a.writable,c.preventClose,c.preventAbort,c.preventCancel,c.signal);return I(D),a.readable}pipeTo(n,s={}){if(!Me(this))return b(Je("pipeTo"));if(n===void 0)return b("Parameter 1 is required in 'pipeTo'.");if(!rt(n))return b(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));let a;try{a=ts(s,"Second parameter")}catch(c){return b(c)}return qe(this)?b(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):nt(n)?b(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):Zi(this,n,a.preventClose,a.preventAbort,a.preventCancel,a.signal)}tee(){if(!Me(this))throw Je("tee");let n=Cl(this);return vt(n)}values(n=void 0){if(!Me(this))throw Je("values");let s=xl(n,"First parameter");return Ta(this,s.preventCancel)}}Object.defineProperties(Le.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(Le.prototype,i.toStringTag,{value:"ReadableStream",configurable:!0}),typeof i.asyncIterator=="symbol"&&Object.defineProperty(Le.prototype,i.asyncIterator,{value:Le.prototype.values,writable:!0,configurable:!0});function zn(t,n,s,a=1,c=()=>1){let D=Object.create(Le.prototype);Un(D);let h=Object.create(it.prototype);return es(D,h,t,n,s,a,c),D}function rs(t,n,s){let a=Object.create(Le.prototype);Un(a);let c=Object.create(tt.prototype);return Ti(a,c,t,n,s,0,void 0),a}function Un(t){t._state="readable",t._reader=void 0,t._storedError=void 0,t._disturbed=!1}function Me(t){return!d(t)||!Object.prototype.hasOwnProperty.call(t,"_readableStreamController")?!1:t instanceof Le}function qe(t){return t._reader!==void 0}function fe(t,n){if(t._disturbed=!0,t._state==="closed")return m(void 0);if(t._state==="errored")return b(t._storedError);$t(t);let s=t._reader;s!==void 0&&Ve(s)&&(s._readIntoRequests.forEach(c=>{c._closeSteps(void 0)}),s._readIntoRequests=new ae);let a=t._readableStreamController[gn](n);return P(a,u)}function $t(t){t._state="closed";let n=t._reader;n!==void 0&&(oi(n),Ie(n)&&(n._readRequests.forEach(s=>{s._closeSteps()}),n._readRequests=new ae))}function ns(t,n){t._state="errored",t._storedError=n;let s=t._reader;s!==void 0&&(pn(s,n),Ie(s)?(s._readRequests.forEach(a=>{a._errorSteps(n)}),s._readRequests=new ae):(s._readIntoRequests.forEach(a=>{a._errorSteps(n)}),s._readIntoRequests=new ae))}function Je(t){return new TypeError(`ReadableStream.prototype.${t} can only be used on a ReadableStream`)}function os(t,n){Se(t,n);let s=t?.highWaterMark;return Fn(s,"highWaterMark","QueuingStrategyInit"),{highWaterMark:_n(s)}}let is=t=>t.byteLength;try{Object.defineProperty(is,"name",{value:"size",configurable:!0})}catch{}class Ar{constructor(n){ve(n,1,"ByteLengthQueuingStrategy"),n=os(n,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=n.highWaterMark}get highWaterMark(){if(!us(this))throw ss("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!us(this))throw ss("size");return is}}Object.defineProperties(Ar.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(Ar.prototype,i.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});function ss(t){return new TypeError(`ByteLengthQueuingStrategy.prototype.${t} can only be used on a ByteLengthQueuingStrategy`)}function us(t){return!d(t)||!Object.prototype.hasOwnProperty.call(t,"_byteLengthQueuingStrategyHighWaterMark")?!1:t instanceof Ar}let as=()=>1;try{Object.defineProperty(as,"name",{value:"size",configurable:!0})}catch{}class Rr{constructor(n){ve(n,1,"CountQueuingStrategy"),n=os(n,"First parameter"),this._countQueuingStrategyHighWaterMark=n.highWaterMark}get highWaterMark(){if(!cs(this))throw ls("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!cs(this))throw ls("size");return as}}Object.defineProperties(Rr.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(Rr.prototype,i.toStringTag,{value:"CountQueuingStrategy",configurable:!0});function ls(t){return new TypeError(`CountQueuingStrategy.prototype.${t} can only be used on a CountQueuingStrategy`)}function cs(t){return!d(t)||!Object.prototype.hasOwnProperty.call(t,"_countQueuingStrategyHighWaterMark")?!1:t instanceof Rr}function Wl(t,n){Se(t,n);let s=t?.flush,a=t?.readableType,c=t?.start,D=t?.transform,h=t?.writableType;return{flush:s===void 0?void 0:$l(s,t,`${n} has member 'flush' that`),readableType:a,start:c===void 0?void 0:Ll(c,t,`${n} has member 'start' that`),transform:D===void 0?void 0:Ml(D,t,`${n} has member 'transform' that`),writableType:h}}function $l(t,n,s){return le(t,s),a=>Ce(t,n,[a])}function Ll(t,n,s){return le(t,s),a=>He(t,n,[a])}function Ml(t,n,s){return le(t,s),(a,c)=>Ce(t,n,[a,c])}class Br{constructor(n={},s={},a={}){n===void 0&&(n=null);let c=mr(s,"Second parameter"),D=mr(a,"Third parameter"),h=Wl(n,"First parameter");if(h.readableType!==void 0)throw new RangeError("Invalid readableType specified");if(h.writableType!==void 0)throw new RangeError("Invalid writableType specified");let F=Pt(D,0),S=hr(D),A=Pt(c,1),B=hr(c),T,z=_(de=>{T=de});ql(this,z,A,B,F,S),jl(this,h),h.start!==void 0?T(h.start(this._transformStreamController)):T(void 0)}get readable(){if(!fs(this))throw ms("readable");return this._readable}get writable(){if(!fs(this))throw ms("writable");return this._writable}}Object.defineProperties(Br.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(Br.prototype,i.toStringTag,{value:"TransformStream",configurable:!0});function ql(t,n,s,a,c,D){function h(){return n}function F(z){return Hl(t,z)}function S(z){return Gl(t,z)}function A(){return Yl(t)}t._writable=Qa(h,F,A,S,s,a);function B(){return Vl(t)}function T(z){return Pr(t,z),m(void 0)}t._readable=zn(h,B,T,c,D),t._backpressure=void 0,t._backpressureChangePromise=void 0,t._backpressureChangePromise_resolve=void 0,Or(t,!0),t._transformStreamController=void 0}function fs(t){return!d(t)||!Object.prototype.hasOwnProperty.call(t,"_transformStreamController")?!1:t instanceof Br}function Tr(t,n){$e(t._readable._readableStreamController,n),Pr(t,n)}function Pr(t,n){ds(t._transformStreamController),Wn(t._writable._writableStreamController,n),t._backpressure&&Or(t,!1)}function Or(t,n){t._backpressureChangePromise!==void 0&&t._backpressureChangePromise_resolve(),t._backpressureChangePromise=_(s=>{t._backpressureChangePromise_resolve=s}),t._backpressure=n}class Lt{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!xr(this))throw kr("desiredSize");let n=this._controlledTransformStream._readable._readableStreamController;return jn(n)}enqueue(n=void 0){if(!xr(this))throw kr("enqueue");Ds(this,n)}error(n=void 0){if(!xr(this))throw kr("error");zl(this,n)}terminate(){if(!xr(this))throw kr("terminate");Ul(this)}}Object.defineProperties(Lt.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),typeof i.toStringTag=="symbol"&&Object.defineProperty(Lt.prototype,i.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});function xr(t){return!d(t)||!Object.prototype.hasOwnProperty.call(t,"_controlledTransformStream")?!1:t instanceof Lt}function Nl(t,n,s,a){n._controlledTransformStream=t,t._transformStreamController=n,n._transformAlgorithm=s,n._flushAlgorithm=a}function jl(t,n){let s=Object.create(Lt.prototype),a=D=>{try{return Ds(s,D),m(void 0)}catch(h){return b(h)}},c=()=>m(void 0);n.transform!==void 0&&(a=D=>n.transform(D,s)),n.flush!==void 0&&(c=()=>n.flush(s)),Nl(t,s,a,c)}function ds(t){t._transformAlgorithm=void 0,t._flushAlgorithm=void 0}function Ds(t,n){let s=t._controlledTransformStream,a=s._readable._readableStreamController;if(!st(a))throw new TypeError("Readable side is not in a state that permits enqueue");try{Sr(a,n)}catch(D){throw Pr(s,D),s._readable._storedError}yl(a)!==s._backpressure&&Or(s,!0)}function zl(t,n){Tr(t._controlledTransformStream,n)}function hs(t,n){let s=t._transformAlgorithm(n);return P(s,void 0,a=>{throw Tr(t._controlledTransformStream,a),a})}function Ul(t){let n=t._controlledTransformStream,s=n._readable._readableStreamController;Wt(s);let a=new TypeError("TransformStream terminated");Pr(n,a)}function Hl(t,n){let s=t._transformStreamController;if(t._backpressure){let a=t._backpressureChangePromise;return P(a,()=>{let c=t._writable;if(c._state==="erroring")throw c._storedError;return hs(s,n)})}return hs(s,n)}function Gl(t,n){return Tr(t,n),m(void 0)}function Yl(t){let n=t._readable,s=t._transformStreamController,a=s._flushAlgorithm();return ds(s),P(a,()=>{if(n._state==="errored")throw n._storedError;Wt(n._readableStreamController)},c=>{throw Tr(t,c),n._storedError})}function Vl(t){return Or(t,!1),t._backpressureChangePromise}function kr(t){return new TypeError(`TransformStreamDefaultController.prototype.${t} can only be used on a TransformStreamDefaultController`)}function ms(t){return new TypeError(`TransformStream.prototype.${t} can only be used on a TransformStream`)}o.ByteLengthQueuingStrategy=Ar,o.CountQueuingStrategy=Rr,o.ReadableByteStreamController=tt,o.ReadableStream=Le,o.ReadableStreamBYOBReader=Tt,o.ReadableStreamBYOBRequest=At,o.ReadableStreamDefaultController=it,o.ReadableStreamDefaultReader=wt,o.TransformStream=Br,o.TransformStreamDefaultController=Lt,o.WritableStream=Ot,o.WritableStreamDefaultController=ot,o.WritableStreamDefaultWriter=xt,Object.defineProperty(o,"__esModule",{value:!0})})}}),Df=jo({"node_modules/fetch-blob/streams.cjs"(){var e=65536;if(!globalThis.ReadableStream)try{let r=require("process"),{emitWarning:o}=r;try{r.emitWarning=()=>{},Object.assign(globalThis,require("stream/web")),r.emitWarning=o}catch(i){throw r.emitWarning=o,i}}catch{Object.assign(globalThis,df())}try{let{Blob:r}=require("buffer");r&&!r.prototype.stream&&(r.prototype.stream=function(i){let u=0,l=this;return new ReadableStream({type:"bytes",async pull(f){let p=await l.slice(u,Math.min(l.size,u+e)).arrayBuffer();u+=p.byteLength,f.enqueue(new Uint8Array(p)),u===l.size&&f.close()}})})}catch{}}});async function*To(e,r=!0){for(let o of e)if("stream"in o)yield*o.stream();else if(ArrayBuffer.isView(o))if(r){let i=o.byteOffset,u=o.byteOffset+o.byteLength;for(;i!==u;){let l=Math.min(u-i,$o),f=o.buffer.slice(i,i+l);i+=f.byteLength,yield new Uint8Array(f)}}else yield o;else{let i=0,u=o;for(;i!==u.size;){let f=await u.slice(i,Math.min(u.size,i+$o)).arrayBuffer();i+=f.byteLength,yield new Uint8Array(f)}}}var hf,$o,Po,Lo,_t,rr=tr({"node_modules/fetch-blob/index.js"(){hf=J(Df()),$o=65536,Po=class Mo{#e=[];#r="";#t=0;#o="transparent";constructor(r=[],o={}){if(typeof r!="object"||r===null)throw new TypeError("Failed to construct 'Blob': The provided value cannot be converted to a sequence.");if(typeof r[Symbol.iterator]!="function")throw new TypeError("Failed to construct 'Blob': The object must have a callable @@iterator property.");if(typeof o!="object"&&typeof o!="function")throw new TypeError("Failed to construct 'Blob': parameter 2 cannot convert to dictionary.");o===null&&(o={});let i=new TextEncoder;for(let l of r){let f;ArrayBuffer.isView(l)?f=new Uint8Array(l.buffer.slice(l.byteOffset,l.byteOffset+l.byteLength)):l instanceof ArrayBuffer?f=new Uint8Array(l.slice(0)):l instanceof Mo?f=l:f=i.encode(`${l}`),this.#t+=ArrayBuffer.isView(f)?f.byteLength:f.size,this.#e.push(f)}this.#o=`${o.endings===void 0?"transparent":o.endings}`;let u=o.type===void 0?"":String(o.type);this.#r=/^[\x20-\x7E]*$/.test(u)?u:""}get size(){return this.#t}get type(){return this.#r}async text(){let r=new TextDecoder,o="";for await(let i of To(this.#e,!1))o+=r.decode(i,{stream:!0});return o+=r.decode(),o}async arrayBuffer(){let r=new Uint8Array(this.size),o=0;for await(let i of To(this.#e,!1))r.set(i,o),o+=i.length;return r.buffer}stream(){let r=To(this.#e,!0);return new globalThis.ReadableStream({type:"bytes",async pull(o){let i=await r.next();i.done?o.close():o.enqueue(i.value)},async cancel(){await r.return()}})}slice(r=0,o=this.size,i=""){let{size:u}=this,l=r<0?Math.max(u+r,0):Math.min(r,u),f=o<0?Math.max(u+o,0):Math.min(o,u),d=Math.max(f-l,0),p=this.#e,g=[],O=0;for(let N of p){if(O>=d)break;let _=ArrayBuffer.isView(N)?N.byteLength:N.size;if(l&&_<=l)l-=_,f-=_;else{let m;ArrayBuffer.isView(N)?(m=N.subarray(l,Math.min(_,f)),O+=m.byteLength):(m=N.slice(l,Math.min(_,f)),O+=m.size),f-=_,g.push(m),l=0}}let H=new Mo([],{type:String(i).toLowerCase()});return H.#t=d,H.#e=g,H}get[Symbol.toStringTag](){return"Blob"}static[Symbol.hasInstance](r){return r&&typeof r=="object"&&typeof r.constructor=="function"&&(typeof r.stream=="function"||typeof r.arrayBuffer=="function")&&/^(Blob|File)$/.test(r[Symbol.toStringTag])}},Object.defineProperties(Po.prototype,{size:{enumerable:!0},type:{enumerable:!0},slice:{enumerable:!0}}),Lo=Po,_t=Lo}}),Fu,_u,nr,Iu=tr({"node_modules/fetch-blob/file.js"(){rr(),Fu=class extends _t{#e=0;#r="";constructor(r,o,i={}){if(arguments.length<2)throw new TypeError(`Failed to construct 'File': 2 arguments required, but only ${arguments.length} present.`);super(r,i),i===null&&(i={});let u=i.lastModified===void 0?Date.now():Number(i.lastModified);Number.isNaN(u)||(this.#e=u),this.#r=String(o)}get name(){return this.#r}get lastModified(){return this.#e}get[Symbol.toStringTag](){return"File"}static[Symbol.hasInstance](r){return!!r&&r instanceof _t&&/^(File)$/.test(r[Symbol.toStringTag])}},_u=Fu,nr=_u}});function mf(e,r=_t){var o=`${qo()}${qo()}`.replace(/\./g,"").slice(-28).padStart(32,"-"),i=[],u=`--${o}\r
Content-Disposition: form-data; name="`;return e.forEach((l,f)=>typeof l=="string"?i.push(u+tn(f)+`"\r
\r
${l.replace(/\r(?!\n)|(?<!\r)\n/g,`\r
`)}\r
`):i.push(u+tn(f)+`"; filename="${tn(l.name,1)}"\r
Content-Type: ${l.type||"application/octet-stream"}\r
\r
`,l,`\r
`)),i.push(`--${o}--`),new r(i,{type:"multipart/form-data; boundary="+o})}var pt,yu,Eu,qo,Cu,Oo,tn,je,yt,an=tr({"node_modules/formdata-polyfill/esm.min.js"(){rr(),Iu(),{toStringTag:pt,iterator:yu,hasInstance:Eu}=Symbol,qo=Math.random,Cu="append,set,get,getAll,delete,keys,values,entries,forEach,constructor".split(","),Oo=(e,r,o)=>(e+="",/^(Blob|File)$/.test(r&&r[pt])?[(o=o!==void 0?o+"":r[pt]=="File"?r.name:"blob",e),r.name!==o||r[pt]=="blob"?new nr([r],o,r):r]:[e,r+""]),tn=(e,r)=>(r?e:e.replace(/\r?\n|\r/g,`\r
`)).replace(/\n/g,"%0A").replace(/\r/g,"%0D").replace(/"/g,"%22"),je=(e,r,o)=>{if(r.length<o)throw new TypeError(`Failed to execute '${e}' on 'FormData': ${o} arguments required, but only ${r.length} present.`)},yt=class{#e=[];constructor(...r){if(r.length)throw new TypeError("Failed to construct 'FormData': parameter 1 is not of type 'HTMLFormElement'.")}get[pt](){return"FormData"}[yu](){return this.entries()}static[Eu](r){return r&&typeof r=="object"&&r[pt]==="FormData"&&!Cu.some(o=>typeof r[o]!="function")}append(...r){je("append",arguments,2),this.#e.push(Oo(...r))}delete(r){je("delete",arguments,1),r+="",this.#e=this.#e.filter(([o])=>o!==r)}get(r){je("get",arguments,1),r+="";for(var o=this.#e,i=o.length,u=0;u<i;u++)if(o[u][0]===r)return o[u][1];return null}getAll(r,o){return je("getAll",arguments,1),o=[],r+="",this.#e.forEach(i=>i[0]===r&&o.push(i[1])),o}has(r){return je("has",arguments,1),r+="",this.#e.some(o=>o[0]===r)}forEach(r,o){je("forEach",arguments,1);for(var[i,u]of this)r.call(o,u,i,this)}set(...r){je("set",arguments,2);var o=[],i=!0;r=Oo(...r),this.#e.forEach(u=>{u[0]===r[0]?i&&(i=!o.push(r)):o.push(u)}),i&&o.push(r),this.#e=o}*entries(){yield*this.#e}*keys(){for(var[r]of this)yield r}*values(){for(var[,r]of this)yield r}}}}),pf=jo({"node_modules/node-domexception/index.js"(e,r){if(!globalThis.DOMException)try{let{MessageChannel:o}=require("worker_threads"),i=new o().port1,u=new ArrayBuffer;i.postMessage(u,[u,u])}catch(o){o.constructor.name==="DOMException"&&(globalThis.DOMException=o.constructor)}r.exports=globalThis.DOMException}}),Qt,wu,Su,Zr,Wu,$u,Lu,Mu,xo,ko,Xr,qu=tr({"node_modules/fetch-blob/from.js"(){Qt=J(require("fs")),wu=J(require("path")),Su=J(pf()),Iu(),rr(),{stat:Zr}=Qt.promises,Wu=(e,r)=>xo((0,Qt.statSync)(e),e,r),$u=(e,r)=>Zr(e).then(o=>xo(o,e,r)),Lu=(e,r)=>Zr(e).then(o=>ko(o,e,r)),Mu=(e,r)=>ko((0,Qt.statSync)(e),e,r),xo=(e,r,o="")=>new _t([new Xr({path:r,size:e.size,lastModified:e.mtimeMs,start:0})],{type:o}),ko=(e,r,o="")=>new nr([new Xr({path:r,size:e.size,lastModified:e.mtimeMs,start:0})],(0,wu.basename)(r),{type:o,lastModified:e.mtimeMs}),Xr=class{#e;#r;constructor(e){this.#e=e.path,this.#r=e.start,this.size=e.size,this.lastModified=e.lastModified}slice(e,r){return new Xr({path:this.#e,lastModified:this.lastModified,size:r-e,start:this.#r+e})}async*stream(){let{mtimeMs:e}=await Zr(this.#e);if(e>this.lastModified)throw new Su.default("The requested file could not be read, typically due to permission problems that have occurred after a reference to a file was acquired.","NotReadableError");yield*(0,Qt.createReadStream)(this.#e,{start:this.#r,end:this.#r+this.size-1})}get[Symbol.toStringTag](){return"Blob"}}}}),Nu={};ku(Nu,{toFormData:()=>bf});function gf(e){let r=e.match(/\bfilename=("(.*?)"|([^()<>@,;:\\"/[\]?={}\s\t]+))($|;\s)/i);if(!r)return;let o=r[2]||r[3]||"",i=o.slice(o.lastIndexOf("\\")+1);return i=i.replace(/%22/g,'"'),i=i.replace(/&#(\d{4});/g,(u,l)=>String.fromCharCode(l)),i}async function bf(e,r){if(!/multipart/i.test(r))throw new TypeError("Failed to fetch");let o=r.match(/boundary=(?:"([^"]+)"|([^;]+))/i);if(!o)throw new TypeError("no or bad content-type header, no multipart boundary");let i=new ju(o[1]||o[2]),u,l,f,d,p,g,O=[],H=new yt,N=E=>{f+=R.decode(E,{stream:!0})},_=E=>{O.push(E)},m=()=>{let E=new nr(O,g,{type:p});H.append(d,E)},b=()=>{H.append(d,f)},R=new TextDecoder("utf-8");R.decode(),i.onPartBegin=function(){i.onPartData=N,i.onPartEnd=b,u="",l="",f="",d="",p="",g=null,O.length=0},i.onHeaderField=function(E){u+=R.decode(E,{stream:!0})},i.onHeaderValue=function(E){l+=R.decode(E,{stream:!0})},i.onHeaderEnd=function(){if(l+=R.decode(),u=u.toLowerCase(),u==="content-disposition"){let E=l.match(/\bname=("([^"]*)"|([^()<>@,;:\\"/[\]?={}\s\t]+))/i);E&&(d=E[2]||E[3]||""),g=gf(l),g&&(i.onPartData=_,i.onPartEnd=m)}else u==="content-type"&&(p=l);l="",u=""};for await(let E of e)i.write(E);return i.end(),H}var ge,x,Io,Oe,Kt,Jt,vu,gt,Au,Ru,Bu,Tu,ze,ju,Ff=tr({"node_modules/node-fetch/src/utils/multipart-parser.js"(){qu(),an(),ge=0,x={START_BOUNDARY:ge++,HEADER_FIELD_START:ge++,HEADER_FIELD:ge++,HEADER_VALUE_START:ge++,HEADER_VALUE:ge++,HEADER_VALUE_ALMOST_DONE:ge++,HEADERS_ALMOST_DONE:ge++,PART_DATA_START:ge++,PART_DATA:ge++,END:ge++},Io=1,Oe={PART_BOUNDARY:Io,LAST_BOUNDARY:Io*=2},Kt=10,Jt=13,vu=32,gt=45,Au=58,Ru=97,Bu=122,Tu=e=>e|32,ze=()=>{},ju=class{constructor(e){this.index=0,this.flags=0,this.onHeaderEnd=ze,this.onHeaderField=ze,this.onHeadersEnd=ze,this.onHeaderValue=ze,this.onPartBegin=ze,this.onPartData=ze,this.onPartEnd=ze,this.boundaryChars={},e=`\r
--`+e;let r=new Uint8Array(e.length);for(let o=0;o<e.length;o++)r[o]=e.charCodeAt(o),this.boundaryChars[r[o]]=!0;this.boundary=r,this.lookbehind=new Uint8Array(this.boundary.length+8),this.state=x.START_BOUNDARY}write(e){let r=0,o=e.length,i=this.index,{lookbehind:u,boundary:l,boundaryChars:f,index:d,state:p,flags:g}=this,O=this.boundary.length,H=O-1,N=e.length,_,m,b=Q=>{this[Q+"Mark"]=r},R=Q=>{delete this[Q+"Mark"]},E=(Q,P,I,j)=>{(P===void 0||P!==I)&&this[Q](j&&j.subarray(P,I))},V=(Q,P)=>{let I=Q+"Mark";I in this&&(P?(E(Q,this[I],r,e),delete this[I]):(E(Q,this[I],e.length,e),this[I]=0))};for(r=0;r<o;r++)switch(_=e[r],p){case x.START_BOUNDARY:if(d===l.length-2){if(_===gt)g|=Oe.LAST_BOUNDARY;else if(_!==Jt)return;d++;break}else if(d-1===l.length-2){if(g&Oe.LAST_BOUNDARY&&_===gt)p=x.END,g=0;else if(!(g&Oe.LAST_BOUNDARY)&&_===Kt)d=0,E("onPartBegin"),p=x.HEADER_FIELD_START;else return;break}_!==l[d+2]&&(d=-2),_===l[d+2]&&d++;break;case x.HEADER_FIELD_START:p=x.HEADER_FIELD,b("onHeaderField"),d=0;case x.HEADER_FIELD:if(_===Jt){R("onHeaderField"),p=x.HEADERS_ALMOST_DONE;break}if(d++,_===gt)break;if(_===Au){if(d===1)return;V("onHeaderField",!0),p=x.HEADER_VALUE_START;break}if(m=Tu(_),m<Ru||m>Bu)return;break;case x.HEADER_VALUE_START:if(_===vu)break;b("onHeaderValue"),p=x.HEADER_VALUE;case x.HEADER_VALUE:_===Jt&&(V("onHeaderValue",!0),E("onHeaderEnd"),p=x.HEADER_VALUE_ALMOST_DONE);break;case x.HEADER_VALUE_ALMOST_DONE:if(_!==Kt)return;p=x.HEADER_FIELD_START;break;case x.HEADERS_ALMOST_DONE:if(_!==Kt)return;E("onHeadersEnd"),p=x.PART_DATA_START;break;case x.PART_DATA_START:p=x.PART_DATA,b("onPartData");case x.PART_DATA:if(i=d,d===0){for(r+=H;r<N&&!(e[r]in f);)r+=O;r-=H,_=e[r]}if(d<l.length)l[d]===_?(d===0&&V("onPartData",!0),d++):d=0;else if(d===l.length)d++,_===Jt?g|=Oe.PART_BOUNDARY:_===gt?g|=Oe.LAST_BOUNDARY:d=0;else if(d-1===l.length)if(g&Oe.PART_BOUNDARY){if(d=0,_===Kt){g&=~Oe.PART_BOUNDARY,E("onPartEnd"),E("onPartBegin"),p=x.HEADER_FIELD_START;break}}else g&Oe.LAST_BOUNDARY&&_===gt?(E("onPartEnd"),p=x.END,g=0):d=0;if(d>0)u[d-1]=_;else if(i>0){let Q=new Uint8Array(u.buffer,u.byteOffset,u.byteLength);E("onPartData",0,i,Q),i=0,b("onPartData"),r--}break;case x.END:break;default:throw new Error(`Unexpected state entered: ${p}`)}V("onHeaderField"),V("onHeaderValue"),V("onPartData"),this.index=d,this.state=p,this.flags=g}end(){if(this.state===x.HEADER_FIELD_START&&this.index===0||this.state===x.PART_DATA&&this.index===this.boundary.length)this.onPartEnd();else if(this.state!==x.END)throw new Error("MultipartParser.end(): stream ended unexpectedly")}}}});ku(Vu,{AbortError:()=>Gu,Blob:()=>Lo,FetchError:()=>Fe,File:()=>nr,FormData:()=>yt,Headers:()=>xe,Request:()=>er,Response:()=>re,blobFrom:()=>$u,blobFromSync:()=>Wu,default:()=>Yu,fileFrom:()=>Lu,fileFromSync:()=>Mu,isRedirect:()=>Uo});var _f=J(require("http")),yf=J(require("https")),bt=J(require("zlib")),ye=J(require("stream")),en=J(require("buffer"));function Ef(e){if(!/^data:/i.test(e))throw new TypeError('`uri` does not appear to be a Data URI (must begin with "data:")');e=e.replace(/\r?\n/g,"");let r=e.indexOf(",");if(r===-1||r<=4)throw new TypeError("malformed data: URI");let o=e.substring(5,r).split(";"),i="",u=!1,l=o[0]||"text/plain",f=l;for(let O=1;O<o.length;O++)o[O]==="base64"?u=!0:o[O]&&(f+=`;${o[O]}`,o[O].indexOf("charset=")===0&&(i=o[O].substring(8)));!o[0]&&!i.length&&(f+=";charset=US-ASCII",i="US-ASCII");let d=u?"base64":"ascii",p=unescape(e.substring(r+1)),g=Buffer.from(p,d);return g.type=l,g.typeFull=f,g.charset=i,g}var Cf=Ef,Ee=J(require("stream")),Et=J(require("util")),ue=J(require("buffer"));rr();an();var ln=class extends Error{constructor(e,r){super(e),Error.captureStackTrace(this,this.constructor),this.type=r}get name(){return this.constructor.name}get[Symbol.toStringTag](){return this.constructor.name}},Fe=class extends ln{constructor(e,r,o){super(e,r),o&&(this.code=this.errno=o.code,this.erroredSysCall=o.syscall)}},nn=Symbol.toStringTag,zu=e=>typeof e=="object"&&typeof e.append=="function"&&typeof e.delete=="function"&&typeof e.get=="function"&&typeof e.getAll=="function"&&typeof e.has=="function"&&typeof e.set=="function"&&typeof e.sort=="function"&&e[nn]==="URLSearchParams",on=e=>e&&typeof e=="object"&&typeof e.arrayBuffer=="function"&&typeof e.type=="string"&&typeof e.stream=="function"&&typeof e.constructor=="function"&&/^(Blob|File)$/.test(e[nn]),wf=e=>typeof e=="object"&&(e[nn]==="AbortSignal"||e[nn]==="EventTarget"),Sf=(e,r)=>{let o=new URL(r).hostname,i=new URL(e).hostname;return o===i||o.endsWith(`.${i}`)},vf=(e,r)=>{let o=new URL(r).protocol,i=new URL(e).protocol;return o===i},Af=(0,Et.promisify)(Ee.default.pipeline),ee=Symbol("Body internals"),Xt=class{constructor(e,{size:r=0}={}){let o=null;e===null?e=null:zu(e)?e=ue.Buffer.from(e.toString()):on(e)||ue.Buffer.isBuffer(e)||(Et.types.isAnyArrayBuffer(e)?e=ue.Buffer.from(e):ArrayBuffer.isView(e)?e=ue.Buffer.from(e.buffer,e.byteOffset,e.byteLength):e instanceof Ee.default||(e instanceof yt?(e=mf(e),o=e.type.split("=")[1]):e=ue.Buffer.from(String(e))));let i=e;ue.Buffer.isBuffer(e)?i=Ee.default.Readable.from(e):on(e)&&(i=Ee.default.Readable.from(e.stream())),this[ee]={body:e,stream:i,boundary:o,disturbed:!1,error:null},this.size=r,e instanceof Ee.default&&e.on("error",u=>{let l=u instanceof ln?u:new Fe(`Invalid response body while trying to fetch ${this.url}: ${u.message}`,"system",u);this[ee].error=l})}get body(){return this[ee].stream}get bodyUsed(){return this[ee].disturbed}async arrayBuffer(){let{buffer:e,byteOffset:r,byteLength:o}=await Wo(this);return e.slice(r,r+o)}async formData(){let e=this.headers.get("content-type");if(e.startsWith("application/x-www-form-urlencoded")){let o=new yt,i=new URLSearchParams(await this.text());for(let[u,l]of i)o.append(u,l);return o}let{toFormData:r}=await Promise.resolve().then(()=>(Ff(),Nu));return r(this.body,e)}async blob(){let e=this.headers&&this.headers.get("content-type")||this[ee].body&&this[ee].body.type||"",r=await this.arrayBuffer();return new _t([r],{type:e})}async json(){let e=await this.text();return JSON.parse(e)}async text(){let e=await Wo(this);return new TextDecoder().decode(e)}buffer(){return Wo(this)}};Xt.prototype.buffer=(0,Et.deprecate)(Xt.prototype.buffer,"Please use 'response.arrayBuffer()' instead of 'response.buffer()'","node-fetch#buffer");Object.defineProperties(Xt.prototype,{body:{enumerable:!0},bodyUsed:{enumerable:!0},arrayBuffer:{enumerable:!0},blob:{enumerable:!0},json:{enumerable:!0},text:{enumerable:!0},data:{get:(0,Et.deprecate)(()=>{},"data doesn't exist, use json(), text(), arrayBuffer(), or body instead","https://github.com/node-fetch/node-fetch/issues/1000 (response)")}});async function Wo(e){if(e[ee].disturbed)throw new TypeError(`body used already for: ${e.url}`);if(e[ee].disturbed=!0,e[ee].error)throw e[ee].error;let{body:r}=e;if(r===null||!(r instanceof Ee.default))return ue.Buffer.alloc(0);let o=[],i=0;try{for await(let u of r){if(e.size>0&&i+u.length>e.size){let l=new Fe(`content size at ${e.url} over limit: ${e.size}`,"max-size");throw r.destroy(l),l}i+=u.length,o.push(u)}}catch(u){throw u instanceof ln?u:new Fe(`Invalid response body while trying to fetch ${e.url}: ${u.message}`,"system",u)}if(r.readableEnded===!0||r._readableState.ended===!0)try{return o.every(u=>typeof u=="string")?ue.Buffer.from(o.join("")):ue.Buffer.concat(o,i)}catch(u){throw new Fe(`Could not create Buffer from response body for ${e.url}: ${u.message}`,"system",u)}else throw new Fe(`Premature close of server response while trying to fetch ${e.url}`)}var zo=(e,r)=>{let o,i,{body:u}=e[ee];if(e.bodyUsed)throw new Error("cannot clone body after it is used");return u instanceof Ee.default&&typeof u.getBoundary!="function"&&(o=new Ee.PassThrough({highWaterMark:r}),i=new Ee.PassThrough({highWaterMark:r}),u.pipe(o),u.pipe(i),e[ee].stream=o,u=i),u},Rf=(0,Et.deprecate)(e=>e.getBoundary(),"form-data doesn't follow the spec and requires special treatment. Use alternative package","https://github.com/node-fetch/node-fetch/issues/1167"),Uu=(e,r)=>e===null?null:typeof e=="string"?"text/plain;charset=UTF-8":zu(e)?"application/x-www-form-urlencoded;charset=UTF-8":on(e)?e.type||null:ue.Buffer.isBuffer(e)||Et.types.isAnyArrayBuffer(e)||ArrayBuffer.isView(e)?null:e instanceof yt?`multipart/form-data; boundary=${r[ee].boundary}`:e&&typeof e.getBoundary=="function"?`multipart/form-data;boundary=${Rf(e)}`:e instanceof Ee.default?null:"text/plain;charset=UTF-8",Bf=e=>{let{body:r}=e[ee];return r===null?0:on(r)?r.size:ue.Buffer.isBuffer(r)?r.length:r&&typeof r.getLengthSync=="function"&&r.hasKnownLength&&r.hasKnownLength()?r.getLengthSync():null},Tf=async(e,{body:r})=>{r===null?e.end():await Af(r,e)},Pu=J(require("util")),sn=J(require("http")),rn=typeof sn.default.validateHeaderName=="function"?sn.default.validateHeaderName:e=>{if(!/^[\^`\-\w!#$%&'*+.|~]+$/.test(e)){let r=new TypeError(`Header name must be a valid HTTP token [${e}]`);throw Object.defineProperty(r,"code",{value:"ERR_INVALID_HTTP_TOKEN"}),r}},No=typeof sn.default.validateHeaderValue=="function"?sn.default.validateHeaderValue:(e,r)=>{if(/[^\t\u0020-\u007E\u0080-\u00FF]/.test(r)){let o=new TypeError(`Invalid character in header content ["${e}"]`);throw Object.defineProperty(o,"code",{value:"ERR_INVALID_CHAR"}),o}},xe=class extends URLSearchParams{constructor(e){let r=[];if(e instanceof xe){let o=e.raw();for(let[i,u]of Object.entries(o))r.push(...u.map(l=>[i,l]))}else if(e!=null)if(typeof e=="object"&&!Pu.types.isBoxedPrimitive(e)){let o=e[Symbol.iterator];if(o==null)r.push(...Object.entries(e));else{if(typeof o!="function")throw new TypeError("Header pairs must be iterable");r=[...e].map(i=>{if(typeof i!="object"||Pu.types.isBoxedPrimitive(i))throw new TypeError("Each header pair must be an iterable object");return[...i]}).map(i=>{if(i.length!==2)throw new TypeError("Each header pair must be a name/value tuple");return[...i]})}}else throw new TypeError("Failed to construct 'Headers': The provided value is not of type '(sequence<sequence<ByteString>> or record<ByteString, ByteString>)");return r=r.length>0?r.map(([o,i])=>(rn(o),No(o,String(i)),[String(o).toLowerCase(),String(i)])):void 0,super(r),new Proxy(this,{get(o,i,u){switch(i){case"append":case"set":return(l,f)=>(rn(l),No(l,String(f)),URLSearchParams.prototype[i].call(o,String(l).toLowerCase(),String(f)));case"delete":case"has":case"getAll":return l=>(rn(l),URLSearchParams.prototype[i].call(o,String(l).toLowerCase()));case"keys":return()=>(o.sort(),new Set(URLSearchParams.prototype.keys.call(o)).keys());default:return Reflect.get(o,i,u)}}})}get[Symbol.toStringTag](){return this.constructor.name}toString(){return Object.prototype.toString.call(this)}get(e){let r=this.getAll(e);if(r.length===0)return null;let o=r.join(", ");return/^content-encoding$/i.test(e)&&(o=o.toLowerCase()),o}forEach(e,r=void 0){for(let o of this.keys())Reflect.apply(e,r,[this.get(o),o,this])}*values(){for(let e of this.keys())yield this.get(e)}*entries(){for(let e of this.keys())yield[e,this.get(e)]}[Symbol.iterator](){return this.entries()}raw(){return[...this.keys()].reduce((e,r)=>(e[r]=this.getAll(r),e),{})}[Symbol.for("nodejs.util.inspect.custom")](){return[...this.keys()].reduce((e,r)=>{let o=this.getAll(r);return r==="host"?e[r]=o[0]:e[r]=o.length>1?o:o[0],e},{})}};Object.defineProperties(xe.prototype,["get","entries","forEach","values"].reduce((e,r)=>(e[r]={enumerable:!0},e),{}));function Pf(e=[]){return new xe(e.reduce((r,o,i,u)=>(i%2===0&&r.push(u.slice(i,i+2)),r),[]).filter(([r,o])=>{try{return rn(r),No(r,String(o)),!0}catch{return!1}}))}var Of=new Set([301,302,303,307,308]),Uo=e=>Of.has(e),be=Symbol("Response internals"),re=class extends Xt{constructor(e=null,r={}){super(e,r);let o=r.status!=null?r.status:200,i=new xe(r.headers);if(e!==null&&!i.has("Content-Type")){let u=Uu(e,this);u&&i.append("Content-Type",u)}this[be]={type:"default",url:r.url,status:o,statusText:r.statusText||"",headers:i,counter:r.counter,highWaterMark:r.highWaterMark}}get type(){return this[be].type}get url(){return this[be].url||""}get status(){return this[be].status}get ok(){return this[be].status>=200&&this[be].status<300}get redirected(){return this[be].counter>0}get statusText(){return this[be].statusText}get headers(){return this[be].headers}get highWaterMark(){return this[be].highWaterMark}clone(){return new re(zo(this,this.highWaterMark),{type:this.type,url:this.url,status:this.status,statusText:this.statusText,headers:this.headers,ok:this.ok,redirected:this.redirected,size:this.size,highWaterMark:this.highWaterMark})}static redirect(e,r=302){if(!Uo(r))throw new RangeError('Failed to execute "redirect" on "response": Invalid status code');return new re(null,{headers:{location:new URL(e).toString()},status:r})}static error(){let e=new re(null,{status:0,statusText:""});return e[be].type="error",e}static json(e=void 0,r={}){let o=JSON.stringify(e);if(o===void 0)throw new TypeError("data is not JSON serializable");let i=new xe(r&&r.headers);return i.has("content-type")||i.set("content-type","application/json"),new re(o,{...r,headers:i})}get[Symbol.toStringTag](){return"Response"}};Object.defineProperties(re.prototype,{type:{enumerable:!0},url:{enumerable:!0},status:{enumerable:!0},ok:{enumerable:!0},redirected:{enumerable:!0},statusText:{enumerable:!0},headers:{enumerable:!0},clone:{enumerable:!0}});var xf=J(require("url")),kf=J(require("util")),If=e=>{if(e.search)return e.search;let r=e.href.length-1,o=e.hash||(e.href[r]==="#"?"#":"");return e.href[r-o.length]==="?"?"?":""},Wf=J(require("net"));function Ou(e,r=!1){return e==null||(e=new URL(e),/^(about|blob|data):$/.test(e.protocol))?"no-referrer":(e.username="",e.password="",e.hash="",r&&(e.pathname="",e.search=""),e)}var Hu=new Set(["","no-referrer","no-referrer-when-downgrade","same-origin","origin","strict-origin","origin-when-cross-origin","strict-origin-when-cross-origin","unsafe-url"]),$f="strict-origin-when-cross-origin";function Lf(e){if(!Hu.has(e))throw new TypeError(`Invalid referrerPolicy: ${e}`);return e}function Mf(e){if(/^(http|ws)s:$/.test(e.protocol))return!0;let r=e.host.replace(/(^\[)|(]$)/g,""),o=(0,Wf.isIP)(r);return o===4&&/^127\./.test(r)||o===6&&/^(((0+:){7})|(::(0+:){0,6}))0*1$/.test(r)?!0:e.host==="localhost"||e.host.endsWith(".localhost")?!1:e.protocol==="file:"}function Ft(e){return/^about:(blank|srcdoc)$/.test(e)||e.protocol==="data:"||/^(blob|filesystem):$/.test(e.protocol)?!0:Mf(e)}function qf(e,{referrerURLCallback:r,referrerOriginCallback:o}={}){if(e.referrer==="no-referrer"||e.referrerPolicy==="")return null;let i=e.referrerPolicy;if(e.referrer==="about:client")return"no-referrer";let u=e.referrer,l=Ou(u),f=Ou(u,!0);l.toString().length>4096&&(l=f),r&&(l=r(l)),o&&(f=o(f));let d=new URL(e.url);switch(i){case"no-referrer":return"no-referrer";case"origin":return f;case"unsafe-url":return l;case"strict-origin":return Ft(l)&&!Ft(d)?"no-referrer":f.toString();case"strict-origin-when-cross-origin":return l.origin===d.origin?l:Ft(l)&&!Ft(d)?"no-referrer":f;case"same-origin":return l.origin===d.origin?l:"no-referrer";case"origin-when-cross-origin":return l.origin===d.origin?l:f;case"no-referrer-when-downgrade":return Ft(l)&&!Ft(d)?"no-referrer":l;default:throw new TypeError(`Invalid referrerPolicy: ${i}`)}}function Nf(e){let r=(e.get("referrer-policy")||"").split(/[,\s]+/),o="";for(let i of r)i&&Hu.has(i)&&(o=i);return o}var Y=Symbol("Request internals"),Zt=e=>typeof e=="object"&&typeof e[Y]=="object",jf=(0,kf.deprecate)(()=>{},".data is not a valid RequestInit property, use .body instead","https://github.com/node-fetch/node-fetch/issues/1000 (request)"),er=class extends Xt{constructor(e,r={}){let o;if(Zt(e)?o=new URL(e.url):(o=new URL(e),e={}),o.username!==""||o.password!=="")throw new TypeError(`${o} is an url with embedded credentials.`);let i=r.method||e.method||"GET";if(/^(delete|get|head|options|post|put)$/i.test(i)&&(i=i.toUpperCase()),!Zt(r)&&"data"in r&&jf(),(r.body!=null||Zt(e)&&e.body!==null)&&(i==="GET"||i==="HEAD"))throw new TypeError("Request with GET/HEAD method cannot have body");let u=r.body?r.body:Zt(e)&&e.body!==null?zo(e):null;super(u,{size:r.size||e.size||0});let l=new xe(r.headers||e.headers||{});if(u!==null&&!l.has("Content-Type")){let p=Uu(u,this);p&&l.set("Content-Type",p)}let f=Zt(e)?e.signal:null;if("signal"in r&&(f=r.signal),f!=null&&!wf(f))throw new TypeError("Expected signal to be an instanceof AbortSignal or EventTarget");let d=r.referrer==null?e.referrer:r.referrer;if(d==="")d="no-referrer";else if(d){let p=new URL(d);d=/^about:(\/\/)?client$/.test(p)?"client":p}else d=void 0;this[Y]={method:i,redirect:r.redirect||e.redirect||"follow",headers:l,parsedURL:o,signal:f,referrer:d},this.follow=r.follow===void 0?e.follow===void 0?20:e.follow:r.follow,this.compress=r.compress===void 0?e.compress===void 0?!0:e.compress:r.compress,this.counter=r.counter||e.counter||0,this.agent=r.agent||e.agent,this.highWaterMark=r.highWaterMark||e.highWaterMark||16384,this.insecureHTTPParser=r.insecureHTTPParser||e.insecureHTTPParser||!1,this.referrerPolicy=r.referrerPolicy||e.referrerPolicy||""}get method(){return this[Y].method}get url(){return(0,xf.format)(this[Y].parsedURL)}get headers(){return this[Y].headers}get redirect(){return this[Y].redirect}get signal(){return this[Y].signal}get referrer(){if(this[Y].referrer==="no-referrer")return"";if(this[Y].referrer==="client")return"about:client";if(this[Y].referrer)return this[Y].referrer.toString()}get referrerPolicy(){return this[Y].referrerPolicy}set referrerPolicy(e){this[Y].referrerPolicy=Lf(e)}clone(){return new er(this)}get[Symbol.toStringTag](){return"Request"}};Object.defineProperties(er.prototype,{method:{enumerable:!0},url:{enumerable:!0},headers:{enumerable:!0},redirect:{enumerable:!0},clone:{enumerable:!0},signal:{enumerable:!0},referrer:{enumerable:!0},referrerPolicy:{enumerable:!0}});var zf=e=>{let{parsedURL:r}=e[Y],o=new xe(e[Y].headers);o.has("Accept")||o.set("Accept","*/*");let i=null;if(e.body===null&&/^(post|put)$/i.test(e.method)&&(i="0"),e.body!==null){let d=Bf(e);typeof d=="number"&&!Number.isNaN(d)&&(i=String(d))}i&&o.set("Content-Length",i),e.referrerPolicy===""&&(e.referrerPolicy=$f),e.referrer&&e.referrer!=="no-referrer"?e[Y].referrer=qf(e):e[Y].referrer="no-referrer",e[Y].referrer instanceof URL&&o.set("Referer",e.referrer),o.has("User-Agent")||o.set("User-Agent","node-fetch"),e.compress&&!o.has("Accept-Encoding")&&o.set("Accept-Encoding","gzip, deflate, br");let{agent:u}=e;typeof u=="function"&&(u=u(r));let l=If(r),f={path:r.pathname+l,method:e.method,headers:o[Symbol.for("nodejs.util.inspect.custom")](),insecureHTTPParser:e.insecureHTTPParser,agent:u};return{parsedURL:r,options:f}},Gu=class extends ln{constructor(e,r="aborted"){super(e,r)}};an();qu();var Uf=new Set(["data:","http:","https:"]);async function Yu(e,r){return new Promise((o,i)=>{let u=new er(e,r),{parsedURL:l,options:f}=zf(u);if(!Uf.has(l.protocol))throw new TypeError(`node-fetch cannot load ${e}. URL scheme "${l.protocol.replace(/:$/,"")}" is not supported.`);if(l.protocol==="data:"){let m=Cf(u.url),b=new re(m,{headers:{"Content-Type":m.typeFull}});o(b);return}let d=(l.protocol==="https:"?yf.default:_f.default).request,{signal:p}=u,g=null,O=()=>{let m=new Gu("The operation was aborted.");i(m),u.body&&u.body instanceof ye.default.Readable&&u.body.destroy(m),!(!g||!g.body)&&g.body.emit("error",m)};if(p&&p.aborted){O();return}let H=()=>{O(),_()},N=d(l.toString(),f);p&&p.addEventListener("abort",H);let _=()=>{N.abort(),p&&p.removeEventListener("abort",H)};N.on("error",m=>{i(new Fe(`request to ${u.url} failed, reason: ${m.message}`,"system",m)),_()}),Hf(N,m=>{g&&g.body&&g.body.destroy(m)}),process.version<"v14"&&N.on("socket",m=>{let b;m.prependListener("end",()=>{b=m._eventsCount}),m.prependListener("close",R=>{if(g&&b<m._eventsCount&&!R){let E=new Error("Premature close");E.code="ERR_STREAM_PREMATURE_CLOSE",g.body.emit("error",E)}})}),N.on("response",m=>{N.setTimeout(0);let b=Pf(m.rawHeaders);if(Uo(m.statusCode)){let P=b.get("Location"),I=null;try{I=P===null?null:new URL(P,u.url)}catch{if(u.redirect!=="manual"){i(new Fe(`uri requested responds with an invalid redirect URL: ${P}`,"invalid-redirect")),_();return}}switch(u.redirect){case"error":i(new Fe(`uri requested responds with a redirect, redirect mode is set to error: ${u.url}`,"no-redirect")),_();return;case"manual":break;case"follow":{if(I===null)break;if(u.counter>=u.follow){i(new Fe(`maximum redirect reached at: ${u.url}`,"max-redirect")),_();return}let j={headers:new xe(u.headers),follow:u.follow,counter:u.counter+1,agent:u.agent,compress:u.compress,method:u.method,body:zo(u),signal:u.signal,size:u.size,referrer:u.referrer,referrerPolicy:u.referrerPolicy};if(!Sf(u.url,I)||!vf(u.url,I))for(let Ce of["authorization","www-authenticate","cookie","cookie2"])j.headers.delete(Ce);if(m.statusCode!==303&&u.body&&r.body instanceof ye.default.Readable){i(new Fe("Cannot follow redirect with body being a readable stream","unsupported-redirect")),_();return}(m.statusCode===303||(m.statusCode===301||m.statusCode===302)&&u.method==="POST")&&(j.method="GET",j.body=void 0,j.headers.delete("content-length"));let He=Nf(b);He&&(j.referrerPolicy=He),o(Yu(new er(I,j))),_();return}default:return i(new TypeError(`Redirect option '${u.redirect}' is not a valid value of RequestRedirect`))}}p&&m.once("end",()=>{p.removeEventListener("abort",H)});let R=(0,ye.pipeline)(m,new ye.PassThrough,P=>{P&&i(P)});process.version<"v12.10"&&m.on("aborted",H);let E={url:u.url,status:m.statusCode,statusText:m.statusMessage,headers:b,size:u.size,counter:u.counter,highWaterMark:u.highWaterMark},V=b.get("Content-Encoding");if(!u.compress||u.method==="HEAD"||V===null||m.statusCode===204||m.statusCode===304){g=new re(R,E),o(g);return}let Q={flush:bt.default.Z_SYNC_FLUSH,finishFlush:bt.default.Z_SYNC_FLUSH};if(V==="gzip"||V==="x-gzip"){R=(0,ye.pipeline)(R,bt.default.createGunzip(Q),P=>{P&&i(P)}),g=new re(R,E),o(g);return}if(V==="deflate"||V==="x-deflate"){let P=(0,ye.pipeline)(m,new ye.PassThrough,I=>{I&&i(I)});P.once("data",I=>{(I[0]&15)===8?R=(0,ye.pipeline)(R,bt.default.createInflate(),j=>{j&&i(j)}):R=(0,ye.pipeline)(R,bt.default.createInflateRaw(),j=>{j&&i(j)}),g=new re(R,E),o(g)}),P.once("end",()=>{g||(g=new re(R,E),o(g))});return}if(V==="br"){R=(0,ye.pipeline)(R,bt.default.createBrotliDecompress(),P=>{P&&i(P)}),g=new re(R,E),o(g);return}g=new re(R,E),o(g)}),Tf(N,u).catch(i)})}function Hf(e,r){let o=en.Buffer.from(`0\r
\r
`),i=!1,u=!1,l;e.on("response",f=>{let{headers:d}=f;i=d["transfer-encoding"]==="chunked"&&!d["content-length"]}),e.on("socket",f=>{let d=()=>{if(i&&!u){let g=new Error("Premature close");g.code="ERR_STREAM_PREMATURE_CLOSE",r(g)}},p=g=>{u=en.Buffer.compare(g.slice(-5),o)===0,!u&&l&&(u=en.Buffer.compare(l.slice(-3),o.slice(0,3))===0&&en.Buffer.compare(g.slice(-2),o.slice(3))===0),l=g};f.prependListener("close",d),f.on("data",p),e.on("close",()=>{f.removeListener("close",d),f.removeListener("data",p)})})}rr();an();});var Go=Be((ID,Ju)=>{"use strict";var Ku=require("fs"),Ho;function Gf(){try{return Ku.statSync("/.dockerenv"),!0}catch{return!1}}function Yf(){try{return Ku.readFileSync("/proc/self/cgroup","utf8").includes("docker")}catch{return!1}}Ju.exports=()=>(Ho===void 0&&(Ho=Gf()||Yf()),Ho)});var ea=Be((WD,Yo)=>{"use strict";var Vf=require("os"),Qf=require("fs"),Zu=Go(),Xu=()=>{if(process.platform!=="linux")return!1;if(Vf.release().toLowerCase().includes("microsoft"))return!Zu();try{return Qf.readFileSync("/proc/version","utf8").toLowerCase().includes("microsoft")?!Zu():!1}catch{return!1}};process.env.__IS_WSL_TEST__?Yo.exports=Xu:Yo.exports=Xu()});var ra=Be(($D,ta)=>{"use strict";ta.exports=(e,r,o)=>{let i=u=>Object.defineProperty(e,r,{value:u,enumerable:!0,writable:!0});return Object.defineProperty(e,r,{configurable:!0,enumerable:!0,get(){let u=o();return i(u),u},set(u){i(u)}}),e}});var la=Be((LD,aa)=>{var Kf=require("path"),Jf=require("child_process"),{promises:fn,constants:ua}=require("fs"),cn=ea(),Zf=Go(),Qo=ra(),na=Kf.join(__dirname,"xdg-open"),{platform:Ct,arch:oa}=process,Xf=()=>{try{return fn.statSync("/run/.containerenv"),!0}catch{return!1}},Vo;function ed(){return Vo===void 0&&(Vo=Xf()||Zf()),Vo}var td=(()=>{let e="/mnt/",r;return async function(){if(r)return r;let o="/etc/wsl.conf",i=!1;try{await fn.access(o,ua.F_OK),i=!0}catch{}if(!i)return e;let u=await fn.readFile(o,{encoding:"utf8"}),l=/(?<!#.*)root\s*=\s*(?<mountPoint>.*)/g.exec(u);return l?(r=l.groups.mountPoint.trim(),r=r.endsWith("/")?r:`${r}/`,r):e}})(),ia=async(e,r)=>{let o;for(let i of e)try{return await r(i)}catch(u){o=u}throw o},dn=async e=>{if(e={wait:!1,background:!1,newInstance:!1,allowNonzeroExitCode:!1,...e},Array.isArray(e.app))return ia(e.app,d=>dn({...e,app:d}));let{name:r,arguments:o=[]}=e.app||{};if(o=[...o],Array.isArray(r))return ia(r,d=>dn({...e,app:{name:d,arguments:o}}));let i,u=[],l={};if(Ct==="darwin")i="open",e.wait&&u.push("--wait-apps"),e.background&&u.push("--background"),e.newInstance&&u.push("--new"),r&&u.push("-a",r);else if(Ct==="win32"||cn&&!ed()&&!r){let d=await td();i=cn?`${d}c/Windows/System32/WindowsPowerShell/v1.0/powershell.exe`:`${process.env.SYSTEMROOT}\\System32\\WindowsPowerShell\\v1.0\\powershell`,u.push("-NoProfile","-NonInteractive","\u2013ExecutionPolicy","Bypass","-EncodedCommand"),cn||(l.windowsVerbatimArguments=!0);let p=["Start"];e.wait&&p.push("-Wait"),r?(p.push(`"\`"${r}\`""`,"-ArgumentList"),e.target&&o.unshift(e.target)):e.target&&p.push(`"${e.target}"`),o.length>0&&(o=o.map(g=>`"\`"${g}\`""`),p.push(o.join(","))),e.target=Buffer.from(p.join(" "),"utf16le").toString("base64")}else{if(r)i=r;else{let d=!__dirname||__dirname==="/",p=!1;try{await fn.access(na,ua.X_OK),p=!0}catch{}i=process.versions.electron||Ct==="android"||d||!p?"xdg-open":na}o.length>0&&u.push(...o),e.wait||(l.stdio="ignore",l.detached=!0)}e.target&&u.push(e.target),Ct==="darwin"&&o.length>0&&u.push("--args",...o);let f=Jf.spawn(i,u,l);return e.wait?new Promise((d,p)=>{f.once("error",p),f.once("close",g=>{if(!e.allowNonzeroExitCode&&g>0){p(new Error(`Exited with code ${g}`));return}d(f)})}):(f.unref(),f)},Ko=(e,r)=>{if(typeof e!="string")throw new TypeError("Expected a `target`");return dn({...r,target:e})},rd=(e,r)=>{if(typeof e!="string")throw new TypeError("Expected a `name`");let{arguments:o=[]}=r||{};if(o!=null&&!Array.isArray(o))throw new TypeError("Expected `appArguments` as Array type");return dn({...r,app:{name:e,arguments:o}})};function sa(e){if(typeof e=="string"||Array.isArray(e))return e;let{[oa]:r}=e;if(!r)throw new Error(`${oa} is not supported`);return r}function Jo({[Ct]:e},{wsl:r}){if(r&&cn)return sa(r);if(!e)throw new Error(`${Ct} is not supported`);return sa(e)}var Dn={};Qo(Dn,"chrome",()=>Jo({darwin:"google chrome",win32:"chrome",linux:["google-chrome","google-chrome-stable","chromium"]},{wsl:{ia32:"/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe",x64:["/mnt/c/Program Files/Google/Chrome/Application/chrome.exe","/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe"]}}));Qo(Dn,"firefox",()=>Jo({darwin:"firefox",win32:"C:\\Program Files\\Mozilla Firefox\\firefox.exe",linux:"firefox"},{wsl:"/mnt/c/Program Files/Mozilla Firefox/firefox.exe"}));Qo(Dn,"edge",()=>Jo({darwin:"microsoft edge",win32:"msedge",linux:["microsoft-edge","microsoft-edge-dev"]},{wsl:"/mnt/c/Program Files (x86)/Microsoft/Edge/Application/msedge.exe"}));Ko.apps=Dn;Ko.openApp=rd;aa.exports=Ko});var nd={};ec(nd,{setupGitHubFork:()=>Ca});module.exports=tc(nd);var ei=W(require("path")),Ea=W(require("fs"));var yo=require("child_process");var G=W(Fs());var Gt=W(require("node:process"),1);var _s=(e=0)=>r=>`\x1B[${r+e}m`,ys=(e=0)=>r=>`\x1B[${38+e};5;${r}m`,Es=(e=0)=>(r,o,i)=>`\x1B[${38+e};2;${r};${o};${i}m`,$={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],gray:[90,39],grey:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgGray:[100,49],bgGrey:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}},ud=Object.keys($.modifier),oc=Object.keys($.color),ic=Object.keys($.bgColor),ad=[...oc,...ic];function sc(){let e=new Map;for(let[r,o]of Object.entries($)){for(let[i,u]of Object.entries(o))$[i]={open:`\x1B[${u[0]}m`,close:`\x1B[${u[1]}m`},o[i]=$[i],e.set(u[0],u[1]);Object.defineProperty($,r,{value:o,enumerable:!1})}return Object.defineProperty($,"codes",{value:e,enumerable:!1}),$.color.close="\x1B[39m",$.bgColor.close="\x1B[49m",$.color.ansi=_s(),$.color.ansi256=ys(),$.color.ansi16m=Es(),$.bgColor.ansi=_s(10),$.bgColor.ansi256=ys(10),$.bgColor.ansi16m=Es(10),Object.defineProperties($,{rgbToAnsi256:{value(r,o,i){return r===o&&o===i?r<8?16:r>248?231:Math.round((r-8)/247*24)+232:16+36*Math.round(r/255*5)+6*Math.round(o/255*5)+Math.round(i/255*5)},enumerable:!1},hexToRgb:{value(r){let o=/[a-f\d]{6}|[a-f\d]{3}/i.exec(r.toString(16));if(!o)return[0,0,0];let[i]=o;i.length===3&&(i=[...i].map(l=>l+l).join(""));let u=Number.parseInt(i,16);return[u>>16&255,u>>8&255,u&255]},enumerable:!1},hexToAnsi256:{value:r=>$.rgbToAnsi256(...$.hexToRgb(r)),enumerable:!1},ansi256ToAnsi:{value(r){if(r<8)return 30+r;if(r<16)return 90+(r-8);let o,i,u;if(r>=232)o=((r-232)*10+8)/255,i=o,u=o;else{r-=16;let d=r%36;o=Math.floor(r/36)/5,i=Math.floor(d/6)/5,u=d%6/5}let l=Math.max(o,i,u)*2;if(l===0)return 30;let f=30+(Math.round(u)<<2|Math.round(i)<<1|Math.round(o));return l===2&&(f+=60),f},enumerable:!1},rgbToAnsi:{value:(r,o,i)=>$.ansi256ToAnsi($.rgbToAnsi256(r,o,i)),enumerable:!1},hexToAnsi:{value:r=>$.ansi256ToAnsi($.hexToAnsi256(r)),enumerable:!1}}),$}var uc=sc(),De=uc;var Lr=W(require("node:process"),1),ws=W(require("node:os"),1),Gn=W(require("node:tty"),1);function oe(e,r=globalThis.Deno?globalThis.Deno.args:Lr.default.argv){let o=e.startsWith("-")?"":e.length===1?"-":"--",i=r.indexOf(o+e),u=r.indexOf("--");return i!==-1&&(u===-1||i<u)}var{env:M}=Lr.default,$r;oe("no-color")||oe("no-colors")||oe("color=false")||oe("color=never")?$r=0:(oe("color")||oe("colors")||oe("color=true")||oe("color=always"))&&($r=1);function ac(){if("FORCE_COLOR"in M)return M.FORCE_COLOR==="true"?1:M.FORCE_COLOR==="false"?0:M.FORCE_COLOR.length===0?1:Math.min(Number.parseInt(M.FORCE_COLOR,10),3)}function lc(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function cc(e,{streamIsTTY:r,sniffFlags:o=!0}={}){let i=ac();i!==void 0&&($r=i);let u=o?$r:i;if(u===0)return 0;if(o){if(oe("color=16m")||oe("color=full")||oe("color=truecolor"))return 3;if(oe("color=256"))return 2}if("TF_BUILD"in M&&"AGENT_NAME"in M)return 1;if(e&&!r&&u===void 0)return 0;let l=u||0;if(M.TERM==="dumb")return l;if(Lr.default.platform==="win32"){let f=ws.default.release().split(".");return Number(f[0])>=10&&Number(f[2])>=10586?Number(f[2])>=14931?3:2:1}if("CI"in M)return"GITHUB_ACTIONS"in M||"GITEA_ACTIONS"in M?3:["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","BUILDKITE","DRONE"].some(f=>f in M)||M.CI_NAME==="codeship"?1:l;if("TEAMCITY_VERSION"in M)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(M.TEAMCITY_VERSION)?1:0;if(M.COLORTERM==="truecolor"||M.TERM==="xterm-kitty")return 3;if("TERM_PROGRAM"in M){let f=Number.parseInt((M.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(M.TERM_PROGRAM){case"iTerm.app":return f>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(M.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(M.TERM)||"COLORTERM"in M?1:l}function Cs(e,r={}){let o=cc(e,{streamIsTTY:e&&e.isTTY,...r});return lc(o)}var fc={stdout:Cs({isTTY:Gn.default.isatty(1)}),stderr:Cs({isTTY:Gn.default.isatty(2)})},Ss=fc;function vs(e,r,o){let i=e.indexOf(r);if(i===-1)return e;let u=r.length,l=0,f="";do f+=e.slice(l,i)+r+o,l=i+u,i=e.indexOf(r,l);while(i!==-1);return f+=e.slice(l),f}function As(e,r,o,i){let u=0,l="";do{let f=e[i-1]==="\r";l+=e.slice(u,f?i-1:i)+r+(f?`\r
`:`
`)+o,u=i+1,i=e.indexOf(`
`,u)}while(i!==-1);return l+=e.slice(u),l}var{stdout:Rs,stderr:Bs}=Ss,Yn=Symbol("GENERATOR"),ft=Symbol("STYLER"),Mt=Symbol("IS_EMPTY"),Ts=["ansi","ansi","ansi256","ansi16m"],dt=Object.create(null),dc=(e,r={})=>{if(r.level&&!(Number.isInteger(r.level)&&r.level>=0&&r.level<=3))throw new Error("The `level` option should be an integer from 0 to 3");let o=Rs?Rs.level:0;e.level=r.level===void 0?o:r.level};var Dc=e=>{let r=(...o)=>o.join(" ");return dc(r,e),Object.setPrototypeOf(r,qt.prototype),r};function qt(e){return Dc(e)}Object.setPrototypeOf(qt.prototype,Function.prototype);for(let[e,r]of Object.entries(De))dt[e]={get(){let o=Mr(this,Qn(r.open,r.close,this[ft]),this[Mt]);return Object.defineProperty(this,e,{value:o}),o}};dt.visible={get(){let e=Mr(this,this[ft],!0);return Object.defineProperty(this,"visible",{value:e}),e}};var Vn=(e,r,o,...i)=>e==="rgb"?r==="ansi16m"?De[o].ansi16m(...i):r==="ansi256"?De[o].ansi256(De.rgbToAnsi256(...i)):De[o].ansi(De.rgbToAnsi(...i)):e==="hex"?Vn("rgb",r,o,...De.hexToRgb(...i)):De[o][e](...i),hc=["rgb","hex","ansi256"];for(let e of hc){dt[e]={get(){let{level:o}=this;return function(...i){let u=Qn(Vn(e,Ts[o],"color",...i),De.color.close,this[ft]);return Mr(this,u,this[Mt])}}};let r="bg"+e[0].toUpperCase()+e.slice(1);dt[r]={get(){let{level:o}=this;return function(...i){let u=Qn(Vn(e,Ts[o],"bgColor",...i),De.bgColor.close,this[ft]);return Mr(this,u,this[Mt])}}}}var mc=Object.defineProperties(()=>{},{...dt,level:{enumerable:!0,get(){return this[Yn].level},set(e){this[Yn].level=e}}}),Qn=(e,r,o)=>{let i,u;return o===void 0?(i=e,u=r):(i=o.openAll+e,u=r+o.closeAll),{open:e,close:r,openAll:i,closeAll:u,parent:o}},Mr=(e,r,o)=>{let i=(...u)=>pc(i,u.length===1?""+u[0]:u.join(" "));return Object.setPrototypeOf(i,mc),i[Yn]=e,i[ft]=r,i[Mt]=o,i},pc=(e,r)=>{if(e.level<=0||!r)return e[Mt]?"":r;let o=e[ft];if(o===void 0)return r;let{openAll:i,closeAll:u}=o;if(r.includes("\x1B"))for(;o!==void 0;)r=vs(r,o.close,o.open),o=o.parent;let l=r.indexOf(`
`);return l!==-1&&(r=As(r,u,i,l)),i+r+u};Object.defineProperties(qt.prototype,dt);var gc=qt(),md=qt({level:Bs?Bs.level:0});var Ps=gc;var no=W(require("node:process"),1);var Nt=W(require("node:process"),1);var bc=(e,r,o,i)=>{if(o==="length"||o==="prototype"||o==="arguments"||o==="caller")return;let u=Object.getOwnPropertyDescriptor(e,o),l=Object.getOwnPropertyDescriptor(r,o);!Fc(u,l)&&i||Object.defineProperty(e,o,l)},Fc=function(e,r){return e===void 0||e.configurable||e.writable===r.writable&&e.enumerable===r.enumerable&&e.configurable===r.configurable&&(e.writable||e.value===r.value)},_c=(e,r)=>{let o=Object.getPrototypeOf(r);o!==Object.getPrototypeOf(e)&&Object.setPrototypeOf(e,o)},yc=(e,r)=>`/* Wrapped ${e}*/
${r}`,Ec=Object.getOwnPropertyDescriptor(Function.prototype,"toString"),Cc=Object.getOwnPropertyDescriptor(Function.prototype.toString,"name"),wc=(e,r,o)=>{let i=o===""?"":`with ${o.trim()}() `,u=yc.bind(null,i,r.toString());Object.defineProperty(u,"name",Cc);let{writable:l,enumerable:f,configurable:d}=Ec;Object.defineProperty(e,"toString",{value:u,writable:l,enumerable:f,configurable:d})};function Kn(e,r,{ignoreNonConfigurable:o=!1}={}){let{name:i}=e;for(let u of Reflect.ownKeys(r))bc(e,r,u,o);return _c(e,r),wc(e,r,i),e}var qr=new WeakMap,Os=(e,r={})=>{if(typeof e!="function")throw new TypeError("Expected a function");let o,i=0,u=e.displayName||e.name||"<anonymous>",l=function(...f){if(qr.set(l,++i),i===1)o=e.apply(this,f),e=void 0;else if(r.throw===!0)throw new Error(`Function \`${u}\` can only be called once`);return o};return Kn(l,e),qr.set(l,i),l};Os.callCount=e=>{if(!qr.has(e))throw new Error(`The given function \`${e.name}\` is not wrapped by the \`onetime\` package`);return qr.get(e)};var xs=Os;var Ze=[];Ze.push("SIGHUP","SIGINT","SIGTERM");process.platform!=="win32"&&Ze.push("SIGALRM","SIGABRT","SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");process.platform==="linux"&&Ze.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT");var Nr=e=>!!e&&typeof e=="object"&&typeof e.removeListener=="function"&&typeof e.emit=="function"&&typeof e.reallyExit=="function"&&typeof e.listeners=="function"&&typeof e.kill=="function"&&typeof e.pid=="number"&&typeof e.on=="function",Jn=Symbol.for("signal-exit emitter"),Zn=globalThis,Sc=Object.defineProperty.bind(Object),Xn=class{emitted={afterExit:!1,exit:!1};listeners={afterExit:[],exit:[]};count=0;id=Math.random();constructor(){if(Zn[Jn])return Zn[Jn];Sc(Zn,Jn,{value:this,writable:!1,enumerable:!1,configurable:!1})}on(r,o){this.listeners[r].push(o)}removeListener(r,o){let i=this.listeners[r],u=i.indexOf(o);u!==-1&&(u===0&&i.length===1?i.length=0:i.splice(u,1))}emit(r,o,i){if(this.emitted[r])return!1;this.emitted[r]=!0;let u=!1;for(let l of this.listeners[r])u=l(o,i)===!0||u;return r==="exit"&&(u=this.emit("afterExit",o,i)||u),u}},jr=class{},vc=e=>({onExit(r,o){return e.onExit(r,o)},load(){return e.load()},unload(){return e.unload()}}),eo=class extends jr{onExit(){return()=>{}}load(){}unload(){}},to=class extends jr{#e=ro.platform==="win32"?"SIGINT":"SIGHUP";#r=new Xn;#t;#o;#d;#n={};#s=!1;constructor(r){super(),this.#t=r,this.#n={};for(let o of Ze)this.#n[o]=()=>{let i=this.#t.listeners(o),{count:u}=this.#r,l=r;if(typeof l.__signal_exit_emitter__=="object"&&typeof l.__signal_exit_emitter__.count=="number"&&(u+=l.__signal_exit_emitter__.count),i.length===u){this.unload();let f=this.#r.emit("exit",null,o),d=o==="SIGHUP"?this.#e:o;f||r.kill(r.pid,d)}};this.#d=r.reallyExit,this.#o=r.emit}onExit(r,o){if(!Nr(this.#t))return()=>{};this.#s===!1&&this.load();let i=o?.alwaysLast?"afterExit":"exit";return this.#r.on(i,r),()=>{this.#r.removeListener(i,r),this.#r.listeners.exit.length===0&&this.#r.listeners.afterExit.length===0&&this.unload()}}load(){if(!this.#s){this.#s=!0,this.#r.count+=1;for(let r of Ze)try{let o=this.#n[r];o&&this.#t.on(r,o)}catch{}this.#t.emit=(r,...o)=>this.#D(r,...o),this.#t.reallyExit=r=>this.#i(r)}}unload(){this.#s&&(this.#s=!1,Ze.forEach(r=>{let o=this.#n[r];if(!o)throw new Error("Listener not defined for signal: "+r);try{this.#t.removeListener(r,o)}catch{}}),this.#t.emit=this.#o,this.#t.reallyExit=this.#d,this.#r.count-=1)}#i(r){return Nr(this.#t)?(this.#t.exitCode=r||0,this.#r.emit("exit",this.#t.exitCode,null),this.#d.call(this.#t,this.#t.exitCode)):0}#D(r,...o){let i=this.#o;if(r==="exit"&&Nr(this.#t)){typeof o[0]=="number"&&(this.#t.exitCode=o[0]);let u=i.call(this.#t,r,...o);return this.#r.emit("exit",this.#t.exitCode,null),u}else return i.call(this.#t,r,...o)}},ro=globalThis.process,{onExit:ks,load:Ed,unload:Cd}=vc(Nr(ro)?new to(ro):new eo);var Is=Nt.default.stderr.isTTY?Nt.default.stderr:Nt.default.stdout.isTTY?Nt.default.stdout:void 0,Ac=Is?xs(()=>{ks(()=>{Is.write("\x1B[?25h")},{alwaysLast:!0})}):()=>{},Ws=Ac;var zr=!1,Dt={};Dt.show=(e=no.default.stderr)=>{e.isTTY&&(zr=!1,e.write("\x1B[?25h"))};Dt.hide=(e=no.default.stderr)=>{e.isTTY&&(Ws(),zr=!0,e.write("\x1B[?25l"))};Dt.toggle=(e,r)=>{e!==void 0&&(zr=e),zr?Dt.show(r):Dt.hide(r)};var oo=Dt;var Yt=W(io(),1);var qs=(e=0)=>r=>`\x1B[${r+e}m`,Ns=(e=0)=>r=>`\x1B[${38+e};5;${r}m`,js=(e=0)=>(r,o,i)=>`\x1B[${38+e};2;${r};${o};${i}m`,L={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],gray:[90,39],grey:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgGray:[100,49],bgGrey:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}},Od=Object.keys(L.modifier),Bc=Object.keys(L.color),Tc=Object.keys(L.bgColor),xd=[...Bc,...Tc];function Pc(){let e=new Map;for(let[r,o]of Object.entries(L)){for(let[i,u]of Object.entries(o))L[i]={open:`\x1B[${u[0]}m`,close:`\x1B[${u[1]}m`},o[i]=L[i],e.set(u[0],u[1]);Object.defineProperty(L,r,{value:o,enumerable:!1})}return Object.defineProperty(L,"codes",{value:e,enumerable:!1}),L.color.close="\x1B[39m",L.bgColor.close="\x1B[49m",L.color.ansi=qs(),L.color.ansi256=Ns(),L.color.ansi16m=js(),L.bgColor.ansi=qs(10),L.bgColor.ansi256=Ns(10),L.bgColor.ansi16m=js(10),Object.defineProperties(L,{rgbToAnsi256:{value(r,o,i){return r===o&&o===i?r<8?16:r>248?231:Math.round((r-8)/247*24)+232:16+36*Math.round(r/255*5)+6*Math.round(o/255*5)+Math.round(i/255*5)},enumerable:!1},hexToRgb:{value(r){let o=/[a-f\d]{6}|[a-f\d]{3}/i.exec(r.toString(16));if(!o)return[0,0,0];let[i]=o;i.length===3&&(i=[...i].map(l=>l+l).join(""));let u=Number.parseInt(i,16);return[u>>16&255,u>>8&255,u&255]},enumerable:!1},hexToAnsi256:{value:r=>L.rgbToAnsi256(...L.hexToRgb(r)),enumerable:!1},ansi256ToAnsi:{value(r){if(r<8)return 30+r;if(r<16)return 90+(r-8);let o,i,u;if(r>=232)o=((r-232)*10+8)/255,i=o,u=o;else{r-=16;let d=r%36;o=Math.floor(r/36)/5,i=Math.floor(d/6)/5,u=d%6/5}let l=Math.max(o,i,u)*2;if(l===0)return 30;let f=30+(Math.round(u)<<2|Math.round(i)<<1|Math.round(o));return l===2&&(f+=60),f},enumerable:!1},rgbToAnsi:{value:(r,o,i)=>L.ansi256ToAnsi(L.rgbToAnsi256(r,o,i)),enumerable:!1},hexToAnsi:{value:r=>L.ansi256ToAnsi(L.hexToAnsi256(r)),enumerable:!1}}),L}var Oc=Pc(),he=Oc;var Gr=W(require("node:process"),1),Us=W(require("node:os"),1),so=W(require("node:tty"),1);function ie(e,r=globalThis.Deno?globalThis.Deno.args:Gr.default.argv){let o=e.startsWith("-")?"":e.length===1?"-":"--",i=r.indexOf(o+e),u=r.indexOf("--");return i!==-1&&(u===-1||i<u)}var{env:q}=Gr.default,Hr;ie("no-color")||ie("no-colors")||ie("color=false")||ie("color=never")?Hr=0:(ie("color")||ie("colors")||ie("color=true")||ie("color=always"))&&(Hr=1);function xc(){if("FORCE_COLOR"in q)return q.FORCE_COLOR==="true"?1:q.FORCE_COLOR==="false"?0:q.FORCE_COLOR.length===0?1:Math.min(Number.parseInt(q.FORCE_COLOR,10),3)}function kc(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function Ic(e,{streamIsTTY:r,sniffFlags:o=!0}={}){let i=xc();i!==void 0&&(Hr=i);let u=o?Hr:i;if(u===0)return 0;if(o){if(ie("color=16m")||ie("color=full")||ie("color=truecolor"))return 3;if(ie("color=256"))return 2}if("TF_BUILD"in q&&"AGENT_NAME"in q)return 1;if(e&&!r&&u===void 0)return 0;let l=u||0;if(q.TERM==="dumb")return l;if(Gr.default.platform==="win32"){let f=Us.default.release().split(".");return Number(f[0])>=10&&Number(f[2])>=10586?Number(f[2])>=14931?3:2:1}if("CI"in q)return"GITHUB_ACTIONS"in q||"GITEA_ACTIONS"in q?3:["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","BUILDKITE","DRONE"].some(f=>f in q)||q.CI_NAME==="codeship"?1:l;if("TEAMCITY_VERSION"in q)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(q.TEAMCITY_VERSION)?1:0;if(q.COLORTERM==="truecolor"||q.TERM==="xterm-kitty")return 3;if("TERM_PROGRAM"in q){let f=Number.parseInt((q.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(q.TERM_PROGRAM){case"iTerm.app":return f>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(q.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(q.TERM)||"COLORTERM"in q?1:l}function zs(e,r={}){let o=Ic(e,{streamIsTTY:e&&e.isTTY,...r});return kc(o)}var Wc={stdout:zs({isTTY:so.default.isatty(1)}),stderr:zs({isTTY:so.default.isatty(2)})},Hs=Wc;function Gs(e,r,o){let i=e.indexOf(r);if(i===-1)return e;let u=r.length,l=0,f="";do f+=e.slice(l,i)+r+o,l=i+u,i=e.indexOf(r,l);while(i!==-1);return f+=e.slice(l),f}function Ys(e,r,o,i){let u=0,l="";do{let f=e[i-1]==="\r";l+=e.slice(u,f?i-1:i)+r+(f?`\r
`:`
`)+o,u=i+1,i=e.indexOf(`
`,u)}while(i!==-1);return l+=e.slice(u),l}var{stdout:Vs,stderr:Qs}=Hs,uo=Symbol("GENERATOR"),ht=Symbol("STYLER"),jt=Symbol("IS_EMPTY"),Ks=["ansi","ansi","ansi256","ansi16m"],mt=Object.create(null),$c=(e,r={})=>{if(r.level&&!(Number.isInteger(r.level)&&r.level>=0&&r.level<=3))throw new Error("The `level` option should be an integer from 0 to 3");let o=Vs?Vs.level:0;e.level=r.level===void 0?o:r.level};var Lc=e=>{let r=(...o)=>o.join(" ");return $c(r,e),Object.setPrototypeOf(r,zt.prototype),r};function zt(e){return Lc(e)}Object.setPrototypeOf(zt.prototype,Function.prototype);for(let[e,r]of Object.entries(he))mt[e]={get(){let o=Yr(this,lo(r.open,r.close,this[ht]),this[jt]);return Object.defineProperty(this,e,{value:o}),o}};mt.visible={get(){let e=Yr(this,this[ht],!0);return Object.defineProperty(this,"visible",{value:e}),e}};var ao=(e,r,o,...i)=>e==="rgb"?r==="ansi16m"?he[o].ansi16m(...i):r==="ansi256"?he[o].ansi256(he.rgbToAnsi256(...i)):he[o].ansi(he.rgbToAnsi(...i)):e==="hex"?ao("rgb",r,o,...he.hexToRgb(...i)):he[o][e](...i),Mc=["rgb","hex","ansi256"];for(let e of Mc){mt[e]={get(){let{level:o}=this;return function(...i){let u=lo(ao(e,Ks[o],"color",...i),he.color.close,this[ht]);return Yr(this,u,this[jt])}}};let r="bg"+e[0].toUpperCase()+e.slice(1);mt[r]={get(){let{level:o}=this;return function(...i){let u=lo(ao(e,Ks[o],"bgColor",...i),he.bgColor.close,this[ht]);return Yr(this,u,this[jt])}}}}var qc=Object.defineProperties(()=>{},{...mt,level:{enumerable:!0,get(){return this[uo].level},set(e){this[uo].level=e}}}),lo=(e,r,o)=>{let i,u;return o===void 0?(i=e,u=r):(i=o.openAll+e,u=r+o.closeAll),{open:e,close:r,openAll:i,closeAll:u,parent:o}},Yr=(e,r,o)=>{let i=(...u)=>Nc(i,u.length===1?""+u[0]:u.join(" "));return Object.setPrototypeOf(i,qc),i[uo]=e,i[ht]=r,i[jt]=o,i},Nc=(e,r)=>{if(e.level<=0||!r)return e[jt]?"":r;let o=e[ht];if(o===void 0)return r;let{openAll:i,closeAll:u}=o;if(r.includes("\x1B"))for(;o!==void 0;)r=Gs(r,o.close,o.open),o=o.parent;let l=r.indexOf(`
`);return l!==-1&&(r=Ys(r,u,i,l)),i+r+u};Object.defineProperties(zt.prototype,mt);var jc=zt(),qd=zt({level:Qs?Qs.level:0});var Te=jc;var se=W(require("node:process"),1);function co(){return se.default.platform!=="win32"?se.default.env.TERM!=="linux":!!se.default.env.CI||!!se.default.env.WT_SESSION||!!se.default.env.TERMINUS_SUBLIME||se.default.env.ConEmuTask==="{cmd::Cmder}"||se.default.env.TERM_PROGRAM==="Terminus-Sublime"||se.default.env.TERM_PROGRAM==="vscode"||se.default.env.TERM==="xterm-256color"||se.default.env.TERM==="alacritty"||se.default.env.TERMINAL_EMULATOR==="JetBrains-JediTerm"}var zc={info:Te.blue("\u2139"),success:Te.green("\u2714"),warning:Te.yellow("\u26A0"),error:Te.red("\u2716")},Uc={info:Te.blue("i"),success:Te.green("\u221A"),warning:Te.yellow("\u203C"),error:Te.red("\xD7")},Hc=co()?zc:Uc,Ut=Hc;function fo({onlyFirst:e=!1}={}){let o=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?(?:\\u0007|\\u001B\\u005C|\\u009C))","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-nq-uy=><~]))"].join("|");return new RegExp(o,e?void 0:"g")}var Gc=fo();function Ht(e){if(typeof e!="string")throw new TypeError(`Expected a \`string\`, got \`${typeof e}\``);return e.replace(Gc,"")}function Js(e){return e===161||e===164||e===167||e===168||e===170||e===173||e===174||e>=176&&e<=180||e>=182&&e<=186||e>=188&&e<=191||e===198||e===208||e===215||e===216||e>=222&&e<=225||e===230||e>=232&&e<=234||e===236||e===237||e===240||e===242||e===243||e>=247&&e<=250||e===252||e===254||e===257||e===273||e===275||e===283||e===294||e===295||e===299||e>=305&&e<=307||e===312||e>=319&&e<=322||e===324||e>=328&&e<=331||e===333||e===338||e===339||e===358||e===359||e===363||e===462||e===464||e===466||e===468||e===470||e===472||e===474||e===476||e===593||e===609||e===708||e===711||e>=713&&e<=715||e===717||e===720||e>=728&&e<=731||e===733||e===735||e>=768&&e<=879||e>=913&&e<=929||e>=931&&e<=937||e>=945&&e<=961||e>=963&&e<=969||e===1025||e>=1040&&e<=1103||e===1105||e===8208||e>=8211&&e<=8214||e===8216||e===8217||e===8220||e===8221||e>=8224&&e<=8226||e>=8228&&e<=8231||e===8240||e===8242||e===8243||e===8245||e===8251||e===8254||e===8308||e===8319||e>=8321&&e<=8324||e===8364||e===8451||e===8453||e===8457||e===8467||e===8470||e===8481||e===8482||e===8486||e===8491||e===8531||e===8532||e>=8539&&e<=8542||e>=8544&&e<=8555||e>=8560&&e<=8569||e===8585||e>=8592&&e<=8601||e===8632||e===8633||e===8658||e===8660||e===8679||e===8704||e===8706||e===8707||e===8711||e===8712||e===8715||e===8719||e===8721||e===8725||e===8730||e>=8733&&e<=8736||e===8739||e===8741||e>=8743&&e<=8748||e===8750||e>=8756&&e<=8759||e===8764||e===8765||e===8776||e===8780||e===8786||e===8800||e===8801||e>=8804&&e<=8807||e===8810||e===8811||e===8814||e===8815||e===8834||e===8835||e===8838||e===8839||e===8853||e===8857||e===8869||e===8895||e===8978||e>=9312&&e<=9449||e>=9451&&e<=9547||e>=9552&&e<=9587||e>=9600&&e<=9615||e>=9618&&e<=9621||e===9632||e===9633||e>=9635&&e<=9641||e===9650||e===9651||e===9654||e===9655||e===9660||e===9661||e===9664||e===9665||e>=9670&&e<=9672||e===9675||e>=9678&&e<=9681||e>=9698&&e<=9701||e===9711||e===9733||e===9734||e===9737||e===9742||e===9743||e===9756||e===9758||e===9792||e===9794||e===9824||e===9825||e>=9827&&e<=9829||e>=9831&&e<=9834||e===9836||e===9837||e===9839||e===9886||e===9887||e===9919||e>=9926&&e<=9933||e>=9935&&e<=9939||e>=9941&&e<=9953||e===9955||e===9960||e===9961||e>=9963&&e<=9969||e===9972||e>=9974&&e<=9977||e===9979||e===9980||e===9982||e===9983||e===10045||e>=10102&&e<=10111||e>=11094&&e<=11097||e>=12872&&e<=12879||e>=57344&&e<=63743||e>=65024&&e<=65039||e===65533||e>=127232&&e<=127242||e>=127248&&e<=127277||e>=127280&&e<=127337||e>=127344&&e<=127373||e===127375||e===127376||e>=127387&&e<=127404||e>=917760&&e<=917999||e>=983040&&e<=1048573||e>=1048576&&e<=1114109}function Zs(e){return e===12288||e>=65281&&e<=65376||e>=65504&&e<=65510}function Xs(e){return e>=4352&&e<=4447||e===8986||e===8987||e===9001||e===9002||e>=9193&&e<=9196||e===9200||e===9203||e===9725||e===9726||e===9748||e===9749||e>=9776&&e<=9783||e>=9800&&e<=9811||e===9855||e>=9866&&e<=9871||e===9875||e===9889||e===9898||e===9899||e===9917||e===9918||e===9924||e===9925||e===9934||e===9940||e===9962||e===9970||e===9971||e===9973||e===9978||e===9981||e===9989||e===9994||e===9995||e===10024||e===10060||e===10062||e>=10067&&e<=10069||e===10071||e>=10133&&e<=10135||e===10160||e===10175||e===11035||e===11036||e===11088||e===11093||e>=11904&&e<=11929||e>=11931&&e<=12019||e>=12032&&e<=12245||e>=12272&&e<=12287||e>=12289&&e<=12350||e>=12353&&e<=12438||e>=12441&&e<=12543||e>=12549&&e<=12591||e>=12593&&e<=12686||e>=12688&&e<=12773||e>=12783&&e<=12830||e>=12832&&e<=12871||e>=12880&&e<=42124||e>=42128&&e<=42182||e>=43360&&e<=43388||e>=44032&&e<=55203||e>=63744&&e<=64255||e>=65040&&e<=65049||e>=65072&&e<=65106||e>=65108&&e<=65126||e>=65128&&e<=65131||e>=94176&&e<=94180||e===94192||e===94193||e>=94208&&e<=100343||e>=100352&&e<=101589||e>=101631&&e<=101640||e>=110576&&e<=110579||e>=110581&&e<=110587||e===110589||e===110590||e>=110592&&e<=110882||e===110898||e>=110928&&e<=110930||e===110933||e>=110948&&e<=110951||e>=110960&&e<=111355||e>=119552&&e<=119638||e>=119648&&e<=119670||e===126980||e===127183||e===127374||e>=127377&&e<=127386||e>=127488&&e<=127490||e>=127504&&e<=127547||e>=127552&&e<=127560||e===127568||e===127569||e>=127584&&e<=127589||e>=127744&&e<=127776||e>=127789&&e<=127797||e>=127799&&e<=127868||e>=127870&&e<=127891||e>=127904&&e<=127946||e>=127951&&e<=127955||e>=127968&&e<=127984||e===127988||e>=127992&&e<=128062||e===128064||e>=128066&&e<=128252||e>=128255&&e<=128317||e>=128331&&e<=128334||e>=128336&&e<=128359||e===128378||e===128405||e===128406||e===128420||e>=128507&&e<=128591||e>=128640&&e<=128709||e===128716||e>=128720&&e<=128722||e>=128725&&e<=128727||e>=128732&&e<=128735||e===128747||e===128748||e>=128756&&e<=128764||e>=128992&&e<=129003||e===129008||e>=129292&&e<=129338||e>=129340&&e<=129349||e>=129351&&e<=129535||e>=129648&&e<=129660||e>=129664&&e<=129673||e>=129679&&e<=129734||e>=129742&&e<=129756||e>=129759&&e<=129769||e>=129776&&e<=129784||e>=131072&&e<=196605||e>=196608&&e<=262141}function Yc(e){if(!Number.isSafeInteger(e))throw new TypeError(`Expected a code point, got \`${typeof e}\`.`)}function eu(e,{ambiguousAsWide:r=!1}={}){return Yc(e),Zs(e)||Xs(e)||r&&Js(e)?2:1}var nu=W(ru(),1),Vc=new Intl.Segmenter,Qc=/^\p{Default_Ignorable_Code_Point}$/u;function Do(e,r={}){if(typeof e!="string"||e.length===0)return 0;let{ambiguousIsNarrow:o=!0,countAnsiEscapeCodes:i=!1}=r;if(i||(e=Ht(e)),e.length===0)return 0;let u=0,l={ambiguousAsWide:!o};for(let{segment:f}of Vc.segment(e)){let d=f.codePointAt(0);if(!(d<=31||d>=127&&d<=159)&&!(d>=8203&&d<=8207||d===65279)&&!(d>=768&&d<=879||d>=6832&&d<=6911||d>=7616&&d<=7679||d>=8400&&d<=8447||d>=65056&&d<=65071)&&!(d>=55296&&d<=57343)&&!(d>=65024&&d<=65039)&&!Qc.test(f)){if((0,nu.default)().test(f)){u+=2;continue}u+=eu(d,l)}}return u}function ho({stream:e=process.stdout}={}){return!!(e&&e.isTTY&&process.env.TERM!=="dumb"&&!("CI"in process.env))}var mo=W(require("node:process"),1);function po(){let{env:e}=mo.default,{TERM:r,TERM_PROGRAM:o}=e;return mo.default.platform!=="win32"?r!=="linux":!!e.WT_SESSION||!!e.TERMINUS_SUBLIME||e.ConEmuTask==="{cmd::Cmder}"||o==="Terminus-Sublime"||o==="vscode"||r==="xterm-256color"||r==="alacritty"||r==="rxvt-unicode"||r==="rxvt-unicode-256color"||e.TERMINAL_EMULATOR==="JetBrains-JediTerm"}var me=W(require("node:process"),1),Kc=3,go=class{#e=0;start(){this.#e++,this.#e===1&&this.#r()}stop(){if(this.#e<=0)throw new Error("`stop` called more times than `start`");this.#e--,this.#e===0&&this.#t()}#r(){me.default.platform==="win32"||!me.default.stdin.isTTY||(me.default.stdin.setRawMode(!0),me.default.stdin.on("data",this.#o),me.default.stdin.resume())}#t(){me.default.stdin.isTTY&&(me.default.stdin.off("data",this.#o),me.default.stdin.pause(),me.default.stdin.setRawMode(!1))}#o(r){r[0]===Kc&&me.default.emit("SIGINT")}},Jc=new go,bo=Jc;var Zc=W(io(),1),Fo=class{#e=0;#r=!1;#t=0;#o=-1;#d=0;#n;#s;#i;#D;#m;#l;#c;#f;#p;#u;#a;color;constructor(r){typeof r=="string"&&(r={text:r}),this.#n={color:"cyan",stream:Gt.default.stderr,discardStdin:!0,hideCursor:!0,...r},this.color=this.#n.color,this.spinner=this.#n.spinner,this.#m=this.#n.interval,this.#i=this.#n.stream,this.#l=typeof this.#n.isEnabled=="boolean"?this.#n.isEnabled:ho({stream:this.#i}),this.#c=typeof this.#n.isSilent=="boolean"?this.#n.isSilent:!1,this.text=this.#n.text,this.prefixText=this.#n.prefixText,this.suffixText=this.#n.suffixText,this.indent=this.#n.indent,Gt.default.env.NODE_ENV==="test"&&(this._stream=this.#i,this._isEnabled=this.#l,Object.defineProperty(this,"_linesToClear",{get(){return this.#e},set(o){this.#e=o}}),Object.defineProperty(this,"_frameIndex",{get(){return this.#o}}),Object.defineProperty(this,"_lineCount",{get(){return this.#t}}))}get indent(){return this.#f}set indent(r=0){if(!(r>=0&&Number.isInteger(r)))throw new Error("The `indent` option must be an integer from 0 and up");this.#f=r,this.#h()}get interval(){return this.#m??this.#s.interval??100}get spinner(){return this.#s}set spinner(r){if(this.#o=-1,this.#m=void 0,typeof r=="object"){if(r.frames===void 0)throw new Error("The given spinner must have a `frames` property");this.#s=r}else if(!po())this.#s=Yt.default.line;else if(r===void 0)this.#s=Yt.default.dots;else if(r!=="default"&&Yt.default[r])this.#s=Yt.default[r];else throw new Error(`There is no built-in spinner named '${r}'. See https://github.com/sindresorhus/cli-spinners/blob/main/spinners.json for a full list.`)}get text(){return this.#p}set text(r=""){this.#p=r,this.#h()}get prefixText(){return this.#u}set prefixText(r=""){this.#u=r,this.#h()}get suffixText(){return this.#a}set suffixText(r=""){this.#a=r,this.#h()}get isSpinning(){return this.#D!==void 0}#g(r=this.#u,o=" "){return typeof r=="string"&&r!==""?r+o:typeof r=="function"?r()+o:""}#b(r=this.#a,o=" "){return typeof r=="string"&&r!==""?o+r:typeof r=="function"?o+r():""}#h(){let r=this.#i.columns??80,o=this.#g(this.#u,"-"),i=this.#b(this.#a,"-"),u=" ".repeat(this.#f)+o+"--"+this.#p+"--"+i;this.#t=0;for(let l of Ht(u).split(`
`))this.#t+=Math.max(1,Math.ceil(Do(l,{countAnsiEscapeCodes:!0})/r))}get isEnabled(){return this.#l&&!this.#c}set isEnabled(r){if(typeof r!="boolean")throw new TypeError("The `isEnabled` option must be a boolean");this.#l=r}get isSilent(){return this.#c}set isSilent(r){if(typeof r!="boolean")throw new TypeError("The `isSilent` option must be a boolean");this.#c=r}frame(){let r=Date.now();(this.#o===-1||r-this.#d>=this.interval)&&(this.#o=++this.#o%this.#s.frames.length,this.#d=r);let{frames:o}=this.#s,i=o[this.#o];this.color&&(i=Ps[this.color](i));let u=typeof this.#u=="string"&&this.#u!==""?this.#u+" ":"",l=typeof this.text=="string"?" "+this.text:"",f=typeof this.#a=="string"&&this.#a!==""?" "+this.#a:"";return u+i+l+f}clear(){if(!this.#l||!this.#i.isTTY)return this;this.#i.cursorTo(0);for(let r=0;r<this.#e;r++)r>0&&this.#i.moveCursor(0,-1),this.#i.clearLine(1);return(this.#f||this.lastIndent!==this.#f)&&this.#i.cursorTo(this.#f),this.lastIndent=this.#f,this.#e=0,this}render(){return this.#c?this:(this.clear(),this.#i.write(this.frame()),this.#e=this.#t,this)}start(r){return r&&(this.text=r),this.#c?this:this.#l?this.isSpinning?this:(this.#n.hideCursor&&oo.hide(this.#i),this.#n.discardStdin&&Gt.default.stdin.isTTY&&(this.#r=!0,bo.start()),this.render(),this.#D=setInterval(this.render.bind(this),this.interval),this):(this.text&&this.#i.write(`- ${this.text}
`),this)}stop(){return this.#l?(clearInterval(this.#D),this.#D=void 0,this.#o=0,this.clear(),this.#n.hideCursor&&oo.show(this.#i),this.#n.discardStdin&&Gt.default.stdin.isTTY&&this.#r&&(bo.stop(),this.#r=!1),this):this}succeed(r){return this.stopAndPersist({symbol:Ut.success,text:r})}fail(r){return this.stopAndPersist({symbol:Ut.error,text:r})}warn(r){return this.stopAndPersist({symbol:Ut.warning,text:r})}info(r){return this.stopAndPersist({symbol:Ut.info,text:r})}stopAndPersist(r={}){if(this.#c)return this;let o=r.prefixText??this.#u,i=this.#g(o," "),u=r.symbol??" ",l=r.text??this.text,d=typeof l=="string"?(u?" ":"")+l:"",p=r.suffixText??this.#a,g=this.#b(p," "),O=i+u+d+g+`
`;return this.stop(),this.#i.write(O),this}};function _o(e){return new Fo(e)}var pD=(0,G.blue)((0,G.dim)("internal only"));function Pe(e,r,o){console.log(ou[e]+r),typeof o?.exit<"u"&&process.exit(o.exit)}async function Vr(e,r,o){if(!Xc){Pe("wait",e);try{let u=await r();u&&console.log(u),Pe("success",e);return}catch(u){return Pe("error",e),o?.printError!==!1&&console.log((0,G.red)(u.message)),u}}let i=_o({spinner:"simpleDots",prefixText:ou.wait+e}).start();try{let u=await r();i.stop(),Pe("success",e),u&&console.log(u)}catch(u){return i.stop(),Pe("error",e),o?.printError!==!1&&console.error(u.message),u}}var ou={wait:`\u{1F550}${(0,G.blue)("wait")}  - `,info:`${process.env.GITHUB_ACTIONS==="true"?"\u2139\uFE0F":"\u2139\uFE0F "}${(0,G.cyan)("info")}  - `,success:`\u2705${(0,G.green)("ready")}  - `,warn:`${process.env.GITHUB_ACTIONS==="true"?"\u26A0\uFE0F":"\u26A0\uFE0F "}${(0,G.yellow)("warn")}  - `,error:`\u{1F4A5}${(0,G.red)("error")}  - `,event:`\u26A1\uFE0F${(0,G.magenta)("event")}  - `,paymentPrompt:`\u{1F4B0}${(0,G.yellowBright)("plan")}  - `},Xc=!0;var iu=W(require("path"));var Eo;function Qr(e){Eo=e}function K(e,r){return new Promise((o,i)=>{(0,yo.exec)(e,{cwd:Eo},(u,l,f)=>{if(r.throwOnError&&u){i(new Error(`failed running git ${u.message.replace(/oauth2:gho_[a-zA-Z0-9]+@/g,"oauth2:gho_xxxxx")}
${l.trim()}
${f.trim()}`));return}o(l.trim())})})}function ef(){return new Promise(e=>{(0,yo.exec)("git --version",r=>{e(r===null)})})}async function su(){return await ef()?await K("git rev-parse --is-inside-work-tree",{throwOnError:!1})==="true":(Pe("info","git is not installed"),!1)}async function uu(){return await K("git status -s",{throwOnError:!0})!==""}async function au(){return await tf()?K("git rev-parse HEAD",{throwOnError:!0}):""}async function lu(e,r){Eo=iu.default.dirname(r),await K(`git clone --filter=blob:none --no-checkout ${e} "${r}"`,{throwOnError:!0})}async function cu(e,r){try{await K(`git remote set-url ${e} ${r}`,{throwOnError:!0})}catch{await K(`git remote add ${e} ${r}`,{throwOnError:!0})}}function Co(e){return K(`git config --get ${e}`,{throwOnError:!0})}function wo(e,r){return K(`git config --local ${e} "${r}"`,{throwOnError:!0})}function fu(e){return K(`git sparse-checkout set ${e}`,{throwOnError:!0})}function du(e){return K(`git checkout ${e}`,{throwOnError:!0})}function Kr(e){return K(`git checkout -b ${e} || git checkout ${e}`,{throwOnError:!0})}function So(e){return K(`git branch -D ${e}`,{throwOnError:!1})}async function Du(e){return await So(e),K(`git push origin -d ${e}`,{throwOnError:!1})}async function hu(e){return await K(`git ls-remote --heads origin ${e}`,{throwOnError:!1})!==""}function vo(e,r){return K(`git fetch ${e} ${r} --depth=1`,{throwOnError:!1})}function mu(e){return K(`git reset --hard ${e}`,{throwOnError:!0})}async function tf(){try{return await K("git remote get-url origin",{throwOnError:!1}),!0}catch{return!1}}var Vt=W(require("node:fs")),Ro=W(require("node:path")),bu=W(require("node:os"));var gu={production:{url:"https://www.raycast.com",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},staging:{url:"https://www.rayca.st",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},development:{url:"http://localhost:3000",clientID:"rOCD_LhnXFObSLu9v7788qOGu25FZDqEzGjet4Q45ZY"}}[process.env.API_ENV||"production"],rf="e69bae0ec90f5e838555",U={},nf;function pe(e){switch(e){case"raycastApiURL":return process.env.RAY_APIURL||U.APIURL||gu.url;case"raycastAccessToken":return process.env.RAY_TOKEN||U.Token||U.AccessToken||"";case"raycastClientId":return process.env.RAY_CLIENT_ID||U.ClientID||gu.clientID;case"githubClientId":return process.env.RAY_GithubClientID||U.GithubClientID||rf;case"githubAccessToken":return process.env.GITHUB_ACCESS_TOKEN||U.GithubAccessToken||"";case"flavorName":return process.env.RAY_Target?process.env.RAY_Target:typeof U.Target<"u"?U.Target:Ao(process.platform==="win32"?"x":"release")}}function Jr(e,r){switch(e){case"raycastApiURL":r===void 0?delete U.APIURL:U.APIURL=r;break;case"raycastAccessToken":r===void 0?delete U.Token:U.Token=r,delete U.AccessToken;break;case"raycastClientId":r===void 0?delete U.ClientID:U.ClientID=r;break;case"githubAccessToken":r===void 0?delete U.GithubAccessToken:U.GithubAccessToken=r;break;case"flavorName":r===void 0?delete U.Target:U.Target=r;break}let o=Bo();Vt.writeFileSync(Ro.join(o,"config.json"),JSON.stringify(U,null,"  "),"utf8")}function Ao(e){switch(e){case"debug":return"debug";case"internal":return"internal";case"release":return"";case"x":return"x";case"x-internal":return"x-internal";case"x-development":return"x-development";case void 0:return pe("flavorName")}}function of(){let e=Ao(nf);return e==""?"raycast":`raycast-${e}`}function Bo(){let e=Ro.join(bu.default.homedir(),".config",of());return Vt.mkdirSync(e,{recursive:!0}),e}var ca=require("@oclif/core"),fa=W(Qu()),da=W(la());async function ke(e,r){let o;try{o=await(0,fa.default)(e,{method:r.method||"GET",headers:{"Content-Type":"application/json",Accept:"application/json",...r.token?{Authorization:`Bearer ${r.token}`}:void 0},body:r.body})}catch(i){throw new Error(`HTTP request: ${i.message}`)}if(!o.ok){switch(o.status){case 401:throw new Ue(o,"not authorized - please log in first using `npx ray login`");case 403:throw new Ue(o,"forbidden - you don't have permissions to perform the request");case 402:throw new Ue(o,"the limit of free commands has been reached")}let i=await o.text(),u;try{u=JSON.parse(i)}catch{throw new Ue(o,`HTTP error: ${o.status} - ${i}`)}throw Array.isArray(u.errors)&&u.errors.length>0?new Ue(o,`error: ${u.errors[0].status} - ${u.errors[0].title}`):new Ue(o,`HTTP error: ${o.status} - ${i}`)}return await o.json()}var Ue=class extends Error{constructor(r,o){let i=r.headers.get("X-Request-Id");i?super(`${o} (${r.url} RequestID: ${i})`):super(o),this.name="HTTPError"}};function Da(e){(0,da.default)(e).catch(r=>{ca.ux.error(new Error(`failed opening browser to URL ${e}: ${r.message}`),{exit:1})})}var Zo=W(require("path"));async function ha(){if(pe("githubAccessToken"))return;let e=pe("githubClientId"),r=await ke("https://github.com/login/device/code",{method:"POST",body:JSON.stringify({client_id:e,scope:"repo"})});Pe("info",`

\u{1F510} Raycast extensions are published on GitHub.
To automate this process, you have to authenticate with GitHub.

First copy your one-time code: ${r.user_code}
Press Enter to open github.com in your browser...`),process.stdin.setRawMode(!0),process.stdin.resume(),await new Promise(i=>process.stdin.once("data",u=>{let l=[...u];l.length>0&&l[0]===3&&(console.log("^C"),process.exit(1)),process.stdin.setRawMode(!1),i(void 0)})),Da(r.verification_uri);let o=r.interval*1e3;for(;;){await new Promise(i=>setTimeout(i,o));try{let i=await ke("https://github.com/login/oauth/access_token",{method:"POST",body:JSON.stringify({client_id:e,device_code:r.device_code,grant_type:"urn:ietf:params:oauth:grant-type:device_code"})});if(!i.error){Jr("githubAccessToken",i.access_token);return}if(i.error!=="authorization_pending")throw new Error(i.error_description)}catch(i){throw new Error(`failed to get the access token (${i.message})`)}}}async function ma(e,r){let o=pe("githubAccessToken"),i=await ke(`https://api.github.com/repos/${e.owner.login}/${e.name}/forks`,{method:"POST",token:o,body:JSON.stringify({name:r,default_branch_only:!0})});for(let u=0;u<=30;u++){try{await ke(`https://api.github.com/repos/${i.owner.login}/${i.name}/commits?per_page=1`,{token:o});break}catch(l){if(u===30)throw new Error(`fork not ready after 1min (${l.message})`)}await new Promise(l=>setTimeout(l,2e3))}return i}async function pa(e){let r=pe("githubAccessToken");try{await ke(`https://api.github.com/repos/${e.owner.login}/${e.name}/merge-upstream`,{method:"POST",token:r,body:JSON.stringify({branch:"main"})})}catch(o){throw new Error(`could not get the latest changes. Head to https://github.com/${e.owner.login}/${e.name}, select the Sync fork dropdown menu above the list of files, and then click Update branch. Once you've done that, try running this command again

Error: ${o.message}`)}}async function ga(e){let r=pe("githubAccessToken");await ke(`https://api.github.com/repos/${e.owner.login}/${e.name}`,{method:"POST",token:r,body:JSON.stringify({delete_branch_on_merge:"true"})})}function ba(e,r){return lu(`https://oauth2:${pe("githubAccessToken")}@github.com/${e.owner.login}/${e.name}`,r)}function Xo(e,r){return cu(e,`https://oauth2:${pe("githubAccessToken")}@github.com/${r.owner.login}/${r.name}`)}async function Fa(e,r,o){let i=`"\\"name\\": \\"${r}\\"" "\\"author\\": \\"${o}\\"" repo:raycast/extensions in:file path:extensions extension:json`,u=pe("githubAccessToken"),f=(await ke(`https://api.github.com/search/code?q=${encodeURIComponent(i)}&per_page=3`,{token:u})).items.filter(d=>d.name==="package.json");if(f.length===0)return Zo.default.join("extensions",r);if(f.length>1)throw new Error(`found more than one extension with name ${r}`);return Zo.default.dirname(f[0].path)}async function _a(e){let r=`type:pr repo:raycast/extensions ${e} is:merged`,o=pe("githubAccessToken");return(await ke(`https://api.github.com/search/issues?q=${encodeURIComponent(r)}&per_page=1`,{token:o})).items.length>0}var ya=!1;async function Ca(e){if(!await su())throw new Error("please create a git repository first (git init)");if(await uu())throw new Error("please commit or discard your uncommited changes first (git commit -a -m 'your changes')");await ha();let r={owner:{login:"raycast"},name:"extensions"},o=ei.default.join(Bo(),"public-extensions-fork"),i=`ext/${e.name}`,u="",l,f=await Vr("getting fork",async()=>{try{l=await ma(r,"raycast-extensions"),await pa(l),await ga(l)}catch(d){throw new Error(`fork extensions repo: ${d.message}`)}},{printError:!1});if(f){if(f.message.includes("not authorized - please log in first using `npx ray login`")){if(ya)throw new Error("fork extensions repo: not authorized");return ya=!0,Jr("githubAccessToken",void 0),Ca(e)}throw f}else l=l;if(!Ea.default.existsSync(o)){let d=await Vr("cloning repo",async()=>{await ba(l,o)},{printError:!1});if(d)throw d}if(Qr(o),await Xo("origin",l),await Xo("upstream",r),f=await Vr("preparing clone",async()=>{let d=await Fa(r,e.name,e.author);u=ei.default.join(o,d),Qr(void 0);let p=await Co("user.name"),g=await Co("user.email");if(Qr(o),await wo("user.name",p),await wo("user.email",g),await fu(d),await du("main"),await vo("upstream","main"),await mu("upstream/main"),await hu(i)){await Kr(i),await vo("origin",i);let O=await au();await _a(O)&&(await Du(i),await Kr(i))}else await So(i),await Kr(i)},{printError:!1}),f)throw f;return{upstream:r,fork:l,clonePath:o,extensionPathInClone:u,branch:i}}0&&(module.exports={setupGitHubFork});
/*! Bundled license information:

node-fetch-cjs/dist/index.js:
  (*! fetch-blob. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
  (*! formdata-polyfill. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
  (*! node-domexception. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
*/
