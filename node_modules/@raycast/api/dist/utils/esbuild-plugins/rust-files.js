"use strict";var $f=Object.create;var Ct=Object.defineProperty;var Nf=Object.getOwnPropertyDescriptor;var Lf=Object.getOwnPropertyNames;var jf=Object.getPrototypeOf,Uf=Object.prototype.hasOwnProperty;var v=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),kf=(e,t)=>{for(var r in t)Ct(e,r,{get:t[r],enumerable:!0})},ai=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of Lf(t))!Uf.call(e,o)&&o!==r&&Ct(e,o,{get:()=>t[o],enumerable:!(n=Nf(t,o))||n.enumerable});return e};var E=(e,t,r)=>(r=e!=null?$f(jf(e)):{},ai(t||!e||!e.__esModule?Ct(r,"default",{value:e,enumerable:!0}):r,e)),Gf=e=>ai(Ct({},"__esModule",{value:!0}),e);var Vi=v((AE,zi)=>{zi.exports=Wi;Wi.sync=PD;var ki=require("fs");function MD(e,t){var r=t.pathExt!==void 0?t.pathExt:process.env.PATHEXT;if(!r||(r=r.split(";"),r.indexOf("")!==-1))return!0;for(var n=0;n<r.length;n++){var o=r[n].toLowerCase();if(o&&e.substr(-o.length).toLowerCase()===o)return!0}return!1}function Gi(e,t,r){return!e.isSymbolicLink()&&!e.isFile()?!1:MD(t,r)}function Wi(e,t,r){ki.stat(e,function(n,o){r(n,n?!1:Gi(o,e,t))})}function PD(e,t){return Gi(ki.statSync(e),e,t)}});var Xi=v((BE,Ki)=>{Ki.exports=qi;qi.sync=$D;var Yi=require("fs");function qi(e,t,r){Yi.stat(e,function(n,o){r(n,n?!1:Hi(o,t))})}function $D(e,t){return Hi(Yi.statSync(e),t)}function Hi(e,t){return e.isFile()&&ND(e,t)}function ND(e,t){var r=e.mode,n=e.uid,o=e.gid,i=t.uid!==void 0?t.uid:process.getuid&&process.getuid(),s=t.gid!==void 0?t.gid:process.getgid&&process.getgid(),a=parseInt("100",8),u=parseInt("010",8),l=parseInt("001",8),c=a|u,f=r&l||r&u&&o===s||r&a&&n===i||r&c&&i===0;return f}});var Zi=v((_E,Ji)=>{var TE=require("fs"),Lt;process.platform==="win32"||global.TESTING_WINDOWS?Lt=Vi():Lt=Xi();Ji.exports=cn;cn.sync=LD;function cn(e,t,r){if(typeof t=="function"&&(r=t,t={}),!r){if(typeof Promise!="function")throw new TypeError("callback not provided");return new Promise(function(n,o){cn(e,t||{},function(i,s){i?o(i):n(s)})})}Lt(e,t||{},function(n,o){n&&(n.code==="EACCES"||t&&t.ignoreErrors)&&(n=null,o=!1),r(n,o)})}function LD(e,t){try{return Lt.sync(e,t||{})}catch(r){if(t&&t.ignoreErrors||r.code==="EACCES")return!1;throw r}}});var is=v((OE,os)=>{var Te=process.platform==="win32"||process.env.OSTYPE==="cygwin"||process.env.OSTYPE==="msys",Qi=require("path"),jD=Te?";":":",es=Zi(),ts=e=>Object.assign(new Error(`not found: ${e}`),{code:"ENOENT"}),rs=(e,t)=>{let r=t.colon||jD,n=e.match(/\//)||Te&&e.match(/\\/)?[""]:[...Te?[process.cwd()]:[],...(t.path||process.env.PATH||"").split(r)],o=Te?t.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM":"",i=Te?o.split(r):[""];return Te&&e.indexOf(".")!==-1&&i[0]!==""&&i.unshift(""),{pathEnv:n,pathExt:i,pathExtExe:o}},ns=(e,t,r)=>{typeof t=="function"&&(r=t,t={}),t||(t={});let{pathEnv:n,pathExt:o,pathExtExe:i}=rs(e,t),s=[],a=l=>new Promise((c,f)=>{if(l===n.length)return t.all&&s.length?c(s):f(ts(e));let D=n[l],p=/^".*"$/.test(D)?D.slice(1,-1):D,d=Qi.join(p,e),g=!p&&/^\.[\\\/]/.test(e)?e.slice(0,2)+d:d;c(u(g,l,0))}),u=(l,c,f)=>new Promise((D,p)=>{if(f===o.length)return D(a(c+1));let d=o[f];es(l+d,{pathExt:i},(g,w)=>{if(!g&&w)if(t.all)s.push(l+d);else return D(l+d);return D(u(l,c,f+1))})});return r?a(0).then(l=>r(null,l),r):a(0)},UD=(e,t)=>{t=t||{};let{pathEnv:r,pathExt:n,pathExtExe:o}=rs(e,t),i=[];for(let s=0;s<r.length;s++){let a=r[s],u=/^".*"$/.test(a)?a.slice(1,-1):a,l=Qi.join(u,e),c=!u&&/^\.[\\\/]/.test(e)?e.slice(0,2)+l:l;for(let f=0;f<n.length;f++){let D=c+n[f];try{if(es.sync(D,{pathExt:o}))if(t.all)i.push(D);else return D}catch{}}}if(t.all&&i.length)return i;if(t.nothrow)return null;throw ts(e)};os.exports=ns;ns.sync=UD});var as=v((RE,ln)=>{"use strict";var ss=(e={})=>{let t=e.env||process.env;return(e.platform||process.platform)!=="win32"?"PATH":Object.keys(t).reverse().find(n=>n.toUpperCase()==="PATH")||"Path"};ln.exports=ss;ln.exports.default=ss});var fs=v((IE,ls)=>{"use strict";var us=require("path"),kD=is(),GD=as();function cs(e,t){let r=e.options.env||process.env,n=process.cwd(),o=e.options.cwd!=null,i=o&&process.chdir!==void 0&&!process.chdir.disabled;if(i)try{process.chdir(e.options.cwd)}catch{}let s;try{s=kD.sync(e.command,{path:r[GD({env:r})],pathExt:t?us.delimiter:void 0})}catch{}finally{i&&process.chdir(n)}return s&&(s=us.resolve(o?e.options.cwd:"",s)),s}function WD(e){return cs(e)||cs(e,!0)}ls.exports=WD});var Ds=v((vE,Dn)=>{"use strict";var fn=/([()\][%!^"`<>&|;, *?])/g;function zD(e){return e=e.replace(fn,"^$1"),e}function VD(e,t){return e=`${e}`,e=e.replace(/(?=(\\+?)?)\1"/g,'$1$1\\"'),e=e.replace(/(?=(\\+?)?)\1$/,"$1$1"),e=`"${e}"`,e=e.replace(fn,"^$1"),t&&(e=e.replace(fn,"^$1")),e}Dn.exports.command=zD;Dn.exports.argument=VD});var ds=v((ME,ps)=>{"use strict";ps.exports=/^#!(.*)/});var hs=v((PE,ms)=>{"use strict";var YD=ds();ms.exports=(e="")=>{let t=e.match(YD);if(!t)return null;let[r,n]=t[0].replace(/#! ?/,"").split(" "),o=r.split("/").pop();return o==="env"?n:n?`${o} ${n}`:o}});var gs=v(($E,Fs)=>{"use strict";var pn=require("fs"),qD=hs();function HD(e){let r=Buffer.alloc(150),n;try{n=pn.openSync(e,"r"),pn.readSync(n,r,0,150,0),pn.closeSync(n)}catch{}return qD(r.toString())}Fs.exports=HD});var Ss=v((NE,bs)=>{"use strict";var KD=require("path"),Es=fs(),ys=Ds(),XD=gs(),JD=process.platform==="win32",ZD=/\.(?:com|exe)$/i,QD=/node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;function ep(e){e.file=Es(e);let t=e.file&&XD(e.file);return t?(e.args.unshift(e.file),e.command=t,Es(e)):e.file}function tp(e){if(!JD)return e;let t=ep(e),r=!ZD.test(t);if(e.options.forceShell||r){let n=QD.test(t);e.command=KD.normalize(e.command),e.command=ys.command(e.command),e.args=e.args.map(i=>ys.argument(i,n));let o=[e.command].concat(e.args).join(" ");e.args=["/d","/s","/c",`"${o}"`],e.command=process.env.comspec||"cmd.exe",e.options.windowsVerbatimArguments=!0}return e}function rp(e,t,r){t&&!Array.isArray(t)&&(r=t,t=null),t=t?t.slice(0):[],r=Object.assign({},r);let n={command:e,args:t,options:r,file:void 0,original:{command:e,args:t}};return r.shell?n:tp(n)}bs.exports=rp});var xs=v((LE,Cs)=>{"use strict";var dn=process.platform==="win32";function mn(e,t){return Object.assign(new Error(`${t} ${e.command} ENOENT`),{code:"ENOENT",errno:"ENOENT",syscall:`${t} ${e.command}`,path:e.command,spawnargs:e.args})}function np(e,t){if(!dn)return;let r=e.emit;e.emit=function(n,o){if(n==="exit"){let i=ws(o,t);if(i)return r.call(e,"error",i)}return r.apply(e,arguments)}}function ws(e,t){return dn&&e===1&&!t.file?mn(t.original,"spawn"):null}function op(e,t){return dn&&e===1&&!t.file?mn(t.original,"spawnSync"):null}Cs.exports={hookChildProcess:np,verifyENOENT:ws,verifyENOENTSync:op,notFoundError:mn}});var Ts=v((jE,_e)=>{"use strict";var As=require("child_process"),hn=Ss(),Fn=xs();function Bs(e,t,r){let n=hn(e,t,r),o=As.spawn(n.command,n.args,n.options);return Fn.hookChildProcess(o,n),o}function ip(e,t,r){let n=hn(e,t,r),o=As.spawnSync(n.command,n.args,n.options);return o.error=o.error||Fn.verifyENOENTSync(o.status,n),o}_e.exports=Bs;_e.exports.spawn=Bs;_e.exports.sync=ip;_e.exports._parse=hn;_e.exports._enoent=Fn});var Hl=v((_1,ql)=>{var Zh=require("node:tty"),Qh=Zh?.WriteStream?.prototype?.hasColors?.()??!1,F=(e,t)=>{if(!Qh)return o=>o;let r=`\x1B[${e}m`,n=`\x1B[${t}m`;return o=>{let i=o+"",s=i.indexOf(n);if(s===-1)return r+i+n;let a=r,u=0;for(;s!==-1;)a+=i.slice(u,s)+r,u=s+n.length,s=i.indexOf(n,u);return a+=i.slice(u)+n,a}},m={};m.reset=F(0,0);m.bold=F(1,22);m.dim=F(2,22);m.italic=F(3,23);m.underline=F(4,24);m.overline=F(53,55);m.inverse=F(7,27);m.hidden=F(8,28);m.strikethrough=F(9,29);m.black=F(30,39);m.red=F(31,39);m.green=F(32,39);m.yellow=F(33,39);m.blue=F(34,39);m.magenta=F(35,39);m.cyan=F(36,39);m.white=F(37,39);m.gray=F(90,39);m.bgBlack=F(40,49);m.bgRed=F(41,49);m.bgGreen=F(42,49);m.bgYellow=F(43,49);m.bgBlue=F(44,49);m.bgMagenta=F(45,49);m.bgCyan=F(46,49);m.bgWhite=F(47,49);m.bgGray=F(100,49);m.redBright=F(91,39);m.greenBright=F(92,39);m.yellowBright=F(93,39);m.blueBright=F(94,39);m.magentaBright=F(95,39);m.cyanBright=F(96,39);m.whiteBright=F(97,39);m.bgRedBright=F(101,49);m.bgGreenBright=F(102,49);m.bgYellowBright=F(103,49);m.bgBlueBright=F(104,49);m.bgMagentaBright=F(105,49);m.bgCyanBright=F(106,49);m.bgWhiteBright=F(107,49);ql.exports=m});var Df=v((H1,SF)=>{SF.exports={dots:{interval:80,frames:["\u280B","\u2819","\u2839","\u2838","\u283C","\u2834","\u2826","\u2827","\u2807","\u280F"]},dots2:{interval:80,frames:["\u28FE","\u28FD","\u28FB","\u28BF","\u287F","\u28DF","\u28EF","\u28F7"]},dots3:{interval:80,frames:["\u280B","\u2819","\u281A","\u281E","\u2816","\u2826","\u2834","\u2832","\u2833","\u2813"]},dots4:{interval:80,frames:["\u2804","\u2806","\u2807","\u280B","\u2819","\u2838","\u2830","\u2820","\u2830","\u2838","\u2819","\u280B","\u2807","\u2806"]},dots5:{interval:80,frames:["\u280B","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B"]},dots6:{interval:80,frames:["\u2801","\u2809","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2824","\u2804","\u2804","\u2824","\u2834","\u2832","\u2812","\u2802","\u2802","\u2812","\u281A","\u2819","\u2809","\u2801"]},dots7:{interval:80,frames:["\u2808","\u2809","\u280B","\u2813","\u2812","\u2810","\u2810","\u2812","\u2816","\u2826","\u2824","\u2820","\u2820","\u2824","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B","\u2809","\u2808"]},dots8:{interval:80,frames:["\u2801","\u2801","\u2809","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2824","\u2804","\u2804","\u2824","\u2820","\u2820","\u2824","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B","\u2809","\u2808","\u2808"]},dots9:{interval:80,frames:["\u28B9","\u28BA","\u28BC","\u28F8","\u28C7","\u2867","\u2857","\u284F"]},dots10:{interval:80,frames:["\u2884","\u2882","\u2881","\u2841","\u2848","\u2850","\u2860"]},dots11:{interval:100,frames:["\u2801","\u2802","\u2804","\u2840","\u2880","\u2820","\u2810","\u2808"]},dots12:{interval:80,frames:["\u2880\u2800","\u2840\u2800","\u2804\u2800","\u2882\u2800","\u2842\u2800","\u2805\u2800","\u2883\u2800","\u2843\u2800","\u280D\u2800","\u288B\u2800","\u284B\u2800","\u280D\u2801","\u288B\u2801","\u284B\u2801","\u280D\u2809","\u280B\u2809","\u280B\u2809","\u2809\u2819","\u2809\u2819","\u2809\u2829","\u2808\u2899","\u2808\u2859","\u2888\u2829","\u2840\u2899","\u2804\u2859","\u2882\u2829","\u2842\u2898","\u2805\u2858","\u2883\u2828","\u2843\u2890","\u280D\u2850","\u288B\u2820","\u284B\u2880","\u280D\u2841","\u288B\u2801","\u284B\u2801","\u280D\u2809","\u280B\u2809","\u280B\u2809","\u2809\u2819","\u2809\u2819","\u2809\u2829","\u2808\u2899","\u2808\u2859","\u2808\u2829","\u2800\u2899","\u2800\u2859","\u2800\u2829","\u2800\u2898","\u2800\u2858","\u2800\u2828","\u2800\u2890","\u2800\u2850","\u2800\u2820","\u2800\u2880","\u2800\u2840"]},dots13:{interval:80,frames:["\u28FC","\u28F9","\u28BB","\u283F","\u285F","\u28CF","\u28E7","\u28F6"]},dots8Bit:{interval:80,frames:["\u2800","\u2801","\u2802","\u2803","\u2804","\u2805","\u2806","\u2807","\u2840","\u2841","\u2842","\u2843","\u2844","\u2845","\u2846","\u2847","\u2808","\u2809","\u280A","\u280B","\u280C","\u280D","\u280E","\u280F","\u2848","\u2849","\u284A","\u284B","\u284C","\u284D","\u284E","\u284F","\u2810","\u2811","\u2812","\u2813","\u2814","\u2815","\u2816","\u2817","\u2850","\u2851","\u2852","\u2853","\u2854","\u2855","\u2856","\u2857","\u2818","\u2819","\u281A","\u281B","\u281C","\u281D","\u281E","\u281F","\u2858","\u2859","\u285A","\u285B","\u285C","\u285D","\u285E","\u285F","\u2820","\u2821","\u2822","\u2823","\u2824","\u2825","\u2826","\u2827","\u2860","\u2861","\u2862","\u2863","\u2864","\u2865","\u2866","\u2867","\u2828","\u2829","\u282A","\u282B","\u282C","\u282D","\u282E","\u282F","\u2868","\u2869","\u286A","\u286B","\u286C","\u286D","\u286E","\u286F","\u2830","\u2831","\u2832","\u2833","\u2834","\u2835","\u2836","\u2837","\u2870","\u2871","\u2872","\u2873","\u2874","\u2875","\u2876","\u2877","\u2838","\u2839","\u283A","\u283B","\u283C","\u283D","\u283E","\u283F","\u2878","\u2879","\u287A","\u287B","\u287C","\u287D","\u287E","\u287F","\u2880","\u2881","\u2882","\u2883","\u2884","\u2885","\u2886","\u2887","\u28C0","\u28C1","\u28C2","\u28C3","\u28C4","\u28C5","\u28C6","\u28C7","\u2888","\u2889","\u288A","\u288B","\u288C","\u288D","\u288E","\u288F","\u28C8","\u28C9","\u28CA","\u28CB","\u28CC","\u28CD","\u28CE","\u28CF","\u2890","\u2891","\u2892","\u2893","\u2894","\u2895","\u2896","\u2897","\u28D0","\u28D1","\u28D2","\u28D3","\u28D4","\u28D5","\u28D6","\u28D7","\u2898","\u2899","\u289A","\u289B","\u289C","\u289D","\u289E","\u289F","\u28D8","\u28D9","\u28DA","\u28DB","\u28DC","\u28DD","\u28DE","\u28DF","\u28A0","\u28A1","\u28A2","\u28A3","\u28A4","\u28A5","\u28A6","\u28A7","\u28E0","\u28E1","\u28E2","\u28E3","\u28E4","\u28E5","\u28E6","\u28E7","\u28A8","\u28A9","\u28AA","\u28AB","\u28AC","\u28AD","\u28AE","\u28AF","\u28E8","\u28E9","\u28EA","\u28EB","\u28EC","\u28ED","\u28EE","\u28EF","\u28B0","\u28B1","\u28B2","\u28B3","\u28B4","\u28B5","\u28B6","\u28B7","\u28F0","\u28F1","\u28F2","\u28F3","\u28F4","\u28F5","\u28F6","\u28F7","\u28B8","\u28B9","\u28BA","\u28BB","\u28BC","\u28BD","\u28BE","\u28BF","\u28F8","\u28F9","\u28FA","\u28FB","\u28FC","\u28FD","\u28FE","\u28FF"]},sand:{interval:80,frames:["\u2801","\u2802","\u2804","\u2840","\u2848","\u2850","\u2860","\u28C0","\u28C1","\u28C2","\u28C4","\u28CC","\u28D4","\u28E4","\u28E5","\u28E6","\u28EE","\u28F6","\u28F7","\u28FF","\u287F","\u283F","\u289F","\u281F","\u285B","\u281B","\u282B","\u288B","\u280B","\u280D","\u2849","\u2809","\u2811","\u2821","\u2881"]},line:{interval:130,frames:["-","\\","|","/"]},line2:{interval:100,frames:["\u2802","-","\u2013","\u2014","\u2013","-"]},pipe:{interval:100,frames:["\u2524","\u2518","\u2534","\u2514","\u251C","\u250C","\u252C","\u2510"]},simpleDots:{interval:400,frames:[".  ",".. ","...","   "]},simpleDotsScrolling:{interval:200,frames:[".  ",".. ","..."," ..","  .","   "]},star:{interval:70,frames:["\u2736","\u2738","\u2739","\u273A","\u2739","\u2737"]},star2:{interval:80,frames:["+","x","*"]},flip:{interval:70,frames:["_","_","_","-","`","`","'","\xB4","-","_","_","_"]},hamburger:{interval:100,frames:["\u2631","\u2632","\u2634"]},growVertical:{interval:120,frames:["\u2581","\u2583","\u2584","\u2585","\u2586","\u2587","\u2586","\u2585","\u2584","\u2583"]},growHorizontal:{interval:120,frames:["\u258F","\u258E","\u258D","\u258C","\u258B","\u258A","\u2589","\u258A","\u258B","\u258C","\u258D","\u258E"]},balloon:{interval:140,frames:[" ",".","o","O","@","*"," "]},balloon2:{interval:120,frames:[".","o","O","\xB0","O","o","."]},noise:{interval:100,frames:["\u2593","\u2592","\u2591"]},bounce:{interval:120,frames:["\u2801","\u2802","\u2804","\u2802"]},boxBounce:{interval:120,frames:["\u2596","\u2598","\u259D","\u2597"]},boxBounce2:{interval:100,frames:["\u258C","\u2580","\u2590","\u2584"]},triangle:{interval:50,frames:["\u25E2","\u25E3","\u25E4","\u25E5"]},binary:{interval:80,frames:["010010","001100","100101","111010","111101","010111","101011","111000","110011","110101"]},arc:{interval:100,frames:["\u25DC","\u25E0","\u25DD","\u25DE","\u25E1","\u25DF"]},circle:{interval:120,frames:["\u25E1","\u2299","\u25E0"]},squareCorners:{interval:180,frames:["\u25F0","\u25F3","\u25F2","\u25F1"]},circleQuarters:{interval:120,frames:["\u25F4","\u25F7","\u25F6","\u25F5"]},circleHalves:{interval:50,frames:["\u25D0","\u25D3","\u25D1","\u25D2"]},squish:{interval:100,frames:["\u256B","\u256A"]},toggle:{interval:250,frames:["\u22B6","\u22B7"]},toggle2:{interval:80,frames:["\u25AB","\u25AA"]},toggle3:{interval:120,frames:["\u25A1","\u25A0"]},toggle4:{interval:100,frames:["\u25A0","\u25A1","\u25AA","\u25AB"]},toggle5:{interval:100,frames:["\u25AE","\u25AF"]},toggle6:{interval:300,frames:["\u101D","\u1040"]},toggle7:{interval:80,frames:["\u29BE","\u29BF"]},toggle8:{interval:100,frames:["\u25CD","\u25CC"]},toggle9:{interval:100,frames:["\u25C9","\u25CE"]},toggle10:{interval:100,frames:["\u3282","\u3280","\u3281"]},toggle11:{interval:50,frames:["\u29C7","\u29C6"]},toggle12:{interval:120,frames:["\u2617","\u2616"]},toggle13:{interval:80,frames:["=","*","-"]},arrow:{interval:100,frames:["\u2190","\u2196","\u2191","\u2197","\u2192","\u2198","\u2193","\u2199"]},arrow2:{interval:80,frames:["\u2B06\uFE0F ","\u2197\uFE0F ","\u27A1\uFE0F ","\u2198\uFE0F ","\u2B07\uFE0F ","\u2199\uFE0F ","\u2B05\uFE0F ","\u2196\uFE0F "]},arrow3:{interval:120,frames:["\u25B9\u25B9\u25B9\u25B9\u25B9","\u25B8\u25B9\u25B9\u25B9\u25B9","\u25B9\u25B8\u25B9\u25B9\u25B9","\u25B9\u25B9\u25B8\u25B9\u25B9","\u25B9\u25B9\u25B9\u25B8\u25B9","\u25B9\u25B9\u25B9\u25B9\u25B8"]},bouncingBar:{interval:80,frames:["[    ]","[=   ]","[==  ]","[=== ]","[====]","[ ===]","[  ==]","[   =]","[    ]","[   =]","[  ==]","[ ===]","[====]","[=== ]","[==  ]","[=   ]"]},bouncingBall:{interval:80,frames:["( \u25CF    )","(  \u25CF   )","(   \u25CF  )","(    \u25CF )","(     \u25CF)","(    \u25CF )","(   \u25CF  )","(  \u25CF   )","( \u25CF    )","(\u25CF     )"]},smiley:{interval:200,frames:["\u{1F604} ","\u{1F61D} "]},monkey:{interval:300,frames:["\u{1F648} ","\u{1F648} ","\u{1F649} ","\u{1F64A} "]},hearts:{interval:100,frames:["\u{1F49B} ","\u{1F499} ","\u{1F49C} ","\u{1F49A} ","\u2764\uFE0F "]},clock:{interval:100,frames:["\u{1F55B} ","\u{1F550} ","\u{1F551} ","\u{1F552} ","\u{1F553} ","\u{1F554} ","\u{1F555} ","\u{1F556} ","\u{1F557} ","\u{1F558} ","\u{1F559} ","\u{1F55A} "]},earth:{interval:180,frames:["\u{1F30D} ","\u{1F30E} ","\u{1F30F} "]},material:{interval:17,frames:["\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581"]},moon:{interval:80,frames:["\u{1F311} ","\u{1F312} ","\u{1F313} ","\u{1F314} ","\u{1F315} ","\u{1F316} ","\u{1F317} ","\u{1F318} "]},runner:{interval:140,frames:["\u{1F6B6} ","\u{1F3C3} "]},pong:{interval:80,frames:["\u2590\u2802       \u258C","\u2590\u2808       \u258C","\u2590 \u2802      \u258C","\u2590 \u2820      \u258C","\u2590  \u2840     \u258C","\u2590  \u2820     \u258C","\u2590   \u2802    \u258C","\u2590   \u2808    \u258C","\u2590    \u2802   \u258C","\u2590    \u2820   \u258C","\u2590     \u2840  \u258C","\u2590     \u2820  \u258C","\u2590      \u2802 \u258C","\u2590      \u2808 \u258C","\u2590       \u2802\u258C","\u2590       \u2820\u258C","\u2590       \u2840\u258C","\u2590      \u2820 \u258C","\u2590      \u2802 \u258C","\u2590     \u2808  \u258C","\u2590     \u2802  \u258C","\u2590    \u2820   \u258C","\u2590    \u2840   \u258C","\u2590   \u2820    \u258C","\u2590   \u2802    \u258C","\u2590  \u2808     \u258C","\u2590  \u2802     \u258C","\u2590 \u2820      \u258C","\u2590 \u2840      \u258C","\u2590\u2820       \u258C"]},shark:{interval:120,frames:["\u2590|\\____________\u258C","\u2590_|\\___________\u258C","\u2590__|\\__________\u258C","\u2590___|\\_________\u258C","\u2590____|\\________\u258C","\u2590_____|\\_______\u258C","\u2590______|\\______\u258C","\u2590_______|\\_____\u258C","\u2590________|\\____\u258C","\u2590_________|\\___\u258C","\u2590__________|\\__\u258C","\u2590___________|\\_\u258C","\u2590____________|\\\u258C","\u2590____________/|\u258C","\u2590___________/|_\u258C","\u2590__________/|__\u258C","\u2590_________/|___\u258C","\u2590________/|____\u258C","\u2590_______/|_____\u258C","\u2590______/|______\u258C","\u2590_____/|_______\u258C","\u2590____/|________\u258C","\u2590___/|_________\u258C","\u2590__/|__________\u258C","\u2590_/|___________\u258C","\u2590/|____________\u258C"]},dqpb:{interval:100,frames:["d","q","p","b"]},weather:{interval:100,frames:["\u2600\uFE0F ","\u2600\uFE0F ","\u2600\uFE0F ","\u{1F324} ","\u26C5\uFE0F ","\u{1F325} ","\u2601\uFE0F ","\u{1F327} ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u26C8 ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u2601\uFE0F ","\u{1F325} ","\u26C5\uFE0F ","\u{1F324} ","\u2600\uFE0F ","\u2600\uFE0F "]},christmas:{interval:400,frames:["\u{1F332}","\u{1F384}"]},grenade:{interval:80,frames:["\u060C  ","\u2032  "," \xB4 "," \u203E ","  \u2E0C","  \u2E0A","  |","  \u204E","  \u2055"," \u0DF4 ","  \u2053","   ","   ","   "]},point:{interval:125,frames:["\u2219\u2219\u2219","\u25CF\u2219\u2219","\u2219\u25CF\u2219","\u2219\u2219\u25CF","\u2219\u2219\u2219"]},layer:{interval:150,frames:["-","=","\u2261"]},betaWave:{interval:80,frames:["\u03C1\u03B2\u03B2\u03B2\u03B2\u03B2\u03B2","\u03B2\u03C1\u03B2\u03B2\u03B2\u03B2\u03B2","\u03B2\u03B2\u03C1\u03B2\u03B2\u03B2\u03B2","\u03B2\u03B2\u03B2\u03C1\u03B2\u03B2\u03B2","\u03B2\u03B2\u03B2\u03B2\u03C1\u03B2\u03B2","\u03B2\u03B2\u03B2\u03B2\u03B2\u03C1\u03B2","\u03B2\u03B2\u03B2\u03B2\u03B2\u03B2\u03C1"]},fingerDance:{interval:160,frames:["\u{1F918} ","\u{1F91F} ","\u{1F596} ","\u270B ","\u{1F91A} ","\u{1F446} "]},fistBump:{interval:80,frames:["\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u3000\u{1F91C}\u3000\u3000\u{1F91B}\u3000 ","\u3000\u3000\u{1F91C}\u{1F91B}\u3000\u3000 ","\u3000\u{1F91C}\u2728\u{1F91B}\u3000\u3000 ","\u{1F91C}\u3000\u2728\u3000\u{1F91B}\u3000 "]},soccerHeader:{interval:80,frames:[" \u{1F9D1}\u26BD\uFE0F       \u{1F9D1} ","\u{1F9D1}  \u26BD\uFE0F      \u{1F9D1} ","\u{1F9D1}   \u26BD\uFE0F     \u{1F9D1} ","\u{1F9D1}    \u26BD\uFE0F    \u{1F9D1} ","\u{1F9D1}     \u26BD\uFE0F   \u{1F9D1} ","\u{1F9D1}      \u26BD\uFE0F  \u{1F9D1} ","\u{1F9D1}       \u26BD\uFE0F\u{1F9D1}  ","\u{1F9D1}      \u26BD\uFE0F  \u{1F9D1} ","\u{1F9D1}     \u26BD\uFE0F   \u{1F9D1} ","\u{1F9D1}    \u26BD\uFE0F    \u{1F9D1} ","\u{1F9D1}   \u26BD\uFE0F     \u{1F9D1} ","\u{1F9D1}  \u26BD\uFE0F      \u{1F9D1} "]},mindblown:{interval:160,frames:["\u{1F610} ","\u{1F610} ","\u{1F62E} ","\u{1F62E} ","\u{1F626} ","\u{1F626} ","\u{1F627} ","\u{1F627} ","\u{1F92F} ","\u{1F4A5} ","\u2728 ","\u3000 ","\u3000 ","\u3000 "]},speaker:{interval:160,frames:["\u{1F508} ","\u{1F509} ","\u{1F50A} ","\u{1F509} "]},orangePulse:{interval:100,frames:["\u{1F538} ","\u{1F536} ","\u{1F7E0} ","\u{1F7E0} ","\u{1F536} "]},bluePulse:{interval:100,frames:["\u{1F539} ","\u{1F537} ","\u{1F535} ","\u{1F535} ","\u{1F537} "]},orangeBluePulse:{interval:100,frames:["\u{1F538} ","\u{1F536} ","\u{1F7E0} ","\u{1F7E0} ","\u{1F536} ","\u{1F539} ","\u{1F537} ","\u{1F535} ","\u{1F535} ","\u{1F537} "]},timeTravel:{interval:100,frames:["\u{1F55B} ","\u{1F55A} ","\u{1F559} ","\u{1F558} ","\u{1F557} ","\u{1F556} ","\u{1F555} ","\u{1F554} ","\u{1F553} ","\u{1F552} ","\u{1F551} ","\u{1F550} "]},aesthetic:{interval:80,frames:["\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0","\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1\u25B1"]},dwarfFortress:{interval:80,frames:[" \u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2593\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2593\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2592\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2592\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2591\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2591\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A \u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2593\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2593\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2592\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2592\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2591\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2591\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A \u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2593\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2593\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2592\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2592\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2591\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2591\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A \u2588\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2593\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2593\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2592\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2592\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2591\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2591\u2588\u2588\xA3\xA3\xA3  ","   \u263A \u2588\u2588\xA3\xA3\xA3  ","    \u263A\u2588\u2588\xA3\xA3\xA3  ","    \u263A\u2588\u2588\xA3\xA3\xA3  ","    \u263A\u2593\u2588\xA3\xA3\xA3  ","    \u263A\u2593\u2588\xA3\xA3\xA3  ","    \u263A\u2592\u2588\xA3\xA3\xA3  ","    \u263A\u2592\u2588\xA3\xA3\xA3  ","    \u263A\u2591\u2588\xA3\xA3\xA3  ","    \u263A\u2591\u2588\xA3\xA3\xA3  ","    \u263A \u2588\xA3\xA3\xA3  ","     \u263A\u2588\xA3\xA3\xA3  ","     \u263A\u2588\xA3\xA3\xA3  ","     \u263A\u2593\xA3\xA3\xA3  ","     \u263A\u2593\xA3\xA3\xA3  ","     \u263A\u2592\xA3\xA3\xA3  ","     \u263A\u2592\xA3\xA3\xA3  ","     \u263A\u2591\xA3\xA3\xA3  ","     \u263A\u2591\xA3\xA3\xA3  ","     \u263A \xA3\xA3\xA3  ","      \u263A\xA3\xA3\xA3  ","      \u263A\xA3\xA3\xA3  ","      \u263A\u2593\xA3\xA3  ","      \u263A\u2593\xA3\xA3  ","      \u263A\u2592\xA3\xA3  ","      \u263A\u2592\xA3\xA3  ","      \u263A\u2591\xA3\xA3  ","      \u263A\u2591\xA3\xA3  ","      \u263A \xA3\xA3  ","       \u263A\xA3\xA3  ","       \u263A\xA3\xA3  ","       \u263A\u2593\xA3  ","       \u263A\u2593\xA3  ","       \u263A\u2592\xA3  ","       \u263A\u2592\xA3  ","       \u263A\u2591\xA3  ","       \u263A\u2591\xA3  ","       \u263A \xA3  ","        \u263A\xA3  ","        \u263A\xA3  ","        \u263A\u2593  ","        \u263A\u2593  ","        \u263A\u2592  ","        \u263A\u2592  ","        \u263A\u2591  ","        \u263A\u2591  ","        \u263A   ","        \u263A  &","        \u263A \u263C&","       \u263A \u263C &","       \u263A\u263C  &","      \u263A\u263C  & ","      \u203C   & ","     \u263A   &  ","    \u203C    &  ","   \u263A    &   ","  \u203C     &   "," \u263A     &    ","\u203C      &    ","      &     ","      &     ","     &   \u2591  ","     &   \u2592  ","    &    \u2593  ","    &    \xA3  ","   &    \u2591\xA3  ","   &    \u2592\xA3  ","  &     \u2593\xA3  ","  &     \xA3\xA3  "," &     \u2591\xA3\xA3  "," &     \u2592\xA3\xA3  ","&      \u2593\xA3\xA3  ","&      \xA3\xA3\xA3  ","      \u2591\xA3\xA3\xA3  ","      \u2592\xA3\xA3\xA3  ","      \u2593\xA3\xA3\xA3  ","      \u2588\xA3\xA3\xA3  ","     \u2591\u2588\xA3\xA3\xA3  ","     \u2592\u2588\xA3\xA3\xA3  ","     \u2593\u2588\xA3\xA3\xA3  ","     \u2588\u2588\xA3\xA3\xA3  ","    \u2591\u2588\u2588\xA3\xA3\xA3  ","    \u2592\u2588\u2588\xA3\xA3\xA3  ","    \u2593\u2588\u2588\xA3\xA3\xA3  ","    \u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2591\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2592\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2593\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2591\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2592\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2593\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2591\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2592\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2593\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "]}}});var qo=v((K1,df)=>{"use strict";var Vr=Object.assign({},Df()),pf=Object.keys(Vr);Object.defineProperty(Vr,"random",{get(){let e=Math.floor(Math.random()*pf.length),t=pf[e];return Vr[t]}});df.exports=Vr});var Rf=v((F3,Of)=>{Of.exports=()=>/[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26D3\uFE0F?(?:\u200D\uD83D\uDCA5)?|\u26F9(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF43\uDF45-\uDF4A\uDF4C-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDF44(?:\u200D\uD83D\uDFEB)?|\uDF4B(?:\u200D\uD83D\uDFE9)?|\uDFC3(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4\uDEB5](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE41\uDE43\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC08(?:\u200D\u2B1B)?|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC26(?:\u200D(?:\u2B1B|\uD83D\uDD25))?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?|\uDE42(?:\u200D[\u2194\u2195]\uFE0F?)?|\uDEB6(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE89\uDE8F-\uDEC2\uDEC6\uDECE-\uDEDC\uDEDF-\uDEE9]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDCE(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1|\uDDD1\u200D\uD83E\uDDD2(?:\u200D\uD83E\uDDD2)?|\uDDD2(?:\u200D\uD83E\uDDD2)?))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g});var QF={};kf(QF,{rustFiles:()=>HF});module.exports=Gf(QF);var _=E(require("node:fs")),M=E(require("node:path"));function B(e){if(typeof e!="object"||e===null)return!1;let t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}var ui=require("node:url"),xe=(e,t)=>{let r=Qr(Wf(e));if(typeof r!="string")throw new TypeError(`${t} must be a string or a file URL: ${r}.`);return r},Wf=e=>Zr(e)?e.toString():e,Zr=e=>typeof e!="string"&&e&&Object.getPrototypeOf(e)===String.prototype,Qr=e=>e instanceof URL?(0,ui.fileURLToPath)(e):e;var xt=(e,t=[],r={})=>{let n=xe(e,"First argument"),[o,i]=B(t)?[[],t]:[t,r];if(!Array.isArray(o))throw new TypeError(`Second argument must be either an array of arguments or an options object: ${o}`);if(o.some(u=>typeof u=="object"&&u!==null))throw new TypeError(`Second argument must be an array of strings: ${o}`);let s=o.map(String),a=s.find(u=>u.includes("\0"));if(a!==void 0)throw new TypeError(`Arguments cannot contain null bytes ("\\0"): ${a}`);if(!B(i))throw new TypeError(`Last argument must be an options object: ${i}`);return[n,s,i]};var Fi=require("node:child_process");var ci=require("node:string_decoder"),{toString:li}=Object.prototype,fi=e=>li.call(e)==="[object ArrayBuffer]",O=e=>li.call(e)==="[object Uint8Array]",re=e=>new Uint8Array(e.buffer,e.byteOffset,e.byteLength),zf=new TextEncoder,Di=e=>zf.encode(e),Vf=new TextDecoder,At=e=>Vf.decode(e),pi=(e,t)=>Yf(e,t).join(""),Yf=(e,t)=>{if(t==="utf8"&&e.every(i=>typeof i=="string"))return e;let r=new ci.StringDecoder(t),n=e.map(i=>typeof i=="string"?Di(i):i).map(i=>r.write(i)),o=r.end();return o===""?n:[...n,o]},Ke=e=>e.length===1&&O(e[0])?e[0]:en(qf(e)),qf=e=>e.map(t=>typeof t=="string"?Di(t):t),en=e=>{let t=new Uint8Array(Hf(e)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t},Hf=e=>{let t=0;for(let r of e)t+=r.length;return t};var gi=e=>Array.isArray(e)&&Array.isArray(e.raw),Ei=(e,t)=>{let r=[];for(let[i,s]of e.entries())r=Kf({templates:e,expressions:t,tokens:r,index:i,template:s});if(r.length===0)throw new TypeError("Template script must not be empty");let[n,...o]=r;return[n,o,{}]},Kf=({templates:e,expressions:t,tokens:r,index:n,template:o})=>{if(o===void 0)throw new TypeError(`Invalid backslash sequence: ${e.raw[n]}`);let{nextTokens:i,leadingWhitespaces:s,trailingWhitespaces:a}=Xf(o,e.raw[n]),u=mi(r,i,s);if(n===t.length)return u;let l=t[n],c=Array.isArray(l)?l.map(f=>hi(f)):[hi(l)];return mi(u,c,a)},Xf=(e,t)=>{if(t.length===0)return{nextTokens:[],leadingWhitespaces:!1,trailingWhitespaces:!1};let r=[],n=0,o=di.has(t[0]);for(let s=0,a=0;s<e.length;s+=1,a+=1){let u=t[a];if(di.has(u))n!==s&&r.push(e.slice(n,s)),n=s+1;else if(u==="\\"){let l=t[a+1];l===`
`?(s-=1,a+=1):l==="u"&&t[a+2]==="{"?a=t.indexOf("}",a+3):a+=Jf[l]??1}}let i=n===e.length;return i||r.push(e.slice(n)),{nextTokens:r,leadingWhitespaces:o,trailingWhitespaces:i}},di=new Set([" ","	","\r",`
`]),Jf={x:3,u:5},mi=(e,t,r)=>r||e.length===0||t.length===0?[...e,...t]:[...e.slice(0,-1),`${e.at(-1)}${t[0]}`,...t.slice(1)],hi=e=>{let t=typeof e;if(t==="string")return e;if(t==="number")return String(e);if(B(e)&&("stdout"in e||"isMaxBuffer"in e))return Zf(e);throw e instanceof Fi.ChildProcess||Object.prototype.toString.call(e)==="[object Promise]"?new TypeError("Unexpected subprocess in template expression. Please use ${await subprocess} instead of ${subprocess}."):new TypeError(`Unexpected "${t}" in template expression`)},Zf=({stdout:e})=>{if(typeof e=="string")return e;if(O(e))return At(e);throw e===void 0?new TypeError(`Missing result.stdout in template expression. This is probably due to the previous subprocess' "stdout" option.`):new TypeError(`Unexpected "${typeof e}" stdout in template expression`)};var _c=require("node:child_process");var bi=require("node:util");var Bt=E(require("node:process"),1),G=e=>Tt.includes(e),Tt=[Bt.default.stdin,Bt.default.stdout,Bt.default.stderr],$=["stdin","stdout","stderr"],_t=e=>$[e]??`stdio[${e}]`;var Si=e=>{let t={...e};for(let r of nn)t[r]=tn(e,r);return t},tn=(e,t)=>{let r=Array.from({length:Qf(e)+1}),n=eD(e[t],r,t);return iD(n,t)},Qf=({stdio:e})=>Array.isArray(e)?Math.max(e.length,$.length):$.length,eD=(e,t,r)=>B(e)?tD(e,t,r):t.fill(e),tD=(e,t,r)=>{for(let n of Object.keys(e).sort(rD))for(let o of nD(n,r,t))t[o]=e[n];return t},rD=(e,t)=>yi(e)<yi(t)?1:-1,yi=e=>e==="stdout"||e==="stderr"?0:e==="all"?2:1,nD=(e,t,r)=>{if(e==="ipc")return[r.length-1];let n=rn(e);if(n===void 0||n===0)throw new TypeError(`"${t}.${e}" is invalid.
It must be "${t}.stdout", "${t}.stderr", "${t}.all", "${t}.ipc", or "${t}.fd3", "${t}.fd4" (and so on).`);if(n>=r.length)throw new TypeError(`"${t}.${e}" is invalid: that file descriptor does not exist.
Please set the "stdio" option to ensure that file descriptor exists.`);return n==="all"?[1,2]:[n]},rn=e=>{if(e==="all")return e;if($.includes(e))return $.indexOf(e);let t=oD.exec(e);if(t!==null)return Number(t[1])},oD=/^fd(\d+)$/,iD=(e,t)=>e.map(r=>r===void 0?aD[t]:r),sD=(0,bi.debuglog)("execa").enabled?"full":"none",aD={lines:!1,buffer:!0,maxBuffer:1e3*1e3*100,verbose:sD,stripFinalNewline:!0},nn=["lines","buffer","maxBuffer","verbose","stripFinalNewline"],ne=(e,t)=>t==="ipc"?e.at(-1):e[t];var Ae=({verbose:e},t)=>on(e,t)!=="none",Be=({verbose:e},t)=>!["none","short"].includes(on(e,t)),wi=({verbose:e},t)=>{let r=on(e,t);return Ot(r)?r:void 0},on=(e,t)=>t===void 0?uD(e):ne(e,t),uD=e=>e.find(t=>Ot(t))??Rt.findLast(t=>e.includes(t)),Ot=e=>typeof e=="function",Rt=["none","short","full"];var Li=require("node:util");var Ci=require("node:process"),xi=require("node:util"),Ai=(e,t)=>{let r=[e,...t],n=r.join(" "),o=r.map(i=>dD(Bi(i))).join(" ");return{command:n,escapedCommand:o}},Xe=e=>(0,xi.stripVTControlCharacters)(e).split(`
`).map(t=>Bi(t)).join(`
`),Bi=e=>e.replaceAll(fD,t=>cD(t)),cD=e=>{let t=DD[e];if(t!==void 0)return t;let r=e.codePointAt(0),n=r.toString(16);return r<=pD?`\\u${n.padStart(4,"0")}`:`\\U${n}`},lD=()=>{try{return new RegExp("\\p{Separator}|\\p{Other}","gu")}catch{return/[\s\u0000-\u001F\u007F-\u009F\u00AD]/g}},fD=lD(),DD={" ":" ","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t"},pD=65535,dD=e=>mD.test(e)?e:Ci.platform==="win32"?`"${e.replaceAll('"','""')}"`:`'${e.replaceAll("'","'\\''")}'`,mD=/^[\w./-]+$/;var sn=E(require("node:process"),1);function Je(){let{env:e}=sn.default,{TERM:t,TERM_PROGRAM:r}=e;return sn.default.platform!=="win32"?t!=="linux":!!e.WT_SESSION||!!e.TERMINUS_SUBLIME||e.ConEmuTask==="{cmd::Cmder}"||r==="Terminus-Sublime"||r==="vscode"||t==="xterm-256color"||t==="alacritty"||t==="rxvt-unicode"||t==="rxvt-unicode-256color"||e.TERMINAL_EMULATOR==="JetBrains-JediTerm"}var Ti={circleQuestionMark:"(?)",questionMarkPrefix:"(?)",square:"\u2588",squareDarkShade:"\u2593",squareMediumShade:"\u2592",squareLightShade:"\u2591",squareTop:"\u2580",squareBottom:"\u2584",squareLeft:"\u258C",squareRight:"\u2590",squareCenter:"\u25A0",bullet:"\u25CF",dot:"\u2024",ellipsis:"\u2026",pointerSmall:"\u203A",triangleUp:"\u25B2",triangleUpSmall:"\u25B4",triangleDown:"\u25BC",triangleDownSmall:"\u25BE",triangleLeftSmall:"\u25C2",triangleRightSmall:"\u25B8",home:"\u2302",heart:"\u2665",musicNote:"\u266A",musicNoteBeamed:"\u266B",arrowUp:"\u2191",arrowDown:"\u2193",arrowLeft:"\u2190",arrowRight:"\u2192",arrowLeftRight:"\u2194",arrowUpDown:"\u2195",almostEqual:"\u2248",notEqual:"\u2260",lessOrEqual:"\u2264",greaterOrEqual:"\u2265",identical:"\u2261",infinity:"\u221E",subscriptZero:"\u2080",subscriptOne:"\u2081",subscriptTwo:"\u2082",subscriptThree:"\u2083",subscriptFour:"\u2084",subscriptFive:"\u2085",subscriptSix:"\u2086",subscriptSeven:"\u2087",subscriptEight:"\u2088",subscriptNine:"\u2089",oneHalf:"\xBD",oneThird:"\u2153",oneQuarter:"\xBC",oneFifth:"\u2155",oneSixth:"\u2159",oneEighth:"\u215B",twoThirds:"\u2154",twoFifths:"\u2156",threeQuarters:"\xBE",threeFifths:"\u2157",threeEighths:"\u215C",fourFifths:"\u2158",fiveSixths:"\u215A",fiveEighths:"\u215D",sevenEighths:"\u215E",line:"\u2500",lineBold:"\u2501",lineDouble:"\u2550",lineDashed0:"\u2504",lineDashed1:"\u2505",lineDashed2:"\u2508",lineDashed3:"\u2509",lineDashed4:"\u254C",lineDashed5:"\u254D",lineDashed6:"\u2574",lineDashed7:"\u2576",lineDashed8:"\u2578",lineDashed9:"\u257A",lineDashed10:"\u257C",lineDashed11:"\u257E",lineDashed12:"\u2212",lineDashed13:"\u2013",lineDashed14:"\u2010",lineDashed15:"\u2043",lineVertical:"\u2502",lineVerticalBold:"\u2503",lineVerticalDouble:"\u2551",lineVerticalDashed0:"\u2506",lineVerticalDashed1:"\u2507",lineVerticalDashed2:"\u250A",lineVerticalDashed3:"\u250B",lineVerticalDashed4:"\u254E",lineVerticalDashed5:"\u254F",lineVerticalDashed6:"\u2575",lineVerticalDashed7:"\u2577",lineVerticalDashed8:"\u2579",lineVerticalDashed9:"\u257B",lineVerticalDashed10:"\u257D",lineVerticalDashed11:"\u257F",lineDownLeft:"\u2510",lineDownLeftArc:"\u256E",lineDownBoldLeftBold:"\u2513",lineDownBoldLeft:"\u2512",lineDownLeftBold:"\u2511",lineDownDoubleLeftDouble:"\u2557",lineDownDoubleLeft:"\u2556",lineDownLeftDouble:"\u2555",lineDownRight:"\u250C",lineDownRightArc:"\u256D",lineDownBoldRightBold:"\u250F",lineDownBoldRight:"\u250E",lineDownRightBold:"\u250D",lineDownDoubleRightDouble:"\u2554",lineDownDoubleRight:"\u2553",lineDownRightDouble:"\u2552",lineUpLeft:"\u2518",lineUpLeftArc:"\u256F",lineUpBoldLeftBold:"\u251B",lineUpBoldLeft:"\u251A",lineUpLeftBold:"\u2519",lineUpDoubleLeftDouble:"\u255D",lineUpDoubleLeft:"\u255C",lineUpLeftDouble:"\u255B",lineUpRight:"\u2514",lineUpRightArc:"\u2570",lineUpBoldRightBold:"\u2517",lineUpBoldRight:"\u2516",lineUpRightBold:"\u2515",lineUpDoubleRightDouble:"\u255A",lineUpDoubleRight:"\u2559",lineUpRightDouble:"\u2558",lineUpDownLeft:"\u2524",lineUpBoldDownBoldLeftBold:"\u252B",lineUpBoldDownBoldLeft:"\u2528",lineUpDownLeftBold:"\u2525",lineUpBoldDownLeftBold:"\u2529",lineUpDownBoldLeftBold:"\u252A",lineUpDownBoldLeft:"\u2527",lineUpBoldDownLeft:"\u2526",lineUpDoubleDownDoubleLeftDouble:"\u2563",lineUpDoubleDownDoubleLeft:"\u2562",lineUpDownLeftDouble:"\u2561",lineUpDownRight:"\u251C",lineUpBoldDownBoldRightBold:"\u2523",lineUpBoldDownBoldRight:"\u2520",lineUpDownRightBold:"\u251D",lineUpBoldDownRightBold:"\u2521",lineUpDownBoldRightBold:"\u2522",lineUpDownBoldRight:"\u251F",lineUpBoldDownRight:"\u251E",lineUpDoubleDownDoubleRightDouble:"\u2560",lineUpDoubleDownDoubleRight:"\u255F",lineUpDownRightDouble:"\u255E",lineDownLeftRight:"\u252C",lineDownBoldLeftBoldRightBold:"\u2533",lineDownLeftBoldRightBold:"\u252F",lineDownBoldLeftRight:"\u2530",lineDownBoldLeftBoldRight:"\u2531",lineDownBoldLeftRightBold:"\u2532",lineDownLeftRightBold:"\u252E",lineDownLeftBoldRight:"\u252D",lineDownDoubleLeftDoubleRightDouble:"\u2566",lineDownDoubleLeftRight:"\u2565",lineDownLeftDoubleRightDouble:"\u2564",lineUpLeftRight:"\u2534",lineUpBoldLeftBoldRightBold:"\u253B",lineUpLeftBoldRightBold:"\u2537",lineUpBoldLeftRight:"\u2538",lineUpBoldLeftBoldRight:"\u2539",lineUpBoldLeftRightBold:"\u253A",lineUpLeftRightBold:"\u2536",lineUpLeftBoldRight:"\u2535",lineUpDoubleLeftDoubleRightDouble:"\u2569",lineUpDoubleLeftRight:"\u2568",lineUpLeftDoubleRightDouble:"\u2567",lineUpDownLeftRight:"\u253C",lineUpBoldDownBoldLeftBoldRightBold:"\u254B",lineUpDownBoldLeftBoldRightBold:"\u2548",lineUpBoldDownLeftBoldRightBold:"\u2547",lineUpBoldDownBoldLeftRightBold:"\u254A",lineUpBoldDownBoldLeftBoldRight:"\u2549",lineUpBoldDownLeftRight:"\u2540",lineUpDownBoldLeftRight:"\u2541",lineUpDownLeftBoldRight:"\u253D",lineUpDownLeftRightBold:"\u253E",lineUpBoldDownBoldLeftRight:"\u2542",lineUpDownLeftBoldRightBold:"\u253F",lineUpBoldDownLeftBoldRight:"\u2543",lineUpBoldDownLeftRightBold:"\u2544",lineUpDownBoldLeftBoldRight:"\u2545",lineUpDownBoldLeftRightBold:"\u2546",lineUpDoubleDownDoubleLeftDoubleRightDouble:"\u256C",lineUpDoubleDownDoubleLeftRight:"\u256B",lineUpDownLeftDoubleRightDouble:"\u256A",lineCross:"\u2573",lineBackslash:"\u2572",lineSlash:"\u2571"},_i={tick:"\u2714",info:"\u2139",warning:"\u26A0",cross:"\u2718",squareSmall:"\u25FB",squareSmallFilled:"\u25FC",circle:"\u25EF",circleFilled:"\u25C9",circleDotted:"\u25CC",circleDouble:"\u25CE",circleCircle:"\u24DE",circleCross:"\u24E7",circlePipe:"\u24BE",radioOn:"\u25C9",radioOff:"\u25EF",checkboxOn:"\u2612",checkboxOff:"\u2610",checkboxCircleOn:"\u24E7",checkboxCircleOff:"\u24BE",pointer:"\u276F",triangleUpOutline:"\u25B3",triangleLeft:"\u25C0",triangleRight:"\u25B6",lozenge:"\u25C6",lozengeOutline:"\u25C7",hamburger:"\u2630",smiley:"\u32E1",mustache:"\u0DF4",star:"\u2605",play:"\u25B6",nodejs:"\u2B22",oneSeventh:"\u2150",oneNinth:"\u2151",oneTenth:"\u2152"},hD={tick:"\u221A",info:"i",warning:"\u203C",cross:"\xD7",squareSmall:"\u25A1",squareSmallFilled:"\u25A0",circle:"( )",circleFilled:"(*)",circleDotted:"( )",circleDouble:"( )",circleCircle:"(\u25CB)",circleCross:"(\xD7)",circlePipe:"(\u2502)",radioOn:"(*)",radioOff:"( )",checkboxOn:"[\xD7]",checkboxOff:"[ ]",checkboxCircleOn:"(\xD7)",checkboxCircleOff:"( )",pointer:">",triangleUpOutline:"\u2206",triangleLeft:"\u25C4",triangleRight:"\u25BA",lozenge:"\u2666",lozengeOutline:"\u25CA",hamburger:"\u2261",smiley:"\u263A",mustache:"\u250C\u2500\u2510",star:"\u2736",play:"\u25BA",nodejs:"\u2666",oneSeventh:"1/7",oneNinth:"1/9",oneTenth:"1/10"},FD={...Ti,..._i},gD={...Ti,...hD},ED=Je(),yD=ED?FD:gD,It=yD,Eg=Object.entries(_i);var Oi=E(require("node:tty"),1),bD=Oi.default?.WriteStream?.prototype?.hasColors?.()??!1,h=(e,t)=>{if(!bD)return o=>o;let r=`\x1B[${e}m`,n=`\x1B[${t}m`;return o=>{let i=o+"",s=i.indexOf(n);if(s===-1)return r+i+n;let a=r,u=0;for(;s!==-1;)a+=i.slice(u,s)+r,u=s+n.length,s=i.indexOf(n,u);return a+=i.slice(u)+n,a}},bg=h(0,0),Ri=h(1,22),Sg=h(2,22),wg=h(3,23),Cg=h(4,24),xg=h(53,55),Ag=h(7,27),Bg=h(8,28),Tg=h(9,29),_g=h(30,39),Og=h(31,39),Rg=h(32,39),Ig=h(33,39),vg=h(34,39),Mg=h(35,39),Pg=h(36,39),$g=h(37,39),vt=h(90,39),Ng=h(40,49),Lg=h(41,49),jg=h(42,49),Ug=h(43,49),kg=h(44,49),Gg=h(45,49),Wg=h(46,49),zg=h(47,49),Vg=h(100,49),Ii=h(91,39),Yg=h(92,39),vi=h(93,39),qg=h(94,39),Hg=h(95,39),Kg=h(96,39),Xg=h(97,39),Jg=h(101,49),Zg=h(102,49),Qg=h(103,49),eE=h(104,49),tE=h(105,49),rE=h(106,49),nE=h(107,49);var $i=({type:e,message:t,timestamp:r,piped:n,commandId:o,result:{failed:i=!1}={},options:{reject:s=!0}})=>{let a=SD(r),u=wD[e]({failed:i,reject:s,piped:n}),l=CD[e]({reject:s});return`${vt(`[${a}]`)} ${vt(`[${o}]`)} ${l(u)} ${l(t)}`},SD=e=>`${Mt(e.getHours(),2)}:${Mt(e.getMinutes(),2)}:${Mt(e.getSeconds(),2)}.${Mt(e.getMilliseconds(),3)}`,Mt=(e,t)=>String(e).padStart(t,"0"),Mi=({failed:e,reject:t})=>e?t?It.cross:It.warning:It.tick,wD={command:({piped:e})=>e?"|":"$",output:()=>" ",ipc:()=>"*",error:Mi,duration:Mi},Pi=e=>e,CD={command:()=>Ri,output:()=>Pi,ipc:()=>Pi,error:({reject:e})=>e?Ii:vi,duration:()=>vt};var Ni=(e,t,r)=>{let n=wi(t,r);return e.map(({verboseLine:o,verboseObject:i})=>xD(o,i,n)).filter(o=>o!==void 0).map(o=>AD(o)).join("")},xD=(e,t,r)=>{if(r===void 0)return e;let n=r(e,t);if(typeof n=="string")return n},AD=e=>e.endsWith(`
`)?e:`${e}
`;var X=({type:e,verboseMessage:t,fdNumber:r,verboseInfo:n,result:o})=>{let i=BD({type:e,result:o,verboseInfo:n}),s=TD(t,i),a=Ni(s,n,r);a!==""&&console.warn(a.slice(0,-1))},BD=({type:e,result:t,verboseInfo:{escapedCommand:r,commandId:n,rawOptions:{piped:o=!1,...i}}})=>({type:e,escapedCommand:r,commandId:`${n}`,timestamp:new Date,piped:o,result:t,options:i}),TD=(e,t)=>e.split(`
`).map(r=>_D({...t,message:r})),_D=e=>({verboseLine:$i(e),verboseObject:e}),Pt=e=>{let t=typeof e=="string"?e:(0,Li.inspect)(e);return Xe(t).replaceAll("	"," ".repeat(OD))},OD=2;var ji=(e,t)=>{Ae(t)&&X({type:"command",verboseMessage:e,verboseInfo:t})};var Ui=(e,t,r)=>{vD(e);let n=RD(e);return{verbose:e,escapedCommand:t,commandId:n,rawOptions:r}},RD=e=>Ae({verbose:e})?ID++:void 0,ID=0n,vD=e=>{for(let t of e){if(t===!1)throw new TypeError(`The "verbose: false" option was renamed to "verbose: 'none'".`);if(t===!0)throw new TypeError(`The "verbose: true" option was renamed to "verbose: 'short'".`);if(!Rt.includes(t)&&!Ot(t)){let r=Rt.map(n=>`'${n}'`).join(", ");throw new TypeError(`The "verbose" option must not be ${t}. Allowed values are: ${r} or a function.`)}}};var an=require("node:process"),$t=()=>an.hrtime.bigint(),un=e=>Number(an.hrtime.bigint()-e)/1e6;var Nt=(e,t,r)=>{let n=$t(),{command:o,escapedCommand:i}=Ai(e,t),s=tn(r,"verbose"),a=Ui(s,i,{...r});return ji(i,a),{command:o,escapedCommand:i,startTime:n,verboseInfo:a}};var eu=E(require("node:path"),1),In=E(require("node:process"),1),tu=E(Ts(),1);var Ze=E(require("node:process"),1),ue=E(require("node:path"),1);function jt(e={}){let{env:t=process.env,platform:r=process.platform}=e;return r!=="win32"?"PATH":Object.keys(t).reverse().find(n=>n.toUpperCase()==="PATH")||"Path"}var _s=require("node:util"),En=require("node:child_process"),gn=E(require("node:path"),1),Os=require("node:url"),kE=(0,_s.promisify)(En.execFile);function Ut(e){return e instanceof URL?(0,Os.fileURLToPath)(e):e}function Rs(e){return{*[Symbol.iterator](){let t=gn.default.resolve(Ut(e)),r;for(;r!==t;)yield t,r=t,t=gn.default.resolve(t,"..")}}}var GE=10*1024*1024;var sp=({cwd:e=Ze.default.cwd(),path:t=Ze.default.env[jt()],preferLocal:r=!0,execPath:n=Ze.default.execPath,addExecPath:o=!0}={})=>{let i=ue.default.resolve(Ut(e)),s=[],a=t.split(ue.default.delimiter);return r&&ap(s,a,i),o&&up(s,a,n,i),t===""||t===ue.default.delimiter?`${s.join(ue.default.delimiter)}${t}`:[...s,t].join(ue.default.delimiter)},ap=(e,t,r)=>{for(let n of Rs(r)){let o=ue.default.join(n,"node_modules/.bin");t.includes(o)||e.push(o)}},up=(e,t,r,n)=>{let o=ue.default.resolve(n,Ut(r),"..");t.includes(o)||e.push(o)},Is=({env:e=Ze.default.env,...t}={})=>{e={...e};let r=jt({env:e});return t.path=e[r],e[r]=sp(t),e};var Hs=require("node:timers/promises");var vs=(e,t,r)=>{let n=r?et:Qe,o=e instanceof W?{}:{cause:e};return new n(t,o)},W=class extends Error{},Ms=(e,t)=>{Object.defineProperty(e.prototype,"name",{value:t,writable:!0,enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,$s,{value:!0,writable:!1,enumerable:!1,configurable:!1})},Ps=e=>kt(e)&&$s in e,$s=Symbol("isExecaError"),kt=e=>Object.prototype.toString.call(e)==="[object Error]",Qe=class extends Error{};Ms(Qe,Qe.name);var et=class extends Error{};Ms(et,et.name);var Oe=require("node:os");var Gs=require("node:os");var Ns=()=>{let e=js-Ls+1;return Array.from({length:e},cp)},cp=(e,t)=>({name:`SIGRT${t+1}`,number:Ls+t,action:"terminate",description:"Application-specific signal (realtime)",standard:"posix"}),Ls=34,js=64;var ks=require("node:os");var Us=[{name:"SIGHUP",number:1,action:"terminate",description:"Terminal closed",standard:"posix"},{name:"SIGINT",number:2,action:"terminate",description:"User interruption with CTRL-C",standard:"ansi"},{name:"SIGQUIT",number:3,action:"core",description:"User interruption with CTRL-\\",standard:"posix"},{name:"SIGILL",number:4,action:"core",description:"Invalid machine instruction",standard:"ansi"},{name:"SIGTRAP",number:5,action:"core",description:"Debugger breakpoint",standard:"posix"},{name:"SIGABRT",number:6,action:"core",description:"Aborted",standard:"ansi"},{name:"SIGIOT",number:6,action:"core",description:"Aborted",standard:"bsd"},{name:"SIGBUS",number:7,action:"core",description:"Bus error due to misaligned, non-existing address or paging error",standard:"bsd"},{name:"SIGEMT",number:7,action:"terminate",description:"Command should be emulated but is not implemented",standard:"other"},{name:"SIGFPE",number:8,action:"core",description:"Floating point arithmetic error",standard:"ansi"},{name:"SIGKILL",number:9,action:"terminate",description:"Forced termination",standard:"posix",forced:!0},{name:"SIGUSR1",number:10,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGSEGV",number:11,action:"core",description:"Segmentation fault",standard:"ansi"},{name:"SIGUSR2",number:12,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGPIPE",number:13,action:"terminate",description:"Broken pipe or socket",standard:"posix"},{name:"SIGALRM",number:14,action:"terminate",description:"Timeout or timer",standard:"posix"},{name:"SIGTERM",number:15,action:"terminate",description:"Termination",standard:"ansi"},{name:"SIGSTKFLT",number:16,action:"terminate",description:"Stack is empty or overflowed",standard:"other"},{name:"SIGCHLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"posix"},{name:"SIGCLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"other"},{name:"SIGCONT",number:18,action:"unpause",description:"Unpaused",standard:"posix",forced:!0},{name:"SIGSTOP",number:19,action:"pause",description:"Paused",standard:"posix",forced:!0},{name:"SIGTSTP",number:20,action:"pause",description:'Paused using CTRL-Z or "suspend"',standard:"posix"},{name:"SIGTTIN",number:21,action:"pause",description:"Background process cannot read terminal input",standard:"posix"},{name:"SIGBREAK",number:21,action:"terminate",description:"User interruption with CTRL-BREAK",standard:"other"},{name:"SIGTTOU",number:22,action:"pause",description:"Background process cannot write to terminal output",standard:"posix"},{name:"SIGURG",number:23,action:"ignore",description:"Socket received out-of-band data",standard:"bsd"},{name:"SIGXCPU",number:24,action:"core",description:"Process timed out",standard:"bsd"},{name:"SIGXFSZ",number:25,action:"core",description:"File too big",standard:"bsd"},{name:"SIGVTALRM",number:26,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGPROF",number:27,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGWINCH",number:28,action:"ignore",description:"Terminal window size changed",standard:"bsd"},{name:"SIGIO",number:29,action:"terminate",description:"I/O is available",standard:"other"},{name:"SIGPOLL",number:29,action:"terminate",description:"Watched event",standard:"other"},{name:"SIGINFO",number:29,action:"ignore",description:"Request for process information",standard:"other"},{name:"SIGPWR",number:30,action:"terminate",description:"Device running out of power",standard:"systemv"},{name:"SIGSYS",number:31,action:"core",description:"Invalid system call",standard:"other"},{name:"SIGUNUSED",number:31,action:"terminate",description:"Invalid system call",standard:"other"}];var yn=()=>{let e=Ns();return[...Us,...e].map(lp)},lp=({name:e,number:t,description:r,action:n,forced:o=!1,standard:i})=>{let{signals:{[e]:s}}=ks.constants,a=s!==void 0;return{name:e,number:a?s:t,description:r,supported:a,action:n,forced:o,standard:i}};var fp=()=>{let e=yn();return Object.fromEntries(e.map(Dp))},Dp=({name:e,number:t,description:r,supported:n,action:o,forced:i,standard:s})=>[e,{name:e,number:t,description:r,supported:n,action:o,forced:i,standard:s}],Ws=fp(),pp=()=>{let e=yn(),t=65,r=Array.from({length:t},(n,o)=>dp(o,e));return Object.assign({},...r)},dp=(e,t)=>{let r=mp(e,t);if(r===void 0)return{};let{name:n,description:o,supported:i,action:s,forced:a,standard:u}=r;return{[e]:{name:n,number:e,description:o,supported:i,action:s,forced:a,standard:u}}},mp=(e,t)=>{let r=t.find(({name:n})=>Gs.constants.signals[n]===e);return r!==void 0?r:t.find(n=>n.number===e)},ty=pp();var Vs=e=>{let t="option `killSignal`";if(e===0)throw new TypeError(`Invalid ${t}: 0 cannot be used.`);return qs(e,t)},Ys=e=>e===0?e:qs(e,"`subprocess.kill()`'s argument"),qs=(e,t)=>{if(Number.isInteger(e))return hp(e,t);if(typeof e=="string")return gp(e,t);throw new TypeError(`Invalid ${t} ${String(e)}: it must be a string or an integer.
${bn()}`)},hp=(e,t)=>{if(zs.has(e))return zs.get(e);throw new TypeError(`Invalid ${t} ${e}: this signal integer does not exist.
${bn()}`)},Fp=()=>new Map(Object.entries(Oe.constants.signals).reverse().map(([e,t])=>[t,e])),zs=Fp(),gp=(e,t)=>{if(e in Oe.constants.signals)return e;throw e.toUpperCase()in Oe.constants.signals?new TypeError(`Invalid ${t} '${e}': please rename it to '${e.toUpperCase()}'.`):new TypeError(`Invalid ${t} '${e}': this signal name does not exist.
${bn()}`)},bn=()=>`Available signal names: ${Ep()}.
Available signal numbers: ${yp()}.`,Ep=()=>Object.keys(Oe.constants.signals).sort().map(e=>`'${e}'`).join(", "),yp=()=>[...new Set(Object.values(Oe.constants.signals).sort((e,t)=>e-t))].join(", "),Gt=e=>Ws[e].description;var Ks=e=>{if(e===!1)return e;if(e===!0)return bp;if(!Number.isFinite(e)||e<0)throw new TypeError(`Expected the \`forceKillAfterDelay\` option to be a non-negative integer, got \`${e}\` (${typeof e})`);return e},bp=1e3*5,Xs=({kill:e,options:{forceKillAfterDelay:t,killSignal:r},onInternalError:n,context:o,controller:i},s,a)=>{let{signal:u,error:l}=Sp(s,a,r);wp(l,n);let c=e(u);return Cp({kill:e,signal:u,forceKillAfterDelay:t,killSignal:r,killResult:c,context:o,controller:i}),c},Sp=(e,t,r)=>{let[n=r,o]=kt(e)?[void 0,e]:[e,t];if(typeof n!="string"&&!Number.isInteger(n))throw new TypeError(`The first argument must be an error instance or a signal name string/integer: ${String(n)}`);if(o!==void 0&&!kt(o))throw new TypeError(`The second argument is optional. If specified, it must be an error instance: ${o}`);return{signal:Ys(n),error:o}},wp=(e,t)=>{e!==void 0&&t.reject(e)},Cp=async({kill:e,signal:t,forceKillAfterDelay:r,killSignal:n,killResult:o,context:i,controller:s})=>{t===n&&o&&Sn({kill:e,forceKillAfterDelay:r,context:i,controllerSignal:s.signal})},Sn=async({kill:e,forceKillAfterDelay:t,context:r,controllerSignal:n})=>{if(t!==!1)try{await(0,Hs.setTimeout)(t,void 0,{signal:n}),e("SIGKILL")&&(r.isForcefullyTerminated??=!0)}catch{}};var Js=require("node:events"),Wt=async(e,t)=>{e.aborted||await(0,Js.once)(e,"abort",{signal:t})};var Zs=({cancelSignal:e})=>{if(e!==void 0&&Object.prototype.toString.call(e)!=="[object AbortSignal]")throw new Error(`The \`cancelSignal\` option must be an AbortSignal: ${String(e)}`)},Qs=({subprocess:e,cancelSignal:t,gracefulCancel:r,context:n,controller:o})=>t===void 0||r?[]:[xp(e,t,n,o)],xp=async(e,t,r,{signal:n})=>{throw await Wt(t,n),r.terminationReason??="cancel",e.kill(),t.reason};var Ia=require("node:timers/promises");var Oa=require("node:util");var Re=({methodName:e,isSubprocess:t,ipc:r,isConnected:n})=>{Ap(e,t,r),wn(e,t,n)},Ap=(e,t,r)=>{if(!r)throw new Error(`${z(e,t)} can only be used if the \`ipc\` option is \`true\`.`)},wn=(e,t,r)=>{if(!r)throw new Error(`${z(e,t)} cannot be used: the ${ce(t)} has already exited or disconnected.`)},ea=e=>{throw new Error(`${z("getOneMessage",e)} could not complete: the ${ce(e)} exited or disconnected.`)},ta=e=>{throw new Error(`${z("sendMessage",e)} failed: the ${ce(e)} is sending a message too, instead of listening to incoming messages.
This can be fixed by both sending a message and listening to incoming messages at the same time:

const [receivedMessage] = await Promise.all([
	${z("getOneMessage",e)},
	${z("sendMessage",e,"message, {strict: true}")},
]);`)},zt=(e,t)=>new Error(`${z("sendMessage",t)} failed when sending an acknowledgment response to the ${ce(t)}.`,{cause:e}),ra=e=>{throw new Error(`${z("sendMessage",e)} failed: the ${ce(e)} is not listening to incoming messages.`)},na=e=>{throw new Error(`${z("sendMessage",e)} failed: the ${ce(e)} exited without listening to incoming messages.`)},oa=()=>new Error(`\`cancelSignal\` aborted: the ${ce(!0)} disconnected.`),ia=()=>{throw new Error("`getCancelSignal()` cannot be used without setting the `cancelSignal` subprocess option.")},sa=({error:e,methodName:t,isSubprocess:r})=>{if(e.code==="EPIPE")throw new Error(`${z(t,r)} cannot be used: the ${ce(r)} is disconnecting.`,{cause:e})},aa=({error:e,methodName:t,isSubprocess:r,message:n})=>{if(Bp(e))throw new Error(`${z(t,r)}'s argument type is invalid: the message cannot be serialized: ${String(n)}.`,{cause:e})},Bp=({code:e,message:t})=>Tp.has(e)||_p.some(r=>t.includes(r)),Tp=new Set(["ERR_MISSING_ARGS","ERR_INVALID_ARG_TYPE"]),_p=["could not be cloned","circular structure","call stack size exceeded"],z=(e,t,r="")=>e==="cancelSignal"?"`cancelSignal`'s `controller.abort()`":`${Op(t)}${e}(${r})`,Op=e=>e?"":"subprocess.",ce=e=>e?"parent process":"subprocess",Ie=e=>{e.connected&&e.disconnect()};var J=()=>{let e={},t=new Promise((r,n)=>{Object.assign(e,{resolve:r,reject:n})});return Object.assign(t,e)};var Yt=(e,t="stdin")=>{let{options:n,fileDescriptors:o}=Z.get(e),i=ua(o,t,!0),s=e.stdio[i];if(s===null)throw new TypeError(ca(i,t,n,!0));return s},ve=(e,t="stdout")=>{let{options:n,fileDescriptors:o}=Z.get(e),i=ua(o,t,!1),s=i==="all"?e.all:e.stdio[i];if(s==null)throw new TypeError(ca(i,t,n,!1));return s},Z=new WeakMap,ua=(e,t,r)=>{let n=Rp(t,r);return Ip(n,t,r,e),n},Rp=(e,t)=>{let r=rn(e);if(r!==void 0)return r;let{validOptions:n,defaultValue:o}=t?{validOptions:'"stdin"',defaultValue:"stdin"}:{validOptions:'"stdout", "stderr", "all"',defaultValue:"stdout"};throw new TypeError(`"${tt(t)}" must not be "${e}".
It must be ${n} or "fd3", "fd4" (and so on).
It is optional and defaults to "${o}".`)},Ip=(e,t,r,n)=>{let o=n[la(e)];if(o===void 0)throw new TypeError(`"${tt(r)}" must not be ${t}. That file descriptor does not exist.
Please set the "stdio" option to ensure that file descriptor exists.`);if(o.direction==="input"&&!r)throw new TypeError(`"${tt(r)}" must not be ${t}. It must be a readable stream, not writable.`);if(o.direction!=="input"&&r)throw new TypeError(`"${tt(r)}" must not be ${t}. It must be a writable stream, not readable.`)},ca=(e,t,r,n)=>{if(e==="all"&&!r.all)return`The "all" option must be true to use "from: 'all'".`;let{optionName:o,optionValue:i}=vp(e,r);return`The "${o}: ${Vt(i)}" option is incompatible with using "${tt(n)}: ${Vt(t)}".
Please set this option with "pipe" instead.`},vp=(e,{stdin:t,stdout:r,stderr:n,stdio:o})=>{let i=la(e);return i===0&&t!==void 0?{optionName:"stdin",optionValue:t}:i===1&&r!==void 0?{optionName:"stdout",optionValue:r}:i===2&&n!==void 0?{optionName:"stderr",optionValue:n}:{optionName:`stdio[${i}]`,optionValue:o[i]}},la=e=>e==="all"?1:e,tt=e=>e?"to":"from",Vt=e=>typeof e=="string"?`'${e}'`:typeof e=="number"?`${e}`:"Stream";var wa=require("node:events");var fa=require("node:events"),ge=(e,t,r)=>{let n=e.getMaxListeners();n===0||n===Number.POSITIVE_INFINITY||(e.setMaxListeners(n+t),(0,fa.addAbortListener)(r,()=>{e.setMaxListeners(e.getMaxListeners()-t)}))};var Sa=require("node:events");var da=require("node:events"),ma=require("node:timers/promises");var qt=(e,t)=>{t&&Cn(e)},Cn=e=>{e.refCounted()},Ht=(e,t)=>{t&&xn(e)},xn=e=>{e.unrefCounted()},Da=(e,t)=>{t&&(xn(e),xn(e))},pa=(e,t)=>{t&&(Cn(e),Cn(e))};var ha=async({anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n},o)=>{if(Ea(o)||ba(o))return;Kt.has(e)||Kt.set(e,[]);let i=Kt.get(e);if(i.push(o),!(i.length>1))for(;i.length>0;){await ya(e,n,o),await ma.scheduler.yield();let s=await ga({wrappedMessage:i[0],anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n});i.shift(),n.emit("message",s),n.emit("message:done")}},Fa=async({anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n,boundOnMessage:o})=>{An();let i=Kt.get(e);for(;i?.length>0;)await(0,da.once)(n,"message:done");e.removeListener("message",o),pa(t,r),n.connected=!1,n.emit("disconnect")},Kt=new WeakMap;var le=(e,t,r)=>{if(Xt.has(e))return Xt.get(e);let n=new Sa.EventEmitter;return n.connected=!0,Xt.set(e,n),Mp({ipcEmitter:n,anyProcess:e,channel:t,isSubprocess:r}),n},Xt=new WeakMap,Mp=({ipcEmitter:e,anyProcess:t,channel:r,isSubprocess:n})=>{let o=ha.bind(void 0,{anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:e});t.on("message",o),t.once("disconnect",Fa.bind(void 0,{anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:e,boundOnMessage:o})),Da(r,n)},Jt=e=>{let t=Xt.get(e);return t===void 0?e.channel!==null:t.connected};var Ca=({anyProcess:e,channel:t,isSubprocess:r,message:n,strict:o})=>{if(!o)return n;let i=le(e,t,r),s=er(e,i);return{id:Pp++,type:Qt,message:n,hasListeners:s}},Pp=0n,xa=(e,t)=>{if(!(t?.type!==Qt||t.hasListeners))for(let{id:r}of e)r!==void 0&&Zt[r].resolve({isDeadlock:!0,hasListeners:!1})},ga=async({wrappedMessage:e,anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:o})=>{if(e?.type!==Qt||!t.connected)return e;let{id:i,message:s}=e,a={id:i,type:Ba,message:er(t,o)};try{await tr({anyProcess:t,channel:r,isSubprocess:n,ipc:!0},a)}catch(u){o.emit("strict:error",u)}return s},Ea=e=>{if(e?.type!==Ba)return!1;let{id:t,message:r}=e;return Zt[t]?.resolve({isDeadlock:!1,hasListeners:r}),!0},Aa=async(e,t,r)=>{if(e?.type!==Qt)return;let n=J();Zt[e.id]=n;let o=new AbortController;try{let{isDeadlock:i,hasListeners:s}=await Promise.race([n,$p(t,r,o)]);i&&ta(r),s||ra(r)}finally{o.abort(),delete Zt[e.id]}},Zt={},$p=async(e,t,{signal:r})=>{ge(e,1,r),await(0,wa.once)(e,"disconnect",{signal:r}),na(t)},Qt="execa:ipc:request",Ba="execa:ipc:response";var Ta=(e,t,r)=>{rt.has(e)||rt.set(e,new Set);let n=rt.get(e),o=J(),i=r?t.id:void 0,s={onMessageSent:o,id:i};return n.add(s),{outgoingMessages:n,outgoingMessage:s}},_a=({outgoingMessages:e,outgoingMessage:t})=>{e.delete(t),t.onMessageSent.resolve()},ya=async(e,t,r)=>{for(;!er(e,t)&&rt.get(e)?.size>0;){let n=[...rt.get(e)];xa(n,r),await Promise.all(n.map(({onMessageSent:o})=>o))}},rt=new WeakMap,er=(e,t)=>t.listenerCount("message")>Np(e),Np=e=>Z.has(e)&&!ne(Z.get(e).options.buffer,"ipc")?1:0;var tr=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},o,{strict:i=!1}={})=>{let s="sendMessage";return Re({methodName:s,isSubprocess:r,ipc:n,isConnected:e.connected}),Lp({anyProcess:e,channel:t,methodName:s,isSubprocess:r,message:o,strict:i})},Lp=async({anyProcess:e,channel:t,methodName:r,isSubprocess:n,message:o,strict:i})=>{let s=Ca({anyProcess:e,channel:t,isSubprocess:n,message:o,strict:i}),a=Ta(e,s,i);try{await Tn({anyProcess:e,methodName:r,isSubprocess:n,wrappedMessage:s,message:o})}catch(u){throw Ie(e),u}finally{_a(a)}},Tn=async({anyProcess:e,methodName:t,isSubprocess:r,wrappedMessage:n,message:o})=>{let i=jp(e);try{await Promise.all([Aa(n,e,r),i(n)])}catch(s){throw sa({error:s,methodName:t,isSubprocess:r}),aa({error:s,methodName:t,isSubprocess:r,message:o}),s}},jp=e=>{if(Bn.has(e))return Bn.get(e);let t=(0,Oa.promisify)(e.send.bind(e));return Bn.set(e,t),t},Bn=new WeakMap;var va=(e,t)=>{let r="cancelSignal";return wn(r,!1,e.connected),Tn({anyProcess:e,methodName:r,isSubprocess:!1,wrappedMessage:{type:Pa,message:t},message:t})},Ma=async({anyProcess:e,channel:t,isSubprocess:r,ipc:n})=>(await Up({anyProcess:e,channel:t,isSubprocess:r,ipc:n}),_n.signal),Up=async({anyProcess:e,channel:t,isSubprocess:r,ipc:n})=>{if(!Ra){if(Ra=!0,!n){ia();return}if(t===null){An();return}le(e,t,r),await Ia.scheduler.yield()}},Ra=!1,ba=e=>e?.type!==Pa?!1:(_n.abort(e.message),!0),Pa="execa:ipc:cancel",An=()=>{_n.abort(oa())},_n=new AbortController;var $a=({gracefulCancel:e,cancelSignal:t,ipc:r,serialization:n})=>{if(e){if(t===void 0)throw new Error("The `cancelSignal` option must be defined when setting the `gracefulCancel` option.");if(!r)throw new Error("The `ipc` option cannot be false when setting the `gracefulCancel` option.");if(n==="json")throw new Error("The `serialization` option cannot be 'json' when setting the `gracefulCancel` option.")}},Na=({subprocess:e,cancelSignal:t,gracefulCancel:r,forceKillAfterDelay:n,context:o,controller:i})=>r?[kp({subprocess:e,cancelSignal:t,forceKillAfterDelay:n,context:o,controller:i})]:[],kp=async({subprocess:e,cancelSignal:t,forceKillAfterDelay:r,context:n,controller:{signal:o}})=>{await Wt(t,o);let i=Gp(t);throw await va(e,i),Sn({kill:e.kill,forceKillAfterDelay:r,context:n,controllerSignal:o}),n.terminationReason??="gracefulCancel",t.reason},Gp=({reason:e})=>{if(!(e instanceof DOMException))return e;let t=new Error(e.message);return Object.defineProperty(t,"stack",{value:e.stack,enumerable:!1,configurable:!0,writable:!0}),t};var La=require("node:timers/promises");var ja=({timeout:e})=>{if(e!==void 0&&(!Number.isFinite(e)||e<0))throw new TypeError(`Expected the \`timeout\` option to be a non-negative integer, got \`${e}\` (${typeof e})`)},Ua=(e,t,r,n)=>t===0||t===void 0?[]:[Wp(e,t,r,n)],Wp=async(e,t,r,{signal:n})=>{throw await(0,La.setTimeout)(t,void 0,{signal:n}),r.terminationReason??="timeout",e.kill(),new W};var rr=require("node:process"),On=E(require("node:path"),1);var ka=({options:e})=>{if(e.node===!1)throw new TypeError('The "node" option cannot be false with `execaNode()`.');return{options:{...e,node:!0}}},Ga=(e,t,{node:r=!1,nodePath:n=rr.execPath,nodeOptions:o=rr.execArgv.filter(u=>!u.startsWith("--inspect")),cwd:i,execPath:s,...a})=>{if(s!==void 0)throw new TypeError('The "execPath" option has been removed. Please use the "nodePath" option instead.');let u=xe(n,'The "nodePath" option'),l=On.default.resolve(i,u),c={...a,nodePath:l,node:r,cwd:i};if(!r)return[e,t,c];if(On.default.basename(e,".exe")==="node")throw new TypeError('When the "node" option is true, the first argument does not need to be "node".');return[l,[...o,e,...t],{ipc:!0,...c,shell:!1}]};var Wa=require("node:v8"),za=({ipcInput:e,ipc:t,serialization:r})=>{if(e!==void 0){if(!t)throw new Error("The `ipcInput` option cannot be set unless the `ipc` option is `true`.");Yp[r](e)}},zp=e=>{try{(0,Wa.serialize)(e)}catch(t){throw new Error("The `ipcInput` option is not serializable with a structured clone.",{cause:t})}},Vp=e=>{try{JSON.stringify(e)}catch(t){throw new Error("The `ipcInput` option is not serializable with JSON.",{cause:t})}},Yp={advanced:zp,json:Vp},Va=async(e,t)=>{t!==void 0&&await e.sendMessage(t)};var qa=({encoding:e})=>{if(Rn.has(e))return;let t=Hp(e);if(t!==void 0)throw new TypeError(`Invalid option \`encoding: ${nr(e)}\`.
Please rename it to ${nr(t)}.`);let r=[...Rn].map(n=>nr(n)).join(", ");throw new TypeError(`Invalid option \`encoding: ${nr(e)}\`.
Please rename it to one of: ${r}.`)},qp=new Set(["utf8","utf16le"]),P=new Set(["buffer","hex","base64","base64url","latin1","ascii"]),Rn=new Set([...qp,...P]),Hp=e=>{if(e===null)return"buffer";if(typeof e!="string")return;let t=e.toLowerCase();if(t in Ya)return Ya[t];if(Rn.has(t))return t},Ya={"utf-8":"utf8","utf-16le":"utf16le","ucs-2":"utf16le",ucs2:"utf16le",binary:"latin1"},nr=e=>typeof e=="string"?`"${e}"`:String(e);var Ha=require("node:fs"),Ka=E(require("node:path"),1),Xa=E(require("node:process"),1);var Ja=(e=Za())=>{let t=xe(e,'The "cwd" option');return Ka.default.resolve(t)},Za=()=>{try{return Xa.default.cwd()}catch(e){throw e.message=`The current directory does not exist.
${e.message}`,e}},Qa=(e,t)=>{if(t===Za())return e;let r;try{r=(0,Ha.statSync)(t)}catch(n){return`The "cwd" option is invalid: ${t}.
${n.message}
${e}`}return r.isDirectory()?e:`The "cwd" option is not a directory: ${t}.
${e}`};var or=(e,t,r)=>{r.cwd=Ja(r.cwd);let[n,o,i]=Ga(e,t,r),{command:s,args:a,options:u}=tu.default._parse(n,o,i),l=Si(u),c=Kp(l);return ja(c),qa(c),za(c),Zs(c),$a(c),c.shell=Qr(c.shell),c.env=Xp(c),c.killSignal=Vs(c.killSignal),c.forceKillAfterDelay=Ks(c.forceKillAfterDelay),c.lines=c.lines.map((f,D)=>f&&!P.has(c.encoding)&&c.buffer[D]),In.default.platform==="win32"&&eu.default.basename(s,".exe")==="cmd"&&a.unshift("/q"),{file:s,commandArguments:a,options:c}},Kp=({extendEnv:e=!0,preferLocal:t=!1,cwd:r,localDir:n=r,encoding:o="utf8",reject:i=!0,cleanup:s=!0,all:a=!1,windowsHide:u=!0,killSignal:l="SIGTERM",forceKillAfterDelay:c=!0,gracefulCancel:f=!1,ipcInput:D,ipc:p=D!==void 0||f,serialization:d="advanced",...g})=>({...g,extendEnv:e,preferLocal:t,cwd:r,localDirectory:n,encoding:o,reject:i,cleanup:s,all:a,windowsHide:u,killSignal:l,forceKillAfterDelay:c,gracefulCancel:f,ipcInput:D,ipc:p,serialization:d}),Xp=({env:e,extendEnv:t,preferLocal:r,node:n,localDirectory:o,nodePath:i})=>{let s=t?{...In.default.env,...e}:e;return r||n?Is({env:s,cwd:o,execPath:i,preferLocal:r,addExecPath:n}):s};var ir=(e,t,r)=>r.shell&&t.length>0?[[e,...t].join(" "),[],r]:[e,t,r];var wu=require("node:util");function Me(e){if(typeof e=="string")return Jp(e);if(!(ArrayBuffer.isView(e)&&e.BYTES_PER_ELEMENT===1))throw new Error("Input must be a string or a Uint8Array");return Zp(e)}var Jp=e=>e.at(-1)===ru?e.slice(0,e.at(-2)===nu?-2:-1):e,Zp=e=>e.at(-1)===Qp?e.subarray(0,e.at(-2)===ed?-2:-1):e,ru=`
`,Qp=ru.codePointAt(0),nu="\r",ed=nu.codePointAt(0);var mu=require("node:events"),hu=require("node:stream/promises");function V(e,{checkOpen:t=!0}={}){return e!==null&&typeof e=="object"&&(e.writable||e.readable||!t||e.writable===void 0&&e.readable===void 0)&&typeof e.pipe=="function"}function vn(e,{checkOpen:t=!0}={}){return V(e,{checkOpen:t})&&(e.writable||!t)&&typeof e.write=="function"&&typeof e.end=="function"&&typeof e.writable=="boolean"&&typeof e.writableObjectMode=="boolean"&&typeof e.destroy=="function"&&typeof e.destroyed=="boolean"}function Ee(e,{checkOpen:t=!0}={}){return V(e,{checkOpen:t})&&(e.readable||!t)&&typeof e.read=="function"&&typeof e.readable=="boolean"&&typeof e.readableObjectMode=="boolean"&&typeof e.destroy=="function"&&typeof e.destroyed=="boolean"}function Mn(e,t){return vn(e,t)&&Ee(e,t)}var td=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype),Pn=class{#r;#o;#e=!1;#n=void 0;constructor(t,r){this.#r=t,this.#o=r}next(){let t=()=>this.#a();return this.#n=this.#n?this.#n.then(t,t):t(),this.#n}return(t){let r=()=>this.#t(t);return this.#n?this.#n.then(r,r):r()}async#a(){if(this.#e)return{done:!0,value:void 0};let t;try{t=await this.#r.read()}catch(r){throw this.#n=void 0,this.#e=!0,this.#r.releaseLock(),r}return t.done&&(this.#n=void 0,this.#e=!0,this.#r.releaseLock()),t}async#t(t){if(this.#e)return{done:!0,value:t};if(this.#e=!0,!this.#o){let r=this.#r.cancel(t);return this.#r.releaseLock(),await r,{done:!0,value:t}}return this.#r.releaseLock(),{done:!0,value:t}}},$n=Symbol();function ou(){return this[$n].next()}Object.defineProperty(ou,"name",{value:"next"});function iu(e){return this[$n].return(e)}Object.defineProperty(iu,"name",{value:"return"});var rd=Object.create(td,{next:{enumerable:!0,configurable:!0,writable:!0,value:ou},return:{enumerable:!0,configurable:!0,writable:!0,value:iu}});function Nn({preventCancel:e=!1}={}){let t=this.getReader(),r=new Pn(t,e),n=Object.create(rd);return n[$n]=r,n}var su=e=>{if(Ee(e,{checkOpen:!1})&&nt.on!==void 0)return od(e);if(typeof e?.[Symbol.asyncIterator]=="function")return e;if(nd.call(e)==="[object ReadableStream]")return Nn.call(e);throw new TypeError("The first argument must be a Readable, a ReadableStream, or an async iterable.")},{toString:nd}=Object.prototype,od=async function*(e){let t=new AbortController,r={};id(e,t,r);try{for await(let[n]of nt.on(e,"data",{signal:t.signal}))yield n}catch(n){if(r.error!==void 0)throw r.error;if(!t.signal.aborted)throw n}finally{e.destroy()}},id=async(e,t,r)=>{try{await nt.finished(e,{cleanup:!0,readable:!0,writable:!1,error:!1})}catch(n){r.error=n}finally{t.abort()}},nt={};var Pe=async(e,{init:t,convertChunk:r,getSize:n,truncateChunk:o,addChunk:i,getFinalChunk:s,finalize:a},{maxBuffer:u=Number.POSITIVE_INFINITY}={})=>{let l=su(e),c=t();c.length=0;try{for await(let f of l){let D=ad(f),p=r[D](f,c);cu({convertedChunk:p,state:c,getSize:n,truncateChunk:o,addChunk:i,maxBuffer:u})}return sd({state:c,convertChunk:r,getSize:n,truncateChunk:o,addChunk:i,getFinalChunk:s,maxBuffer:u}),a(c)}catch(f){let D=typeof f=="object"&&f!==null?f:new Error(f);throw D.bufferedData=a(c),D}},sd=({state:e,getSize:t,truncateChunk:r,addChunk:n,getFinalChunk:o,maxBuffer:i})=>{let s=o(e);s!==void 0&&cu({convertedChunk:s,state:e,getSize:t,truncateChunk:r,addChunk:n,maxBuffer:i})},cu=({convertedChunk:e,state:t,getSize:r,truncateChunk:n,addChunk:o,maxBuffer:i})=>{let s=r(e),a=t.length+s;if(a<=i){au(e,t,o,a);return}let u=n(e,i-t.length);throw u!==void 0&&au(u,t,o,i),new Q},au=(e,t,r,n)=>{t.contents=r(e,t,n),t.length=n},ad=e=>{let t=typeof e;if(t==="string")return"string";if(t!=="object"||e===null)return"others";if(globalThis.Buffer?.isBuffer(e))return"buffer";let r=uu.call(e);return r==="[object ArrayBuffer]"?"arrayBuffer":r==="[object DataView]"?"dataView":Number.isInteger(e.byteLength)&&Number.isInteger(e.byteOffset)&&uu.call(e.buffer)==="[object ArrayBuffer]"?"typedArray":"others"},{toString:uu}=Object.prototype,Q=class extends Error{name="MaxBufferError";constructor(){super("maxBuffer exceeded")}};var oe=e=>e,ot=()=>{},sr=({contents:e})=>e,ar=e=>{throw new Error(`Streams in object mode are not supported: ${String(e)}`)},ur=e=>e.length;async function cr(e,t){return Pe(e,fd,t)}var ud=()=>({contents:[]}),cd=()=>1,ld=(e,{contents:t})=>(t.push(e),t),fd={init:ud,convertChunk:{string:oe,buffer:oe,arrayBuffer:oe,dataView:oe,typedArray:oe,others:oe},getSize:cd,truncateChunk:ot,addChunk:ld,getFinalChunk:ot,finalize:sr};async function lr(e,t){return Pe(e,yd,t)}var Dd=()=>({contents:new ArrayBuffer(0)}),pd=e=>dd.encode(e),dd=new TextEncoder,lu=e=>new Uint8Array(e),fu=e=>new Uint8Array(e.buffer,e.byteOffset,e.byteLength),md=(e,t)=>e.slice(0,t),hd=(e,{contents:t,length:r},n)=>{let o=du()?gd(t,n):Fd(t,n);return new Uint8Array(o).set(e,r),o},Fd=(e,t)=>{if(t<=e.byteLength)return e;let r=new ArrayBuffer(pu(t));return new Uint8Array(r).set(new Uint8Array(e),0),r},gd=(e,t)=>{if(t<=e.maxByteLength)return e.resize(t),e;let r=new ArrayBuffer(t,{maxByteLength:pu(t)});return new Uint8Array(r).set(new Uint8Array(e),0),r},pu=e=>Du**Math.ceil(Math.log(e)/Math.log(Du)),Du=2,Ed=({contents:e,length:t})=>du()?e:e.slice(0,t),du=()=>"resize"in ArrayBuffer.prototype,yd={init:Dd,convertChunk:{string:pd,buffer:lu,arrayBuffer:lu,dataView:fu,typedArray:fu,others:ar},getSize:ur,truncateChunk:md,addChunk:hd,getFinalChunk:ot,finalize:Ed};async function Dr(e,t){return Pe(e,xd,t)}var bd=()=>({contents:"",textDecoder:new TextDecoder}),fr=(e,{textDecoder:t})=>t.decode(e,{stream:!0}),Sd=(e,{contents:t})=>t+e,wd=(e,t)=>e.slice(0,t),Cd=({textDecoder:e})=>{let t=e.decode();return t===""?void 0:t},xd={init:bd,convertChunk:{string:oe,buffer:fr,arrayBuffer:fr,dataView:fr,typedArray:fr,others:ar},getSize:ur,truncateChunk:wd,addChunk:Sd,getFinalChunk:Cd,finalize:sr};Object.assign(nt,{on:mu.on,finished:hu.finished});var Fu=({error:e,stream:t,readableObjectMode:r,lines:n,encoding:o,fdNumber:i})=>{if(!(e instanceof Q))throw e;if(i==="all")return e;let s=Ad(r,n,o);throw e.maxBufferInfo={fdNumber:i,unit:s},t.destroy(),e},Ad=(e,t,r)=>e?"objects":t?"lines":r==="buffer"?"bytes":"characters",gu=(e,t,r)=>{if(t.length!==r)return;let n=new Q;throw n.maxBufferInfo={fdNumber:"ipc"},n},Eu=(e,t)=>{let{streamName:r,threshold:n,unit:o}=Bd(e,t);return`Command's ${r} was larger than ${n} ${o}`},Bd=(e,t)=>{if(e?.maxBufferInfo===void 0)return{streamName:"output",threshold:t[1],unit:"bytes"};let{maxBufferInfo:{fdNumber:r,unit:n}}=e;delete e.maxBufferInfo;let o=ne(t,r);return r==="ipc"?{streamName:"IPC output",threshold:o,unit:"messages"}:{streamName:_t(r),threshold:o,unit:n}},yu=(e,t,r)=>e?.code==="ENOBUFS"&&t!==null&&t.some(n=>n!==null&&n.length>pr(r)),bu=(e,t,r)=>{if(!t)return e;let n=pr(r);return e.length>n?e.slice(0,n):e},pr=([,e])=>e;var Cu=({stdio:e,all:t,ipcOutput:r,originalError:n,signal:o,signalDescription:i,exitCode:s,escapedCommand:a,timedOut:u,isCanceled:l,isGracefullyCanceled:c,isMaxBuffer:f,isForcefullyTerminated:D,forceKillAfterDelay:p,killSignal:d,maxBuffer:g,timeout:w,cwd:y})=>{let A=n?.code,T=Td({originalError:n,timedOut:u,timeout:w,isMaxBuffer:f,maxBuffer:g,errorCode:A,signal:o,signalDescription:i,exitCode:s,isCanceled:l,isGracefullyCanceled:c,isForcefullyTerminated:D,forceKillAfterDelay:p,killSignal:d}),R=Od(n,y),k=R===void 0?"":`
${R}`,K=`${T}: ${a}${k}`,Fe=t===void 0?[e[2],e[1]]:[t],Ce=[K,...Fe,...e.slice(3),r.map(ae=>Rd(ae)).join(`
`)].map(ae=>Xe(Me(Id(ae)))).filter(Boolean).join(`

`);return{originalMessage:R,shortMessage:K,message:Ce}},Td=({originalError:e,timedOut:t,timeout:r,isMaxBuffer:n,maxBuffer:o,errorCode:i,signal:s,signalDescription:a,exitCode:u,isCanceled:l,isGracefullyCanceled:c,isForcefullyTerminated:f,forceKillAfterDelay:D,killSignal:p})=>{let d=_d(f,D);return t?`Command timed out after ${r} milliseconds${d}`:c?s===void 0?`Command was gracefully canceled with exit code ${u}`:f?`Command was gracefully canceled${d}`:`Command was gracefully canceled with ${s} (${a})`:l?`Command was canceled${d}`:n?`${Eu(e,o)}${d}`:i!==void 0?`Command failed with ${i}${d}`:f?`Command was killed with ${p} (${Gt(p)})${d}`:s!==void 0?`Command was killed with ${s} (${a})`:u!==void 0?`Command failed with exit code ${u}`:"Command failed"},_d=(e,t)=>e?` and was forcefully terminated after ${t} milliseconds`:"",Od=(e,t)=>{if(e instanceof W)return;let r=Ps(e)?e.originalMessage:String(e?.message??e),n=Xe(Qa(r,t));return n===""?void 0:n},Rd=e=>typeof e=="string"?e:(0,wu.inspect)(e),Id=e=>Array.isArray(e)?e.map(t=>Me(Su(t))).filter(Boolean).join(`
`):Su(e),Su=e=>typeof e=="string"?e:O(e)?At(e):"";var dr=({command:e,escapedCommand:t,stdio:r,all:n,ipcOutput:o,options:{cwd:i},startTime:s})=>xu({command:e,escapedCommand:t,cwd:i,durationMs:un(s),failed:!1,timedOut:!1,isCanceled:!1,isGracefullyCanceled:!1,isTerminated:!1,isMaxBuffer:!1,isForcefullyTerminated:!1,exitCode:0,stdout:r[1],stderr:r[2],all:n,stdio:r,ipcOutput:o,pipedFrom:[]}),$e=({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,isSync:s})=>it({error:e,command:t,escapedCommand:r,startTime:i,timedOut:!1,isCanceled:!1,isGracefullyCanceled:!1,isMaxBuffer:!1,isForcefullyTerminated:!1,stdio:Array.from({length:n.length}),ipcOutput:[],options:o,isSync:s}),it=({error:e,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:a,isForcefullyTerminated:u,exitCode:l,signal:c,stdio:f,all:D,ipcOutput:p,options:{timeoutDuration:d,timeout:g=d,forceKillAfterDelay:w,killSignal:y,cwd:A,maxBuffer:T},isSync:R})=>{let{exitCode:k,signal:K,signalDescription:Fe}=Md(l,c),{originalMessage:Ce,shortMessage:ae,message:Jr}=Cu({stdio:f,all:D,ipcOutput:p,originalError:e,signal:K,signalDescription:Fe,exitCode:k,escapedCommand:r,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:a,isForcefullyTerminated:u,forceKillAfterDelay:w,killSignal:y,maxBuffer:T,timeout:g,cwd:A}),He=vs(e,Jr,R);return Object.assign(He,vd({error:He,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:a,isForcefullyTerminated:u,exitCode:k,signal:K,signalDescription:Fe,stdio:f,all:D,ipcOutput:p,cwd:A,originalMessage:Ce,shortMessage:ae})),He},vd=({error:e,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:a,isForcefullyTerminated:u,exitCode:l,signal:c,signalDescription:f,stdio:D,all:p,ipcOutput:d,cwd:g,originalMessage:w,shortMessage:y})=>xu({shortMessage:y,originalMessage:w,command:t,escapedCommand:r,cwd:g,durationMs:un(n),failed:!0,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isTerminated:c!==void 0,isMaxBuffer:a,isForcefullyTerminated:u,exitCode:l,signal:c,signalDescription:f,code:e.cause?.code,stdout:D[1],stderr:D[2],all:p,stdio:D,ipcOutput:d,pipedFrom:[]}),xu=e=>Object.fromEntries(Object.entries(e).filter(([,t])=>t!==void 0)),Md=(e,t)=>{let r=e===null?void 0:e,n=t===null?void 0:t,o=n===void 0?void 0:Gt(t);return{exitCode:r,signal:n,signalDescription:o}};var Au=e=>Number.isFinite(e)?e:0;function Pd(e){return{days:Math.trunc(e/864e5),hours:Math.trunc(e/36e5%24),minutes:Math.trunc(e/6e4%60),seconds:Math.trunc(e/1e3%60),milliseconds:Math.trunc(e%1e3),microseconds:Math.trunc(Au(e*1e3)%1e3),nanoseconds:Math.trunc(Au(e*1e6)%1e3)}}function $d(e){return{days:e/86400000n,hours:e/3600000n%24n,minutes:e/60000n%60n,seconds:e/1000n%60n,milliseconds:e%1000n,microseconds:0n,nanoseconds:0n}}function Ln(e){switch(typeof e){case"number":{if(Number.isFinite(e))return Pd(e);break}case"bigint":return $d(e)}throw new TypeError("Expected a finite number or bigint")}var Nd=e=>e===0||e===0n,Ld=(e,t)=>t===1||t===1n?e:`${e}s`,jd=1e-7,Ud=24n*60n*60n*1000n;function jn(e,t){let r=typeof e=="bigint";if(!r&&!Number.isFinite(e))throw new TypeError("Expected a finite number or bigint");t={...t};let n=e<0?"-":"";e=e<0?-e:e,t.colonNotation&&(t.compact=!1,t.formatSubMilliseconds=!1,t.separateMilliseconds=!1,t.verbose=!1),t.compact&&(t.unitCount=1,t.secondsDecimalDigits=0,t.millisecondsDecimalDigits=0);let o=[],i=(c,f)=>{let D=Math.floor(c*10**f+jd);return(Math.round(D)/10**f).toFixed(f)},s=(c,f,D,p)=>{if(!((o.length===0||!t.colonNotation)&&Nd(c)&&!(t.colonNotation&&D==="m"))){if(p??=String(c),t.colonNotation){let d=p.includes(".")?p.split(".")[0].length:p.length,g=o.length>0?2:1;p="0".repeat(Math.max(0,g-d))+p}else p+=t.verbose?" "+Ld(f,c):D;o.push(p)}},a=Ln(e),u=BigInt(a.days);if(t.hideYearAndDays?s(BigInt(u)*24n+BigInt(a.hours),"hour","h"):(t.hideYear?s(u,"day","d"):(s(u/365n,"year","y"),s(u%365n,"day","d")),s(Number(a.hours),"hour","h")),s(Number(a.minutes),"minute","m"),!t.hideSeconds)if(t.separateMilliseconds||t.formatSubMilliseconds||!t.colonNotation&&e<1e3){let c=Number(a.seconds),f=Number(a.milliseconds),D=Number(a.microseconds),p=Number(a.nanoseconds);if(s(c,"second","s"),t.formatSubMilliseconds)s(f,"millisecond","ms"),s(D,"microsecond","\xB5s"),s(p,"nanosecond","ns");else{let d=f+D/1e3+p/1e6,g=typeof t.millisecondsDecimalDigits=="number"?t.millisecondsDecimalDigits:0,w=d>=1?Math.round(d):Math.ceil(d),y=g?d.toFixed(g):w;s(Number.parseFloat(y),"millisecond","ms",y)}}else{let c=(r?Number(e%Ud):e)/1e3%60,f=typeof t.secondsDecimalDigits=="number"?t.secondsDecimalDigits:1,D=i(c,f),p=t.keepDecimalsOnWholeSeconds?D:D.replace(/\.0+$/,"");s(Number.parseFloat(p),"second","s",p)}if(o.length===0)return n+"0"+(t.verbose?" milliseconds":"ms");let l=t.colonNotation?":":" ";return typeof t.unitCount=="number"&&(o=o.slice(0,Math.max(t.unitCount,1))),n+o.join(l)}var Bu=(e,t)=>{e.failed&&X({type:"error",verboseMessage:e.shortMessage,verboseInfo:t,result:e})};var Tu=(e,t)=>{Ae(t)&&(Bu(e,t),kd(e,t))},kd=(e,t)=>{let r=`(done in ${jn(e.durationMs)})`;X({type:"duration",verboseMessage:r,verboseInfo:t,result:e})};var Ne=(e,t,{reject:r})=>{if(Tu(e,t),e.failed&&r)throw e;return e};var qn=require("node:fs");var Ru=(e,t)=>ye(e)?"asyncGenerator":Mu(e)?"generator":mr(e)?"fileUrl":Yd(e)?"filePath":Kd(e)?"webStream":V(e,{checkOpen:!1})?"native":O(e)?"uint8Array":Xd(e)?"asyncIterable":Jd(e)?"iterable":Gn(e)?Iu({transform:e},t):Vd(e)?Gd(e,t):"native",Gd=(e,t)=>Mn(e.transform,{checkOpen:!1})?Wd(e,t):Gn(e.transform)?Iu(e,t):zd(e,t),Wd=(e,t)=>(vu(e,t,"Duplex stream"),"duplex"),Iu=(e,t)=>(vu(e,t,"web TransformStream"),"webTransform"),vu=({final:e,binary:t,objectMode:r},n,o)=>{_u(e,`${n}.final`,o),_u(t,`${n}.binary`,o),Un(r,`${n}.objectMode`)},_u=(e,t,r)=>{if(e!==void 0)throw new TypeError(`The \`${t}\` option can only be defined when using a generator, not a ${r}.`)},zd=({transform:e,final:t,binary:r,objectMode:n},o)=>{if(e!==void 0&&!Ou(e))throw new TypeError(`The \`${o}.transform\` option must be a generator, a Duplex stream or a web TransformStream.`);if(Mn(t,{checkOpen:!1}))throw new TypeError(`The \`${o}.final\` option must not be a Duplex stream.`);if(Gn(t))throw new TypeError(`The \`${o}.final\` option must not be a web TransformStream.`);if(t!==void 0&&!Ou(t))throw new TypeError(`The \`${o}.final\` option must be a generator.`);return Un(r,`${o}.binary`),Un(n,`${o}.objectMode`),ye(e)||ye(t)?"asyncGenerator":"generator"},Un=(e,t)=>{if(e!==void 0&&typeof e!="boolean")throw new TypeError(`The \`${t}\` option must use a boolean.`)},Ou=e=>ye(e)||Mu(e),ye=e=>Object.prototype.toString.call(e)==="[object AsyncGeneratorFunction]",Mu=e=>Object.prototype.toString.call(e)==="[object GeneratorFunction]",Vd=e=>B(e)&&(e.transform!==void 0||e.final!==void 0),mr=e=>Object.prototype.toString.call(e)==="[object URL]",Pu=e=>mr(e)&&e.protocol!=="file:",Yd=e=>B(e)&&Object.keys(e).length>0&&Object.keys(e).every(t=>qd.has(t))&&kn(e.file),qd=new Set(["file","append"]),kn=e=>typeof e=="string",$u=(e,t)=>e==="native"&&typeof t=="string"&&!Hd.has(t),Hd=new Set(["ipc","ignore","inherit","overlapped","pipe"]),Nu=e=>Object.prototype.toString.call(e)==="[object ReadableStream]",hr=e=>Object.prototype.toString.call(e)==="[object WritableStream]",Kd=e=>Nu(e)||hr(e),Gn=e=>Nu(e?.readable)&&hr(e?.writable),Xd=e=>Lu(e)&&typeof e[Symbol.asyncIterator]=="function",Jd=e=>Lu(e)&&typeof e[Symbol.iterator]=="function",Lu=e=>typeof e=="object"&&e!==null,N=new Set(["generator","asyncGenerator","duplex","webTransform"]),Fr=new Set(["fileUrl","filePath","fileNumber"]),Wn=new Set(["fileUrl","filePath"]),ju=new Set([...Wn,"webStream","nodeStream"]),Uu=new Set(["webTransform","duplex"]),fe={generator:"a generator",asyncGenerator:"an async generator",fileUrl:"a file URL",filePath:"a file path string",fileNumber:"a file descriptor number",webStream:"a web stream",nodeStream:"a Node.js stream",webTransform:"a web TransformStream",duplex:"a Duplex stream",native:"any value",iterable:"an iterable",asyncIterable:"an async iterable",string:"a string",uint8Array:"a Uint8Array"};var zn=(e,t,r,n)=>n==="output"?Zd(e,t,r):Qd(e,t,r),Zd=(e,t,r)=>{let n=t!==0&&r[t-1].value.readableObjectMode;return{writableObjectMode:n,readableObjectMode:e??n}},Qd=(e,t,r)=>{let n=t===0?e===!0:r[t-1].value.readableObjectMode,o=t!==r.length-1&&(e??n);return{writableObjectMode:n,readableObjectMode:o}},ku=(e,t)=>{let r=e.findLast(({type:n})=>N.has(n));return r===void 0?!1:t==="input"?r.value.writableObjectMode:r.value.readableObjectMode};var Gu=(e,t,r,n)=>[...e.filter(({type:o})=>!N.has(o)),...em(e,t,r,n)],em=(e,t,r,{encoding:n})=>{let o=e.filter(({type:s})=>N.has(s)),i=Array.from({length:o.length});for(let[s,a]of Object.entries(o))i[s]=tm({stdioItem:a,index:Number(s),newTransforms:i,optionName:t,direction:r,encoding:n});return im(i,r)},tm=({stdioItem:e,stdioItem:{type:t},index:r,newTransforms:n,optionName:o,direction:i,encoding:s})=>t==="duplex"?rm({stdioItem:e,optionName:o}):t==="webTransform"?nm({stdioItem:e,index:r,newTransforms:n,direction:i}):om({stdioItem:e,index:r,newTransforms:n,direction:i,encoding:s}),rm=({stdioItem:e,stdioItem:{value:{transform:t,transform:{writableObjectMode:r,readableObjectMode:n},objectMode:o=n}},optionName:i})=>{if(o&&!n)throw new TypeError(`The \`${i}.objectMode\` option can only be \`true\` if \`new Duplex({objectMode: true})\` is used.`);if(!o&&n)throw new TypeError(`The \`${i}.objectMode\` option cannot be \`false\` if \`new Duplex({objectMode: true})\` is used.`);return{...e,value:{transform:t,writableObjectMode:r,readableObjectMode:n}}},nm=({stdioItem:e,stdioItem:{value:t},index:r,newTransforms:n,direction:o})=>{let{transform:i,objectMode:s}=B(t)?t:{transform:t},{writableObjectMode:a,readableObjectMode:u}=zn(s,r,n,o);return{...e,value:{transform:i,writableObjectMode:a,readableObjectMode:u}}},om=({stdioItem:e,stdioItem:{value:t},index:r,newTransforms:n,direction:o,encoding:i})=>{let{transform:s,final:a,binary:u=!1,preserveNewlines:l=!1,objectMode:c}=B(t)?t:{transform:t},f=u||P.has(i),{writableObjectMode:D,readableObjectMode:p}=zn(c,r,n,o);return{...e,value:{transform:s,final:a,binary:f,preserveNewlines:l,writableObjectMode:D,readableObjectMode:p}}},im=(e,t)=>t==="input"?e.reverse():e;var gr=E(require("node:process"),1);var Wu=(e,t,r)=>{let n=e.map(o=>sm(o,t));if(n.includes("input")&&n.includes("output"))throw new TypeError(`The \`${r}\` option must not be an array of both readable and writable values.`);return n.find(Boolean)??cm},sm=({type:e,value:t},r)=>am[r]??zu[e](t),am=["input","output","output"],Le=()=>{},Vn=()=>"input",zu={generator:Le,asyncGenerator:Le,fileUrl:Le,filePath:Le,iterable:Vn,asyncIterable:Vn,uint8Array:Vn,webStream:e=>hr(e)?"output":"input",nodeStream(e){return Ee(e,{checkOpen:!1})?vn(e,{checkOpen:!1})?void 0:"input":"output"},webTransform:Le,duplex:Le,native(e){let t=um(e);if(t!==void 0)return t;if(V(e,{checkOpen:!1}))return zu.nodeStream(e)}},um=e=>{if([0,gr.default.stdin].includes(e))return"input";if([1,2,gr.default.stdout,gr.default.stderr].includes(e))return"output"},cm="output";var Vu=(e,t)=>t&&!e.includes("ipc")?[...e,"ipc"]:e;var Yu=({stdio:e,ipc:t,buffer:r,...n},o,i)=>{let s=lm(e,n).map((a,u)=>qu(a,u));return i?Dm(s,r,o):Vu(s,t)},lm=(e,t)=>{if(e===void 0)return $.map(n=>t[n]);if(fm(t))throw new Error(`It's not possible to provide \`stdio\` in combination with one of ${$.map(n=>`\`${n}\``).join(", ")}`);if(typeof e=="string")return[e,e,e];if(!Array.isArray(e))throw new TypeError(`Expected \`stdio\` to be of type \`string\` or \`Array\`, got \`${typeof e}\``);let r=Math.max(e.length,$.length);return Array.from({length:r},(n,o)=>e[o])},fm=e=>$.some(t=>e[t]!==void 0),qu=(e,t)=>Array.isArray(e)?e.map(r=>qu(r,t)):e??(t>=$.length?"ignore":"pipe"),Dm=(e,t,r)=>e.map((n,o)=>!t[o]&&o!==0&&!Be(r,o)&&pm(n)?"ignore":n),pm=e=>e==="pipe"||Array.isArray(e)&&e.every(t=>t==="pipe");var Ku=require("node:fs"),Xu=E(require("node:tty"),1);var Ju=({stdioItem:e,stdioItem:{type:t},isStdioArray:r,fdNumber:n,direction:o,isSync:i})=>!r||t!=="native"?e:i?dm({stdioItem:e,fdNumber:n,direction:o}):Fm({stdioItem:e,fdNumber:n}),dm=({stdioItem:e,stdioItem:{value:t,optionName:r},fdNumber:n,direction:o})=>{let i=mm({value:t,optionName:r,fdNumber:n,direction:o});if(i!==void 0)return i;if(V(t,{checkOpen:!1}))throw new TypeError(`The \`${r}: Stream\` option cannot both be an array and include a stream with synchronous methods.`);return e},mm=({value:e,optionName:t,fdNumber:r,direction:n})=>{let o=hm(e,r);if(o!==void 0){if(n==="output")return{type:"fileNumber",value:o,optionName:t};if(Xu.default.isatty(o))throw new TypeError(`The \`${t}: ${Vt(e)}\` option is invalid: it cannot be a TTY with synchronous methods.`);return{type:"uint8Array",value:re((0,Ku.readFileSync)(o)),optionName:t}}},hm=(e,t)=>{if(e==="inherit")return t;if(typeof e=="number")return e;let r=Tt.indexOf(e);if(r!==-1)return r},Fm=({stdioItem:e,stdioItem:{value:t,optionName:r},fdNumber:n})=>t==="inherit"?{type:"nodeStream",value:Hu(n,t,r),optionName:r}:typeof t=="number"?{type:"nodeStream",value:Hu(t,t,r),optionName:r}:V(t,{checkOpen:!1})?{type:"nodeStream",value:t,optionName:r}:e,Hu=(e,t,r)=>{let n=Tt[e];if(n===void 0)throw new TypeError(`The \`${r}: ${t}\` option is invalid: no such standard stream.`);return n};var Zu=({input:e,inputFile:t},r)=>r===0?[...gm(e),...ym(t)]:[],gm=e=>e===void 0?[]:[{type:Em(e),value:e,optionName:"input"}],Em=e=>{if(Ee(e,{checkOpen:!1}))return"nodeStream";if(typeof e=="string")return"string";if(O(e))return"uint8Array";throw new Error("The `input` option must be a string, a Uint8Array or a Node.js Readable stream.")},ym=e=>e===void 0?[]:[{...bm(e),optionName:"inputFile"}],bm=e=>{if(mr(e))return{type:"fileUrl",value:e};if(kn(e))return{type:"filePath",value:{file:e}};throw new Error("The `inputFile` option must be a file path string or a file URL.")};var Qu=e=>e.filter((t,r)=>e.every((n,o)=>t.value!==n.value||r>=o||t.type==="generator"||t.type==="asyncGenerator")),ec=({stdioItem:{type:e,value:t,optionName:r},direction:n,fileDescriptors:o,isSync:i})=>{let s=Sm(o,e);if(s.length!==0){if(i){wm({otherStdioItems:s,type:e,value:t,optionName:r,direction:n});return}if(ju.has(e))return tc({otherStdioItems:s,type:e,value:t,optionName:r,direction:n});Uu.has(e)&&xm({otherStdioItems:s,type:e,value:t,optionName:r})}},Sm=(e,t)=>e.flatMap(({direction:r,stdioItems:n})=>n.filter(o=>o.type===t).map(o=>({...o,direction:r}))),wm=({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})=>{Wn.has(t)&&tc({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})},tc=({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})=>{let i=e.filter(a=>Cm(a,r));if(i.length===0)return;let s=i.find(a=>a.direction!==o);return rc(s,n,t),o==="output"?i[0].stream:void 0},Cm=({type:e,value:t},r)=>e==="filePath"?t.file===r.file:e==="fileUrl"?t.href===r.href:t===r,xm=({otherStdioItems:e,type:t,value:r,optionName:n})=>{let o=e.find(({value:{transform:i}})=>i===r.transform);rc(o,n,t)},rc=(e,t,r)=>{if(e!==void 0)throw new TypeError(`The \`${e.optionName}\` and \`${t}\` options must not target ${fe[r]} that is the same.`)};var Er=(e,t,r,n)=>{let i=Yu(t,r,n).map((a,u)=>Am({stdioOption:a,fdNumber:u,options:t,isSync:n})),s=Mm({initialFileDescriptors:i,addProperties:e,options:t,isSync:n});return t.stdio=s.map(({stdioItems:a})=>Nm(a)),s},Am=({stdioOption:e,fdNumber:t,options:r,isSync:n})=>{let o=_t(t),{stdioItems:i,isStdioArray:s}=Bm({stdioOption:e,fdNumber:t,options:r,optionName:o}),a=Wu(i,t,o),u=i.map(f=>Ju({stdioItem:f,isStdioArray:s,fdNumber:t,direction:a,isSync:n})),l=Gu(u,o,a,r),c=ku(l,a);return vm(l,c),{direction:a,objectMode:c,stdioItems:l}},Bm=({stdioOption:e,fdNumber:t,options:r,optionName:n})=>{let i=[...(Array.isArray(e)?e:[e]).map(u=>Tm(u,n)),...Zu(r,t)],s=Qu(i),a=s.length>1;return _m(s,a,n),Rm(s),{stdioItems:s,isStdioArray:a}},Tm=(e,t)=>({type:Ru(e,t),value:e,optionName:t}),_m=(e,t,r)=>{if(e.length===0)throw new TypeError(`The \`${r}\` option must not be an empty array.`);if(t){for(let{value:n,optionName:o}of e)if(Om.has(n))throw new Error(`The \`${o}\` option must not include \`${n}\`.`)}},Om=new Set(["ignore","ipc"]),Rm=e=>{for(let t of e)Im(t)},Im=({type:e,value:t,optionName:r})=>{if(Pu(t))throw new TypeError(`The \`${r}: URL\` option must use the \`file:\` scheme.
For example, you can use the \`pathToFileURL()\` method of the \`url\` core module.`);if($u(e,t))throw new TypeError(`The \`${r}: { file: '...' }\` option must be used instead of \`${r}: '...'\`.`)},vm=(e,t)=>{if(!t)return;let r=e.find(({type:n})=>Fr.has(n));if(r!==void 0)throw new TypeError(`The \`${r.optionName}\` option cannot use both files and transforms in objectMode.`)},Mm=({initialFileDescriptors:e,addProperties:t,options:r,isSync:n})=>{let o=[];try{for(let i of e)o.push(Pm({fileDescriptor:i,fileDescriptors:o,addProperties:t,options:r,isSync:n}));return o}catch(i){throw Yn(o),i}},Pm=({fileDescriptor:{direction:e,objectMode:t,stdioItems:r},fileDescriptors:n,addProperties:o,options:i,isSync:s})=>{let a=r.map(u=>$m({stdioItem:u,addProperties:o,direction:e,options:i,fileDescriptors:n,isSync:s}));return{direction:e,objectMode:t,stdioItems:a}},$m=({stdioItem:e,addProperties:t,direction:r,options:n,fileDescriptors:o,isSync:i})=>{let s=ec({stdioItem:e,direction:r,fileDescriptors:o,isSync:i});return s!==void 0?{...e,stream:s}:{...e,...t[r][e.type](e,n)}},Yn=e=>{for(let{stdioItems:t}of e)for(let{stream:r}of t)r!==void 0&&!G(r)&&r.destroy()},Nm=e=>{if(e.length>1)return e.some(({value:n})=>n==="overlapped")?"overlapped":"pipe";let[{type:t,value:r}]=e;return t==="native"?r:"pipe"};var oc=(e,t)=>Er(jm,e,t,!0),ee=({type:e,optionName:t})=>{ic(t,fe[e])},Lm=({optionName:e,value:t})=>((t==="ipc"||t==="overlapped")&&ic(e,`"${t}"`),{}),ic=(e,t)=>{throw new TypeError(`The \`${e}\` option cannot be ${t} with synchronous methods.`)},nc={generator(){},asyncGenerator:ee,webStream:ee,nodeStream:ee,webTransform:ee,duplex:ee,asyncIterable:ee,native:Lm},jm={input:{...nc,fileUrl:({value:e})=>({contents:[re((0,qn.readFileSync)(e))]}),filePath:({value:{file:e}})=>({contents:[re((0,qn.readFileSync)(e))]}),fileNumber:ee,iterable:({value:e})=>({contents:[...e]}),string:({value:e})=>({contents:[e]}),uint8Array:({value:e})=>({contents:[e]})},output:{...nc,fileUrl:({value:e})=>({path:e}),filePath:({value:{file:e,append:t}})=>({path:e,append:t}),fileNumber:({value:e})=>({path:e}),iterable:ee,string:ee,uint8Array:ee}};var ie=(e,{stripFinalNewline:t},r)=>Hn(t,r)&&e!==void 0&&!Array.isArray(e)?Me(e):e,Hn=(e,t)=>t==="all"?e[1]||e[2]:e[t];var at=require("node:stream");var yr=(e,t,r,n)=>e||r?void 0:ac(t,n),Xn=(e,t,r)=>r?e.flatMap(n=>sc(n,t)):sc(e,t),sc=(e,t)=>{let{transform:r,final:n}=ac(t,{});return[...r(e),...n()]},ac=(e,t)=>(t.previousChunks="",{transform:Um.bind(void 0,t,e),final:Gm.bind(void 0,t)}),Um=function*(e,t,r){if(typeof r!="string"){yield r;return}let{previousChunks:n}=e,o=-1;for(let i=0;i<r.length;i+=1)if(r[i]===`
`){let s=km(r,i,t,e),a=r.slice(o+1,i+1-s);n.length>0&&(a=Kn(n,a),n=""),yield a,o=i}o!==r.length-1&&(n=Kn(n,r.slice(o+1))),e.previousChunks=n},km=(e,t,r,n)=>r?0:(n.isWindowsNewline=t!==0&&e[t-1]==="\r",n.isWindowsNewline?2:1),Gm=function*({previousChunks:e}){e.length>0&&(yield e)},uc=({binary:e,preserveNewlines:t,readableObjectMode:r,state:n})=>e||t||r?void 0:{transform:Wm.bind(void 0,n)},Wm=function*({isWindowsNewline:e=!1},t){let{unixNewline:r,windowsNewline:n,LF:o,concatBytes:i}=typeof t=="string"?zm:Ym;if(t.at(-1)===o){yield t;return}yield i(t,e?n:r)},Kn=(e,t)=>`${e}${t}`,zm={windowsNewline:`\r
`,unixNewline:`
`,LF:`
`,concatBytes:Kn},Vm=(e,t)=>{let r=new Uint8Array(e.length+t.length);return r.set(e,0),r.set(t,e.length),r},Ym={windowsNewline:new Uint8Array([13,10]),unixNewline:new Uint8Array([10]),LF:10,concatBytes:Vm};var cc=require("node:buffer");var lc=(e,t)=>e?void 0:qm.bind(void 0,t),qm=function*(e,t){if(typeof t!="string"&&!O(t)&&!cc.Buffer.isBuffer(t))throw new TypeError(`The \`${e}\` option's transform must use "objectMode: true" to receive as input: ${typeof t}.`);yield t},fc=(e,t)=>e?Hm.bind(void 0,t):Km.bind(void 0,t),Hm=function*(e,t){Dc(e,t),yield t},Km=function*(e,t){if(Dc(e,t),typeof t!="string"&&!O(t))throw new TypeError(`The \`${e}\` option's function must yield a string or an Uint8Array, not ${typeof t}.`);yield t},Dc=(e,t)=>{if(t==null)throw new TypeError(`The \`${e}\` option's function must not call \`yield ${t}\`.
Instead, \`yield\` should either be called with a value, or not be called at all. For example:
  if (condition) { yield value; }`)};var pc=require("node:buffer"),dc=require("node:string_decoder");var br=(e,t,r)=>{if(r)return;if(e)return{transform:Xm.bind(void 0,new TextEncoder)};let n=new dc.StringDecoder(t);return{transform:Jm.bind(void 0,n),final:Zm.bind(void 0,n)}},Xm=function*(e,t){pc.Buffer.isBuffer(t)?yield re(t):typeof t=="string"?yield e.encode(t):yield t},Jm=function*(e,t){yield O(t)?e.write(t):t},Zm=function*(e){let t=e.end();t!==""&&(yield t)};var Jn=require("node:util"),Zn=(0,Jn.callbackify)(async(e,t,r,n)=>{t.currentIterable=e(...r);try{for await(let o of t.currentIterable)n.push(o)}finally{delete t.currentIterable}}),Sr=async function*(e,t,r){if(r===t.length){yield e;return}let{transform:n=e0}=t[r];for await(let o of n(e))yield*Sr(o,t,r+1)},mc=async function*(e){for(let[t,{final:r}]of Object.entries(e))yield*Qm(r,Number(t),e)},Qm=async function*(e,t,r){if(e!==void 0)for await(let n of e())yield*Sr(n,r,t+1)},hc=(0,Jn.callbackify)(async({currentIterable:e},t)=>{if(e!==void 0){await(t?e.throw(t):e.return());return}if(t)throw t}),e0=function*(e){yield e};var Qn=(e,t,r,n)=>{try{for(let o of e(...t))r.push(o);n()}catch(o){n(o)}},Fc=(e,t)=>[...t.flatMap(r=>[...be(r,e,0)]),...st(e)],be=function*(e,t,r){if(r===t.length){yield e;return}let{transform:n=r0}=t[r];for(let o of n(e))yield*be(o,t,r+1)},st=function*(e){for(let[t,{final:r}]of Object.entries(e))yield*t0(r,Number(t),e)},t0=function*(e,t,r){if(e!==void 0)for(let n of e())yield*be(n,r,t+1)},r0=function*(e){yield e};var eo=({value:e,value:{transform:t,final:r,writableObjectMode:n,readableObjectMode:o},optionName:i},{encoding:s})=>{let a={},u=gc(e,s,i),l=ye(t),c=ye(r),f=l?Zn.bind(void 0,Sr,a):Qn.bind(void 0,be),D=l||c?Zn.bind(void 0,mc,a):Qn.bind(void 0,st),p=l||c?hc.bind(void 0,a):void 0;return{stream:new at.Transform({writableObjectMode:n,writableHighWaterMark:(0,at.getDefaultHighWaterMark)(n),readableObjectMode:o,readableHighWaterMark:(0,at.getDefaultHighWaterMark)(o),transform(g,w,y){f([g,u,0],this,y)},flush(g){D([u],this,g)},destroy:p})}},wr=(e,t,r,n)=>{let o=t.filter(({type:s})=>s==="generator"),i=n?o.reverse():o;for(let{value:s,optionName:a}of i){let u=gc(s,r,a);e=Fc(u,e)}return e},gc=({transform:e,final:t,binary:r,writableObjectMode:n,readableObjectMode:o,preserveNewlines:i},s,a)=>{let u={};return[{transform:lc(n,a)},br(r,s,n),yr(r,i,n,u),{transform:e,final:t},{transform:fc(o,a)},uc({binary:r,preserveNewlines:i,readableObjectMode:o,state:u})].filter(Boolean)};var Ec=(e,t)=>{for(let r of n0(e))o0(e,r,t)},n0=e=>new Set(Object.entries(e).filter(([,{direction:t}])=>t==="input").map(([t])=>Number(t))),o0=(e,t,r)=>{let{stdioItems:n}=e[t],o=n.filter(({contents:a})=>a!==void 0);if(o.length===0)return;if(t!==0){let[{type:a,optionName:u}]=o;throw new TypeError(`Only the \`stdin\` option, not \`${u}\`, can be ${fe[a]} with synchronous methods.`)}let s=o.map(({contents:a})=>a).map(a=>i0(a,n));r.input=Ke(s)},i0=(e,t)=>{let r=wr(e,t,"utf8",!0);return s0(r),Ke(r)},s0=e=>{let t=e.find(r=>typeof r!="string"&&!O(r));if(t!==void 0)throw new TypeError(`The \`stdin\` option is invalid: when passing objects as input, a transform must be used to serialize them to strings or Uint8Arrays: ${t}.`)};var xr=require("node:fs");var Cr=({stdioItems:e,encoding:t,verboseInfo:r,fdNumber:n})=>n!=="all"&&Be(r,n)&&!P.has(t)&&a0(n)&&(e.some(({type:o,value:i})=>o==="native"&&u0.has(i))||e.every(({type:o})=>N.has(o))),a0=e=>e===1||e===2,u0=new Set(["pipe","overlapped"]),yc=async(e,t,r,n)=>{for await(let o of e)c0(t)||Sc(o,r,n)},bc=(e,t,r)=>{for(let n of e)Sc(n,t,r)},c0=e=>e._readableState.pipes.length>0,Sc=(e,t,r)=>{let n=Pt(e);X({type:"output",verboseMessage:n,fdNumber:t,verboseInfo:r})};var wc=({fileDescriptors:e,syncResult:{output:t},options:r,isMaxBuffer:n,verboseInfo:o})=>{if(t===null)return{output:Array.from({length:3})};let i={},s=new Set([]);return{output:t.map((u,l)=>l0({result:u,fileDescriptors:e,fdNumber:l,state:i,outputFiles:s,isMaxBuffer:n,verboseInfo:o},r)),...i}},l0=({result:e,fileDescriptors:t,fdNumber:r,state:n,outputFiles:o,isMaxBuffer:i,verboseInfo:s},{buffer:a,encoding:u,lines:l,stripFinalNewline:c,maxBuffer:f})=>{if(e===null)return;let D=bu(e,i,f),p=re(D),{stdioItems:d,objectMode:g}=t[r],w=f0([p],d,u,n),{serializedResult:y,finalResult:A=y}=D0({chunks:w,objectMode:g,encoding:u,lines:l,stripFinalNewline:c,fdNumber:r});p0({serializedResult:y,fdNumber:r,state:n,verboseInfo:s,encoding:u,stdioItems:d,objectMode:g});let T=a[r]?A:void 0;try{return n.error===void 0&&d0(y,d,o),T}catch(R){return n.error=R,T}},f0=(e,t,r,n)=>{try{return wr(e,t,r,!1)}catch(o){return n.error=o,e}},D0=({chunks:e,objectMode:t,encoding:r,lines:n,stripFinalNewline:o,fdNumber:i})=>{if(t)return{serializedResult:e};if(r==="buffer")return{serializedResult:Ke(e)};let s=pi(e,r);return n[i]?{serializedResult:s,finalResult:Xn(s,!o[i],t)}:{serializedResult:s}},p0=({serializedResult:e,fdNumber:t,state:r,verboseInfo:n,encoding:o,stdioItems:i,objectMode:s})=>{if(!Cr({stdioItems:i,encoding:o,verboseInfo:n,fdNumber:t}))return;let a=Xn(e,!1,s);try{bc(a,t,n)}catch(u){r.error??=u}},d0=(e,t,r)=>{for(let{path:n,append:o}of t.filter(({type:i})=>Fr.has(i))){let i=typeof n=="string"?n:n.toString();o||r.has(i)?(0,xr.appendFileSync)(n,e):(r.add(i),(0,xr.writeFileSync)(n,e))}};var Cc=([,e,t],r)=>{if(r.all)return e===void 0?t:t===void 0?e:Array.isArray(e)?Array.isArray(t)?[...e,...t]:[...e,ie(t,r,"all")]:Array.isArray(t)?[ie(e,r,"all"),...t]:O(e)&&O(t)?en([e,t]):`${e}${t}`};var Ar=require("node:events");var xc=async(e,t)=>{let[r,n]=await m0(e);return t.isForcefullyTerminated??=!1,[r,n]},m0=async e=>{let[t,r]=await Promise.allSettled([(0,Ar.once)(e,"spawn"),(0,Ar.once)(e,"exit")]);return t.status==="rejected"?[]:r.status==="rejected"?Ac(e):r.value},Ac=async e=>{try{return await(0,Ar.once)(e,"exit")}catch{return Ac(e)}},Bc=async e=>{let[t,r]=await e;if(!h0(t,r)&&to(t,r))throw new W;return[t,r]},h0=(e,t)=>e===void 0&&t===void 0,to=(e,t)=>e!==0||t!==null;var Tc=({error:e,status:t,signal:r,output:n},{maxBuffer:o})=>{let i=F0(e,t,r),s=i?.code==="ETIMEDOUT",a=yu(i,n,o);return{resultError:i,exitCode:t,signal:r,timedOut:s,isMaxBuffer:a}},F0=(e,t,r)=>e!==void 0?e:to(t,r)?new W:void 0;var Oc=(e,t,r)=>{let{file:n,commandArguments:o,command:i,escapedCommand:s,startTime:a,verboseInfo:u,options:l,fileDescriptors:c}=g0(e,t,r),f=b0({file:n,commandArguments:o,options:l,command:i,escapedCommand:s,verboseInfo:u,fileDescriptors:c,startTime:a});return Ne(f,u,l)},g0=(e,t,r)=>{let{command:n,escapedCommand:o,startTime:i,verboseInfo:s}=Nt(e,t,r),a=E0(r),{file:u,commandArguments:l,options:c}=or(e,t,a);y0(c);let f=oc(c,s);return{file:u,commandArguments:l,command:n,escapedCommand:o,startTime:i,verboseInfo:s,options:c,fileDescriptors:f}},E0=e=>e.node&&!e.ipc?{...e,ipc:!1}:e,y0=({ipc:e,ipcInput:t,detached:r,cancelSignal:n})=>{t&&Br("ipcInput"),e&&Br("ipc: true"),r&&Br("detached: true"),n&&Br("cancelSignal")},Br=e=>{throw new TypeError(`The "${e}" option cannot be used with synchronous methods.`)},b0=({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,verboseInfo:i,fileDescriptors:s,startTime:a})=>{let u=S0({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,fileDescriptors:s,startTime:a});if(u.failed)return u;let{resultError:l,exitCode:c,signal:f,timedOut:D,isMaxBuffer:p}=Tc(u,r),{output:d,error:g=l}=wc({fileDescriptors:s,syncResult:u,options:r,isMaxBuffer:p,verboseInfo:i}),w=d.map((A,T)=>ie(A,r,T)),y=ie(Cc(d,r),r,"all");return C0({error:g,exitCode:c,signal:f,timedOut:D,isMaxBuffer:p,stdio:w,all:y,options:r,command:n,escapedCommand:o,startTime:a})},S0=({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,fileDescriptors:i,startTime:s})=>{try{Ec(i,r);let a=w0(r);return(0,_c.spawnSync)(...ir(e,t,a))}catch(a){return $e({error:a,command:n,escapedCommand:o,fileDescriptors:i,options:r,startTime:s,isSync:!0})}},w0=({encoding:e,maxBuffer:t,...r})=>({...r,encoding:"buffer",maxBuffer:pr(t)}),C0=({error:e,exitCode:t,signal:r,timedOut:n,isMaxBuffer:o,stdio:i,all:s,options:a,command:u,escapedCommand:l,startTime:c})=>e===void 0?dr({command:u,escapedCommand:l,stdio:i,all:s,ipcOutput:[],options:a,startTime:c}):it({error:e,command:u,escapedCommand:l,timedOut:n,isCanceled:!1,isGracefullyCanceled:!1,isMaxBuffer:o,isForcefullyTerminated:!1,exitCode:t,signal:r,stdio:i,all:s,ipcOutput:[],options:a,startTime:c,isSync:!0});var Nl=require("node:events"),Ll=require("node:child_process");var no=E(require("node:process"),1);var je=require("node:events");var Rc=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},{reference:o=!0,filter:i}={})=>(Re({methodName:"getOneMessage",isSubprocess:r,ipc:n,isConnected:Jt(e)}),x0({anyProcess:e,channel:t,isSubprocess:r,filter:i,reference:o})),x0=async({anyProcess:e,channel:t,isSubprocess:r,filter:n,reference:o})=>{qt(t,o);let i=le(e,t,r),s=new AbortController;try{return await Promise.race([A0(i,n,s),B0(i,r,s),T0(i,r,s)])}catch(a){throw Ie(e),a}finally{s.abort(),Ht(t,o)}},A0=async(e,t,{signal:r})=>{if(t===void 0){let[n]=await(0,je.once)(e,"message",{signal:r});return n}for await(let[n]of(0,je.on)(e,"message",{signal:r}))if(t(n))return n},B0=async(e,t,{signal:r})=>{await(0,je.once)(e,"disconnect",{signal:r}),ea(t)},T0=async(e,t,{signal:r})=>{let[n]=await(0,je.once)(e,"strict:error",{signal:r});throw zt(n,t)};var ut=require("node:events");var vc=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},{reference:o=!0}={})=>ro({anyProcess:e,channel:t,isSubprocess:r,ipc:n,shouldAwait:!r,reference:o}),ro=({anyProcess:e,channel:t,isSubprocess:r,ipc:n,shouldAwait:o,reference:i})=>{Re({methodName:"getEachMessage",isSubprocess:r,ipc:n,isConnected:Jt(e)}),qt(t,i);let s=le(e,t,r),a=new AbortController,u={};return _0(e,s,a),O0({ipcEmitter:s,isSubprocess:r,controller:a,state:u}),R0({anyProcess:e,channel:t,ipcEmitter:s,isSubprocess:r,shouldAwait:o,controller:a,state:u,reference:i})},_0=async(e,t,r)=>{try{await(0,ut.once)(t,"disconnect",{signal:r.signal}),r.abort()}catch{}},O0=async({ipcEmitter:e,isSubprocess:t,controller:r,state:n})=>{try{let[o]=await(0,ut.once)(e,"strict:error",{signal:r.signal});n.error=zt(o,t),r.abort()}catch{}},R0=async function*({anyProcess:e,channel:t,ipcEmitter:r,isSubprocess:n,shouldAwait:o,controller:i,state:s,reference:a}){try{for await(let[u]of(0,ut.on)(r,"message",{signal:i.signal}))Ic(s),yield u}catch{Ic(s)}finally{i.abort(),Ht(t,a),n||Ie(e),o&&await e}},Ic=({error:e})=>{if(e)throw e};var Mc=(e,{ipc:t})=>{Object.assign(e,$c(e,!1,t))},Pc=()=>{let e=no.default,t=!0,r=no.default.channel!==void 0;return{...$c(e,t,r),getCancelSignal:Ma.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r})}},$c=(e,t,r)=>({sendMessage:tr.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r}),getOneMessage:Rc.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r}),getEachMessage:vc.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r})});var Nc=require("node:child_process"),De=require("node:stream");var Lc=({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,verboseInfo:s})=>{Yn(n);let a=new Nc.ChildProcess;I0(a,n),Object.assign(a,{readable:v0,writable:M0,duplex:P0});let u=$e({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,isSync:!1}),l=$0(u,s,o);return{subprocess:a,promise:l}},I0=(e,t)=>{let r=ct(),n=ct(),o=ct(),i=Array.from({length:t.length-3},ct),s=ct(),a=[r,n,o,...i];Object.assign(e,{stdin:r,stdout:n,stderr:o,all:s,stdio:a})},ct=()=>{let e=new De.PassThrough;return e.end(),e},v0=()=>new De.Readable({read(){}}),M0=()=>new De.Writable({write(){}}),P0=()=>new De.Duplex({read(){},write(){}}),$0=async(e,t,r)=>Ne(e,t,r);var Ue=require("node:fs"),Uc=require("node:buffer"),te=require("node:stream");var kc=(e,t)=>Er(N0,e,t,!1),lt=({type:e,optionName:t})=>{throw new TypeError(`The \`${t}\` option cannot be ${fe[e]}.`)},jc={fileNumber:lt,generator:eo,asyncGenerator:eo,nodeStream:({value:e})=>({stream:e}),webTransform({value:{transform:e,writableObjectMode:t,readableObjectMode:r}}){let n=t||r;return{stream:te.Duplex.fromWeb(e,{objectMode:n})}},duplex:({value:{transform:e}})=>({stream:e}),native(){}},N0={input:{...jc,fileUrl:({value:e})=>({stream:(0,Ue.createReadStream)(e)}),filePath:({value:{file:e}})=>({stream:(0,Ue.createReadStream)(e)}),webStream:({value:e})=>({stream:te.Readable.fromWeb(e)}),iterable:({value:e})=>({stream:te.Readable.from(e)}),asyncIterable:({value:e})=>({stream:te.Readable.from(e)}),string:({value:e})=>({stream:te.Readable.from(e)}),uint8Array:({value:e})=>({stream:te.Readable.from(Uc.Buffer.from(e))})},output:{...jc,fileUrl:({value:e})=>({stream:(0,Ue.createWriteStream)(e)}),filePath:({value:{file:e,append:t}})=>({stream:(0,Ue.createWriteStream)(e,t?{flags:"a"}:{})}),webStream:({value:e})=>({stream:te.Writable.fromWeb(e)}),iterable:lt,asyncIterable:lt,string:lt,uint8Array:lt}};var ft=require("node:events"),_r=require("node:stream"),so=require("node:stream/promises");function Se(e){if(!Array.isArray(e))throw new TypeError(`Expected an array, got \`${typeof e}\`.`);for(let o of e)io(o);let t=e.some(({readableObjectMode:o})=>o),r=L0(e,t),n=new oo({objectMode:t,writableHighWaterMark:r,readableHighWaterMark:r});for(let o of e)n.add(o);return n}var L0=(e,t)=>{if(e.length===0)return(0,_r.getDefaultHighWaterMark)(t);let r=e.filter(({readableObjectMode:n})=>n===t).map(({readableHighWaterMark:n})=>n);return Math.max(...r)},oo=class extends _r.PassThrough{#r=new Set([]);#o=new Set([]);#e=new Set([]);#n;#a=Symbol("unpipe");#t=new WeakMap;add(t){if(io(t),this.#r.has(t))return;this.#r.add(t),this.#n??=j0(this,this.#r,this.#a);let r=G0({passThroughStream:this,stream:t,streams:this.#r,ended:this.#o,aborted:this.#e,onFinished:this.#n,unpipeEvent:this.#a});this.#t.set(t,r),t.pipe(this,{end:!1})}async remove(t){if(io(t),!this.#r.has(t))return!1;let r=this.#t.get(t);return r===void 0?!1:(this.#t.delete(t),t.unpipe(this),await r,!0)}},j0=async(e,t,r)=>{Tr(e,Gc);let n=new AbortController;try{await Promise.race([U0(e,n),k0(e,t,r,n)])}finally{n.abort(),Tr(e,-Gc)}},U0=async(e,{signal:t})=>{try{await(0,so.finished)(e,{signal:t,cleanup:!0})}catch(r){throw zc(e,r),r}},k0=async(e,t,r,{signal:n})=>{for await(let[o]of(0,ft.on)(e,"unpipe",{signal:n}))t.has(o)&&o.emit(r)},io=e=>{if(typeof e?.pipe!="function")throw new TypeError(`Expected a readable stream, got: \`${typeof e}\`.`)},G0=async({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,onFinished:i,unpipeEvent:s})=>{Tr(e,Wc);let a=new AbortController;try{await Promise.race([W0(i,t,a),z0({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,controller:a}),V0({stream:t,streams:r,ended:n,aborted:o,unpipeEvent:s,controller:a})])}finally{a.abort(),Tr(e,-Wc)}r.size>0&&r.size===n.size+o.size&&(n.size===0&&o.size>0?ao(e):Y0(e))},W0=async(e,t,{signal:r})=>{try{await e,r.aborted||ao(t)}catch(n){r.aborted||zc(t,n)}},z0=async({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,controller:{signal:i}})=>{try{await(0,so.finished)(t,{signal:i,cleanup:!0,readable:!0,writable:!1}),r.has(t)&&n.add(t)}catch(s){if(i.aborted||!r.has(t))return;Vc(s)?o.add(t):Yc(e,s)}},V0=async({stream:e,streams:t,ended:r,aborted:n,unpipeEvent:o,controller:{signal:i}})=>{if(await(0,ft.once)(e,o,{signal:i}),!e.readable)return(0,ft.once)(i,"abort",{signal:i});t.delete(e),r.delete(e),n.delete(e)},Y0=e=>{e.writable&&e.end()},zc=(e,t)=>{Vc(t)?ao(e):Yc(e,t)},Vc=e=>e?.code==="ERR_STREAM_PREMATURE_CLOSE",ao=e=>{(e.readable||e.writable)&&e.destroy()},Yc=(e,t)=>{e.destroyed||(e.once("error",q0),e.destroy(t))},q0=()=>{},Tr=(e,t)=>{let r=e.getMaxListeners();r!==0&&r!==Number.POSITIVE_INFINITY&&e.setMaxListeners(r+t)},Gc=2,Wc=1;var uo=require("node:stream/promises");var ke=(e,t)=>{e.pipe(t),H0(e,t),K0(e,t)},H0=async(e,t)=>{if(!(G(e)||G(t))){try{await(0,uo.finished)(e,{cleanup:!0,readable:!0,writable:!1})}catch{}co(t)}},co=e=>{e.writable&&e.end()},K0=async(e,t)=>{if(!(G(e)||G(t))){try{await(0,uo.finished)(t,{cleanup:!0,readable:!1,writable:!0})}catch{}lo(e)}},lo=e=>{e.readable&&e.destroy()};var qc=(e,t,r)=>{let n=new Map;for(let[o,{stdioItems:i,direction:s}]of Object.entries(t)){for(let{stream:a}of i.filter(({type:u})=>N.has(u)))X0(e,a,s,o);for(let{stream:a}of i.filter(({type:u})=>!N.has(u)))Z0({subprocess:e,stream:a,direction:s,fdNumber:o,pipeGroups:n,controller:r})}for(let[o,i]of n.entries()){let s=i.length===1?i[0]:Se(i);ke(s,o)}},X0=(e,t,r,n)=>{r==="output"?ke(e.stdio[n],t):ke(t,e.stdio[n]);let o=J0[n];o!==void 0&&(e[o]=t),e.stdio[n]=t},J0=["stdin","stdout","stderr"],Z0=({subprocess:e,stream:t,direction:r,fdNumber:n,pipeGroups:o,controller:i})=>{if(t===void 0)return;Q0(t,i);let[s,a]=r==="output"?[t,e.stdio[n]]:[e.stdio[n],t],u=o.get(s)??[];o.set(s,[...u,a])},Q0=(e,{signal:t})=>{G(e)&&ge(e,eh,t)},eh=2;var Hc=require("node:events");var we=[];we.push("SIGHUP","SIGINT","SIGTERM");process.platform!=="win32"&&we.push("SIGALRM","SIGABRT","SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");process.platform==="linux"&&we.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT");var Or=e=>!!e&&typeof e=="object"&&typeof e.removeListener=="function"&&typeof e.emit=="function"&&typeof e.reallyExit=="function"&&typeof e.listeners=="function"&&typeof e.kill=="function"&&typeof e.pid=="number"&&typeof e.on=="function",fo=Symbol.for("signal-exit emitter"),Do=globalThis,th=Object.defineProperty.bind(Object),po=class{emitted={afterExit:!1,exit:!1};listeners={afterExit:[],exit:[]};count=0;id=Math.random();constructor(){if(Do[fo])return Do[fo];th(Do,fo,{value:this,writable:!1,enumerable:!1,configurable:!1})}on(t,r){this.listeners[t].push(r)}removeListener(t,r){let n=this.listeners[t],o=n.indexOf(r);o!==-1&&(o===0&&n.length===1?n.length=0:n.splice(o,1))}emit(t,r,n){if(this.emitted[t])return!1;this.emitted[t]=!0;let o=!1;for(let i of this.listeners[t])o=i(r,n)===!0||o;return t==="exit"&&(o=this.emit("afterExit",r,n)||o),o}},Rr=class{},rh=e=>({onExit(t,r){return e.onExit(t,r)},load(){return e.load()},unload(){return e.unload()}}),mo=class extends Rr{onExit(){return()=>{}}load(){}unload(){}},ho=class extends Rr{#r=Fo.platform==="win32"?"SIGINT":"SIGHUP";#o=new po;#e;#n;#a;#t={};#s=!1;constructor(t){super(),this.#e=t,this.#t={};for(let r of we)this.#t[r]=()=>{let n=this.#e.listeners(r),{count:o}=this.#o,i=t;if(typeof i.__signal_exit_emitter__=="object"&&typeof i.__signal_exit_emitter__.count=="number"&&(o+=i.__signal_exit_emitter__.count),n.length===o){this.unload();let s=this.#o.emit("exit",null,r),a=r==="SIGHUP"?this.#r:r;s||t.kill(t.pid,a)}};this.#a=t.reallyExit,this.#n=t.emit}onExit(t,r){if(!Or(this.#e))return()=>{};this.#s===!1&&this.load();let n=r?.alwaysLast?"afterExit":"exit";return this.#o.on(n,t),()=>{this.#o.removeListener(n,t),this.#o.listeners.exit.length===0&&this.#o.listeners.afterExit.length===0&&this.unload()}}load(){if(!this.#s){this.#s=!0,this.#o.count+=1;for(let t of we)try{let r=this.#t[t];r&&this.#e.on(t,r)}catch{}this.#e.emit=(t,...r)=>this.#p(t,...r),this.#e.reallyExit=t=>this.#i(t)}}unload(){this.#s&&(this.#s=!1,we.forEach(t=>{let r=this.#t[t];if(!r)throw new Error("Listener not defined for signal: "+t);try{this.#e.removeListener(t,r)}catch{}}),this.#e.emit=this.#n,this.#e.reallyExit=this.#a,this.#o.count-=1)}#i(t){return Or(this.#e)?(this.#e.exitCode=t||0,this.#o.emit("exit",this.#e.exitCode,null),this.#a.call(this.#e,this.#e.exitCode)):0}#p(t,...r){let n=this.#n;if(t==="exit"&&Or(this.#e)){typeof r[0]=="number"&&(this.#e.exitCode=r[0]);let o=n.call(this.#e,t,...r);return this.#o.emit("exit",this.#e.exitCode,null),o}else return n.call(this.#e,t,...r)}},Fo=globalThis.process,{onExit:Ir,load:Yw,unload:qw}=rh(Or(Fo)?new ho(Fo):new mo);var Kc=(e,{cleanup:t,detached:r},{signal:n})=>{if(!t||r)return;let o=Ir(()=>{e.kill()});(0,Hc.addAbortListener)(n,()=>{o()})};var Jc=({source:e,sourcePromise:t,boundOptions:r,createNested:n},...o)=>{let i=$t(),{destination:s,destinationStream:a,destinationError:u,from:l,unpipeSignal:c}=nh(r,n,o),{sourceStream:f,sourceError:D}=ih(e,l),{options:p,fileDescriptors:d}=Z.get(e);return{sourcePromise:t,sourceStream:f,sourceOptions:p,sourceError:D,destination:s,destinationStream:a,destinationError:u,unpipeSignal:c,fileDescriptors:d,startTime:i}},nh=(e,t,r)=>{try{let{destination:n,pipeOptions:{from:o,to:i,unpipeSignal:s}={}}=oh(e,t,...r),a=Yt(n,i);return{destination:n,destinationStream:a,from:o,unpipeSignal:s}}catch(n){return{destinationError:n}}},oh=(e,t,r,...n)=>{if(Array.isArray(r))return{destination:t(Xc,e)(r,...n),pipeOptions:e};if(typeof r=="string"||r instanceof URL||Zr(r)){if(Object.keys(e).length>0)throw new TypeError('Please use .pipe("file", ..., options) or .pipe(execa("file", ..., options)) instead of .pipe(options)("file", ...).');let[o,i,s]=xt(r,...n);return{destination:t(Xc)(o,i,s),pipeOptions:s}}if(Z.has(r)){if(Object.keys(e).length>0)throw new TypeError("Please use .pipe(options)`command` or .pipe($(options)`command`) instead of .pipe(options)($`command`).");return{destination:r,pipeOptions:n[0]}}throw new TypeError(`The first argument must be a template string, an options object, or an Execa subprocess: ${r}`)},Xc=({options:e})=>({options:{...e,stdin:"pipe",piped:!0}}),ih=(e,t)=>{try{return{sourceStream:ve(e,t)}}catch(r){return{sourceError:r}}};var Qc=({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n,fileDescriptors:o,sourceOptions:i,startTime:s})=>{let a=sh({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n});if(a!==void 0)throw go({error:a,fileDescriptors:o,sourceOptions:i,startTime:s})},sh=({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n})=>{if(t!==void 0&&n!==void 0)return n;if(n!==void 0)return lo(e),n;if(t!==void 0)return co(r),t},go=({error:e,fileDescriptors:t,sourceOptions:r,startTime:n})=>$e({error:e,command:Zc,escapedCommand:Zc,fileDescriptors:t,options:r,startTime:n,isSync:!1}),Zc="source.pipe(destination)";var el=async e=>{let[{status:t,reason:r,value:n=r},{status:o,reason:i,value:s=i}]=await e;if(s.pipedFrom.includes(n)||s.pipedFrom.push(n),o==="rejected")throw s;if(t==="rejected")throw n;return s};var tl=require("node:stream/promises");var rl=(e,t,r)=>{let n=vr.has(t)?uh(e,t):ah(e,t);return ge(e,lh,r.signal),ge(t,fh,r.signal),ch(t),n},ah=(e,t)=>{let r=Se([e]);return ke(r,t),vr.set(t,r),r},uh=(e,t)=>{let r=vr.get(t);return r.add(e),r},ch=async e=>{try{await(0,tl.finished)(e,{cleanup:!0,readable:!1,writable:!0})}catch{}vr.delete(e)},vr=new WeakMap,lh=2,fh=1;var nl=require("node:util");var ol=(e,t)=>e===void 0?[]:[Dh(e,t)],Dh=async(e,{sourceStream:t,mergedStream:r,fileDescriptors:n,sourceOptions:o,startTime:i})=>{await(0,nl.aborted)(e,t),await r.remove(t);let s=new Error("Pipe canceled by `unpipeSignal` option.");throw go({error:s,fileDescriptors:n,sourceOptions:o,startTime:i})};var Mr=(e,...t)=>{if(B(t[0]))return Mr.bind(void 0,{...e,boundOptions:{...e.boundOptions,...t[0]}});let{destination:r,...n}=Jc(e,...t),o=ph({...n,destination:r});return o.pipe=Mr.bind(void 0,{...e,source:r,sourcePromise:o,boundOptions:{}}),o},ph=async({sourcePromise:e,sourceStream:t,sourceOptions:r,sourceError:n,destination:o,destinationStream:i,destinationError:s,unpipeSignal:a,fileDescriptors:u,startTime:l})=>{let c=dh(e,o);Qc({sourceStream:t,sourceError:n,destinationStream:i,destinationError:s,fileDescriptors:u,sourceOptions:r,startTime:l});let f=new AbortController;try{let D=rl(t,i,f);return await Promise.race([el(c),...ol(a,{sourceStream:t,mergedStream:D,sourceOptions:r,fileDescriptors:u,startTime:l})])}finally{f.abort()}},dh=(e,t)=>Promise.allSettled([e,t]);var cl=require("node:timers/promises");var sl=require("node:events"),al=require("node:stream");var Pr=({subprocessStdout:e,subprocess:t,binary:r,shouldEncode:n,encoding:o,preserveNewlines:i})=>{let s=new AbortController;return mh(t,s),ul({stream:e,controller:s,binary:r,shouldEncode:!e.readableObjectMode&&n,encoding:o,shouldSplit:!e.readableObjectMode,preserveNewlines:i})},mh=async(e,t)=>{try{await e}catch{}finally{t.abort()}},Eo=({stream:e,onStreamEnd:t,lines:r,encoding:n,stripFinalNewline:o,allMixed:i})=>{let s=new AbortController;hh(t,s,e);let a=e.readableObjectMode&&!i;return ul({stream:e,controller:s,binary:n==="buffer",shouldEncode:!a,encoding:n,shouldSplit:!a&&r,preserveNewlines:!o})},hh=async(e,t,r)=>{try{await e}catch{r.destroy()}finally{t.abort()}},ul=({stream:e,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s})=>{let a=(0,sl.on)(e,"data",{signal:t.signal,highWaterMark:il,highWatermark:il});return Fh({onStdoutChunk:a,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s})},yo=(0,al.getDefaultHighWaterMark)(!0),il=yo,Fh=async function*({onStdoutChunk:e,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s}){let a=gh({binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s});try{for await(let[u]of e)yield*be(u,a,0)}catch(u){if(!t.signal.aborted)throw u}finally{yield*st(a)}},gh=({binary:e,shouldEncode:t,encoding:r,shouldSplit:n,preserveNewlines:o})=>[br(e,r,!t),yr(e,o,!n,{})].filter(Boolean);var ll=async({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,buffer:o,maxBuffer:i,lines:s,allMixed:a,stripFinalNewline:u,verboseInfo:l,streamInfo:c})=>{let f=Eh({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,allMixed:a,verboseInfo:l,streamInfo:c});if(!o){await Promise.all([yh(e),f]);return}let D=Hn(u,r),p=Eo({stream:e,onStreamEnd:t,lines:s,encoding:n,stripFinalNewline:D,allMixed:a}),[d]=await Promise.all([bh({stream:e,iterable:p,fdNumber:r,encoding:n,maxBuffer:i,lines:s}),f]);return d},Eh=async({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,allMixed:o,verboseInfo:i,streamInfo:{fileDescriptors:s}})=>{if(!Cr({stdioItems:s[r]?.stdioItems,encoding:n,verboseInfo:i,fdNumber:r}))return;let a=Eo({stream:e,onStreamEnd:t,lines:!0,encoding:n,stripFinalNewline:!0,allMixed:o});await yc(a,e,r,i)},yh=async e=>{await(0,cl.setImmediate)(),e.readableFlowing===null&&e.resume()},bh=async({stream:e,stream:{readableObjectMode:t},iterable:r,fdNumber:n,encoding:o,maxBuffer:i,lines:s})=>{try{return t||s?await cr(r,{maxBuffer:i}):o==="buffer"?new Uint8Array(await lr(r,{maxBuffer:i})):await Dr(r,{maxBuffer:i})}catch(a){return fl(Fu({error:a,stream:e,readableObjectMode:t,lines:s,encoding:o,fdNumber:n}))}},bo=async e=>{try{return await e}catch(t){return fl(t)}},fl=({bufferedData:e})=>fi(e)?new Uint8Array(e):e;var pl=require("node:stream/promises"),Dt=async(e,t,r,{isSameDirection:n,stopOnExit:o=!1}={})=>{let i=Sh(e,r),s=new AbortController;try{await Promise.race([...o?[r.exitPromise]:[],(0,pl.finished)(e,{cleanup:!0,signal:s.signal})])}catch(a){i.stdinCleanedUp||xh(a,t,r,n)}finally{s.abort()}},Sh=(e,{originalStreams:[t],subprocess:r})=>{let n={stdinCleanedUp:!1};return e===t&&wh(e,r,n),n},wh=(e,t,r)=>{let{_destroy:n}=e;e._destroy=(...o)=>{Ch(t,r),n.call(e,...o)}},Ch=({exitCode:e,signalCode:t},r)=>{(e!==null||t!==null)&&(r.stdinCleanedUp=!0)},xh=(e,t,r,n)=>{if(!Ah(e,t,r,n))throw e},Ah=(e,t,r,n=!0)=>r.propagating?Dl(e)||$r(e):(r.propagating=!0,So(r,t)===n?Dl(e):$r(e)),So=({fileDescriptors:e},t)=>t!=="all"&&e[t].direction==="input",$r=e=>e?.code==="ERR_STREAM_PREMATURE_CLOSE",Dl=e=>e?.code==="EPIPE";var dl=({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:i,verboseInfo:s,streamInfo:a})=>e.stdio.map((u,l)=>wo({stream:u,fdNumber:l,encoding:t,buffer:r[l],maxBuffer:n[l],lines:o[l],allMixed:!1,stripFinalNewline:i,verboseInfo:s,streamInfo:a})),wo=async({stream:e,fdNumber:t,encoding:r,buffer:n,maxBuffer:o,lines:i,allMixed:s,stripFinalNewline:a,verboseInfo:u,streamInfo:l})=>{if(!e)return;let c=Dt(e,t,l);if(So(l,t)){await c;return}let[f]=await Promise.all([ll({stream:e,onStreamEnd:c,fdNumber:t,encoding:r,buffer:n,maxBuffer:o,lines:i,allMixed:s,stripFinalNewline:a,verboseInfo:u,streamInfo:l}),c]);return f};var ml=({stdout:e,stderr:t},{all:r})=>r&&(e||t)?Se([e,t].filter(Boolean)):void 0,hl=({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:i,verboseInfo:s,streamInfo:a})=>wo({...Bh(e,r),fdNumber:"all",encoding:t,maxBuffer:n[1]+n[2],lines:o[1]||o[2],allMixed:Th(e),stripFinalNewline:i,verboseInfo:s,streamInfo:a}),Bh=({stdout:e,stderr:t,all:r},[,n,o])=>{let i=n||o;return i?n?o?{stream:r,buffer:i}:{stream:e,buffer:i}:{stream:t,buffer:i}:{stream:r,buffer:i}},Th=({all:e,stdout:t,stderr:r})=>e&&t&&r&&t.readableObjectMode!==r.readableObjectMode;var bl=require("node:events");var Fl=e=>Be(e,"ipc"),gl=(e,t)=>{let r=Pt(e);X({type:"ipc",verboseMessage:r,fdNumber:"ipc",verboseInfo:t})};var El=async({subprocess:e,buffer:t,maxBuffer:r,ipc:n,ipcOutput:o,verboseInfo:i})=>{if(!n)return o;let s=Fl(i),a=ne(t,"ipc"),u=ne(r,"ipc");for await(let l of ro({anyProcess:e,channel:e.channel,isSubprocess:!1,ipc:n,shouldAwait:!1,reference:!0}))a&&(gu(e,o,u),o.push(l)),s&&gl(l,i);return o},yl=async(e,t)=>(await Promise.allSettled([e]),t);var Sl=async({subprocess:e,options:{encoding:t,buffer:r,maxBuffer:n,lines:o,timeoutDuration:i,cancelSignal:s,gracefulCancel:a,forceKillAfterDelay:u,stripFinalNewline:l,ipc:c,ipcInput:f},context:D,verboseInfo:p,fileDescriptors:d,originalStreams:g,onInternalError:w,controller:y})=>{let A=xc(e,D),T={originalStreams:g,fileDescriptors:d,subprocess:e,exitPromise:A,propagating:!1},R=dl({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:l,verboseInfo:p,streamInfo:T}),k=hl({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:l,verboseInfo:p,streamInfo:T}),K=[],Fe=El({subprocess:e,buffer:r,maxBuffer:n,ipc:c,ipcOutput:K,verboseInfo:p}),Ce=_h(g,e,T),ae=Oh(d,T);try{return await Promise.race([Promise.all([{},Bc(A),Promise.all(R),k,Fe,Va(e,f),...Ce,...ae]),w,Rh(e,y),...Ua(e,i,D,y),...Qs({subprocess:e,cancelSignal:s,gracefulCancel:a,context:D,controller:y}),...Na({subprocess:e,cancelSignal:s,gracefulCancel:a,forceKillAfterDelay:u,context:D,controller:y})])}catch(Jr){return D.terminationReason??="other",Promise.all([{error:Jr},A,Promise.all(R.map(He=>bo(He))),bo(k),yl(Fe,K),Promise.allSettled(Ce),Promise.allSettled(ae)])}},_h=(e,t,r)=>e.map((n,o)=>n===t.stdio[o]?void 0:Dt(n,o,r)),Oh=(e,t)=>e.flatMap(({stdioItems:r},n)=>r.filter(({value:o,stream:i=o})=>V(i,{checkOpen:!1})&&!G(i)).map(({type:o,value:i,stream:s=i})=>Dt(s,n,t,{isSameDirection:N.has(o),stopOnExit:o==="native"}))),Rh=async(e,{signal:t})=>{let[r]=await(0,bl.once)(e,"error",{signal:t});throw r};var wl=()=>({readableDestroy:new WeakMap,writableFinal:new WeakMap,writableDestroy:new WeakMap}),pt=(e,t,r)=>{let n=e[r];n.has(t)||n.set(t,[]);let o=n.get(t),i=J();return o.push(i),{resolve:i.resolve.bind(i),promises:o}},Ge=async({resolve:e,promises:t},r)=>{e();let[n]=await Promise.race([Promise.allSettled([!0,r]),Promise.all([!1,...t])]);return!n};var xl=require("node:stream"),Al=require("node:util");var Co=require("node:stream/promises");var xo=async e=>{if(e!==void 0)try{await Ao(e)}catch{}},Cl=async e=>{if(e!==void 0)try{await Bo(e)}catch{}},Ao=async e=>{await(0,Co.finished)(e,{cleanup:!0,readable:!1,writable:!0})},Bo=async e=>{await(0,Co.finished)(e,{cleanup:!0,readable:!0,writable:!1})},Nr=async(e,t)=>{if(await e,t)throw t},Lr=(e,t,r)=>{r&&!$r(r)?e.destroy(r):t&&e.destroy()};var Bl=({subprocess:e,concurrentStreams:t,encoding:r},{from:n,binary:o=!0,preserveNewlines:i=!0}={})=>{let s=o||P.has(r),{subprocessStdout:a,waitReadableDestroy:u}=To(e,n,t),{readableEncoding:l,readableObjectMode:c,readableHighWaterMark:f}=_o(a,s),{read:D,onStdoutDataDone:p}=Oo({subprocessStdout:a,subprocess:e,binary:s,encoding:r,preserveNewlines:i}),d=new xl.Readable({read:D,destroy:(0,Al.callbackify)(Io.bind(void 0,{subprocessStdout:a,subprocess:e,waitReadableDestroy:u})),highWaterMark:f,objectMode:c,encoding:l});return Ro({subprocessStdout:a,onStdoutDataDone:p,readable:d,subprocess:e}),d},To=(e,t,r)=>{let n=ve(e,t),o=pt(r,n,"readableDestroy");return{subprocessStdout:n,waitReadableDestroy:o}},_o=({readableEncoding:e,readableObjectMode:t,readableHighWaterMark:r},n)=>n?{readableEncoding:e,readableObjectMode:t,readableHighWaterMark:r}:{readableEncoding:e,readableObjectMode:!0,readableHighWaterMark:yo},Oo=({subprocessStdout:e,subprocess:t,binary:r,encoding:n,preserveNewlines:o})=>{let i=J(),s=Pr({subprocessStdout:e,subprocess:t,binary:r,shouldEncode:!r,encoding:n,preserveNewlines:o});return{read(){Ih(this,s,i)},onStdoutDataDone:i}},Ih=async(e,t,r)=>{try{let{value:n,done:o}=await t.next();o?r.resolve():e.push(n)}catch{}},Ro=async({subprocessStdout:e,onStdoutDataDone:t,readable:r,subprocess:n,subprocessStdin:o})=>{try{await Bo(e),await n,await xo(o),await t,r.readable&&r.push(null)}catch(i){await xo(o),Tl(r,i)}},Io=async({subprocessStdout:e,subprocess:t,waitReadableDestroy:r},n)=>{await Ge(r,t)&&(Tl(e,n),await Nr(t,n))},Tl=(e,t)=>{Lr(e,e.readable,t)};var _l=require("node:stream"),vo=require("node:util");var Ol=({subprocess:e,concurrentStreams:t},{to:r}={})=>{let{subprocessStdin:n,waitWritableFinal:o,waitWritableDestroy:i}=Mo(e,r,t),s=new _l.Writable({...Po(n,e,o),destroy:(0,vo.callbackify)(No.bind(void 0,{subprocessStdin:n,subprocess:e,waitWritableFinal:o,waitWritableDestroy:i})),highWaterMark:n.writableHighWaterMark,objectMode:n.writableObjectMode});return $o(n,s),s},Mo=(e,t,r)=>{let n=Yt(e,t),o=pt(r,n,"writableFinal"),i=pt(r,n,"writableDestroy");return{subprocessStdin:n,waitWritableFinal:o,waitWritableDestroy:i}},Po=(e,t,r)=>({write:vh.bind(void 0,e),final:(0,vo.callbackify)(Mh.bind(void 0,e,t,r))}),vh=(e,t,r,n)=>{e.write(t,r)?n():e.once("drain",n)},Mh=async(e,t,r)=>{await Ge(r,t)&&(e.writable&&e.end(),await t)},$o=async(e,t,r)=>{try{await Ao(e),t.writable&&t.end()}catch(n){await Cl(r),Rl(t,n)}},No=async({subprocessStdin:e,subprocess:t,waitWritableFinal:r,waitWritableDestroy:n},o)=>{await Ge(r,t),await Ge(n,t)&&(Rl(e,o),await Nr(t,o))},Rl=(e,t)=>{Lr(e,e.writable,t)};var Il=require("node:stream"),vl=require("node:util");var Ml=({subprocess:e,concurrentStreams:t,encoding:r},{from:n,to:o,binary:i=!0,preserveNewlines:s=!0}={})=>{let a=i||P.has(r),{subprocessStdout:u,waitReadableDestroy:l}=To(e,n,t),{subprocessStdin:c,waitWritableFinal:f,waitWritableDestroy:D}=Mo(e,o,t),{readableEncoding:p,readableObjectMode:d,readableHighWaterMark:g}=_o(u,a),{read:w,onStdoutDataDone:y}=Oo({subprocessStdout:u,subprocess:e,binary:a,encoding:r,preserveNewlines:s}),A=new Il.Duplex({read:w,...Po(c,e,f),destroy:(0,vl.callbackify)(Ph.bind(void 0,{subprocessStdout:u,subprocessStdin:c,subprocess:e,waitReadableDestroy:l,waitWritableFinal:f,waitWritableDestroy:D})),readableHighWaterMark:g,writableHighWaterMark:c.writableHighWaterMark,readableObjectMode:d,writableObjectMode:c.writableObjectMode,encoding:p});return Ro({subprocessStdout:u,onStdoutDataDone:y,readable:A,subprocess:e,subprocessStdin:c}),$o(c,A,u),A},Ph=async({subprocessStdout:e,subprocessStdin:t,subprocess:r,waitReadableDestroy:n,waitWritableFinal:o,waitWritableDestroy:i},s)=>{await Promise.all([Io({subprocessStdout:e,subprocess:r,waitReadableDestroy:n},s),No({subprocessStdin:t,subprocess:r,waitWritableFinal:o,waitWritableDestroy:i},s)])};var Lo=(e,t,{from:r,binary:n=!1,preserveNewlines:o=!1}={})=>{let i=n||P.has(t),s=ve(e,r),a=Pr({subprocessStdout:s,subprocess:e,binary:i,shouldEncode:!0,encoding:t,preserveNewlines:o});return $h(a,s,e)},$h=async function*(e,t,r){try{yield*e}finally{t.readable&&t.destroy(),await r}};var Pl=(e,{encoding:t})=>{let r=wl();e.readable=Bl.bind(void 0,{subprocess:e,concurrentStreams:r,encoding:t}),e.writable=Ol.bind(void 0,{subprocess:e,concurrentStreams:r}),e.duplex=Ml.bind(void 0,{subprocess:e,concurrentStreams:r,encoding:t}),e.iterable=Lo.bind(void 0,e,t),e[Symbol.asyncIterator]=Lo.bind(void 0,e,t,{})};var $l=(e,t)=>{for(let[r,n]of Lh){let o=n.value.bind(t);Reflect.defineProperty(e,r,{...n,value:o})}},Nh=(async()=>{})().constructor.prototype,Lh=["then","catch","finally"].map(e=>[e,Reflect.getOwnPropertyDescriptor(Nh,e)]);var jl=(e,t,r,n)=>{let{file:o,commandArguments:i,command:s,escapedCommand:a,startTime:u,verboseInfo:l,options:c,fileDescriptors:f}=jh(e,t,r),{subprocess:D,promise:p}=kh({file:o,commandArguments:i,options:c,startTime:u,verboseInfo:l,command:s,escapedCommand:a,fileDescriptors:f});return D.pipe=Mr.bind(void 0,{source:D,sourcePromise:p,boundOptions:{},createNested:n}),$l(D,p),Z.set(D,{options:c,fileDescriptors:f}),D},jh=(e,t,r)=>{let{command:n,escapedCommand:o,startTime:i,verboseInfo:s}=Nt(e,t,r),{file:a,commandArguments:u,options:l}=or(e,t,r),c=Uh(l),f=kc(c,s);return{file:a,commandArguments:u,command:n,escapedCommand:o,startTime:i,verboseInfo:s,options:c,fileDescriptors:f}},Uh=({timeout:e,signal:t,...r})=>{if(t!==void 0)throw new TypeError('The "signal" option has been renamed to "cancelSignal" instead.');return{...r,timeoutDuration:e}},kh=({file:e,commandArguments:t,options:r,startTime:n,verboseInfo:o,command:i,escapedCommand:s,fileDescriptors:a})=>{let u;try{u=(0,Ll.spawn)(...ir(e,t,r))}catch(d){return Lc({error:d,command:i,escapedCommand:s,fileDescriptors:a,options:r,startTime:n,verboseInfo:o})}let l=new AbortController;(0,Nl.setMaxListeners)(Number.POSITIVE_INFINITY,l.signal);let c=[...u.stdio];qc(u,a,l),Kc(u,r,l);let f={},D=J();u.kill=Xs.bind(void 0,{kill:u.kill.bind(u),options:r,onInternalError:D,context:f,controller:l}),u.all=ml(u,r),Pl(u,r),Mc(u,r);let p=Gh({subprocess:u,options:r,startTime:n,verboseInfo:o,fileDescriptors:a,originalStreams:c,command:i,escapedCommand:s,context:f,onInternalError:D,controller:l});return{subprocess:u,promise:p}},Gh=async({subprocess:e,options:t,startTime:r,verboseInfo:n,fileDescriptors:o,originalStreams:i,command:s,escapedCommand:a,context:u,onInternalError:l,controller:c})=>{let[f,[D,p],d,g,w]=await Sl({subprocess:e,options:t,context:u,verboseInfo:n,fileDescriptors:o,originalStreams:i,onInternalError:l,controller:c});c.abort(),l.resolve();let y=d.map((R,k)=>ie(R,t,k)),A=ie(g,t,"all"),T=Wh({errorInfo:f,exitCode:D,signal:p,stdio:y,all:A,ipcOutput:w,context:u,options:t,command:s,escapedCommand:a,startTime:r});return Ne(T,n,t)},Wh=({errorInfo:e,exitCode:t,signal:r,stdio:n,all:o,ipcOutput:i,context:s,options:a,command:u,escapedCommand:l,startTime:c})=>"error"in e?it({error:e.error,command:u,escapedCommand:l,timedOut:s.terminationReason==="timeout",isCanceled:s.terminationReason==="cancel"||s.terminationReason==="gracefulCancel",isGracefullyCanceled:s.terminationReason==="gracefulCancel",isMaxBuffer:e.error instanceof Q,isForcefullyTerminated:s.isForcefullyTerminated,exitCode:t,signal:r,stdio:n,all:o,ipcOutput:i,options:a,startTime:c,isSync:!1}):dr({command:u,escapedCommand:l,stdio:n,all:o,ipcOutput:i,options:a,startTime:c});var jr=(e,t)=>{let r=Object.fromEntries(Object.entries(t).map(([n,o])=>[n,zh(n,e[n],o)]));return{...e,...r}},zh=(e,t,r)=>Vh.has(e)&&B(t)&&B(r)?{...t,...r}:r,Vh=new Set(["env",...nn]);var pe=(e,t,r,n)=>{let o=(s,a,u)=>pe(s,a,r,u),i=(...s)=>Yh({mapArguments:e,deepOptions:r,boundOptions:t,setBoundExeca:n,createNested:o},...s);return n!==void 0&&n(i,o,t),i},Yh=({mapArguments:e,deepOptions:t={},boundOptions:r={},setBoundExeca:n,createNested:o},i,...s)=>{if(B(i))return o(e,jr(r,i),n);let{file:a,commandArguments:u,options:l,isSync:c}=qh({mapArguments:e,firstArgument:i,nextArguments:s,deepOptions:t,boundOptions:r});return c?Oc(a,u,l):jl(a,u,l,o)},qh=({mapArguments:e,firstArgument:t,nextArguments:r,deepOptions:n,boundOptions:o})=>{let i=gi(t)?Ei(t,r):[t,...r],[s,a,u]=xt(...i),l=jr(jr(n,o),u),{file:c=s,commandArguments:f=a,options:D=l,isSync:p=!1}=e({file:s,commandArguments:a,options:l});return{file:c,commandArguments:f,options:D,isSync:p}};var Ul=({file:e,commandArguments:t})=>Gl(e,t),kl=({file:e,commandArguments:t})=>({...Gl(e,t),isSync:!0}),Gl=(e,t)=>{if(t.length>0)throw new TypeError(`The command and its arguments must be passed as a single string: ${e} ${t}.`);let[r,...n]=Hh(e);return{file:r,commandArguments:n}},Hh=e=>{if(typeof e!="string")throw new TypeError(`The command must be a string: ${String(e)}.`);let t=e.trim();if(t==="")return[];let r=[];for(let n of t.split(Kh)){let o=r.at(-1);o&&o.endsWith("\\")?r[r.length-1]=`${o.slice(0,-1)} ${n}`:r.push(n)}return r},Kh=/ +/g;var Wl=(e,t,r)=>{e.sync=t(Xh,r),e.s=e.sync},zl=({options:e})=>Vl(e),Xh=({options:e})=>({...Vl(e),isSync:!0}),Vl=e=>({options:{...Jh(e),...e}}),Jh=({input:e,inputFile:t,stdio:r})=>e===void 0&&t===void 0&&r===void 0?{stdin:"inherit"}:{},Yl={preferLocal:!0};var jo=pe(()=>({})),E1=pe(()=>({isSync:!0})),y1=pe(Ul),b1=pe(kl),S1=pe(ka),w1=pe(zl,{},Yl,Wl),{sendMessage:C1,getOneMessage:x1,getEachMessage:A1,getCancelSignal:B1}=Pc();var I=E(Hl());var bt=E(require("node:process"),1);var Kl=(e=0)=>t=>`\x1B[${t+e}m`,Xl=(e=0)=>t=>`\x1B[${38+e};5;${t}m`,Jl=(e=0)=>(t,r,n)=>`\x1B[${38+e};2;${t};${r};${n}m`,b={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],gray:[90,39],grey:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgGray:[100,49],bgGrey:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}},O1=Object.keys(b.modifier),eF=Object.keys(b.color),tF=Object.keys(b.bgColor),R1=[...eF,...tF];function rF(){let e=new Map;for(let[t,r]of Object.entries(b)){for(let[n,o]of Object.entries(r))b[n]={open:`\x1B[${o[0]}m`,close:`\x1B[${o[1]}m`},r[n]=b[n],e.set(o[0],o[1]);Object.defineProperty(b,t,{value:r,enumerable:!1})}return Object.defineProperty(b,"codes",{value:e,enumerable:!1}),b.color.close="\x1B[39m",b.bgColor.close="\x1B[49m",b.color.ansi=Kl(),b.color.ansi256=Xl(),b.color.ansi16m=Jl(),b.bgColor.ansi=Kl(10),b.bgColor.ansi256=Xl(10),b.bgColor.ansi16m=Jl(10),Object.defineProperties(b,{rgbToAnsi256:{value(t,r,n){return t===r&&r===n?t<8?16:t>248?231:Math.round((t-8)/247*24)+232:16+36*Math.round(t/255*5)+6*Math.round(r/255*5)+Math.round(n/255*5)},enumerable:!1},hexToRgb:{value(t){let r=/[a-f\d]{6}|[a-f\d]{3}/i.exec(t.toString(16));if(!r)return[0,0,0];let[n]=r;n.length===3&&(n=[...n].map(i=>i+i).join(""));let o=Number.parseInt(n,16);return[o>>16&255,o>>8&255,o&255]},enumerable:!1},hexToAnsi256:{value:t=>b.rgbToAnsi256(...b.hexToRgb(t)),enumerable:!1},ansi256ToAnsi:{value(t){if(t<8)return 30+t;if(t<16)return 90+(t-8);let r,n,o;if(t>=232)r=((t-232)*10+8)/255,n=r,o=r;else{t-=16;let a=t%36;r=Math.floor(t/36)/5,n=Math.floor(a/6)/5,o=a%6/5}let i=Math.max(r,n,o)*2;if(i===0)return 30;let s=30+(Math.round(o)<<2|Math.round(n)<<1|Math.round(r));return i===2&&(s+=60),s},enumerable:!1},rgbToAnsi:{value:(t,r,n)=>b.ansi256ToAnsi(b.rgbToAnsi256(t,r,n)),enumerable:!1},hexToAnsi:{value:t=>b.ansi256ToAnsi(b.hexToAnsi256(t)),enumerable:!1}}),b}var nF=rF(),Y=nF;var kr=E(require("node:process"),1),Ql=E(require("node:os"),1),Uo=E(require("node:tty"),1);function L(e,t=globalThis.Deno?globalThis.Deno.args:kr.default.argv){let r=e.startsWith("-")?"":e.length===1?"-":"--",n=t.indexOf(r+e),o=t.indexOf("--");return n!==-1&&(o===-1||n<o)}var{env:C}=kr.default,Ur;L("no-color")||L("no-colors")||L("color=false")||L("color=never")?Ur=0:(L("color")||L("colors")||L("color=true")||L("color=always"))&&(Ur=1);function oF(){if("FORCE_COLOR"in C)return C.FORCE_COLOR==="true"?1:C.FORCE_COLOR==="false"?0:C.FORCE_COLOR.length===0?1:Math.min(Number.parseInt(C.FORCE_COLOR,10),3)}function iF(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function sF(e,{streamIsTTY:t,sniffFlags:r=!0}={}){let n=oF();n!==void 0&&(Ur=n);let o=r?Ur:n;if(o===0)return 0;if(r){if(L("color=16m")||L("color=full")||L("color=truecolor"))return 3;if(L("color=256"))return 2}if("TF_BUILD"in C&&"AGENT_NAME"in C)return 1;if(e&&!t&&o===void 0)return 0;let i=o||0;if(C.TERM==="dumb")return i;if(kr.default.platform==="win32"){let s=Ql.default.release().split(".");return Number(s[0])>=10&&Number(s[2])>=10586?Number(s[2])>=14931?3:2:1}if("CI"in C)return"GITHUB_ACTIONS"in C||"GITEA_ACTIONS"in C?3:["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","BUILDKITE","DRONE"].some(s=>s in C)||C.CI_NAME==="codeship"?1:i;if("TEAMCITY_VERSION"in C)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(C.TEAMCITY_VERSION)?1:0;if(C.COLORTERM==="truecolor"||C.TERM==="xterm-kitty")return 3;if("TERM_PROGRAM"in C){let s=Number.parseInt((C.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(C.TERM_PROGRAM){case"iTerm.app":return s>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(C.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(C.TERM)||"COLORTERM"in C?1:i}function Zl(e,t={}){let r=sF(e,{streamIsTTY:e&&e.isTTY,...t});return iF(r)}var aF={stdout:Zl({isTTY:Uo.default.isatty(1)}),stderr:Zl({isTTY:Uo.default.isatty(2)})},ef=aF;function tf(e,t,r){let n=e.indexOf(t);if(n===-1)return e;let o=t.length,i=0,s="";do s+=e.slice(i,n)+t+r,i=n+o,n=e.indexOf(t,i);while(n!==-1);return s+=e.slice(i),s}function rf(e,t,r,n){let o=0,i="";do{let s=e[n-1]==="\r";i+=e.slice(o,s?n-1:n)+t+(s?`\r
`:`
`)+r,o=n+1,n=e.indexOf(`
`,o)}while(n!==-1);return i+=e.slice(o),i}var{stdout:nf,stderr:of}=ef,ko=Symbol("GENERATOR"),We=Symbol("STYLER"),dt=Symbol("IS_EMPTY"),sf=["ansi","ansi","ansi256","ansi16m"],ze=Object.create(null),uF=(e,t={})=>{if(t.level&&!(Number.isInteger(t.level)&&t.level>=0&&t.level<=3))throw new Error("The `level` option should be an integer from 0 to 3");let r=nf?nf.level:0;e.level=t.level===void 0?r:t.level};var cF=e=>{let t=(...r)=>r.join(" ");return uF(t,e),Object.setPrototypeOf(t,mt.prototype),t};function mt(e){return cF(e)}Object.setPrototypeOf(mt.prototype,Function.prototype);for(let[e,t]of Object.entries(Y))ze[e]={get(){let r=Gr(this,Wo(t.open,t.close,this[We]),this[dt]);return Object.defineProperty(this,e,{value:r}),r}};ze.visible={get(){let e=Gr(this,this[We],!0);return Object.defineProperty(this,"visible",{value:e}),e}};var Go=(e,t,r,...n)=>e==="rgb"?t==="ansi16m"?Y[r].ansi16m(...n):t==="ansi256"?Y[r].ansi256(Y.rgbToAnsi256(...n)):Y[r].ansi(Y.rgbToAnsi(...n)):e==="hex"?Go("rgb",t,r,...Y.hexToRgb(...n)):Y[r][e](...n),lF=["rgb","hex","ansi256"];for(let e of lF){ze[e]={get(){let{level:r}=this;return function(...n){let o=Wo(Go(e,sf[r],"color",...n),Y.color.close,this[We]);return Gr(this,o,this[dt])}}};let t="bg"+e[0].toUpperCase()+e.slice(1);ze[t]={get(){let{level:r}=this;return function(...n){let o=Wo(Go(e,sf[r],"bgColor",...n),Y.bgColor.close,this[We]);return Gr(this,o,this[dt])}}}}var fF=Object.defineProperties(()=>{},{...ze,level:{enumerable:!0,get(){return this[ko].level},set(e){this[ko].level=e}}}),Wo=(e,t,r)=>{let n,o;return r===void 0?(n=e,o=t):(n=r.openAll+e,o=t+r.closeAll),{open:e,close:t,openAll:n,closeAll:o,parent:r}},Gr=(e,t,r)=>{let n=(...o)=>DF(n,o.length===1?""+o[0]:o.join(" "));return Object.setPrototypeOf(n,fF),n[ko]=e,n[We]=t,n[dt]=r,n},DF=(e,t)=>{if(e.level<=0||!t)return e[dt]?"":t;let r=e[We];if(r===void 0)return t;let{openAll:n,closeAll:o}=r;if(t.includes("\x1B"))for(;r!==void 0;)t=tf(t,r.close,r.open),r=r.parent;let i=t.indexOf(`
`);return i!==-1&&(t=rf(t,o,n,i)),n+t+o};Object.defineProperties(mt.prototype,ze);var pF=mt(),L1=mt({level:of?of.level:0});var af=pF;var Vo=E(require("node:process"),1);var ht=E(require("node:process"),1);var dF=(e,t,r,n)=>{if(r==="length"||r==="prototype"||r==="arguments"||r==="caller")return;let o=Object.getOwnPropertyDescriptor(e,r),i=Object.getOwnPropertyDescriptor(t,r);!mF(o,i)&&n||Object.defineProperty(e,r,i)},mF=function(e,t){return e===void 0||e.configurable||e.writable===t.writable&&e.enumerable===t.enumerable&&e.configurable===t.configurable&&(e.writable||e.value===t.value)},hF=(e,t)=>{let r=Object.getPrototypeOf(t);r!==Object.getPrototypeOf(e)&&Object.setPrototypeOf(e,r)},FF=(e,t)=>`/* Wrapped ${e}*/
${t}`,gF=Object.getOwnPropertyDescriptor(Function.prototype,"toString"),EF=Object.getOwnPropertyDescriptor(Function.prototype.toString,"name"),yF=(e,t,r)=>{let n=r===""?"":`with ${r.trim()}() `,o=FF.bind(null,n,t.toString());Object.defineProperty(o,"name",EF);let{writable:i,enumerable:s,configurable:a}=gF;Object.defineProperty(e,"toString",{value:o,writable:i,enumerable:s,configurable:a})};function zo(e,t,{ignoreNonConfigurable:r=!1}={}){let{name:n}=e;for(let o of Reflect.ownKeys(t))dF(e,t,o,r);return hF(e,t),yF(e,t,n),e}var Wr=new WeakMap,uf=(e,t={})=>{if(typeof e!="function")throw new TypeError("Expected a function");let r,n=0,o=e.displayName||e.name||"<anonymous>",i=function(...s){if(Wr.set(i,++n),n===1)r=e.apply(this,s),e=void 0;else if(t.throw===!0)throw new Error(`Function \`${o}\` can only be called once`);return r};return zo(i,e),Wr.set(i,n),i};uf.callCount=e=>{if(!Wr.has(e))throw new Error(`The given function \`${e.name}\` is not wrapped by the \`onetime\` package`);return Wr.get(e)};var cf=uf;var lf=ht.default.stderr.isTTY?ht.default.stderr:ht.default.stdout.isTTY?ht.default.stdout:void 0,bF=lf?cf(()=>{Ir(()=>{lf.write("\x1B[?25h")},{alwaysLast:!0})}):()=>{},ff=bF;var zr=!1,Ve={};Ve.show=(e=Vo.default.stderr)=>{e.isTTY&&(zr=!1,e.write("\x1B[?25h"))};Ve.hide=(e=Vo.default.stderr)=>{e.isTTY&&(ff(),zr=!0,e.write("\x1B[?25l"))};Ve.toggle=(e,t)=>{e!==void 0&&(zr=e),zr?Ve.show(t):Ve.hide(t)};var Yo=Ve;var St=E(qo(),1);var mf=(e=0)=>t=>`\x1B[${t+e}m`,hf=(e=0)=>t=>`\x1B[${38+e};5;${t}m`,Ff=(e=0)=>(t,r,n)=>`\x1B[${38+e};2;${t};${r};${n}m`,S={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],gray:[90,39],grey:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgGray:[100,49],bgGrey:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}},X1=Object.keys(S.modifier),wF=Object.keys(S.color),CF=Object.keys(S.bgColor),J1=[...wF,...CF];function xF(){let e=new Map;for(let[t,r]of Object.entries(S)){for(let[n,o]of Object.entries(r))S[n]={open:`\x1B[${o[0]}m`,close:`\x1B[${o[1]}m`},r[n]=S[n],e.set(o[0],o[1]);Object.defineProperty(S,t,{value:r,enumerable:!1})}return Object.defineProperty(S,"codes",{value:e,enumerable:!1}),S.color.close="\x1B[39m",S.bgColor.close="\x1B[49m",S.color.ansi=mf(),S.color.ansi256=hf(),S.color.ansi16m=Ff(),S.bgColor.ansi=mf(10),S.bgColor.ansi256=hf(10),S.bgColor.ansi16m=Ff(10),Object.defineProperties(S,{rgbToAnsi256:{value(t,r,n){return t===r&&r===n?t<8?16:t>248?231:Math.round((t-8)/247*24)+232:16+36*Math.round(t/255*5)+6*Math.round(r/255*5)+Math.round(n/255*5)},enumerable:!1},hexToRgb:{value(t){let r=/[a-f\d]{6}|[a-f\d]{3}/i.exec(t.toString(16));if(!r)return[0,0,0];let[n]=r;n.length===3&&(n=[...n].map(i=>i+i).join(""));let o=Number.parseInt(n,16);return[o>>16&255,o>>8&255,o&255]},enumerable:!1},hexToAnsi256:{value:t=>S.rgbToAnsi256(...S.hexToRgb(t)),enumerable:!1},ansi256ToAnsi:{value(t){if(t<8)return 30+t;if(t<16)return 90+(t-8);let r,n,o;if(t>=232)r=((t-232)*10+8)/255,n=r,o=r;else{t-=16;let a=t%36;r=Math.floor(t/36)/5,n=Math.floor(a/6)/5,o=a%6/5}let i=Math.max(r,n,o)*2;if(i===0)return 30;let s=30+(Math.round(o)<<2|Math.round(n)<<1|Math.round(r));return i===2&&(s+=60),s},enumerable:!1},rgbToAnsi:{value:(t,r,n)=>S.ansi256ToAnsi(S.rgbToAnsi256(t,r,n)),enumerable:!1},hexToAnsi:{value:t=>S.ansi256ToAnsi(S.hexToAnsi256(t)),enumerable:!1}}),S}var AF=xF(),q=AF;var qr=E(require("node:process"),1),Ef=E(require("node:os"),1),Ho=E(require("node:tty"),1);function j(e,t=globalThis.Deno?globalThis.Deno.args:qr.default.argv){let r=e.startsWith("-")?"":e.length===1?"-":"--",n=t.indexOf(r+e),o=t.indexOf("--");return n!==-1&&(o===-1||n<o)}var{env:x}=qr.default,Yr;j("no-color")||j("no-colors")||j("color=false")||j("color=never")?Yr=0:(j("color")||j("colors")||j("color=true")||j("color=always"))&&(Yr=1);function BF(){if("FORCE_COLOR"in x)return x.FORCE_COLOR==="true"?1:x.FORCE_COLOR==="false"?0:x.FORCE_COLOR.length===0?1:Math.min(Number.parseInt(x.FORCE_COLOR,10),3)}function TF(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function _F(e,{streamIsTTY:t,sniffFlags:r=!0}={}){let n=BF();n!==void 0&&(Yr=n);let o=r?Yr:n;if(o===0)return 0;if(r){if(j("color=16m")||j("color=full")||j("color=truecolor"))return 3;if(j("color=256"))return 2}if("TF_BUILD"in x&&"AGENT_NAME"in x)return 1;if(e&&!t&&o===void 0)return 0;let i=o||0;if(x.TERM==="dumb")return i;if(qr.default.platform==="win32"){let s=Ef.default.release().split(".");return Number(s[0])>=10&&Number(s[2])>=10586?Number(s[2])>=14931?3:2:1}if("CI"in x)return"GITHUB_ACTIONS"in x||"GITEA_ACTIONS"in x?3:["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","BUILDKITE","DRONE"].some(s=>s in x)||x.CI_NAME==="codeship"?1:i;if("TEAMCITY_VERSION"in x)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(x.TEAMCITY_VERSION)?1:0;if(x.COLORTERM==="truecolor"||x.TERM==="xterm-kitty")return 3;if("TERM_PROGRAM"in x){let s=Number.parseInt((x.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(x.TERM_PROGRAM){case"iTerm.app":return s>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(x.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(x.TERM)||"COLORTERM"in x?1:i}function gf(e,t={}){let r=_F(e,{streamIsTTY:e&&e.isTTY,...t});return TF(r)}var OF={stdout:gf({isTTY:Ho.default.isatty(1)}),stderr:gf({isTTY:Ho.default.isatty(2)})},yf=OF;function bf(e,t,r){let n=e.indexOf(t);if(n===-1)return e;let o=t.length,i=0,s="";do s+=e.slice(i,n)+t+r,i=n+o,n=e.indexOf(t,i);while(n!==-1);return s+=e.slice(i),s}function Sf(e,t,r,n){let o=0,i="";do{let s=e[n-1]==="\r";i+=e.slice(o,s?n-1:n)+t+(s?`\r
`:`
`)+r,o=n+1,n=e.indexOf(`
`,o)}while(n!==-1);return i+=e.slice(o),i}var{stdout:wf,stderr:Cf}=yf,Ko=Symbol("GENERATOR"),Ye=Symbol("STYLER"),Ft=Symbol("IS_EMPTY"),xf=["ansi","ansi","ansi256","ansi16m"],qe=Object.create(null),RF=(e,t={})=>{if(t.level&&!(Number.isInteger(t.level)&&t.level>=0&&t.level<=3))throw new Error("The `level` option should be an integer from 0 to 3");let r=wf?wf.level:0;e.level=t.level===void 0?r:t.level};var IF=e=>{let t=(...r)=>r.join(" ");return RF(t,e),Object.setPrototypeOf(t,gt.prototype),t};function gt(e){return IF(e)}Object.setPrototypeOf(gt.prototype,Function.prototype);for(let[e,t]of Object.entries(q))qe[e]={get(){let r=Hr(this,Jo(t.open,t.close,this[Ye]),this[Ft]);return Object.defineProperty(this,e,{value:r}),r}};qe.visible={get(){let e=Hr(this,this[Ye],!0);return Object.defineProperty(this,"visible",{value:e}),e}};var Xo=(e,t,r,...n)=>e==="rgb"?t==="ansi16m"?q[r].ansi16m(...n):t==="ansi256"?q[r].ansi256(q.rgbToAnsi256(...n)):q[r].ansi(q.rgbToAnsi(...n)):e==="hex"?Xo("rgb",t,r,...q.hexToRgb(...n)):q[r][e](...n),vF=["rgb","hex","ansi256"];for(let e of vF){qe[e]={get(){let{level:r}=this;return function(...n){let o=Jo(Xo(e,xf[r],"color",...n),q.color.close,this[Ye]);return Hr(this,o,this[Ft])}}};let t="bg"+e[0].toUpperCase()+e.slice(1);qe[t]={get(){let{level:r}=this;return function(...n){let o=Jo(Xo(e,xf[r],"bgColor",...n),q.bgColor.close,this[Ye]);return Hr(this,o,this[Ft])}}}}var MF=Object.defineProperties(()=>{},{...qe,level:{enumerable:!0,get(){return this[Ko].level},set(e){this[Ko].level=e}}}),Jo=(e,t,r)=>{let n,o;return r===void 0?(n=e,o=t):(n=r.openAll+e,o=t+r.closeAll),{open:e,close:t,openAll:n,closeAll:o,parent:r}},Hr=(e,t,r)=>{let n=(...o)=>PF(n,o.length===1?""+o[0]:o.join(" "));return Object.setPrototypeOf(n,MF),n[Ko]=e,n[Ye]=t,n[Ft]=r,n},PF=(e,t)=>{if(e.level<=0||!t)return e[Ft]?"":t;let r=e[Ye];if(r===void 0)return t;let{openAll:n,closeAll:o}=r;if(t.includes("\x1B"))for(;r!==void 0;)t=bf(t,r.close,r.open),r=r.parent;let i=t.indexOf(`
`);return i!==-1&&(t=Sf(t,o,n,i)),n+t+o};Object.defineProperties(gt.prototype,qe);var $F=gt(),o3=gt({level:Cf?Cf.level:0});var se=$F;var U=E(require("node:process"),1);function Zo(){return U.default.platform!=="win32"?U.default.env.TERM!=="linux":!!U.default.env.CI||!!U.default.env.WT_SESSION||!!U.default.env.TERMINUS_SUBLIME||U.default.env.ConEmuTask==="{cmd::Cmder}"||U.default.env.TERM_PROGRAM==="Terminus-Sublime"||U.default.env.TERM_PROGRAM==="vscode"||U.default.env.TERM==="xterm-256color"||U.default.env.TERM==="alacritty"||U.default.env.TERMINAL_EMULATOR==="JetBrains-JediTerm"}var NF={info:se.blue("\u2139"),success:se.green("\u2714"),warning:se.yellow("\u26A0"),error:se.red("\u2716")},LF={info:se.blue("i"),success:se.green("\u221A"),warning:se.yellow("\u203C"),error:se.red("\xD7")},jF=Zo()?NF:LF,Et=jF;function Qo({onlyFirst:e=!1}={}){let r=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?(?:\\u0007|\\u001B\\u005C|\\u009C))","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-nq-uy=><~]))"].join("|");return new RegExp(r,e?void 0:"g")}var UF=Qo();function yt(e){if(typeof e!="string")throw new TypeError(`Expected a \`string\`, got \`${typeof e}\``);return e.replace(UF,"")}function Af(e){return e===161||e===164||e===167||e===168||e===170||e===173||e===174||e>=176&&e<=180||e>=182&&e<=186||e>=188&&e<=191||e===198||e===208||e===215||e===216||e>=222&&e<=225||e===230||e>=232&&e<=234||e===236||e===237||e===240||e===242||e===243||e>=247&&e<=250||e===252||e===254||e===257||e===273||e===275||e===283||e===294||e===295||e===299||e>=305&&e<=307||e===312||e>=319&&e<=322||e===324||e>=328&&e<=331||e===333||e===338||e===339||e===358||e===359||e===363||e===462||e===464||e===466||e===468||e===470||e===472||e===474||e===476||e===593||e===609||e===708||e===711||e>=713&&e<=715||e===717||e===720||e>=728&&e<=731||e===733||e===735||e>=768&&e<=879||e>=913&&e<=929||e>=931&&e<=937||e>=945&&e<=961||e>=963&&e<=969||e===1025||e>=1040&&e<=1103||e===1105||e===8208||e>=8211&&e<=8214||e===8216||e===8217||e===8220||e===8221||e>=8224&&e<=8226||e>=8228&&e<=8231||e===8240||e===8242||e===8243||e===8245||e===8251||e===8254||e===8308||e===8319||e>=8321&&e<=8324||e===8364||e===8451||e===8453||e===8457||e===8467||e===8470||e===8481||e===8482||e===8486||e===8491||e===8531||e===8532||e>=8539&&e<=8542||e>=8544&&e<=8555||e>=8560&&e<=8569||e===8585||e>=8592&&e<=8601||e===8632||e===8633||e===8658||e===8660||e===8679||e===8704||e===8706||e===8707||e===8711||e===8712||e===8715||e===8719||e===8721||e===8725||e===8730||e>=8733&&e<=8736||e===8739||e===8741||e>=8743&&e<=8748||e===8750||e>=8756&&e<=8759||e===8764||e===8765||e===8776||e===8780||e===8786||e===8800||e===8801||e>=8804&&e<=8807||e===8810||e===8811||e===8814||e===8815||e===8834||e===8835||e===8838||e===8839||e===8853||e===8857||e===8869||e===8895||e===8978||e>=9312&&e<=9449||e>=9451&&e<=9547||e>=9552&&e<=9587||e>=9600&&e<=9615||e>=9618&&e<=9621||e===9632||e===9633||e>=9635&&e<=9641||e===9650||e===9651||e===9654||e===9655||e===9660||e===9661||e===9664||e===9665||e>=9670&&e<=9672||e===9675||e>=9678&&e<=9681||e>=9698&&e<=9701||e===9711||e===9733||e===9734||e===9737||e===9742||e===9743||e===9756||e===9758||e===9792||e===9794||e===9824||e===9825||e>=9827&&e<=9829||e>=9831&&e<=9834||e===9836||e===9837||e===9839||e===9886||e===9887||e===9919||e>=9926&&e<=9933||e>=9935&&e<=9939||e>=9941&&e<=9953||e===9955||e===9960||e===9961||e>=9963&&e<=9969||e===9972||e>=9974&&e<=9977||e===9979||e===9980||e===9982||e===9983||e===10045||e>=10102&&e<=10111||e>=11094&&e<=11097||e>=12872&&e<=12879||e>=57344&&e<=63743||e>=65024&&e<=65039||e===65533||e>=127232&&e<=127242||e>=127248&&e<=127277||e>=127280&&e<=127337||e>=127344&&e<=127373||e===127375||e===127376||e>=127387&&e<=127404||e>=917760&&e<=917999||e>=983040&&e<=1048573||e>=1048576&&e<=1114109}function Bf(e){return e===12288||e>=65281&&e<=65376||e>=65504&&e<=65510}function Tf(e){return e>=4352&&e<=4447||e===8986||e===8987||e===9001||e===9002||e>=9193&&e<=9196||e===9200||e===9203||e===9725||e===9726||e===9748||e===9749||e>=9776&&e<=9783||e>=9800&&e<=9811||e===9855||e>=9866&&e<=9871||e===9875||e===9889||e===9898||e===9899||e===9917||e===9918||e===9924||e===9925||e===9934||e===9940||e===9962||e===9970||e===9971||e===9973||e===9978||e===9981||e===9989||e===9994||e===9995||e===10024||e===10060||e===10062||e>=10067&&e<=10069||e===10071||e>=10133&&e<=10135||e===10160||e===10175||e===11035||e===11036||e===11088||e===11093||e>=11904&&e<=11929||e>=11931&&e<=12019||e>=12032&&e<=12245||e>=12272&&e<=12287||e>=12289&&e<=12350||e>=12353&&e<=12438||e>=12441&&e<=12543||e>=12549&&e<=12591||e>=12593&&e<=12686||e>=12688&&e<=12773||e>=12783&&e<=12830||e>=12832&&e<=12871||e>=12880&&e<=42124||e>=42128&&e<=42182||e>=43360&&e<=43388||e>=44032&&e<=55203||e>=63744&&e<=64255||e>=65040&&e<=65049||e>=65072&&e<=65106||e>=65108&&e<=65126||e>=65128&&e<=65131||e>=94176&&e<=94180||e===94192||e===94193||e>=94208&&e<=100343||e>=100352&&e<=101589||e>=101631&&e<=101640||e>=110576&&e<=110579||e>=110581&&e<=110587||e===110589||e===110590||e>=110592&&e<=110882||e===110898||e>=110928&&e<=110930||e===110933||e>=110948&&e<=110951||e>=110960&&e<=111355||e>=119552&&e<=119638||e>=119648&&e<=119670||e===126980||e===127183||e===127374||e>=127377&&e<=127386||e>=127488&&e<=127490||e>=127504&&e<=127547||e>=127552&&e<=127560||e===127568||e===127569||e>=127584&&e<=127589||e>=127744&&e<=127776||e>=127789&&e<=127797||e>=127799&&e<=127868||e>=127870&&e<=127891||e>=127904&&e<=127946||e>=127951&&e<=127955||e>=127968&&e<=127984||e===127988||e>=127992&&e<=128062||e===128064||e>=128066&&e<=128252||e>=128255&&e<=128317||e>=128331&&e<=128334||e>=128336&&e<=128359||e===128378||e===128405||e===128406||e===128420||e>=128507&&e<=128591||e>=128640&&e<=128709||e===128716||e>=128720&&e<=128722||e>=128725&&e<=128727||e>=128732&&e<=128735||e===128747||e===128748||e>=128756&&e<=128764||e>=128992&&e<=129003||e===129008||e>=129292&&e<=129338||e>=129340&&e<=129349||e>=129351&&e<=129535||e>=129648&&e<=129660||e>=129664&&e<=129673||e>=129679&&e<=129734||e>=129742&&e<=129756||e>=129759&&e<=129769||e>=129776&&e<=129784||e>=131072&&e<=196605||e>=196608&&e<=262141}function kF(e){if(!Number.isSafeInteger(e))throw new TypeError(`Expected a code point, got \`${typeof e}\`.`)}function _f(e,{ambiguousAsWide:t=!1}={}){return kF(e),Bf(e)||Tf(e)||t&&Af(e)?2:1}var If=E(Rf(),1),GF=new Intl.Segmenter,WF=/^\p{Default_Ignorable_Code_Point}$/u;function ei(e,t={}){if(typeof e!="string"||e.length===0)return 0;let{ambiguousIsNarrow:r=!0,countAnsiEscapeCodes:n=!1}=t;if(n||(e=yt(e)),e.length===0)return 0;let o=0,i={ambiguousAsWide:!r};for(let{segment:s}of GF.segment(e)){let a=s.codePointAt(0);if(!(a<=31||a>=127&&a<=159)&&!(a>=8203&&a<=8207||a===65279)&&!(a>=768&&a<=879||a>=6832&&a<=6911||a>=7616&&a<=7679||a>=8400&&a<=8447||a>=65056&&a<=65071)&&!(a>=55296&&a<=57343)&&!(a>=65024&&a<=65039)&&!WF.test(s)){if((0,If.default)().test(s)){o+=2;continue}o+=_f(a,i)}}return o}function ti({stream:e=process.stdout}={}){return!!(e&&e.isTTY&&process.env.TERM!=="dumb"&&!("CI"in process.env))}var H=E(require("node:process"),1),zF=3,ri=class{#r=0;start(){this.#r++,this.#r===1&&this.#o()}stop(){if(this.#r<=0)throw new Error("`stop` called more times than `start`");this.#r--,this.#r===0&&this.#e()}#o(){H.default.platform==="win32"||!H.default.stdin.isTTY||(H.default.stdin.setRawMode(!0),H.default.stdin.on("data",this.#n),H.default.stdin.resume())}#e(){H.default.stdin.isTTY&&(H.default.stdin.off("data",this.#n),H.default.stdin.pause(),H.default.stdin.setRawMode(!1))}#n(t){t[0]===zF&&H.default.emit("SIGINT")}},VF=new ri,ni=VF;var YF=E(qo(),1),oi=class{#r=0;#o=!1;#e=0;#n=-1;#a=0;#t;#s;#i;#p;#m;#l;#f;#D;#h;#u;#c;color;constructor(t){typeof t=="string"&&(t={text:t}),this.#t={color:"cyan",stream:bt.default.stderr,discardStdin:!0,hideCursor:!0,...t},this.color=this.#t.color,this.spinner=this.#t.spinner,this.#m=this.#t.interval,this.#i=this.#t.stream,this.#l=typeof this.#t.isEnabled=="boolean"?this.#t.isEnabled:ti({stream:this.#i}),this.#f=typeof this.#t.isSilent=="boolean"?this.#t.isSilent:!1,this.text=this.#t.text,this.prefixText=this.#t.prefixText,this.suffixText=this.#t.suffixText,this.indent=this.#t.indent,bt.default.env.NODE_ENV==="test"&&(this._stream=this.#i,this._isEnabled=this.#l,Object.defineProperty(this,"_linesToClear",{get(){return this.#r},set(r){this.#r=r}}),Object.defineProperty(this,"_frameIndex",{get(){return this.#n}}),Object.defineProperty(this,"_lineCount",{get(){return this.#e}}))}get indent(){return this.#D}set indent(t=0){if(!(t>=0&&Number.isInteger(t)))throw new Error("The `indent` option must be an integer from 0 and up");this.#D=t,this.#d()}get interval(){return this.#m??this.#s.interval??100}get spinner(){return this.#s}set spinner(t){if(this.#n=-1,this.#m=void 0,typeof t=="object"){if(t.frames===void 0)throw new Error("The given spinner must have a `frames` property");this.#s=t}else if(!Je())this.#s=St.default.line;else if(t===void 0)this.#s=St.default.dots;else if(t!=="default"&&St.default[t])this.#s=St.default[t];else throw new Error(`There is no built-in spinner named '${t}'. See https://github.com/sindresorhus/cli-spinners/blob/main/spinners.json for a full list.`)}get text(){return this.#h}set text(t=""){this.#h=t,this.#d()}get prefixText(){return this.#u}set prefixText(t=""){this.#u=t,this.#d()}get suffixText(){return this.#c}set suffixText(t=""){this.#c=t,this.#d()}get isSpinning(){return this.#p!==void 0}#F(t=this.#u,r=" "){return typeof t=="string"&&t!==""?t+r:typeof t=="function"?t()+r:""}#g(t=this.#c,r=" "){return typeof t=="string"&&t!==""?r+t:typeof t=="function"?r+t():""}#d(){let t=this.#i.columns??80,r=this.#F(this.#u,"-"),n=this.#g(this.#c,"-"),o=" ".repeat(this.#D)+r+"--"+this.#h+"--"+n;this.#e=0;for(let i of yt(o).split(`
`))this.#e+=Math.max(1,Math.ceil(ei(i,{countAnsiEscapeCodes:!0})/t))}get isEnabled(){return this.#l&&!this.#f}set isEnabled(t){if(typeof t!="boolean")throw new TypeError("The `isEnabled` option must be a boolean");this.#l=t}get isSilent(){return this.#f}set isSilent(t){if(typeof t!="boolean")throw new TypeError("The `isSilent` option must be a boolean");this.#f=t}frame(){let t=Date.now();(this.#n===-1||t-this.#a>=this.interval)&&(this.#n=++this.#n%this.#s.frames.length,this.#a=t);let{frames:r}=this.#s,n=r[this.#n];this.color&&(n=af[this.color](n));let o=typeof this.#u=="string"&&this.#u!==""?this.#u+" ":"",i=typeof this.text=="string"?" "+this.text:"",s=typeof this.#c=="string"&&this.#c!==""?" "+this.#c:"";return o+n+i+s}clear(){if(!this.#l||!this.#i.isTTY)return this;this.#i.cursorTo(0);for(let t=0;t<this.#r;t++)t>0&&this.#i.moveCursor(0,-1),this.#i.clearLine(1);return(this.#D||this.lastIndent!==this.#D)&&this.#i.cursorTo(this.#D),this.lastIndent=this.#D,this.#r=0,this}render(){return this.#f?this:(this.clear(),this.#i.write(this.frame()),this.#r=this.#e,this)}start(t){return t&&(this.text=t),this.#f?this:this.#l?this.isSpinning?this:(this.#t.hideCursor&&Yo.hide(this.#i),this.#t.discardStdin&&bt.default.stdin.isTTY&&(this.#o=!0,ni.start()),this.render(),this.#p=setInterval(this.render.bind(this),this.interval),this):(this.text&&this.#i.write(`- ${this.text}
`),this)}stop(){return this.#l?(clearInterval(this.#p),this.#p=void 0,this.#n=0,this.clear(),this.#t.hideCursor&&Yo.show(this.#i),this.#t.discardStdin&&bt.default.stdin.isTTY&&this.#o&&(ni.stop(),this.#o=!1),this):this}succeed(t){return this.stopAndPersist({symbol:Et.success,text:t})}fail(t){return this.stopAndPersist({symbol:Et.error,text:t})}warn(t){return this.stopAndPersist({symbol:Et.warning,text:t})}info(t){return this.stopAndPersist({symbol:Et.info,text:t})}stopAndPersist(t={}){if(this.#f)return this;let r=t.prefixText??this.#u,n=this.#F(r," "),o=t.symbol??" ",i=t.text??this.text,a=typeof i=="string"?(o?" ":"")+i:"",u=t.suffixText??this.#c,l=this.#g(u," "),c=n+o+a+l+`
`;return this.stop(),this.#i.write(c),this}};function ii(e){return new oi(e)}var v3=(0,I.blue)((0,I.dim)("internal only"));function de(e,t,r){console.log(Mf[e]+t),typeof r?.exit<"u"&&process.exit(r.exit)}async function vf(e,t,r){if(!qF){de("wait",e);try{let o=await t();o&&console.log(o),de("success",e);return}catch(o){return de("error",e),r?.printError!==!1&&console.log((0,I.red)(o.message)),o}}let n=ii({spinner:"simpleDots",prefixText:Mf.wait+e}).start();try{let o=await t();n.stop(),de("success",e),o&&console.log(o)}catch(o){return n.stop(),de("error",e),r?.printError!==!1&&console.error(o.message),o}}var Mf={wait:`\u{1F550}${(0,I.blue)("wait")}  - `,info:`${process.env.GITHUB_ACTIONS==="true"?"\u2139\uFE0F":"\u2139\uFE0F "}${(0,I.cyan)("info")}  - `,success:`\u2705${(0,I.green)("ready")}  - `,warn:`${process.env.GITHUB_ACTIONS==="true"?"\u26A0\uFE0F":"\u26A0\uFE0F "}${(0,I.yellow)("warn")}  - `,error:`\u{1F4A5}${(0,I.red)("error")}  - `,event:`\u26A1\uFE0F${(0,I.magenta)("event")}  - `,paymentPrompt:`\u{1F4B0}${(0,I.yellowBright)("plan")}  - `},qF=!0;var me=E(require("node:fs")),si=E(require("node:path")),Kr=class e{structs=new Map;functions=[];static analyzePackage(t){let r=new e;return r.parseDirectory(si.join(t,"src")),{functions:r.functions,structs:Array.from(r.structs.values())}}parseDirectory(t){if(!me.existsSync(t))return;let r=me.readdirSync(t);for(let n of r){let o=si.join(t,n);if(me.statSync(o).isDirectory())this.parseDirectory(o);else if(n.endsWith(".rs")){let i=me.readFileSync(o,"utf-8");this.parseRustFile(i)}}}parseRustFile(t){let r=this.removeComments(t);this.parseStructs(r),this.parseFunctions(r)}removeComments(t){return t=t.replace(/\/\/.*$/gm,""),t=t.replace(/\/\*[\s\S]*?\*\//g,""),t}parseStructs(t){let r=/#\[derive\([^)]*(?:Serialize|Deserialize)[^)]*\)\]\s*(?:pub\s+)?struct\s+(\w+)\s*\{([^}]+)\}/g,n;for(;(n=r.exec(t))!==null;){let o=n[1],i=n[2],s=this.parseStructFields(i);this.structs.set(o,{name:o,fields:s})}}parseStructFields(t){let r=[],n=/(?:pub\s+)?(\w+)\s*:\s*([^,\n]+)/g,o;for(;(o=n.exec(t))!==null;){let i=o[1],s=o[2].trim();r.push({name:i,rustType:s,tsType:this.rustTypeToTypeScript(s)})}return r}parseFunctions(t){let r=/#\[raycast\]\s*(?:pub\s+)?(async\s+)?fn\s+(\w+)\s*\(([^)]*)\)\s*(?:->\s*([^{]+?))?\s*\{/g,n;for(;(n=r.exec(t))!==null;){let o=!!n[1],i=n[2],s=n[3],a=n[4]?.trim()||"()",u=this.parseParameters(s),{returnType:l,returnTypeTs:c,isResult:f}=this.parseReturnType(a);this.functions.push({name:i,isAsync:o,parameters:u,returnType:l,returnTypeTs:c,isResult:f})}}parseParameters(t){let r=[];if(!t.trim())return r;let n=this.splitParameters(t);for(let o of n){let i=o.trim().match(/(\w+)\s*:\s*(.+)/);if(i){let s=i[1],a=i[2].trim();r.push({name:s,rustType:a,tsType:this.rustTypeToTypeScript(a)})}}return r}splitParameters(t){let r=[],n="",o=0;for(let i of t){if(i==="<")o++;else if(i===">")o--;else if(i===","&&o===0){r.push(n.trim()),n="";continue}n+=i}return n.trim()&&r.push(n.trim()),r}parseReturnType(t){let r=t.trim(),n=r.match(/Result<([^,]+),\s*(.+)>/);if(n){let o=n[1].trim();return{returnType:r,returnTypeTs:this.rustTypeToTypeScript(o),isResult:!0}}return{returnType:r,returnTypeTs:this.rustTypeToTypeScript(r),isResult:!1}}rustTypeToTypeScript(t){let r=t.trim();if(r==="()")return"void";if(r==="String"||r==="&str")return"string";if(r==="bool")return"boolean";if(r.match(/^[ui]\d+$/)||r.match(/^f\d+$/))return"number";let n=r.match(/Option<(.+)>/);if(n)return`${this.rustTypeToTypeScript(n[1])} | null`;let o=r.match(/Vec<(.+)>/);if(o)return`${this.rustTypeToTypeScript(o[1])}[]`;let i=r.match(/Result<([^,]+),\s*(.+)>/);return i?this.rustTypeToTypeScript(i[1]):(this.structs.has(r),r)}},wt=class{static generateDefinitions(t,r){let n="";for(let o of r){n+=`export interface ${o.name} {
`;for(let i of o.fields)n+=`  ${i.name}: ${i.tsType};
`;n+=`}

`}for(let o of t){let i=o.parameters.map(a=>`${a.name}: ${a.tsType}`).join(", "),s=o.returnTypeTs==="void"?"Promise<void>":`Promise<${o.returnTypeTs}>`;n+=`export declare function ${o.name}(${i}): ${s};
`}return n}static generateImplementation(t){let r=`const proxy = {
`;for(let n of t){let o=n.parameters.map(s=>s.name).join(", "),i=n.parameters.length>0?`, ${o}`:"";r+=`  ${n.name}: async (${n.parameters.map(s=>`${s.name}`).join(", ")}) => {
`,r+=`    return await runRustFunction("${n.name}"${i});
`,r+=`  },
`}r+=`};

`;for(let n of t)r+=`export const ${n.name} = proxy.${n.name};
`;return r+=`
export default proxy;
`,r}};var he="x86_64-pc-windows-msvc";function HF(e,t,r){let n=e?"release":"debug";return{name:"rust-files",setup(o){o.onResolve({filter:/^rust:/},i=>({path:M.default.join(i.resolveDir,i.path.replace(/^rust:/,"")),namespace:"rust",sideEffects:!1})),o.onLoad({filter:/.*/,namespace:"rust"},async i=>{let s=i.path,a=M.default.basename(s),u=M.default.join("compiled_raycast_rust",a),l="target",c=a,f="";if(!_.existsSync(M.default.join(s,"Cargo.toml")))return{errors:[{text:`failed to import Rust package from "${s}". Make sure you reference the folder that includes the 'Cargo.toml' file`}]};if(!e&&process.platform!=="win32")de("warn",`skipping Rust compilation for ${i.path} because the current platform is not Windows`);else{let D=await vf("compiling rust package",async()=>{let p=M.default.join(s,"Cargo.toml"),d=_.readFileSync(p,"utf-8"),g=d.match(/^name\s*=\s*"([^"]+)"/m);g&&(f=g[1],c=f);let w=d.match(/\[\[bin\]\]\s*\nname\s*=\s*"([^"]+)"/);if(w&&(c=w[1]),!f)throw new Error("failed to read the package name from Cargo.toml");let y=(process.platform==="win32","cargo"),A=process.platform==="win32"?["build","--target",he]:["xwin","build","--target",he];n==="release"&&A.push("--release"),await Pf(y,A,s);let T=M.default.join(s,l,he,n,`${c}.exe`);if(!_.existsSync(T))throw new Error(`failed to compile rust: Could not find the compiled Windows binary at ${T}.

Make sure you have the Windows target installed: 'rustup target add ${he}'
Then try running 'cargo clean' and try again.`);if((!_.existsSync(M.default.join("assets",u))||!ZF(T,M.default.join("assets",u)))&&(_.mkdirSync(M.default.dirname(M.default.join("assets",u)),{recursive:!0}),_.cpSync(T,M.default.join("assets",u),{recursive:!0,force:!0})),t){let R=M.default.join(s,"src"),k=process.cwd();try{R=M.default.relative(k,R)}catch{}if(!_.existsSync(R))throw new Error(`failed to compile rust: Could not find Rust sources directory ${R}`);t(R)}},{printError:!0});if(D)return{errors:[{text:`failed to import Rust package from "${s}". ${D.message}`}]}}try{let{functions:D,structs:p}=Kr.analyzePackage(s),d=wt.generateDefinitions(D,p),g=wt.generateImplementation(D);return r(KF(a,d)),{contents:JF(u,g),loader:"js"}}catch(D){return{errors:[{text:`failed to parse Rust source files: ${D.message}`}]}}})}}}function KF(e,t){return`declare module "rust:*/${e}" {
${XF(t)}

  export class RustError extends Error {
    stderr: string;
    stdout: string;
  }
}`}function XF(e){let t=e.split(`
`);for(let r=0;r<t.length;r++)t[r]="  "+t[r];return t.join(`
`)}function JF(e,t){return`
import { environment } from "@raycast/api";
import { join } from "path";
import { chmod } from "fs/promises";
import { spawn } from "child_process";

async function runRustFunction(command, ...args) {
  if (process.platform !== "win32") {
    return Promise.reject(new Error("Rust functions are not supported on macOS"));
  }
  const rustPath = join(environment.assetsPath, "${e}");
  await chmod(rustPath, "755");

  return new Promise((resolve, reject) => {
    const child = spawn(rustPath, [command], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    const stdout = [];
    const stderr = [];

    child.stdout?.on("data", (data) => {
      stdout.push(data.toString());
    });

    child.stderr?.on("data", (data) => {
      stderr.push(data.toString());
    });

    child.on("exit", (code) => {
      if (code === 0) {
        try {
          const result = stdout.join("").trim();
          if (result.length != 0) {
            resolve(JSON.parse(result));
          } else {
            resolve(null);
          }
        } catch (err) {
          const error = new RustError("Failed to deserialize result from JSON: " + err.message);
          error.stdout = stdout.join("").trim();
          error.stderr = stderr.join("").trim();
          reject(error);
        }
      } else {
        const error = new RustError(stderr.join("").trim() || stdout.join("").trim() || "Could not get any data");
        error.stdout = stdout.join("").trim();
        error.stderr = stderr.join("").trim();
        reject(error);
      }
    });

    child.on("error", (error) => {
      reject(error);
    });

    // Send arguments as JSON via stdin
    try {
      const jsonArgs = JSON.stringify(args);
      child.stdin.write(jsonArgs);
      child.stdin.end();
    } catch (err) {
      reject(new RustError("Failed to serialize input to JSON: " + err.message));
    }
  });
}

${t}

export class RustError extends Error {
  constructor(message) {
    super(message);
    this.name = "RustError";
  }
}
`}var Xr=64e3;function ZF(e,t){let r=_.openSync(e,"r"),n=_.openSync(t,"r");try{let o=Buffer.alloc(Xr),i=Buffer.alloc(Xr);for(;;){let s=_.readSync(r,o,0,Xr,null),a=_.readSync(n,i,0,Xr,null);if(s!==a)return!1;if(s===0&&a===0)return!0;if(!o.equals(i))return!1}}finally{_.closeSync(r),_.closeSync(n)}}async function Pf(e,t,r,n=!1){let o=await jo(e,t,{cwd:r,preferLocal:!0,reject:!1});if(o.failed){if(o.stderr.includes("no such command: `xwin`")){if(process.env.CI==="true"&&!n){let i=await jo("cargo",["install","cargo-xwin"],{cwd:r,reject:!1});if(i.failed)throw new Error(`failed to compile rust: ${o.stderr}

The 'xwin' command is not available. Please install the 'cargo-xwin' package with:
  cargo install cargo-xwin

Error installing cargo-xwin: ${i.stderr}`);return de("info","Installed cargo-xwin successfully"),await Pf(e,t,r,!0)}throw new Error(`failed to compile rust: ${o.stderr}

The 'xwin' command is not available. Please install the 'cargo-xwin' package with:
  cargo install cargo-xwin`)}throw o.stderr.includes("target")&&o.stderr.includes(he)?new Error(`failed to compile rust: ${o.stderr}

Windows target not found. Please install it with:
  rustup target add ${he}`):o.stderr.includes("rustc")||o.stderr.includes("cargo")?new Error(`failed to compile rust: ${o.stderr}

Rust compilation error detected. Please ensure Rust is correctly installed and your code compiles with 'cargo build --target ${he}'`):new Error(`failed to compile rust: ${o.stderr}

You can try running 'cargo clean' and then 'cargo build --target ${he}' manually to see the full error`)}return o.stdout}0&&(module.exports={rustFiles});
